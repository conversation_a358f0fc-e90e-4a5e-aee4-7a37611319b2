.department-list {
    display: flex;

    position: relative;
    /* width: 1217px; */
    margin-left: auto;
    margin-right: auto;
}

.dept-item {
    width: 125px;
    height: 92px;
    background: #F1F1F1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    position: relative;
    cursor: pointer;
    margin-right: 10.85px;
    transition: all 0.3s ease;
}

.dept-item.active {
    background: #FFFFFF;
    color: black;
}

.dept-item:last-child {
    margin-right: 0;
}

.dept-description {
    max-width: 1352px;
    height: 74px;
    padding: 25px 0;
    text-align: left;
    background: #FFFFFF;
    margin: 40px auto 0;
}

.dept-description h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    margin-bottom: 16px;
}

.dept-description p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    margin: 0 auto;
    margin-left: 26px;
}

/* 加入我们页面样式 */
.joinus-banner{
    position: relative;
    width: 100%;
    height: 450px;
    background-image: url('../images/profile-bg.png');
    background-size: cover;
    background-position: center;
    margin-top: 56px;
    display: flex;
    align-items: center;
}

.joinus-banner-title {
    color: white;
    font-weight: 500;
    font-size: 36px;
    margin-left: 283px;
}

.joinus-tabs {
    background-color: white;
    border-bottom: 1px solid #EAEAEA;
}

.joinus-tabs .nav-link {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    padding: 1rem 2rem;
    border: none;
}

.joinus-tabs .nav-link.active {
    border-bottom: 2px solid #0052d9;
    background-color: transparent;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
}

.container-content {
    width: 100%;
    background-color: white;
    margin: 0 auto;
    padding-bottom: 50px;
}

.container-content h1 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    width: 1352px;
    text-align: left;
    margin: auto;
    padding: 50px 0;
}
.team-container{
    display: grid;
    grid-template-columns: repeat(4, 338px);
    background-color: #FBFBFB;
    max-width: 1352px;
    margin: auto;
    justify-content: center;

}

h1 {
    font-size: 24px;
    font-weight: normal;
    margin: 30px 0;
    color: #333;
}

.team-grid {
    width: 338px;
    height: 338px;
    display: flex;
    flex-wrap: wrap;
    transition: all 0.3s ease;
    position: relative;
    background-color: #FBFBFB;
}

.team-info{
    width: 222px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    padding-left: 26px;
}

.team-info h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #123DEA;
    line-height: 25px;
    transition: color 0.3s ease;
}

.team-info p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    transition: color 0.3s ease;
}

.team-member {
    width: 116px;
    height: 116px;
    overflow: hidden;
}

.image-wrapper {
    position: relative;
    width: 116px;
    height: 116px;
    overflow: hidden;
}

.team-member img {
    width: 116px;
    height: 116px;
    object-fit: cover;
    /* border: 1px dashed #ABACAE; */
    transition: transform 0.5s ease;
}

.image-wrapper::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 25px 25px 0px;
    border-color: transparent transparent #0047cc transparent;
    transition: transform 0.3s ease;
}

/* 鼠标悬停特效 - 仅适用于桌面和大型平板 */
@media (min-width: 993px) {
    .team-grid:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        z-index: 1;
        background-color: #ffffff;
    }
    
    .team-grid:hover .team-info h3 {
        color: #0047cc;
    }
    
    .team-grid:hover .team-info p {
        color: #333333;
    }
    
    .team-grid:hover .team-member img {
        transform: scale(1.05);
    }
    
    .team-grid:hover .image-wrapper::after {
        transform: translateY(5px);
    }
}

.team-grid h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #123DEA;
    line-height: 25px;
    margin: 0 0 5px 0;

}

.team-grid p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 26px;
    margin: 0;
}

.value-section {
    width: 1352px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 50px 0;
}

.value-image{
    /* border: 1px dashed #ABACAE; */
}

.value-text {
    width: 600px;
}

.value-text h2 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    margin-bottom: 40px;
}

.value-item {
    margin-bottom: 30px;
}

.value-item h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
    margin-bottom: 10px;
}

.value-item p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 22px;
    margin: 0;
}

.value-image img {
    width: 660px;
    object-fit: cover;
}

.office-section {
    width: 100%;
    background-color: white;
    margin: 40px 0 auto;
    padding: 30px 0;
}

.office-section h2 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    text-align: center;
    margin-bottom: 30px;
}

.office-grid {
    max-width: 1352px;
    margin: 0 auto;
    text-align: center;

}

.office-item {
    flex: 1;
    text-align: center;
}

.office-item h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #333333;
    line-height: 25px;
    margin-bottom: 16px;
    text-align: center;
}

.office-item p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
    text-align: left;
    margin: 0;
}

.office-item p:last-child {
    margin-top: 8px;
}

.business-structure {
    width: 100%;
    background-color: #FBFBFB;
    padding: 50px 0;
}

.business-structure h2 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    width: 1375px;
    text-align: left;
    margin: auto;
    padding-bottom: 50px;
}

.structure-content {
    width: 1375px;
    margin: 0 auto;
}

.logo-center {
    text-align: center;
    margin-bottom: 20px;
}

.logo-center img {
    height: 55px;
    object-fit: contain;
}

.structure-tree {
    text-align: center;
}

.structure-tree img {
    max-width: 1217px;
    height: 61px;
    object-fit: contain;
}

.company-intro {
    width: 100%;
    background-color: white;
    padding: 60px 0;
}

.company-intro .container {
    width: 1352px;
    margin: 0 auto;
}

.company-intro h2 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    margin-bottom: 40px;
    text-align: center;
}

.company-intro .intro-text {
    max-width: 1352px;
    margin: 0 auto;
    margin-bottom: 10px;
}
.company-intro .intro-text h2{
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 26px;
    text-align: left;
    margin-bottom: 24px;
}

.company-intro .intro-text p{
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 17px;
    text-align: left;
    margin-bottom: 24px;
}

.company-intro .intro-text p:last-child {
    margin-bottom: 0;
}
.intro-image{
    background-image: url('../images/baling-bg.png');
    background-size: cover;
    background-position: center;
    max-width: 1352px;
    height: 286px;
    /* border: 1px dashed #ABACAE; */
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    padding: 30px;
}

.mission-left {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ABACAE;
    padding-bottom: 5px;
    margin-bottom: 20px;
}

.mission-right {
    width: 100%;
    padding-left: 0;
}

/* 大屏幕时的左右布局 */
@media (min-width: 992px) {
    .intro-image {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        /* border: 2px dashed #ABACAE; */
        background-color: #F5F9FF;
        padding: 40px;
    }
    
    .mission-left {
        width: 50%;
        border-bottom: none;
        padding-right: 186px;
        padding-bottom: 0;
        margin-bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }
    
    .mission-left::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 180%;  
        background-color: #ABACAE;
    }
    
    .mission-right {
        width: 50%;
        padding-left: 60px;
        display: flex;
        flex-direction: column;
        gap: 0px;
        justify-content: center;
    }
    
    .mission-left h3 {
        font-size: 36px;
        text-align: center;
    }
    
    .mission-item {
        margin-bottom: 10px;
    }
}

.mission-left h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
}

.mission-item h4 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    margin-bottom: 8px;
}

.mission-item p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 24px;
    margin: 0;
}

.company-intro p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 28px;
    margin-bottom: 24px;
    text-align: justify;
}

.company-intro p:last-child {
    margin-bottom: 0;
}

.profile-nav {
    width: 100%;
    position: sticky;
    top: 60px;
    z-index: 100;
    background-color: #FBFBFB;
}

.profile-nav .container {
    width: 1352px;
    margin: 0 auto;
    padding-top: 20px;
}

.nav-list {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    justify-content: flex-start;
    gap: 98px;
}

.nav-item-tab {
    font-family: PingFangSC, PingFang SC;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    position: relative;
    white-space: nowrap;
}

.nav-item-tab a {
    color: #666666;
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-item-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #123DEA;
}

@media (max-width: 1350px) {
    .business-structure {
        display: none;
    }
}

@media (min-width: 1201px) and (max-width: 1350px) {
    .profile-nav .container,
    .company-intro .container,
    .structure-content,
    .team-container,
    .value-section,
    .office-grid,
    .container-content h1,
    .business-structure h2,
    .dept-description {
        max-width: 1200px;
        width: 95%;
        margin-left: auto;
        margin-right: auto;
    }
    
    .container-content h1 {
        width: 95%;
    }
    
    .team-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        justify-content: space-between;
    }
    
    .team-grid {
        width: 100%;
        height: auto;
        min-height: 338px;
    }
    
    .team-info {
        width: calc(100% - 116px);
        flex: 1;
        padding-left: 15px;
    }
    
    .department-list {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        max-width: 1200px;
    }
    
    .dept-item {
        width: 130px;
        margin-right: 8px;
        margin-bottom: 8px;
        font-size: 13px;
    }
    
    .dept-item:hover {
        background-color: #E6F7FF;
        color: #123DEA;
    }
    
    .structure-tree {
        text-align: center;
    }
    
    .structure-tree img {
        width: 100%;
        max-width: 1200px;
    }
    
    .value-section {
        display: flex;
        justify-content: space-between;
    }
    
    .value-image {
        width: 45%;
    }
    
    .value-image img {
        max-width: 100%;
        border: 1px dashed #ABACAE;
    }
    
    .value-text {
        width: 52%;
        padding-left: 20px;
    }
    
    .office-grid {
        gap: 30px;
    }
    
    .intro-image {
        width: 100%;
        max-width: 1200px;
        height: auto;
        min-height: 286px;
    }
}

@media (max-width: 1200px) {
    .profile-nav .container,
    .company-intro .container,
    .structure-content,
    .team-container,
    .value-section,
    .office-grid,
    .container-content h1,
    .business-structure h2,
    .dept-description {
        width: 90%;
        max-width: 1200px;
    }
    
    .team-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        justify-content: center;
        margin-left: auto;
        margin-right: auto;
    }
    
    .team-grid {
        width: 100%;
        height: auto;
        justify-content: center;
        align-items: center;
    }
    
    .team-info {
        width: auto;
        flex: 1;
        padding-left: 15px;
    }
    
    .structure-tree img {
        width: 100%;
        height: auto;
    }
    
    .intro-image {
        width: 100%;
        height: auto;
    }
    
    .value-image img {
        width: 100%;
        height: auto;
    }
    
    .office-grid {
        gap: 40px;
    }
    
    .nav-list {
        gap: 50px;
    }
}

@media (max-width: 992px) {
    .joinus-banner {
        height: 350px;
    }
    
    .joinus-banner-title {
        margin-left: 50px;
        font-size: 30px;
    }
    
    .container-content {
        padding-bottom: 40px;
    }
    
    .team-container {
        grid-template-columns: repeat(2, 1fr);
        justify-content: center;
        margin-left: auto;
        margin-right: auto;
    }
    
    .team-grid {
        justify-content: center;
        align-items: center;
    }
    
    .team-info {
        width: auto;
        flex: 1;
        padding-left: 15px;
    }
    
    .value-section {
        flex-direction: column;
        padding: 20px 0;
    }
    
    .value-text {
        width: 100%;
        margin-top: 30px;
    }
    
    .value-text h2 {
        margin-bottom: 25px;
    }
    
    .value-item {
        margin-bottom: 20px;
    }
    
    .value-image {
        width: 100%;
    }
    
    .office-section {
        margin: 30px 0 auto;
        padding: 20px 0;
    }
    
    .office-section h2 {
        margin-bottom: 20px;
    }
    
    .office-grid {
        flex-wrap: wrap;
    }
    
    .office-item {
        width: 48%;
        margin-bottom: 20px;
    }
    
    .department-list {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dept-item {
        margin-bottom: 10px;
    }
    
    .container-content h1,
    .business-structure h2,
    .value-section h2,
    .office-section h2 {
        font-size: 26px;
    }
    
    .nav-list {
        gap: 30px;
        overflow-x: auto;
        padding-bottom: 5px;
        justify-content: flex-start;
    }
}

@media (max-width: 768px) {
    .joinus-banner {
        height: 250px;
    }
    
    .joinus-banner-title {
        margin-left: 30px;
        font-size: 26px;
    }
    
    .profile-nav {
        display: none;
    }
    
    .container-content {
        padding-bottom: 30px;
    }
    
    .team-container {
        justify-content: center;
        margin-left: auto;
        margin-right: auto;
    }
    
    .team-grid {
        width: 100%;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    
    .team-member {
        margin-right: 15px;
    }
    
    .team-info {
        width: auto;
        flex: 1;
        padding-left: 15px;
        align-items: flex-start;
        margin-top: 0;
    }
    
    .container-content h1,
    .business-structure h2,
    .value-section h2,
    .office-section h2 {
        font-size: 24px;
        padding: 10px 0;
    }
    
    .value-section {
        padding: 10px 0;
    }
    
    .value-text h2 {
        margin-bottom: 10px;
    }
    
    .value-item {
        margin-bottom: 15px;
    }
    
    .value-item h3 {
        font-size: 18px;
        margin-bottom: 8px;
    }
    
    .value-item p {
        font-size: 14px;
    }
    
    .office-section {
        margin: 30px 0 auto;
        padding: 20px 0;
    }
    
    .office-section h2 {
        margin-bottom: 20px;
    }
    
    .office-grid {
        flex-direction: column;
        gap: 30px;
    }
    
    .office-item {
        width: 100%;
    }
    
    .nav-list {
        gap: 20px;
    }
    
    .nav-item-tab {
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .joinus-banner {
        height: 200px;
    }
    
    .joinus-banner-title {
        margin-left: 20px;
        font-size: 22px;
    }
    
    .container-content {
        padding-bottom: 20px;
    }
    
    /* .team-container {
        grid-template-columns: 1fr;
    } */
    
    .team-grid {
        margin-bottom: 20px;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .team-member {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .team-info {
        width: 100%;
        padding-left: 0;
        align-items: center;
    }
    
    .team-info h3,
    .team-info p {
        text-align: center;
    }
  
    
    .value-section {
        padding: 10px 0;
    }
    
    .value-text {
        margin-top: 20px;
    }
    
    .value-text h2 {
        margin-bottom: 15px;
    }
    
    .value-item {
        margin-bottom: 12px;
    }
    
    .value-item h3 {
        font-size: 16px;
        margin-bottom: 5px;
    }
    
    .value-item p {
        font-size: 12px;
        line-height: 1.4;
    }
    
    .office-section {
        margin: 20px 0 auto;
        padding: 15px 0;
    }
    
    .office-section h2 {
        margin-bottom: 15px;
    }
    
    .office-grid {
        gap: 20px;
    }
    .office-item {
        margin-bottom: 10px;
    }
    
    .team-member {
        width: 100px;
        height: 100px;
    }
    
    .image-wrapper {
        width: 100px;
        height: 100px;
    }
    
    .team-member img {
        width: 100px;
        height: 100px;
    }
    
    .image-wrapper::after {
        border-width: 0 20px 20px 0px;
    }
    
    .team-info h3 {
        font-size: 16px;
    }
    
    .team-info p {
        font-size: 14px;
    }
    
    .container-content h1,
    .business-structure h2,
    .value-section h2,
    .office-section h2 {
        font-size: 20px;
        padding: 8px 0;
    }
    
    .dept-item {
        width: 120px;
        height: 80px;
        font-size: 12px;
        margin-right: 8px;
    }
    
    .dept-description {
        height: auto;
        padding: 15px 0;
    }
    
    .dept-description p {
        margin-left: 0;
    }
    
    .company-intro .intro-text h2 {
        font-size: 16px;
        line-height: 24px;
    }
    
    .company-intro .intro-text p {
        font-size: 14px;
        line-height: 22px;
    }
    
    .mission-left h3 {
        font-size: 22px;
    }
    
    .mission-item h4 {
        font-size: 18px;
    }
    
    .mission-item p {
        font-size: 14px;
    }
    
    .value-item h3 {
        font-size: 18px;
    }
    
    .value-item p {
        font-size: 14px;
    }
    
    .office-item h3 {
        font-size: 16px;
    }
    
    .office-item p {
        font-size: 12px;
    }
    
    .nav-list {
        gap: 15px;
    }
    
    .nav-item-tab {
        font-size: 12px;
        padding-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .joinus-banner-title {
        margin-left: 15px;
        font-size: 20px;
    }
    
    .company-intro h2,
    .container-content h1,
    .business-structure h2,
    .value-section h2,
    .office-section h2 {
        font-size: 18px;
    }
    
    .nav-list {
        gap: 10px;
    }
}
/* 页面样式 */
.website-container {
    font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
  }
  
  .joinus-banner {
    background-image: url('/website/one/assets/images/profile-bg.png');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  
    color: white;
  }
  
  .joinus-banner-title {
      font-size: 36px;
  
  
  }
  
  .joinus-tabs {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 56px;
    z-index: 100;
  }
  
  @media (max-width: 768px) {
    .joinus-tabs {
      display: none;
    }
    
    .joinus-banner-title {
      font-size: 24px;
    }
    
    .company-intro .section-title {
      font-size: 20px;
      padding: 20px 0;
      width: 100%;
    }
    
    .intro-text h2 {
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 15px;
    }
    
    .intro-text {
      margin-bottom: 20px;
    }
    
    .company-intro {
      padding-bottom: 20px;
    }
    
    .mission-bg {
      flex-direction: column;
    }
    
    .mission-left,
    .mission-right {
      width: 100%;
    }
    
    .mission-left {
      padding: 20px;
    }
    
    .mission-right {
      padding: 20px;
    }
  }
  
  .nav-list {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    justify-content: center;
  }
  
  .nav-item-tab {
    margin: 0 15px;
  }
  
  .nav-link {
    display: block;
    padding: 15px 10px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    position: relative;
    transition: color 0.3s;
  }
  
  .nav-item-tab.active .nav-link {
    color: #0052d9;
  }
  
  .nav-item-tab.active .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #0052d9;
  }
  
  .nav-link:hover {
    color: #0052d9;
  }
  
  /* 公司简介样式 */
  .company-intro {
    width: 100%;
    background-color: white;
    margin: 0 auto;
    padding-bottom: 50px;
  }
  
  .company-intro .container {
    max-width: 1352px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .company-intro .section-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    width: 100%;
    max-width: 1352px;
    text-align: left;
    margin: auto;
    padding: 50px 0;
  }
  
  .intro-text {
    margin-bottom: 40px;
  }
  
  .intro-text h2 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 28px;
    margin-bottom: 20px;
  }
  
  .intro-text p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
    margin-bottom: 15px;
  }
  
  .intro-image {
  
    width: 100%;
  }
  
  .mission-bg {
    display: flex;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    position: relative;
  }
  
  .mission-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/website/one/assets/images/mission-bg.png');
    background-size: 50% auto;
    background-position: left center;
    background-repeat: no-repeat;
    opacity: 0.1;
    z-index: 0;
  }
  
  .mission-left {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
  }
  
  .mission-left h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 34px;
    text-align: center;
  }
  
  .mission-right {
    position: relative;
    z-index: 1;
  }
  
  .mission-item {
    margin-bottom: 30px;
  }
  
  .mission-item:last-child {
    margin-bottom: 0;
  }
  
  .mission-item h4 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 25px;
    margin-bottom: 10px;
  }
  
  .mission-item p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
  }
  
  .business-structure {
    padding: 40px 0;
    background-color: #f9f9f9;
  }
  
  .structure-content {
    max-width: 1450px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .logo-center {
    margin-bottom: 30px;
    text-align: center;
  }
  
  .logo-center img {
    max-width: 200px;
  }
  
  .structure-tree {
    margin-bottom: 40px;
    text-align: center;
  }
  
  .structure-tree img {
    max-width: 100%;
    height: auto;
  }
  
  .department-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    width: 100%;
  }
  
  .dept-item {
    padding: 10px 13px;
    background-color: #f0f0f0;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .dept-item.active {
    background-color: white;
    color: black;
  }
  
  .dept-description {
    text-align: left;
    width: 100%;
    margin: 0 auto;
    padding: 29px 0px 0px 0px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  /* 企业文化部分样式 */
  .value-section {
    padding: 30px 0px 0px 0px;
    background-color: #f9f9f9;
  }
  
  .section-title {
    text-align: center;
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 40px;
    color: #333;
  }
  
  .section-subtitle {
    text-align: center;
    font-size: 28px;
    font-weight: bold;
    margin: 60px 0 30px;
    color: #333;
  }
  
  .culture-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }
  
  .culture-row {
    display: flex;
    align-items: center;
    margin-bottom: 50px;
    gap: 30px;
    transition: all 0.3s ease-in-out;
    padding: 15px;
    border-radius: 8px;
  }
  
  .culture-row:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.8);
  }
  
  .culture-row.reverse {
    flex-direction: row-reverse;
  }
  
  .culture-text {
    flex: 1;
    padding: 20px;
    transition: all 0.3s ease;
  }
  
  .culture-row:hover .culture-text {
    transform: scale(1.02);
  }
  
  .culture-image {
    max-width: 660px;
    max-height: 371px;
    flex: 1;
    text-align: center;
    overflow: hidden;
    border-radius: 8px;
    transition: all 0.3s ease;
  }
  
  .culture-row:hover .culture-image {
    transform: scale(1.02);
  }
  
  .culture-image img {
    width: 100%;
    max-height: 371px;
    object-fit: cover;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
  }
  
  .culture-row:hover .culture-image img {
    transform: scale(1.05);
  }
  
  .culture-title {
    font-weight: 600;
    font-size: 22px;
    color: #333333;
    line-height: 30px;
    margin-bottom: 40px;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
  }
  
  .culture-row:hover .culture-title {
    color: #0056b3;
    transform: translateX(5px);
  }
  
  .culture-row:hover .culture-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #0056b3;
    transform: scaleX(1);
    transform-origin: left;
    transition: transform 0.3s ease;
  }
  
  .culture-title span {
    color: #0056b3;
  }
  
  .culture-description {
    margin-bottom: 15px;
      text-indent: 2em;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 18px;
      color: #666666;
      line-height: 36px;
  }
  
  /* 社会责任部分样式 */
  .responsibility-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }
  
  .responsibility-item {
    flex: 1;
    min-width: 250px;
    max-width: 280px;
    background-color: #fff;
    border-radius: 10px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .responsibility-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .responsibility-icon {
    font-size: 40px;
    color: #0056b3;
    margin-bottom: 20px;
  }
  
  .responsibility-item h3 {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
  }
  
  .responsibility-item p {
    font-size: 15px;
    line-height: 1.6;
    color: #666;
  }
  
  /* 响应式设计 */
  @media (max-width: 992px) {
    .culture-row {
      flex-direction: column;
      gap: 20px;
    }
    
    .culture-row.reverse {
      flex-direction: column;
    }
    
    .culture-text, .culture-image {
      width: 100%;
    }
    
    .responsibility-item {
      min-width: 200px;
    }
  }
  
  @media (max-width: 768px) {
    .responsibility-container {
      flex-direction: column;
      align-items: center;
    }
    
    .responsibility-item {
      width: 100%;
      max-width: 400px;
    }
    
    .section-title, .section-subtitle {
      font-size: 24px;
    }
    
    .culture-title {
      font-size: 20px;
    }
  }
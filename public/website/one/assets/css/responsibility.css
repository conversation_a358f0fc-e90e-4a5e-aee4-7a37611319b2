.joinus-banner{
    position: relative;
    width: 100%;
    height: 450px;
    background-image: url('../images/responsibility-bg.png');
    background-size: cover;
    background-position: center;
    margin-top: 56px;
    display: flex;
    align-items: center;
}

.joinus-banner-title {
    color: white;
    font-weight: 500;
    font-size: 36px;
    margin-left: 283px;
}
/* 社会责任页面样式 */
.responsibility-grid {
    display: grid;
    grid-template-columns: 676px 676px;
    grid-gap: 0;
    max-width: 1352px;
    margin: 80px auto 40px;
    background-color: #f5faff;
}

.resp-block {
    height: 460px;
    background-color: white;
    transition: all 0.3s ease;
}

.resp-text {
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.resp-text h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    font-style: normal;
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.resp-text p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 32px;
    transition: color 0.3s ease;
}

.resp-image {
    overflow: hidden;
}

.resp-image img {
    width: 676px;
    height: 460px;
    object-fit: cover;
    display: block;
    transition: transform 0.5s ease;
}

/* Hover effects - only for desktop and larger tablets */
@media (min-width: 993px) {
    .resp-block:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        z-index: 1;
    }
    
    .resp-text:hover h3 {
        color: #0052D9;
    }
    
    .resp-text:hover p {
        color: #333333;
    }
    
    .resp-image:hover img {
        transform: scale(1.05);
    }
}

/* 大屏幕平板 (1201px - 1350px) */
@media (min-width: 1201px) and (max-width: 1350px) {
    .responsibility-grid {
        width: 95%;
        max-width: 1200px;
        grid-template-columns: repeat(2, 1fr);
        margin-left: auto;
        margin-right: auto;
    }
    
    .resp-image img {
        width: 100%;
        height: 100%;
    }
}

/* 中等屏幕平板 (992px - 1200px) */
@media (max-width: 1200px) {
    .responsibility-grid {
        width: 90%;
        max-width: 1200px;
        grid-template-columns: repeat(2, 1fr);
        margin-left: auto;
        margin-right: auto;
    }
    
    .resp-block {
        height: 400px;
    }
    
    .resp-image img {
        width: 100%;
        height: 100%;
    }
}

/* 小屏幕平板 (768px - 992px) */
@media (max-width: 992px) {
    .joinus-banner {
        height: 350px;
    }
    
    .joinus-banner-title {
        margin-left: 50px;
        font-size: 30px;
    }
    
    .responsibility-grid {
        margin: 60px auto 30px;
    }
    
    .resp-block {
        height: 350px;
    }
    
    .resp-text {
        padding: 30px;
    }
    
    .resp-text h3 {
        font-size: 26px;
        line-height: 36px;
        margin-bottom: 12px;
    }
    
    .resp-text p {
        font-size: 15px;
        line-height: 28px;
    }
}

/* 手机屏幕 (576px - 768px) */
@media (max-width: 768px) {
    .joinus-banner {
        height: 250px;
    }
    
    .joinus-banner-title {
        margin-left: 30px;
        font-size: 26px;
    }
    
    .responsibility-grid {
        grid-template-columns: 1fr;
        margin: 40px auto 20px;
    }
    
    .resp-block {
        height: auto;
    }
    
    .resp-text {
        padding: 25px;
    }
    
    .resp-text h3 {
        font-size: 24px;
        line-height: 32px;
        margin-bottom: 10px;
    }
    
    .resp-text p {
        font-size: 14px;
        line-height: 26px;
    }
    
    .resp-image img {
        width: 100%;
        height: auto;
        max-height: 350px;
    }
    
    /* 调整图文顺序，确保文本在图片上方 */
    .responsibility-grid {
        display: flex;
        flex-direction: column;
    }
    
    /* 公益慈善部分 */
    .responsibility-grid .resp-text:nth-child(1) {
        order: 1;
    }
    
    .responsibility-grid .resp-image:nth-child(2) {
        order: 2;
    }
    
    /* 校企合作部分 - 调整顺序使文本在前，图片在后 */
    .responsibility-grid .resp-text:nth-child(4) {
        order: 3;
    }
    
    .responsibility-grid .resp-image:nth-child(3) {
        order: 4;
    }
    
    /* 党建共建部分 */
    .responsibility-grid .resp-text:nth-child(5) {
        order: 5;
    }
    
    .responsibility-grid .resp-image:nth-child(6) {
        order: 6;
    }
}

/* 小型手机屏幕 (小于576px) */
@media (max-width: 576px) {
    .joinus-banner {
        height: 200px;
    }
    
    .joinus-banner-title {
        margin-left: 20px;
        font-size: 22px;
    }
    
    .responsibility-grid {
        margin: 30px auto 15px;
    }
    
    .resp-text {
        padding: 20px;
    }
    
    .resp-text h3 {
        font-size: 20px;
        line-height: 28px;
        margin-bottom: 8px;
    }
    
    .resp-text p {
        font-size: 13px;
        line-height: 24px;
    }
    
    .resp-image img {
        max-height: 300px;
    }
}

/* 超小型手机屏幕 (小于480px) */
@media (max-width: 480px) {
    .joinus-banner {
        height: 180px;
    }
    
    .joinus-banner-title {
        margin-left: 15px;
        font-size: 20px;
    }
    
    .resp-text h3 {
        font-size: 18px;
        line-height: 26px;
    }
    
    .resp-text p {
        font-size: 12px;
        line-height: 22px;
    }
    
    .resp-image img {
        max-height: 250px;
    }
}

/* 导航栏占位符样式，确保在移动端不被覆盖 */
#navbar-placeholder {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1031;
}

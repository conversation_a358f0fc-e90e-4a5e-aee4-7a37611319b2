

.supply-container {
    margin-top: 50px;

    width: 100%;
    background-color: white;
    display: flex;
    justify-content: center;
    padding: 0 calc((100% - 1352px) / 2);
}

.supply {
    width: 100%;
    max-width: 1352px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
}

.supply .supply-title {
    margin: 50px 0px 30px 0px;
    width: 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    line-height: 33px;
    text-align: left;
    font-style: normal;
}

.supply-grid-container {
    width: 100%;
    max-width: 1352px;
    height: 740px;
    background-image: url('../images/supply_bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    margin-bottom: 30px;
}

.supply-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.supply-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.45s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    margin: 0;
    height: 100%;
    border-right: 1px solid white;
    border-bottom: 1px solid white;
    position: relative;
    overflow: hidden;
    padding: 30px 20px;
}

.supply-item .default-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    will-change: transform;
    width: 100%;
    transition: transform 0.45s cubic-bezier(0.4, 0, 0.2, 1);
}

.supply-item img {
    width: 26px;
    height: 26px;
    margin-right: 8px;
    backface-visibility: hidden;
    will-change: transform;
}

.supply-item .item-title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    backface-visibility: hidden;
    will-change: transform;
}

.supply-item .hover-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(17, 61, 234, 0.95);
    padding: 90px 30px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.45s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    line-height: 1.8;
    font-size: 14px;
    color: white;
    transform: translateY(40px);
    will-change: transform, opacity, visibility;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    overflow-y: auto;
    box-sizing: border-box;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.supply-item:hover .hover-content {
    display: flex;
    flex-direction: column;
    opacity: 1;
    visibility: visible;
    transform: translateY(0px);
}

.supply-item:hover {
    background-color: rgba(17, 61, 234, 0.95);
}

.supply-item:hover .default-content {
    display: none;
}

.supply-icon {
    display: block;
    width: 40px;
    height: 40px;
    margin-bottom: 15px;
    background-color: white;
    border-radius: 50%;
    position: relative;
}

.supply-icon::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    background-color: rgba(17,61,234,0.6);
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
}

.supply-item span {
    font-size: 16px;
    font-weight: 500;
}

.supply .img{
    margin: auto;
    display: block;
}

}

/* 公司信息模块 */
.company-cards {
    width: 100%;
}

.company-cards-row {
    background-color: white;
    margin: 0 auto;
    padding: 50px 10px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 22px;
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    padding-bottom: 20px; /* 为滚动条预留空间 */
}

.company-cards-row::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
}

.company-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    max-width: 436px; /* 根据规范设置固定宽度 */
    flex: 0 0 auto;
}

.card-image {
    position: relative;
    overflow: hidden;
    width: 436px;
    height: 290px;
}

.company-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(17, 61, 234, 0.85);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.5s ease;
    text-align: center;
    line-height: 1.8;
    font-size: 14px;
}

.company-card:hover .card-overlay {
    opacity: 1;
}

.card-overlay h3 {
    margin-bottom: 15px;
    transform: translateY(-20px);
    transition: transform 0.5s ease;
}

.company-card:hover .card-overlay h3 {
    transform: translateY(0);
}

.card-overlay p {
    margin-bottom: 20px;
    transform: translateY(20px);
    transition: transform 0.5s ease;
    opacity: 0;
}

.company-card:hover .card-overlay p {
    transform: translateY(0);
    opacity: 1;
}

.card-overlay .btn {
    transform: translateY(30px);
    transition: transform 0.5s ease, background-color 0.3s ease;
    opacity: 0;
}

.company-card:hover .card-overlay .btn {
    transform: translateY(0);
    opacity: 1;
}

.card-overlay .btn:hover {
    background-color: #113dea;
    color: white;
}

.card-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 15px 30px;
    text-align: center;
    font-size: 28px;
    font-weight: 500;
    color: white;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.company-card:hover .card-title {
    opacity: 0;
}

.card-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
    transition: opacity 0.3s ease;
}

.company-card:hover .card-image::before {
    opacity: 0;
}

/* 响应式布局 */
@media (max-width: 1400px) {
    .company-cards {
        max-width: 1200px;
        padding: 40px 15px;
    }
}

@media (max-width: 1200px) {
    .company-cards {
        max-width: 992px;
    }
    .company-cards-row {
        gap: 20px;
    }
}

@media (max-width: 992px) {
    .company-cards {
        padding: 30px 15px;
    }
    .company-card {
        width: 300px;
    }
}

@media (max-width: 768px) {
    .company-cards {
        padding: 20px 10px;
    }
    .company-cards-row {
        gap: 15px;
    }
    .company-card {
        width: 280px;
    }
}

@media (max-width: 576px) {
    .company-cards {
        padding: 15px 10px;
    }
    .company-cards-row {
        gap: 12px;
    }
    .company-card {
        width: 260px;
    }
}

/* 企业愿景模块 */
.vision-container {
    background-color: #1b2738;
    color: white;
    padding: 60px 0;
    position: relative;
    overflow: hidden;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.vision-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.vision-left {
    padding-right: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.vision-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 34px;
    margin-bottom: 20px;
    position: relative;
    padding-left: 20px;
    display: inline-block;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    will-change: transform;
}

.vision-title:hover,
.vision-title.hover-effect {
    color: white;
    transform: translateX(15px);
}

.vision-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 0;
    background-color: #113dea;
    transition: height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: height;
}

.vision-title:hover::before,
.vision-title.hover-effect::before {
    height: 100%;
}

.vision-content {
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.7);
    margin-left: 30px;
    margin-bottom: 15px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, color;
}

.vision-separator {
    width: 0;
    height: 2px;
    background-color: white;
    margin: 0 0 20px 45px;
    position: relative;
    display: block;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.vision-separator::after {
    content: '';
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 6px solid white;
    opacity: 0;
    transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
}


/* 采购服务模块 */
.purchase-container {
    margin-top: 50px;
    width: 100%;
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 calc((100% - 1352px) / 2);
}

.purchase-title {
    margin: 50px 0px 30px 0px;
    width: 100%;
    max-width: 1352px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    line-height: 33px;
    text-align: left;
    font-style: normal;
    padding: 0 15px;
}

.section-text { 
    max-width: 1352px;
    font-weight: 400;
    font-size: 18px;
    color: #666666;
    line-height: 36px;
    text-align: left;
    font-style: normal;
    padding-bottom: 50px;
    text-indent: 2em;
}
.purchase-image{
    padding-bottom:50px;
}

.purchase-container a-image {
    width: 100%;
    max-width: 1352px;
    margin-bottom: 30px;
}

/* 采购服务模块响应式样式 */
@media (max-width: 1200px) {
    .purchase-container {
        margin-top: 40px;
    }
    
    .purchase-title {
        font-size: 22px;
        line-height: 30px;
        margin: 40px 0px 25px 0px;
    }
}

@media (max-width: 992px) {
    .purchase-container {
        margin-top: 35px;
    }
    
    .purchase-title {
        font-size: 20px;
        line-height: 28px;
        margin: 35px 0px 20px 0px;
    }
}

@media (max-width: 768px) {
    .purchase-container {
        margin-top: 30px;
    }
    
    .purchase-title {
        margin: 30px 0px 20px 0px;
        font-size: 20px;
        line-height: 28px;
    }
}

@media (max-width: 576px) {
    .purchase-container {
        margin-top: 25px;
    }
    
    .purchase-title {
        font-size: 18px;
        margin: 25px 0px 15px 0px;
        text-align: center;
        padding: 0 15px;
        line-height: 26px;
    }
}

@media (max-width: 480px) {
    .purchase-container {
        margin-top: 20px;
    }
    
    .purchase-title {
        font-size: 16px;
        margin: 20px 0px 12px 0px;
        line-height: 24px;
    }
}

@media (max-width: 375px) {
    .purchase-container {
        margin-top: 15px;
    }
    
    .purchase-title {
        font-size: 15px;
        margin: 15px 0px 10px 0px;
        line-height: 22px;
    }
}

.vision2 {
    background-image: url('../images/vision2.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 740px;
    position: relative;
}

.vision2-text {
    position: absolute;
    top: 80px;
    right: 15%;
    max-width: 758px;
    color: #FFFFFF;
    font-size: 14px;
    line-height: 1.8;
    text-align: justify;
    font-family: PingFangSC, PingFang SC;
    font-style: normal;
    font-weight: 400;
    padding: 20px;
}

@media (max-width: 992px) {
    .vision2-text {
        right: 10%;
        max-width: 500px;
        font-size: 15px;
    }
}

@media (max-width: 768px) {
    .vision2-text {
        right: 5%;
        max-width: 400px;
        font-size: 14px;
        top: 60px;
    }
}

@media (max-width: 576px) {
    .vision2-text {
        right: 20px;
        left: 20px;
        max-width: none;
        font-size: 13px;
        top: 40px;
    }
}

.vision-image-container {
    position: relative;
    height: 100%;
    /* border: 2px dashed rgba(255, 255, 255, 0.2); */
}

.vision-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    position: relative;
    z-index: 0;
}

/* 响应式布局 */
@media (max-width: 992px) {
    .vision-container {
        padding: 40px 0;
    }

    .vision-left {
        padding-right: 15px;
        margin-bottom: 30px;
    }

    .vision-title {
        font-size: 28px;
    }

    .vision-content {
        font-size: 15px;
        margin-bottom: 12px;
    }

    .vision-separator {
        margin-bottom: 25px;
    }

    .vision-title:hover,
    .vision-title.hover-effect {
        transform: translateX(10px);
    }
}

@media (max-width: 768px) {
    .vision-container {
        padding: 30px 0;
    }

    .vision-title {
        font-size: 24px;
    }

    .vision-content {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .vision-separator {
        margin-bottom: 20px;
    }

    .vision-title:hover,
    .vision-title.hover-effect {
        transform: translateX(8px);
    }
}

.footer {
    background-color: #FBFBFB;
    margin-top: 0;
}
@media (max-width: 1400px) {
    .card-image {
        height: 320px;
    }
}

@media (max-width: 1200px) {

    .card-image {
        height: 280px;
    }
}

@media (max-width: 992px) {
    .card-image {
        height: 240px;
    }
}

@media (max-width: 768px) {
    .card-image {
        height: 200px;
    }
    .card-title {
        font-size: 24px;
    }
}

/* 响应式布局 */
@media (max-width: 1400px) {
    .supply-grid-container {
        height: 600px;
    }
}

@media (max-width: 1200px) {
    .supply-grid {
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(3, 1fr);
    }
    
    .supply-item:hover .default-content {
        transform: translateY(-40px);
    }
    .supply-item:hover .hover-content {
        transform: translateY(40px);
    }
    .supply-item {
        border-right: 1px solid white;
        padding: 25px 15px;
    }
    .supply-item img {
        margin-bottom: 6px;
    }
    .supply-item .hover-content {
        padding: 80px 20px 20px;
        font-size: 13px;
    }
}

@media (max-width: 992px) {
    .supply-grid {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(4, 1fr);
    }
    .supply-grid-container {
        height: 800px;
    }
    /* 3列布局边框规则 */
    .supply-item:nth-child(4n) {
        border-right: 1px solid white;
    }
    .supply-item:nth-child(3n) {
        border-right: none;
    }
    .supply-item:nth-child(n+9) {
        border-bottom: 1px solid white;
    }
    .supply-item:nth-child(n+10) {
        border-bottom: none;
    }
    .supply-item {
        padding: 20px 12px;
    }
    .supply-item img {
        width: 24px;
        height: 24px;
        margin-bottom: 5px;
    }
    .supply-item .item-title {
        font-size: 14px;
    }
    .supply-item .hover-content {
        padding: 70px 15px 15px;
        font-size: 12px;
        line-height: 1.6;
    }
    .supply-item:hover .default-content {
        transform: translateY(-35px);
    }
    .supply-item:hover .hover-content {
        transform: translateY(35px);
        padding: 25px 15px;
    }
}

@media (max-width: 768px) {
    .supply-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(5, 1fr);
    }
    .supply-grid-container {
        height: 1000px;
    }
    /* 2列布局边框规则 */
    .supply-item:nth-child(3n) {
        border-right: 1px solid white;
    }
    .supply-item:nth-child(2n) {
        border-right: none;
    }
    .supply-item:nth-child(n+9) {
        border-bottom: 1px solid white;
    }
    .supply-item:nth-child(n+10) {
        border-bottom: none;
    }
    .supply-item {
        padding: 25px 15px;
    }
    .supply-item img {
        width: 26px;
        height: 26px;
        margin-bottom: 6px;
    }
    .supply-item .item-title {
        font-size: 15px;
    }
    .supply-item .hover-content {
        padding: 75px 15px 15px;
        font-size: 12px;
    }
    .supply-item:hover .default-content {
        transform: translateY(-35px);
    }
    .supply-item:hover .hover-content {
        transform: translateY(35px);
        padding: 25px 15px;
    }
}

@media (max-width: 576px) {
    .supply-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(5, 1fr);
    }
    .supply-grid-container {
        height: 800px;
    }
    .supply .supply-title {
        font-size: 20px;
        margin: 30px 0px 20px 0px;
        text-align: center;
        padding: 0 15px;
        line-height: 28px;
    }
    /* 2列布局边框规则 */
    .supply-item:nth-child(3n) {
        border-right: 1px solid white;
    }
    .supply-item:nth-child(2n) {
        border-right: none;
    }
    .supply-item:nth-child(n+9) {
        border-bottom: 1px solid white;
    }
    .supply-item:nth-child(n+10) {
        border-bottom: none;
    }
    
    .supply-item img {
        width: 26px;
        height: 26px;
        margin-bottom: 6px;
    }
    .supply-item .item-title {
        font-size: 16px;
    }
    .supply-item .hover-content {
        padding: 65px 15px 15px;
        font-size: 13px;
        overflow-y: auto;
        line-height: 1.6;
    }
    .supply-item:hover .default-content {
        display: none;
    }
    .supply-item:hover .hover-content {
        transform: translateY(0);
        padding: 20px 15px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .supply-item .hover-content > div:first-child {
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .supply-grid-container {
        height: 750px;
    }
    .supply .supply-title {
        font-size: 18px;
        margin: 25px 0px 15px 0px;
        line-height: 24px;
    }
    .supply-item {
        padding: 20px 10px;
    }
    .supply-item img {
        width: 22px;
        height: 22px;
        margin-bottom: 5px;
    }
    .supply-item .item-title {
        font-size: 14px;
    }
    .supply-item .hover-content {
        padding: 50px 10px 10px;
        font-size: 11px;
        line-height: 1.5;
    }
    .supply-item:hover .hover-content {
        padding: 15px 10px;
    }
    .supply-item .hover-content > div:first-child {
        margin-bottom: 8px;
    }
}

@media (max-width: 375px) {
    .supply-grid-container {
        height: 700px;
    }
    .supply .supply-title {
        font-size: 16px;
        margin: 20px 0px 12px 0px;
        line-height: 22px;
    }
    .supply-item {
        padding: 15px 8px;
    }
    .supply-item img {
        width: 20px;
        height: 20px;
        margin-bottom: 4px;
    }
    .supply-item .item-title {
        font-size: 12px;
    }
    .supply-item .hover-content {
        padding: 40px 8px 8px;
        font-size: 10px;
        line-height: 1.4;
    }
    .supply-item:hover .hover-content {
        padding: 12px 8px;
    }
    .supply-item .hover-content > div:first-child {
        margin-bottom: 6px;
    }
}
@media (min-width: 1400px) {
    .supply-item:nth-child(5) {
        border-right: none;
    }
    .supply-item:nth-child(10) {
        border-right: none;
    }
}

@media (max-width: 1200px) {
    .supply .supply-title {
        font-size: 22px;
        line-height: 30px;
    }
}

@media (max-width: 992px) {
    .supply .supply-title {
        font-size: 20px;
        line-height: 28px;
        margin: 40px 0px 25px 0px;
    }
}

@media (max-width: 1200px) {
    .supply-container {
        margin-top: 40px;

    }
    
    .supply .supply-title {
        font-size: 22px;
        line-height: 30px;
        margin: 40px 0px 25px 0px;
    }
}

@media (max-width: 992px) {
    .supply-container {
        margin-top: 35px;
   
    }
    
    .supply .supply-title {
        font-size: 20px;
        line-height: 28px;
        margin: 35px 0px 20px 0px;
    }
    
    .supply-grid-container {
        margin-bottom: 25px;
    }
}

@media (max-width: 768px) {
    .supply-container {
        margin-top: 30px;

    }
    
    .supply .supply-title {
        margin: 30px 0px 20px 0px;
    }
    
    .supply-grid-container {
        margin-bottom: 20px;
    }
    
    .supply-item .hover-content {
        font-size: 12px;
        line-height: 1.6;
    }
}

/* 新的公司信息模块 */
.company-info-container {
    width: 100%;
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50px 0;
}

.company-info-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    margin-bottom: 40px;
    text-align: center;
}

.company-info-cards {
    width: 100%;
    max-width: 1352px;
    display: flex;
    justify-content: center;
    gap: 22px;
    padding: 0 15px;
}

.company-info-card {
    width: 100%;
    max-width: 436px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    flex: 1 1 436px;
}

.company-info-card .card-image {
    position: relative;
    overflow: hidden;
    height: 290px;
}

.company-info-card .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.company-info-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.company-info-card:hover .card-image img {
    transform: scale(1.05);
}

.company-info-card .card-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 15px 30px;
    text-align: center;
    font-size: 28px;
    font-weight: 500;
    color: white;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.company-info-card:hover .card-title {
    opacity: 0;
}

.company-info-card .card-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
    transition: opacity 0.3s ease;
}

.company-info-card:hover .card-image::before {
    opacity: 0;
}

.company-info-card .card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(17, 61, 234, 0.85);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.5s ease;
    text-align: center;
    line-height: 1.8;
    font-size: 14px;
}

.company-info-card:hover .card-overlay {
    opacity: 1;
}

.company-info-card .card-overlay h3 {
    margin-bottom: 15px;
    transform: translateY(-20px);
    transition: transform 0.5s ease;
}

.company-info-card:hover .card-overlay h3 {
    transform: translateY(0);
}

.company-info-card .card-overlay p {
    margin-bottom: 20px;
    transform: translateY(20px);
    transition: transform 0.5s ease;
    opacity: 0;
}

.company-info-card:hover .card-overlay p {
    transform: translateY(0);
    opacity: 1;
}

.company-info-card .card-overlay .btn {
    transform: translateY(30px);
    transition: transform 0.5s ease, background-color 0.3s ease;
    opacity: 0;
}

.company-info-card:hover .card-overlay .btn {
    transform: translateY(0);
    opacity: 1;
}

.company-info-card .card-overlay .btn:hover {
    background-color: #113dea;
    color: white;
}

/* 响应式布局 */
@media (max-width: 1350px) {
    .company-info-cards {
        max-width: 90%;
    }
    
    .company-info-card {
        max-width: 400px;
    }
    
    .company-info-card .card-image {
        height: 270px;
    }
}

@media (max-width: 1200px) {
    .company-info-card {
        max-width: 350px;
    }
    
    .company-info-card .card-image {
        height: 240px;
    }
}

@media (max-width: 992px) {
    .company-info-cards {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .company-info-card {
        flex: 0 0 33.33%;
        max-width: 33.33%;
    }
    
    .company-info-card .card-image {
        height: 200px;
    }
    
    .company-info-card .card-title {
        font-size: 22px;
        padding: 12px 25px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 20px;
        margin-bottom: 12px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    .company-info-cards {
        gap: 20px;
    }
    
    .company-info-card .card-image {
        height: 220px;
    }
    
    .company-info-card .card-title {
        font-size: 24px;
        padding: 12px 25px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 22px;
        margin-bottom: 12px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .company-info-cards {
        flex-direction: column;
        gap: 15px;
    }
    
    .company-info-card {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .company-info-card .card-image {
        height: 200px;
    }
    
    .company-info-card .card-title {
        font-size: 22px;
        padding: 10px 20px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 20px;
        margin-bottom: 10px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 12px;
    }
    
    .company-info-card .card-overlay .btn {
        font-size: 12px;
        padding: 5px 10px;
    }
    
    .company-info-title {
        font-size: 22px;
        margin-bottom: 20px;
    }
}

@media (max-width: 480px) {
    .company-info-card {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .company-info-card .card-image {
        height: 180px;
    }
    
    .company-info-card .card-title {
        font-size: 18px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 18px;
        margin-bottom: 8px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 12px;
        line-height: 1.4;
    }
}

@media (max-width: 375px) {
    .company-info-card {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .company-info-card .card-image {
        height: 160px;
    }
    
    .company-info-card .card-title {
        font-size: 16px;
        padding: 8px 15px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 16px;
        margin-bottom: 6px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 12px;
        line-height: 1.3;
        margin-bottom: 10px;
    }
    
    .company-info-card .card-overlay .btn {
        font-size: 12px;
        padding: 5px 10px;
    }
}

@media (max-width: 1200px) {
    .supply-container {
        margin-top: 40px;

    }
    
    .supply-grid-container {
        height: 720px;
    }
    
    .supply .supply-title {
        font-size: 22px;
        line-height: 30px;
        margin: 40px 0px 25px 0px;
    }
}

@media (max-width: 992px) {
    .supply-container {
        margin-top: 35px;
 
    }
    
    .supply-grid-container {
        height: 680px;
        margin-bottom: 25px;
    }
    
    .supply .supply-title {
        font-size: 20px;
        line-height: 28px;
        margin: 35px 0px 20px 0px;
    }
    
    .supply-grid-container {
        margin-bottom: 25px;
    }
}

@media (max-width: 768px) {
    .supply-container {
        margin-top: 30px;

    }
    
    .supply-grid-container {
        margin-bottom: 20px;
        height: 1000px;
    }
    
    .supply .supply-title {
        margin: 30px 0px 20px 0px;
        font-size: 20px;
        line-height: 28px;
    }
    
    .supply-item .hover-content {
        font-size: 12px;
        line-height: 1.6;
    }
}

@media (max-width: 576px) {
    .supply-container {
        margin-top: 25px;

    }
    
    .supply-grid-container {
        height: 800px;
    }
    
    .supply .supply-title {
        font-size: 18px;
        margin: 25px 0px 15px 0px;
        text-align: center;
        padding: 0 15px;
        line-height: 26px;
    }
}

@media (max-width: 480px) {
    .supply-container {
        margin-top: 20px;

    }
    
    .supply-grid-container {
        height: 750px;
    }
    
    .supply .supply-title {
        font-size: 16px;
        margin: 20px 0px 12px 0px;
        line-height: 24px;
    }
}

@media (max-width: 375px) {
    .supply-container {
        margin-top: 15px;

    }
    
    .supply-grid-container {
        height: 700px;
    }
    
    .supply .supply-title {
        font-size: 15px;
        margin: 15px 0px 10px 0px;
        line-height: 22px;
    }
}

@media (max-width: 992px) {
    /* 供应链模块的移动端点击交互样式 */
    .supply-item.active {
        background-color: rgba(17, 61, 234, 0.95);
    }
    
    .supply-item.active .default-content {
        display: none !important;
    }
    
    .supply-item.active .hover-content {
        display: flex !important;
        flex-direction: column;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }
    
    /* 禁用悬停效果，改为点击触发 */
    .supply-item:hover .hover-content {
        display: none;
        opacity: 0;
        visibility: hidden;
        transform: translateY(40px);
    }
    
    .supply-item:hover .default-content {
        display: flex;
    }
    
    .supply-item:hover {
        background-color: transparent;
    }
    
    /* 公司信息模块的移动端点击交互样式 */
    .company-info-card.active {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }
    
    .company-info-card.active .card-image img {
        transform: scale(1.05);
    }
    
    .company-info-card.active .card-overlay {
        opacity: 1;
        transform: translateY(0);
    }
    
    .company-info-card.active .card-title {
        opacity: 0;
    }
    
    .company-info-card.active .card-image::before {
        opacity: 0;
    }
    
    .company-info-card.active .card-overlay h3,
    .company-info-card.active .card-overlay p,
    .company-info-card.active .card-overlay .btn {
        transform: translateY(0);
        opacity: 1;
    }
    
    /* 禁用悬停效果，改为点击触发 */
    .company-info-card:hover .card-overlay {
        opacity: 0;
    }
    
    .company-info-card:hover .card-title {
        opacity: 1;
    }
    
    .company-info-card:hover .card-image::before {
        opacity: 1;
    }
    
    .company-info-card:hover img {
        transform: scale(1);
    }
    
    .company-info-card:hover {
        transform: translateY(0);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .company-info-card:hover .card-overlay h3,
    .company-info-card:hover .card-overlay p,
    .company-info-card:hover .card-overlay .btn {
        transform: translateY(20px);
        opacity: 0;
    }
}

@media (max-width: 1200px) {
    .company-info-container {
        padding: 40px 0;
    }
    
    .company-info-title {
        font-size: 26px;
        margin-bottom: 35px;
    }
}

@media (max-width: 992px) {
    .company-info-container {
        padding: 35px 0;
    }
    
    .company-info-title {
        font-size: 24px;
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .company-info-container {
        padding: 30px 0;
    }
    
    .company-info-title {
        font-size: 22px;
        margin-bottom: 25px;
    }
}

@media (max-width: 576px) {
    .company-info-container {
        padding: 25px 0;
    }
    
    .company-info-title {
        font-size: 20px;
        margin-bottom: 20px;
    }
}

@media (max-width: 480px) {
    .company-info-container {
        padding: 20px 0;
    }
    
    .company-info-title {
        font-size: 18px;
        margin-bottom: 15px;
    }
}

@media (max-width: 375px) {
    .company-info-container {
        padding: 15px 0;
    }
    
    .company-info-title {
        font-size: 16px;
        margin-bottom: 12px;
    }
}

@media (max-width: 992px) {
    .company-info-cards {
        flex-wrap: wrap;
        gap: 22px;
    }
    
    .company-info-card {
        flex: 0 0 calc(33.33% - 15px);
        max-width: calc(33.33% - 15px);
    }
    
    .company-info-card .card-image {
        height: 240px;
    }
    
    .company-info-card .card-title {
        font-size: 22px;
        padding: 10px 15px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 20px;
        margin-bottom: 10px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 13px;
        line-height: 1.5;
    }
}

@media (max-width: 768px) {
    .company-info-cards {
        gap: 20px;
    }
    
    .company-info-card .card-image {
        height: 220px;
    }
    
    .company-info-card .card-title {
        font-size: 24px;
        padding: 12px 25px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 22px;
        margin-bottom: 12px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .company-info-cards {
        flex-direction: column;
        gap: 15px;
    }
    
    .company-info-card {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .company-info-card .card-image {
        height: 200px;
    }
    
    .company-info-card .card-title {
        font-size: 22px;
        padding: 10px 20px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 20px;
        margin-bottom: 10px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 12px;
    }
    
    .company-info-card .card-overlay .btn {
        font-size: 12px;
        padding: 5px 10px;
    }
    
    .company-info-title {
        font-size: 22px;
        margin-bottom: 20px;
    }
}

@media (max-width: 480px) {
    .company-info-card {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .company-info-card .card-image {
        height: 180px;
    }
    
    .company-info-card .card-title {
        font-size: 18px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 18px;
        margin-bottom: 8px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 12px;
        line-height: 1.4;
    }
}

@media (max-width: 375px) {
    .company-info-card {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .company-info-card .card-image {
        height: 160px;
    }
    
    .company-info-card .card-title {
        font-size: 16px;
        padding: 8px 15px;
    }
    
    .company-info-card .card-overlay h3 {
        font-size: 16px;
        margin-bottom: 6px;
    }
    
    .company-info-card .card-overlay p {
        font-size: 12px;
        line-height: 1.3;
        margin-bottom: 10px;
    }
    
    .company-info-card .card-overlay .btn {
        font-size: 12px;
        padding: 5px 10px;
    }
}
* 移动端适配 */
@media screen and (max-width: 768px) {
  .purchase-container {
    padding: 40px 20px;
  }
  
  .purchase-title {
    font-size: 22px;
    margin-bottom: 20px;
    line-height: 1.3;
  }
  
  .section-text {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 25px;
  }
  
  .section-text p {
    margin-bottom: 12px;
  }
  
  .purchase-image {
    width: 100%;
    margin: 0 auto 25px;
    border-radius: 6px;
  }
  
  .vision2 {
    padding: 40px 20px;
  }
  
  .vision2-text {
    font-size: 14px;
    line-height: 1.6;
  }
}

/* 小屏幕手机适配 */
@media screen and (max-width: 480px) {
  .purchase-container {
    padding: 30px 15px;
  }
  
  .purchase-title {
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  .section-text {
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 20px;
  }
  
  .purchase-image {
    width: calc(100% - 20px);
    margin: 0 10px 20px;
    border-radius: 4px;
  }
  
  .vision2 {
    padding: 30px 15px;
  }
  
  .vision2-text {
    font-size: 13px;
    line-height: 1.5;
  }
}
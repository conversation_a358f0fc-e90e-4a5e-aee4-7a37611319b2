document.addEventListener('DOMContentLoaded', function() {
    const firstVisionTitle = document.querySelector('.vision-title');
    let currentActiveTitle = null;
    
    if (firstVisionTitle) {
        function triggerHoverEffect(element, force = false) {
            if (!force && currentActiveTitle === element) return;
            
            currentActiveTitle = element;
            element.classList.add('hover-effect');
            
            // 创建或更新蓝色垂直条
            let bar = element.querySelector('.vision-bar');
            if (!bar) {
                bar = document.createElement('div');
                bar.classList.add('vision-bar');
                bar.style.cssText = `
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 5px;
                    height: 0;
                    background-color: #113dea;
                    transition: height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                `;
                element.style.position = 'relative';
                element.appendChild(bar);
            }
            
            requestAnimationFrame(() => {
                bar.style.height = '100%';
            });
            
            const content = element.nextElementSibling;
            const separator = content.nextElementSibling;
            
            if (content) {
                content.style.cssText = `
                    color: white;
                    transform: translateX(15px);
                    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                    cursor: pointer;
                `;
            }
            
            if (separator) {
                separator.style.cssText = `
                    width: 50px;
                    opacity: 1;
                    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                `;
                
                let arrow = separator.querySelector('.vision-arrow');
                if (!arrow) {
                    arrow = document.createElement('div');
                    arrow.classList.add('vision-arrow');
                    arrow.style.cssText = `
                        position: absolute;
                        right: -6px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 0;
                        height: 0;
                        border-top: 6px solid transparent;
                        border-bottom: 6px solid transparent;
                        border-left: 6px solid white;
                        opacity: 0;
                        transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                    `;
                    separator.appendChild(arrow);
                }
                
                requestAnimationFrame(() => {
                    arrow.style.opacity = '1';
                });
            }
        }

        function removeHoverEffect(element) {
            if (!element) return;
            
            element.classList.remove('hover-effect');
            
            const bar = element.querySelector('.vision-bar');
            if (bar) {
                bar.style.height = '0';
            }
            
            const content = element.nextElementSibling;
            const separator = content.nextElementSibling;
            
            if (content) {
                content.style.cssText = `
                    color: rgba(255, 255, 255, 0.7);
                    transform: translateX(0);
                    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                    cursor: default;
                `;
            }
            
            if (separator) {
                separator.style.cssText = `
                    width: 0;
                    opacity: 0;
                    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                `;
                
                const arrow = separator.querySelector('.vision-arrow');
                if (arrow) {
                    arrow.style.opacity = '0';
                }
            }
        }

        // 页面加载完成后自动触发第一个元素的hover效果
        requestAnimationFrame(() => {
            triggerHoverEffect(firstVisionTitle, true);
        });

        // 为所有愿景标题和内容添加鼠标事件监听
        const allVisionTitles = document.querySelectorAll('.vision-title');
        allVisionTitles.forEach(title => {
            const content = title.nextElementSibling;
            
            title.addEventListener('mouseenter', function() {
                if (this !== firstVisionTitle) {
                    removeHoverEffect(firstVisionTitle);
                }
                triggerHoverEffect(this);
            });

            title.addEventListener('mouseleave', function() {
                if (this !== firstVisionTitle) {
                    removeHoverEffect(this);
                    triggerHoverEffect(firstVisionTitle);
                }
            });

            // 为content添加hover效果
            if (content) {
                content.addEventListener('mouseenter', function() {
                    if (title !== firstVisionTitle) {
                        removeHoverEffect(firstVisionTitle);
                    }
                    triggerHoverEffect(title);
                });

                content.addEventListener('mouseleave', function() {
                    if (title !== firstVisionTitle) {
                        removeHoverEffect(title);
                        triggerHoverEffect(firstVisionTitle);
                    }
                });
            }
        });
    }
});

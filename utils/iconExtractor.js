/**
 * 从CSS文件内容中提取图标类名
 * @param {string} cssContent - CSS文件内容
 * @returns {Array} - 图标类名数组
 */
export const extractIconClassNames = (cssContent) => {
  console.log('开始提取图标类名，CSS内容长度:', cssContent.length);
  
  try {
    // 提取所有图标规则
    const iconRules = cssContent.match(/\.icon-[^{]+\{[^}]+\}/g) || [];
    console.log('提取到的图标规则数量:', iconRules.length);
    
    // 从规则中提取类名
    const iconClassNames = [];
    iconRules.forEach(rule => {
      // 从每个规则中提取类名，格式如 .icon-xxx:before
      const className = rule.match(/\.(icon-[a-zA-Z0-9_-]+)[^{]*/)?.[1];
      if (className) {
        iconClassNames.push(className);
      }
    });

    // 如果上面的方法没有提取到图标，尝试其他方法
    if (iconClassNames.length === 0) {
      console.log('使用备用方法提取图标类名');
      
      // 直接使用:before选择器提取
      const regex = /\.(icon-[a-zA-Z0-9_-]+):before\s*\{/g;
      let match;
      while ((match = regex.exec(cssContent)) !== null) {
        iconClassNames.push(match[1]);
      }
    }
    
    // 去除重复项
    const uniqueIcons = [...new Set(iconClassNames)];
    console.log('提取到的图标数量:', uniqueIcons.length);
    
    // 如果还是没有提取到，尝试使用更简单的正则表达式
    if (uniqueIcons.length === 0) {
      console.log('使用最简单的正则表达式');
      const simpleMatches = cssContent.match(/\.icon-[a-zA-Z0-9_-]+/g) || [];
      const simpleIcons = simpleMatches.map(m => m.substring(1)); // 去除点号
      return [...new Set(simpleIcons)];
    }
    
    return uniqueIcons;
  } catch (error) {
    console.error('提取图标类名时出错:', error);
    return [];
  }
};

/**
 * 从CSS文件路径中读取并提取图标类名
 * @param {string} cssFilePath - CSS文件路径
 * @returns {Promise<Array>} - 图标类名数组的Promise
 */
export const extractIconClassNamesFromFile = async (cssFilePath) => {
  try {
    // 在Nuxt环境中，我们需要使用不同的方式读取文件
    if (process.server) {
      // 服务器端
      const fs = require('fs');
      const path = require('path');
      const publicPath = path.resolve(process.cwd(), 'public');
      const filePath = path.join(publicPath, cssFilePath);
      
      return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
          if (err) {
            console.error('读取文件失败:', err);
            resolve([]);
            return;
          }
          resolve(extractIconClassNames(data));
        });
      });
    } else {
      // 客户端
      const response = await fetch(cssFilePath);
      if (!response.ok) {
        throw new Error(`无法读取文件: ${response.statusText}`);
      }
      
      const cssContent = await response.text();
      return extractIconClassNames(cssContent);
    }
  } catch (error) {
    console.error('提取图标类名时出错:', error);
    return [];
  }
};

/**
 * 直接从CSS文件路径读取内容
 * @param {string} filePath - 文件绝对路径
 * @returns {Promise<string>} - 文件内容的Promise
 */
export const readCssFileContent = async (filePath) => {
  try {
    const fs = require('fs');
    return new Promise((resolve, reject) => {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          console.error('读取文件失败:', err);
          reject(err);
          return;
        }
        resolve(data);
      });
    });
  } catch (error) {
    console.error('读取文件内容时出错:', error);
    throw error;
  }
};

/**
 * 示例用法
 */
// 使用方法1: 直接从CSS内容提取
// const cssContent = `
//   .icon-jingdong:before { content: "\\e600"; }
//   .icon-tielu:before { content: "\\e636"; }
// `;
// const icons = extractIconClassNames(cssContent);
// console.log(icons); // ['icon-jingdong', 'icon-tielu']

// 使用方法2: 从文件中提取
// extractIconClassNamesFromFile('/assets/css/iconfont.css')
//   .then(icons => {
//     console.log(icons);
//   });

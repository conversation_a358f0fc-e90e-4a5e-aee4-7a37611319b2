<template>
  <div class="search-form-container">
    <!-- 搜索表单 -->
    <div class="search-form">
      <a-form :model="formData" layout="horizontal" auto-label-width>
        <div class="search-grid" :class="{ 'advanced-search': showAdvancedSearch }">
          <template v-for="(item, index) in formItems" :key="index">
            <a-form-item 
              :field="item.field" 
              :label="item.label" 
              class="search-item"
              :style="item.isTimeRange ? { gridColumn: 'span 2' } : {}"
            >
              <!-- 输入框类型 -->
              <a-input 
                v-if="item.type === 'input'"
                :model-value="formData[item.field]" 
                @update:model-value="val => updateFormValue(item.field, val)"
                :placeholder="item.placeholder || '请输入'" 
              />
              
              <!-- 选择框类型 -->
              <a-select 
                v-else-if="item.type === 'select'"
                :model-value="formData[item.field]" 
                @update:model-value="val => updateFormValue(item.field, val)"
                :placeholder="item.placeholder || '请选择'"
                :style="{ width: '100%' }"
              >
                <a-option 
                  v-for="option in item.options" 
                  :key="option.value" 
                  :value="option.value"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
              
              <!-- 时间范围类型 -->
              <div v-else-if="item.isTimeRange" class="time-range-controls">
                <a-select 
                  :model-value="formData[item.timePresetField]" 
                  @update:model-value="val => updateFormValue(item.timePresetField, val)"
                  style="width: 120px"
                >
                  <a-option 
                    v-for="option in item.presetOptions" 
                    :key="option.value" 
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-option>
                </a-select>
                <a-date-picker 
                  :model-value="formData[item.startDateField]" 
                  @update:model-value="val => updateFormValue(item.startDateField, val)"
                  style="width: 300px" 
                />
                <span class="time-separator">至</span>
                <a-date-picker 
                  :model-value="formData[item.endDateField]" 
                  @update:model-value="val => updateFormValue(item.endDateField, val)"
                  style="width: 300px" 
                />
              </div>
            </a-form-item>
          </template>
        </div>
        
        <!-- 操作按钮 -->
        <div class="search-actions">
          <div class="left-actions">
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="resetSearch">重置</a-button>
            <slot name="left-actions"></slot>
          </div>
          <div class="right-actions">
            <slot name="right-actions"></slot>
          </div>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

const props = defineProps({
  formItems: {
    type: Array,
    required: true
  },
  formData: {
    type: Object,
    required: true
  },
  showAdvancedSearch: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:formData', 'search', 'reset']);

// 更新表单值的方法
const updateFormValue = (field, value) => {
  const updatedData = { ...props.formData, [field]: value };
  emit('update:formData', updatedData);
};

// 搜索处理
const handleSearch = () => {
  emit('search', props.formData);
};

// 重置搜索
const resetSearch = () => {
  const resetData = {};
  
  // 根据表单项类型设置默认值
  props.formItems.forEach(item => {
    if (item.isTimeRange) {
      resetData[item.timePresetField] = item.defaultPreset || '7days';
      resetData[item.startDateField] = null;
      resetData[item.endDateField] = null;
    } else if (item.type === 'select') {
      resetData[item.field] = item.defaultValue || (item.options[0]?.value || '');
    } else {
      resetData[item.field] = '';
    }
  });
  
  // 发出重置事件
  emit('update:formData', resetData);
  emit('reset');
};

defineExpose({
  resetSearch
});
</script>

<style scoped lang="less">
.search-form-container {
  width: 100%;
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
  
  &.advanced-search {
    grid-template-columns: repeat(4, 1fr);
  }
}

.search-item {
  margin-bottom: 0;
}

.time-range-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-separator {
  margin: 0 4px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .left-actions {
    display: flex;
    gap: 8px;
  }
  
  .right-actions {
    display: flex;
    align-items: center;
  }
}
</style>

<template>
  <div class="order-list-container">
    <!-- 顶部标签页 -->
    <div class="order-tabs">
      <div class="tab-item active">近6个月订单</div>
      <div class="tab-item">6个月前订单</div>
    </div>
    <div class="search-area">
      <!-- 顶部统计卡片 -->
      <div class="stats-cards-container">
        <div class="stats-scroll-btn stats-scroll-left" @click="scrollStatsCards('left')">
          <icon-left />
        </div>
        <div class="stats-cards" ref="statsCardsRef">
          <div 
            v-for="card in statsCards" 
            :key="card.id"
            class="stats-card" 
            :class="{ active: activeStatsCard === card.id }"
            @click="setActiveStatsCard(card.id)"
          >
            <div class="stats-corner-mark" v-if="activeStatsCard === card.id">
              <icon-check class="check-icon" />
            </div>
            <div class="stats-title">{{ card.title }}</div>
            <div class="stats-value">{{ card.value }}</div>
          </div>
        </div>
        <div class="stats-scroll-btn stats-scroll-right" @click="scrollStatsCards('right')">
          <icon-right />
        </div>
      </div>

      <!-- 订单标签 -->
      <div class="order-tags">
        <div class="tag-item">快递配送</div>
        <div class="tag-item">到店自提</div>
        <div class="tag-item">同城配送</div>
        <div class="tag-item">
          <span>预售订单</span>
          <span class="tag-count">仅含预售订单(0)</span>
        </div>
        <div class="tag-item">
          <span>拼团订单</span>
          <span class="tag-count">仅含拼团订单(0)</span>
        </div>
        <div class="tag-item">
          <span>积分兑换订单</span>
          <span class="tag-count">(0)</span>
        </div>
        <div class="tag-item">
          <span>香港手淘发货</span>
          <span class="tag-count">()</span>
        </div>
        <div class="tag-right">
          <span>包含售罄()</span>
          <a-button size="small" type="text">问题订单(?)</a-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <SearchForm
        v-model:formData="searchForm"
        :formItems="formItems"
        :showAdvancedSearch="showAdvancedSearch"
        @search="handleSearch"
        @reset="resetSearch"
      >
        <!-- 自定义左侧按钮 -->
        <template #left-actions>
          <a-button type="primary" status="success">导出订单</a-button>
          <a-button type="primary" status="warning">导出记录</a-button>
          <a-dropdown>
            <a-button>
              更多筛选
              <icon-down />
            </a-button>
            <template #content>
              <a-doption>选项1</a-doption>
              <a-doption>选项2</a-doption>
            </template>
          </a-dropdown>
        </template>
        
        <!-- 自定义右侧内容 -->
        <!-- <template #right-actions>
          <a-checkbox v-model="searchForm.onlyShowSelected">仅显示已选订单</a-checkbox>
        </template> -->
      </SearchForm>

      <!-- 操作标签 -->
      <div class="operation-tags">
        <div class="tag-item">批量付款</div>
        <div class="tag-item">打单发货</div>
        <div class="tag-item">批量发货</div>
        <div class="tag-item">批量改价</div>
        <div class="tag-item">批量改地址</div>
        <div class="tag-item">
          更多
          <icon-down />
        </div>
        <div class="tag-item">不需要物流发货</div>
        <div class="tag-item">不需要物流发货</div>
        <div class="tag-item">仅退款申请</div>
        <div class="tag-item">下载订单</div>
        <div class="add-order-btn-mobile">
          <a-button type="primary" size="small" class="add-order-btn">添加订单</a-button>
        </div>
      </div>
    </div>

    <a-table :columns="columns" :data="tableData" :pagination="false" :span-method="spanMethod"
       v-model:selectedKeys="selectedRowKeys" row-key="rowId" class="order-table"
      :hoverable="false">
      <template #productInfo-cell="{ record }">
        <div v-if="record.type === 'header'" class="order-header-cell">
          <div class="order-header-left">
            <!-- Header Checkbox for Select/Deselect All on Page -->
            <a-checkbox 
              :model-value="isAllSelectedOnPage"
              :indeterminate="isIndeterminate"
              @change="handleSelectAllOnPage"
              class="header-checkbox order-checkbox" 
            />
            订单编号 {{ record.originalId }}
            <a-button shape="circle" size="mini" class="copy-btn-inline" @click="copyOrderId(record.originalId)">
              <icon-copy />
            </a-button>
            <span class="order-time-inline">下单时间 {{ record.orderTime }}</span>
          </div>
          <div class="order-header-right">
            <a-button type="text" size="small" class="message-btn">
              <icon-message /> 高频购买
            </a-button>
            <a-button type="text" size="small" class="add-btn">
              <icon-plus /> 添加标注
            </a-button>
          </div>
        </div>
        <div v-else class="product-info-cell-content">
          <img :src="record.imageUrl" class="product-image" />
          <div class="product-details">
            <div class="product-name">{{ record.productName }}</div>
            <div class="product-meta">
              <div>{{ record.color }}</div>
              <div>商家编号：{{ record.merchantCode }}</div>
              <div>商品编ID：{{ record.productId }}</div>
              <div class="product-tags">
                <span class="risk-tag">除名加赠</span>
                <span class="days-tag">7天</span>
                <span class="speed-tag">极速退</span>
              </div>
              <div class="store-tags">
                <span class="store-tag">小店自营</span>
                <span class="goods-tag">商品卡</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template #priceQuantity-cell="{ record }">
        <div v-if="record.type === 'details'" class="price-cell-content">
          <div class="price">¥{{ record.price }}</div>
          <div class="quantity">x{{ record.quantity }}</div>
          <a-link v-if="record.returnLink" class="return-link" status="warning">{{ record.returnLink }}</a-link>
        </div>
      </template>

      <template #payment-method-cell="{ record }">
        <div v-if="record.type === 'details'" class="status-tag">
          {{ record.paymentMethod }}
        </div>
      </template>

      <template #delivery-method-cell="{ record }">
        <div v-if="record.type === 'details'" class="status-tag">
          {{ record.deliveryMethod }}
        </div>
      </template>

      <template #payment-cell="{ record }">
        <div v-if="record.type === 'details'" class="payment-cell-content">
          <div class="payment-method">{{ record.paymentMethod }}</div>
          <div class="price highlight">¥{{ record.totalPayable }}</div>
          <div class="payment-detail">{{ record.paymentDetail }}</div>
          <div class="address" :title="record.address">{{ record.address }}</div>
        </div>
      </template>

      <template #receiver-cell="{ record }">
        <div v-if="record.type === 'details'" class="receiver-info">
          {{ record.consumer }}
        </div>
      </template>

      <template #consumer-cell="{ record }">
        <div v-if="record.type === 'details'" class="consumer-info">
          {{ record.consumer }}
        </div>
      </template>

      <template #status-cell="{ record }">
        <div v-if="record.type === 'details'" class="status-cell-content">
          <div class="status-text">{{ record.orderStatus }}</div>
          <div v-if="record.deliveryTime" class="delivery-time">
            预计时效{{ record.deliveryTime }}
          </div>
          <div v-if="record.deliveryDetail" class="delivery-detail">
            {{ record.deliveryDetail }}
          </div>
        </div>
      </template>

      <template #operations-cell="{ record }">
        <div v-if="record.type === 'details' && record.productIndex === 0" class="operations-cell-content">
          <div class="operation-buttons">
            <!-- 全部订单状态下都可以查看订单详情 -->
            <a-button type="text" size="small">订单详情</a-button>
            
            <!-- 待付款状态 -->
            <template v-if="record.orderStatus === '待付款'">
              <a-button type="text" size="small" status="warning">取消订单</a-button>
            </template>
            
            <!-- 待发货状态 -->
            <template v-if="record.orderStatus === '待发货'">
              <a-button type="text" size="small" status="primary">发货</a-button>
              <a-button type="text" size="small" status="warning">取消订单</a-button>
            </template>
            
            <!-- 已关闭状态下不显示额外操作按钮 -->
          </div>
        </div>
      </template>
 
    </a-table>
    <!-- 分页组件 -->
    <div class="pagination-container">
      <a-pagination :total="originalOrders.length" show-total show-jumper size="small" :page-size="pageSize"
        show-page-size :current="currentPage" @change="handlePageChange" @pageSizeChange="handlePageSizeChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { IconCopy, IconMessage, IconPlus, IconDown, IconRight, IconLeft, IconCheck } from '@arco-design/web-vue/es/icon';
import { Message } from '@arco-design/web-vue';
import SearchForm from './components/SearchForm/index.vue';

// 定义页面路由元信息
definePageMeta({
  name: 'merchant-orderManage',
  path: '/merchant/order/orderManage'
})

const showAdvancedSearch = ref(false);

// 表单项定义
const formItems = reactive([
  {
    field: 'orderId',
    label: '订单编号',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'productInfo',
    label: '商品名称/ID',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'buyerPhone',
    label: '买家手机',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'address',
    label: '收货地址',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'orderStatus',
    label: '订单状态',
    type: 'select',
    placeholder: '全部',
    options: [
      { label: '全部', value: '全部' },
      { label: '待付款', value: '待付款' },
      { label: '待发货', value: '待发货' },
      { label: '已发货', value: '已发货' },
      { label: '已关闭', value: '已关闭' }
    ],
    defaultValue: '全部',
    span: 1
  },
  {
    field: 'afterSaleStatus',
    label: '售后状态',
    type: 'select',
    placeholder: '全部',
    options: [
      { label: '全部', value: '全部' },
      { label: '无售后', value: '无售后' },
      { label: '售后中', value: '售后中' },
      { label: '售后完成', value: '售后完成' }
    ],
    defaultValue: '全部',
    span: 1
  },
  {
    field: 'paymentMethod',
    label: '支付方式',
    type: 'select',
    placeholder: '请选择',
    options: [
      { label: '全部', value: '全部' },
      { label: '抖音支付', value: '抖音支付' },
      { label: '微信支付', value: '微信支付' },
      { label: '支付宝', value: '支付宝' }
    ],
    defaultValue: '全部',
    span: 1
  },
  {
    field: 'orderTime',
    label: '下单时间',
    isTimeRange: true,
    timePresetField: 'timePreset',
    startDateField: 'startDate',
    endDateField: 'endDate',
    presetOptions: [
      { label: '今天', value: 'today' },
      { label: '昨天', value: 'yesterday' },
      { label: '近7天', value: '7days' },
      { label: '近30天', value: '30days' },
      { label: '自定义', value: 'custom' }
    ],
    defaultPreset: '7days',
    span: 2
  }
]);

const searchForm = reactive({
  orderId: '',
  productInfo: '',
  buyerPhone: '',
  address: '',
  orderStatus: '全部',
  afterSaleStatus: '全部',
  paymentMethod: '全部',
  timePreset: '7days',
  startDate: null,
  endDate: null,
  onlyShowSelected: false
});


const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;
};

const handleSearch = () => {
  currentPage.value = 1;
  // TODO: 实际搜索逻辑
  console.log('搜索条件:', searchForm);
};

const resetSearch = () => {
  // 重置页码
  currentPage.value = 1;
  console.log('表单已重置');
};

const originalOrders = ref([
  { 
    id: 'ORD001',
    paymentMethod: '抖音支付',
    totalPayable: '29.90',
    paymentDetail: '1**********',
    address: '广东省汕尾市汕尾城区********** 这是一个非常长的地址看看会不会换行或者省略号',
    consumer: '张*',
    orderStatus: '待发货',
    deliveryTime: '03小时27分钟37秒',
    deliveryDetail: '已分配物流，等待揽收',
    isShippable: true, // 整个订单是否可发货
    hasAddressEdit: true,
    hasLogisticsCheck: false,
    orderTime: '2023-07-01 10:30:00', // 示例时间
    products: [
      {
        productId: 'PROD101', // 商品唯一ID
        productName: '女士舒适纯棉短袖T恤',
        imageUrl: 'https://img.freepik.com/free-psd/white-t-shirt-mockup_125540-697.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        price: '29.90',
        quantity: 1,
        returnLink: '退运费', // 商品级别的售后链接
        afterSaleStatus: '-', // 商品级别的售后状态
        merchantCode: 'MC001', // 示例商家编号
        color: '粉色', // 示例颜色
      },
      {
        productId: 'PROD102', // 商品唯一ID
        productName: '透气运动短裤',
        imageUrl: 'https://img.freepik.com/free-psd/white-t-shirt-mockup_125540-697.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        price: '45.00',
        quantity: 1,
        returnLink: null,
        afterSaleStatus: '-',
        merchantCode: 'MC001',
        color: '黑色',
      }
    ]
  },
  { 
    id: 'ORD002',
    paymentMethod: '石*',
    totalPayable: '39.99',
    paymentDetail: '尾号9876',
    address: '泰州省泰兴市泰兴市富源街道**********',
    consumer: '1**********',
    orderStatus: '已发货',
    deliveryTime: null, // 无预计时效
    deliveryDetail: null, // 无配送详情
    isShippable: false,
    hasAddressEdit: true, // 示例：允许修改地址
    hasLogisticsCheck: true,
    orderTime: '2023-07-02 11:00:00', // 示例时间
    products: [
       {
        productId: 'PROD201',
        productName: '舒适孕妇睡衣套装',
        imageUrl: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        price: '39.99',
        quantity: 1,
        returnLink: null,
        afterSaleStatus: '已发货', 
        merchantCode: 'MC002',
        color: '蓝色',
      }
    ]
  }
]);

const currentPage = ref(1);
const pageSize = ref(10);

const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return originalOrders.value.slice(start, end);
});

const tableData = computed(() => {
  const data = [];
  paginatedOrders.value.forEach(order => {
    const productCount = order.products.length;
    data.push({ // 表头行数据
      rowId: `${order.id}-header`, // 表头唯一ID
      type: 'header',
      originalId: order.id,
      orderTime: order.orderTime,
      productCount: productCount, // 用于合并单元格计算
    });
    // 详情行数据 (每个商品一行)
    order.products.forEach((product, productIndex) => {
      data.push({
        // 订单级信息 (用于合并单元格或在详情行显示)
        originalId: order.id,
        paymentMethod: order.paymentMethod,
        totalPayable: order.totalPayable,
        paymentDetail: order.paymentDetail,
        address: order.address,
        consumer: order.consumer,
        orderStatus: order.orderStatus,
        deliveryTime: order.deliveryTime,
        deliveryDetail: order.deliveryDetail,
        isShippable: order.isShippable, // 订单级别的可发货状态
        hasAddressEdit: order.hasAddressEdit,
        hasLogisticsCheck: order.hasLogisticsCheck,
        // 商品级信息
        ...product,
        // 表格行属性
        rowId: `${order.id}-${product.productId}-details`, // 详情行唯一ID (订单ID + 商品ID)
        type: 'details',
        productIndex: productIndex, // 商品在订单中的索引 (0-based)
        productCount: productCount, // 订单总商品数
      });
    });
  });
  return data;
});

// --- 表格配置 ---
const selectedRowKeys = ref([]);
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: false, 
  selectedRowKeys: selectedRowKeys,
  onlyCurrent: false,
});

const selectableRowKeysOnPage = computed(() => {
  return tableData.value
    .filter(row => row.type === 'details')
    .map(row => row.rowId);
});

const isAllSelectedOnPage = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (pageKeys.length === 0) return false;
  return pageKeys.every(key => selectedRowKeys.value.includes(key));
});

const isIndeterminate = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  const selectedOnPageCount = pageKeys.filter(key => selectedRowKeys.value.includes(key)).length;
  return selectedOnPageCount > 0 && selectedOnPageCount < pageKeys.length;
});

const handleSelectAllOnPage = (checked) => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (checked) {
    const newKeys = [...new Set([...selectedRowKeys.value, ...pageKeys])];
    selectedRowKeys.value = newKeys;
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(key => !pageKeys.includes(key));
  }
};

const columns = reactive([
  {
    title: '商品信息',
    slotName: 'productInfo-cell',
    width: 380,
  },
  { title: '单价/数量', slotName: 'priceQuantity-cell', width: 120, align: 'center' },
  { title: '实付金额', slotName: 'payment-cell', width: 150, align: 'right' },
  { title: '收件信息', slotName: 'receiver-cell', width: 130 },
  { title: '支付方式', slotName: 'payment-method-cell', width: 100, align: 'center' },
  { title: '配送方式', slotName: 'delivery-method-cell', width: 100, align: 'center' },
  { title: '订单状态', slotName: 'status-cell', width: 150 },
  { title: '操作', slotName: 'operations-cell', width: 150, align: 'center', fixed: 'right' },
]);

// 用于合并单元格的 Span 方法
const spanMethod = ({ record, column, rowIndex }) => {
  if (record.type === 'header') { // 表头行: 第一列合并所有列
    if (column.slotName === 'productInfo-cell') {
      return { rowspan: 1, colspan: columns.length };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  if (record.type === 'details') {
    // 详情行: 对特定列进行垂直合并
    const isFirstProduct = record.productIndex === 0;
    const rowspan = isFirstProduct ? record.productCount : 0;
    // 需要垂直合并的列 (订单级别信息)
    const colsToSpan = [
      'payment-method-cell',
      'delivery-method-cell',
      'payment-cell',
      'receiver-cell',
      'status-cell',
      'operations-cell',
    ];

    if (colsToSpan.includes(column.slotName)) {
      return { rowspan: rowspan, colspan: 1 };
    }
  }
  // 其他单元格不合并
  return { rowspan: 1, colspan: 1 };
};

// 复制订单 ID 函数
const copyOrderId = async (id) => {
  try {
    await navigator.clipboard.writeText(id);
    Message.success(`订单号 ${id} 已复制`);
  } catch (err) {
    Message.error('复制失败');
    console.error('Failed to copy: ', err);
  }
};

// --- 分页处理程序 ---
const handlePageChange = (page) => {
  currentPage.value = page;
};

const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; 
};

// 获取指定字段的选项
const getOptionsForField = (fieldName) => {
  const item = formItems.find(item => item.field === fieldName);
  return item ? item.options : [];
};

// 获取时间预设选项
const getTimePresetOptions = () => {
  const item = formItems.find(item => item.isTimeRange);
  return item ? item.presetOptions : [];
};

// 统计卡片数据
const statsCards = reactive([
  { id: 'all', title: '全部订单', value: '3,743' },
  { id: 'unpaid', title: '待付款', value: '0' },
  { id: 'unshipped', title: '待发货', value: '1' },
  { id: 'shipped', title: '已发货', value: '5' },
  { id: 'closed', title: '已关闭', value: '0' }
]);

// 当前选中的统计卡片
const activeStatsCard = ref('all');

// 设置当前选中的统计卡片
const setActiveStatsCard = (id) => {
  activeStatsCard.value = id;
};

// 统计卡片滚动相关
const statsCardsRef = ref(null);

// 滚动统计卡片
const scrollStatsCards = (direction) => {
  if (!statsCardsRef.value) return;
  
  const scrollAmount = 200; // 每次滚动的像素数
  const currentScroll = statsCardsRef.value.scrollLeft;
  
  if (direction === 'left') {
    statsCardsRef.value.scrollTo({
      left: Math.max(0, currentScroll - scrollAmount),
      behavior: 'smooth'
    });
  } else {
    statsCardsRef.value.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: 'smooth'
    });
  }
};
</script>

<style scoped lang="less">
// css样式写在index.css 防止单文件代码太长 AI读取不了
@import "./orderManage.css";
</style>
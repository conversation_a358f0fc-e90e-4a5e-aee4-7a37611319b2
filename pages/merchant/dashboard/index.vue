<template>
  <div class="dashboard-container">
    <h1>商家端仪表盘</h1>
    <a-card class="dashboard-card">
      <template #title>系统概览</template>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-statistic title="总用户数" :value="1234" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="活跃商家" :value="256" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="今日订单" :value="89" />
        </a-col>
      </a-row>
    </a-card>

    <a-divider />

    <a-card class="dashboard-card">
      <template #title>快捷功能</template>
      <a-space direction="vertical" size="large" style="width: 100%">
        <a-row :gutter="16">
          <a-col :span="6" v-for="(item, index) in quickActions" :key="index">
            <a-card hoverable>
              <template #cover>
                <div class="action-icon">
                  <IconApps />
                </div>
              </template>
              <a-card-meta :title="item.title">
                <template #description>{{ item.description }}</template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>
      </a-space>
    </a-card>
  </div>
</template>

<script setup>
definePageMeta({
  name: 'merchant-dashboard',
  path: '/merchant/dashboard'
})
import { IconApps } from '@arco-design/web-vue/es/icon';

const quickActions = [
  { title: '用户管理', description: '管理系统用户信息' },
  { title: '商家管理', description: '管理平台商家信息' },
  { title: '订单管理', description: '查看和处理订单' },
  { title: '系统设置', description: '配置系统参数' }
];
</script>

<style scoped>
.dashboard-card {
  margin-bottom: 20px;
}

.action-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  background-color: #f2f3f5;
  font-size: 36px;
  color: #165DFF;
}
</style>
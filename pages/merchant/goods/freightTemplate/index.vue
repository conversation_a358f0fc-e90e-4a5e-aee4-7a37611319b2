<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :data="tableData">
      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-popconfirm content="确定要删除该运费模板吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>

      <!-- 自定义列 - 计费类型 -->
      <template #chargeType="{ record }">
        <a-tag :color="record.chargeType === 1 ? 'blue' : 'green'">
          {{ record.chargeType === 1 ? '按件' : '按重量' }}
        </a-tag>
      </template>

      <!-- 自定义列 - 首件/首重 -->
      <template #firstItem="{ record }">
        {{ record.firstItem }}{{ record.chargeType === 1 ? '件' : 'kg' }}
      </template>

      <!-- 自定义列 - 首件/首重运费 -->
      <template #firstItemFee="{ record }">
        ¥{{ record.firstItemFee }}
      </template>

      <!-- 自定义列 - 续件/续重 -->
      <template #additionalItem="{ record }">
        {{ record.additionalItem }}{{ record.chargeType === 1 ? '件' : 'kg' }}
      </template>

      <!-- 自定义列 - 续件/续重运费 -->
      <template #additionalItemFee="{ record }">
        ¥{{ record.additionalItemFee }}
      </template>
    </ma-crud>

    <!-- 运费模板编辑组件 -->
    <freight-edit
      v-model:visible="modalVisible"
      :title="modalTitle"
      :data="currentTemplate"
      @submit="handleTemplateSubmit"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit, IconDelete, IconPlus } from '@arco-design/web-vue/es/icon';
import FreightEdit from './components/FreightEdit.vue';

definePageMeta({
  name: "merchant-freightTemplate",
  path: "/merchant/goods/freightTemplate",
})

const crudRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增运费模板');
const currentTemplate = ref({});
const currentId = ref(null);

// 模拟表格数据
const tableData = ref([
  {
    id: 1,
    name: "标准快递",
    chargeType: 1, // 1: 按件, 2: 按重量
    deliveryAreas: "北京,上海,广州,深圳",
    firstItem: 1,
    firstItemFee: 10,
    additionalItem: 1,
    additionalItemFee: 5,
    created_at: "2025-04-01 10:30:04"
  },
  {
    id: 2,
    name: "经济快递",
    chargeType: 1,
    deliveryAreas: "全国",
    firstItem: 1,
    firstItemFee: 8,
    additionalItem: 1,
    additionalItemFee: 3,
    created_at: "2025-04-02 14:20:15"
  },
  {
    id: 3,
    name: "重量计费",
    chargeType: 2,
    deliveryAreas: "华东地区,华南地区",
    firstItem: 1, // 首重(kg)
    firstItemFee: 12,
    additionalItem: 1, // 续重(kg)
    additionalItemFee: 6,
    created_at: "2025-04-03 09:45:30"
  }
]);

// 打开新增弹窗
const handleAdd = () => {
  modalTitle.value = '新增运费模板';
  currentTemplate.value = {
    name: '',
    chargeType: 1,
    deliveryAreas: '',
    firstItem: 1,
    firstItemFee: 0,
    additionalItem: 1,
    additionalItemFee: 0
  };
  currentId.value = null;
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record) => {
  modalTitle.value = '编辑运费模板';
  currentTemplate.value = { ...record };
  currentId.value = record.id;
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (id) => {
  // 这里是模拟删除，实际项目中应该调用API
  tableData.value = tableData.value.filter(item => item.id !== id);
  Message.success('删除成功');
};

// 处理运费模板表单提交
const handleTemplateSubmit = (formData) => {
  try {
    if (currentId.value === null) {
      // 新增操作
      const newId = Math.max(...tableData.value.map(item => item.id), 0) + 1;
      const newTemplate = {
        ...formData,
        id: newId,
        created_at: new Date().toLocaleString()
      };
      tableData.value.push(newTemplate);
      Message.success('新增运费模板成功');
    } else {
      // 编辑操作
      const index = tableData.value.findIndex(item => item.id === currentId.value);
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          ...formData
        };
        Message.success('编辑运费模板成功');
      }
    }
    modalVisible.value = false;
  } catch (error) {
    console.error('提交失败:', error);
    Message.error('操作失败，请重试');
  }
};

// CRUD 配置
const crud = reactive({
  api: '', // 实际项目中应该填写API路径
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 150,
  add: {
    show: false, // 使用自定义新增按钮
  },
  edit: {
    show: false, // 使用自定义编辑按钮
  },
  delete: {
    show: false, // 使用自定义删除按钮
  },
  pagination: {
    pageSize: 10,
    showTotal: true,
    showPageSize: true,
  },
});

// 表格列配置
const columns = reactive([
  {
    title: '运费模板名称',
    dataIndex: 'name',
    width: 150,
    sortable: true,
  },
  {
    title: '计费类型',
    dataIndex: 'chargeType',
    width: 100,
    slotName: 'chargeType'
  },
  {
    title: '配送区域',
    dataIndex: 'deliveryAreas',
    width: 200,
    ellipsis: true,
  },
  {
    title: '首件/首重',
    dataIndex: 'firstItem',
    width: 100,
    slotName: 'firstItem'
  },
  {
    title: '首件/首重运费',
    dataIndex: 'firstItemFee',
    width: 120,
    slotName: 'firstItemFee'
  },
  {
    title: '续件/续重',
    dataIndex: 'additionalItem',
    width: 100,
    slotName: 'additionalItem'
  },
  {
    title: '续件/续重运费',
    dataIndex: 'additionalItemFee',
    width: 120,
    slotName: 'additionalItemFee'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180,
    sortable: true,
  }
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>
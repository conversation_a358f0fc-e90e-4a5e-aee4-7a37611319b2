<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-card class="mb-4">
      <ma-form ref="basicFormRef" v-model="form" :columns="basicFormColumns" layout="vertical" :options="{ showButtons: false }">
      </ma-form>
    </a-card>
    
    <a-card title="商品标签" class="mb-4">
      <div class="flex items-center mb-2">
        <a-checkbox-group v-model="labelForm.labels">
          <a-checkbox :value="1">热销</a-checkbox>
          <a-checkbox :value="2">新品</a-checkbox>
          <a-checkbox :value="3">推荐</a-checkbox>
        </a-checkbox-group>
        <a-button type="text" @click="openLabelManage" class="ml-4">
          <template #icon><icon-plus /></template>
          标签管理
        </a-button>
      </div>
    </a-card>
    
    <a-card title="商品服务" class="mb-4">
      <div class="flex items-center mb-2">
        <a-checkbox-group v-model="serviceForm.services">
          <a-checkbox :value="1">7天无理由退货</a-checkbox>
          <a-checkbox :value="2">48小时发货</a-checkbox>
          <a-checkbox :value="3">正品保障</a-checkbox>
        </a-checkbox-group>
        <a-button type="text" @click="openServiceManage" class="ml-4">
          <template #icon><icon-plus /></template>
          商品服务
        </a-button>
      </div>
    </a-card>
    
    <!-- 物流设置 -->
    <a-card title="物流设置" class="mb-4">
      <div class="flex">
        <div style="width: 300px;">
          <ma-form ref="logisticsFormRef" v-model="logisticsForm" :columns="logisticsFormColumns" layout="vertical" :options="{ showButtons: false }"/>
        </div>
        <a-button type="text" @click="openFreightManage" >
          <template #icon><icon-plus /></template>
          运费模板管理
        </a-button>
      </div>
    </a-card>
    
    <!-- 配送信息 -->
    <a-card title="配送信息" class="mb-4">
      <ma-form ref="deliveryFormRef" v-model="deliveryForm" :columns="deliveryFormColumns" layout="vertical" :options="{ showButtons: false }"/>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import MaUpload from '~/components/base/ma-upload/index.vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'open-label-manage', 'open-service-manage', 'open-freight-manage']);

// 基本信息表单
const form = reactive({
  name: '',           // 商品名称
  categoryId: null,   // 商品分类
  brandId: null,      // 商品品牌
  price: 0,           // 商品价格
  marketPrice: 0,     // 市场价格
  costPrice: 0,       // 成本价格
  stock: 0,           // 库存
  weight: 0,          // 重量
  volume: 0,          // 体积
  barcode: '',        // 条形码
  description: '',    // 商品描述
  images: [],         // 商品图片
  video: '',          // 商品视频
  ...props.modelValue
});

// 监听父组件传入的值变化
watch(() => props.modelValue, (newVal) => {
  Object.assign(form, newVal);
}, { deep: true });

// 监听本地表单值变化，同步到父组件
watch(form, (newVal) => {
  emit('update:modelValue', newVal);
}, { deep: true });

// 标签表单
const labelForm = reactive({
  labels: []
});

// 服务表单
const serviceForm = reactive({
  services: []
});

// 物流表单
const logisticsForm = reactive({
  freightTemplateId: null
});

// 配送信息表单
const deliveryForm = reactive({
  deliveryArea: '',
  deliveryTime: '',
  weight: 0,
  volume: 0
});

// 商品分类数据
const categoryData = ref([]);

// 获取商品分类数据
const getCategoryData = async () => {
  // 模拟数据，实际项目中应该调用API
  const data = [
    {
      id: 1,
      name: "服饰鞋包",
      children: [
        {
          id: 2,
          name: "男装",
        },
        {
          id: 3,
          name: "女装",
        },
        {
          id: 4,
          name: "箱包",
        }
      ]
    },
    {
      id: 5,
      name: "手机数码",
      children: [
        {
          id: 6,
          name: "手机",
        },
        {
          id: 7,
          name: "平板电脑",
        },
        {
          id: 8,
          name: "笔记本电脑",
        }
      ]
    },
    {
      id: 9,
      name: "家用电器",
      children: [
        {
          id: 10,
          name: "电视",
        },
        {
          id: 11,
          name: "空调",
        },
        {
          id: 12,
          name: "冰箱",
        }
      ]
    }
  ];
  return data;
};

// 品牌数据
const brandData = ref([]);

// 获取品牌数据
const getBrandData = async () => {
  // 模拟数据，实际项目中应该调用API
  const data = [
    {
      id: 1,
      name: "苹果",
    },
    {
      id: 2,
      name: "华为",
    },
    {
      id: 3,
      name: "小米",
    },
    {
      id: 4,
      name: "三星",
    },
    {
      id: 5,
      name: "OPPO",
    },
    {
      id: 6,
      name: "vivo",
    }
  ];
  return data;
};

// 基本信息表单配置
const basicFormColumns = reactive([
  {
    title: '商品名称',
    dataIndex: 'name',
    formType: 'input',
    placeholder: '请输入商品名称',
    rules: [{ required: true, message: '请输入商品名称' }],
    attrs: {
      maxLength: 100
    }
  },
  {
    title: '商品分类',
    dataIndex: 'categoryId',
    formType: 'tree-select',
    placeholder: '请选择商品分类',
    rules: [{ required: true, message: '请选择商品分类' }],
    fieldNames: { key: 'id', title: 'name' },
    allowSearch: true,
    allowClear: true
  },
  {
    title: '商品品牌',
    dataIndex: 'brandId',
    formType: 'select',
    placeholder: '请选择商品品牌',
    rules: [{ required: true, message: '请选择商品品牌' }],
    allowSearch: true,
    allowClear: true
  },
  {
    title: '副标题',
    dataIndex: 'description',
    formType: 'textarea',
    placeholder: '请输入副标题',
    attrs: {
      maxLength: 200,
      showWordLimit: true,
      autoSize: { minRows: 3, maxRows: 5 }
    }
  },
  {
    title: '商品图片',
    dataIndex: 'images',
    formType: 'upload',
    type: 'image',
    multiple: true,
    limit: 5,
    accept: '.jpg,.jpeg,.png,.gif',
    tip: '最多上传5张图片，建议尺寸800x800px，大小不超过2MB',
    returnType: 'url',
    rules: [{ required: true, message: '请上传至少一张商品图片' }]
  },
  {
    title: '商品视频',
    dataIndex: 'video',
    formType: 'upload',
    type: 'file',
    multiple: false,
    limit: 1,
    accept: '.mp4,.mov',
    tip: '最多上传1个视频，大小不超过50MB',
    returnType: 'url'
  }
]);

// 物流表单配置
const logisticsFormColumns = reactive([
  {
    title: '运费模板',
    dataIndex: 'freightTemplateId',
    formType: 'select',
    placeholder: '请选择运费模板',
    rules: [{ required: true, message: '请选择运费模板' }],
    allowSearch: true,
    allowClear: true,
    data: [
      {
        label: '标准快递',
        value: 1
      },
      {
        label: '经济快递',
        value: 2
      },
      {
        label: '顺丰速运',
        value: 3
      }
    ]
  }
]);

// 配送信息表单配置
const deliveryFormColumns = reactive([
  {
    title: '配送区域',
    dataIndex: 'deliveryArea',
    formType: 'city-linkage',
    placeholder: '请选择配送区域',
    rules: [{ required: true, message: '请选择配送区域' }],
    type: 'cascader',
    mode: 'code'
  },
  {
    title: '发货时间',
    dataIndex: 'deliveryTime',
    formType: 'date',
    placeholder: '请选择发货时间',
    rules: [{ required: true, message: '请选择发货时间' }],
    allowClear: true,
    format: 'YYYY-MM-DD'
  },
]);

// 表单引用
const basicFormRef = ref(null);
const logisticsFormRef = ref(null);
const deliveryFormRef = ref(null);

// 打开标签管理
const openLabelManage = () => {
  emit('open-label-manage');
};

// 打开服务管理
const openServiceManage = () => {
  emit('open-service-manage');
};

// 打开运费模板管理
const openFreightManage = () => {
  emit('open-freight-manage');
};

// 表单验证
const validate = () => {
  return new Promise((resolve, reject) => {
    Promise.all([basicFormRef.value.validateForm(), logisticsFormRef.value.validateForm(), deliveryFormRef.value.validateForm()]).then(([basicValid, logisticsValid, deliveryValid]) => {
      if (!basicValid && !logisticsValid && !deliveryValid) {
        console.log('基本信息验证通过', form);
        console.log('物流信息验证通过', logisticsForm);
        console.log('配送信息验证通过', deliveryForm);
        resolve(true);
      } else {
        console.error('基本信息验证失败', basicValid);
        console.error('物流信息验证失败', logisticsValid);
        console.error('配送信息验证失败', deliveryValid);
        reject([basicValid, logisticsValid, deliveryValid]);
      }
    }).catch(error => {
      console.error('基本信息验证失败', error);
      console.error('物流信息验证失败', error);
      console.error('配送信息验证失败', error);
      reject(error);
    });
  });
};

// 初始化数据
onMounted(async () => {
  // 获取商品分类数据
  categoryData.value = await getCategoryData();
  
  // 获取品牌数据
  brandData.value = await getBrandData();
  
  // 更新表单配置中的数据源
  basicFormColumns.forEach(item => {
    if (item.dataIndex === 'categoryId') {
      item.data = categoryData.value;
    } else if (item.dataIndex === 'brandId') {
      // 将品牌数据转换为select组件需要的格式
      item.data = brandData.value.map(brand => ({
        label: brand.name,
        value: brand.id
      }));
    }
  });
});

// 暴露方法给父组件
defineExpose({
  validate,
  basicFormRef,
  logisticsFormRef,
  deliveryFormRef,
  form,
  labelForm,
  serviceForm,
  logisticsForm,
  deliveryForm
});
</script>

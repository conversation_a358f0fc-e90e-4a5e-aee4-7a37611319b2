<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <a-modal
    :visible="visible"
    :title="`${record.name || ''} - 参数项目`"
    @cancel="handleCancel"
    :footer="false"
    :width="900"
    :mask-closable="false"
  >
    <div class="params-container">
      <div class="mb-4 flex justify-between">
        <a-button type="primary" @click="addParamItem">
          <template #icon><icon-plus /></template>添加参数
        </a-button>
      </div>
      <!-- 使用a-table组件展示参数项 -->
      <a-table :data="paramItems" :pagination="false" row-key="id">
        <template #columns>
          <a-table-column title="参数名称" data-index="name" width="120" />
          <a-table-column title="参数类型" data-index="type" width="100">
            <template #cell="{ record }">
              <a-tag v-if="record.type === 'text'">文本</a-tag>
              <a-tag v-else-if="record.type === 'number'">数字</a-tag>
              <a-tag v-else-if="record.type === 'radio'">单选</a-tag>
              <a-tag v-else-if="record.type === 'checkbox'">多选</a-tag>
              <a-tag v-else-if="record.type === 'select'">下拉选择</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="可选值/单位" data-index="options" width="150" />
          <a-table-column title="必填" data-index="required" width="80">
            <template #cell="{ record }">
              <a-tag color="green" v-if="record.required">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="可筛选" data-index="filterable" width="80">
            <template #cell="{ record }">
              <a-tag color="blue" v-if="record.filterable">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="状态" data-index="status" width="80">
            <template #cell="{ record }">
              <a-tag color="green" v-if="record.status === 1">启用</a-tag>
              <a-tag color="red" v-else>禁用</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="操作" width="200" align="center">
            <template #cell="{ record }">
              <div>
                <a-button type="text" size="small" @click="handleEditParam(record)">
                  <template #icon><icon-edit /></template>
                  编辑
                </a-button>
                <a-popconfirm content="确定要删除该参数项吗?" position="bottom" @ok="removeParamItem(record.id)">
                  <a-button type="text" status="danger" size="small">
                    <template #icon><icon-delete /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>

    <!-- 参数项编辑组件 -->
    <param-item-edit
      v-model:visible="paramEditVisible"
      :title="paramEditTitle"
      :data="currentParamItem"
      @submit="handleParamSubmit"
    />
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import ParamItemEdit from './ParamItemEdit.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save', 'cancel']);

// 参数项编辑相关
const paramEditVisible = ref(false);
const paramEditTitle = ref('编辑参数项');
const currentParamItem = ref({});
const paramItems = ref([]);

// 监听记录变化，初始化参数项数据
watch(() => props.record, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    loadParamItems(newVal);
  }
}, { immediate: true, deep: true });

// 加载参数项数据
const loadParamItems = (record) => {
  console.log('加载参数项数据', record);
  // 模拟获取参数项目数据
  if (record.id === 1) {
    paramItems.value = [
      { id: 1, name: '屏幕尺寸', type: 'text', options: '英寸', required: true, filterable: true, status: 1 },
      { id: 2, name: '电池容量', type: 'number', options: 'mAh', required: true, filterable: false, status: 1 },
      { id: 3, name: '处理器', type: 'select', options: '骁龙8 Gen 2,骁龙8 Gen 1,天玑9000', required: true, filterable: true, status: 1 },
      { id: 4, name: '内存', type: 'radio', options: '8GB,12GB,16GB', required: true, filterable: true, status: 1 },
      { id: 5, name: '存储', type: 'radio', options: '128GB,256GB,512GB', required: true, filterable: true, status: 1 },
      { id: 6, name: '前置摄像头', type: 'text', options: '像素', required: false, filterable: false, status: 1 },
      { id: 7, name: '后置摄像头', type: 'text', options: '像素', required: false, filterable: false, status: 1 },
      { id: 8, name: '操作系统', type: 'select', options: 'Android 13,Android 12,iOS 16', required: false, filterable: true, status: 1 }
    ];
  } else if (record.id === 2) {
    paramItems.value = [
      { id: 1, name: '屏幕尺寸', type: 'text', options: '英寸', required: true, filterable: true, status: 1 },
      { id: 2, name: '处理器', type: 'select', options: 'Intel i7-12700H,Intel i5-12500H,AMD R7-6800H', required: true, filterable: true, status: 1 },
      { id: 3, name: '显卡', type: 'select', options: 'NVIDIA RTX 3060,NVIDIA RTX 3050,AMD RX 6700M', required: true, filterable: true, status: 1 },
      { id: 4, name: '内存', type: 'radio', options: '16GB,32GB', required: true, filterable: true, status: 1 },
      { id: 5, name: '存储', type: 'radio', options: '512GB SSD,1TB SSD', required: true, filterable: true, status: 1 },
      { id: 6, name: '电池容量', type: 'number', options: 'Wh', required: false, filterable: false, status: 1 },
      { id: 7, name: '重量', type: 'text', options: 'kg', required: false, filterable: false, status: 1 },
      { id: 8, name: '接口', type: 'checkbox', options: 'USB 3.2,HDMI,雷电4,SD卡槽', required: false, filterable: true, status: 1 },
      { id: 9, name: '无线连接', type: 'checkbox', options: 'WiFi 6,蓝牙5.2,WiFi 6E', required: false, filterable: true, status: 1 },
      { id: 10, name: '操作系统', type: 'select', options: 'Windows 11,Windows 10,macOS', required: true, filterable: true, status: 1 },
      { id: 11, name: '摄像头', type: 'text', options: '', required: false, filterable: false, status: 1 },
      { id: 12, name: '键盘', type: 'radio', options: '背光键盘,RGB键盘,普通键盘', required: false, filterable: true, status: 1 }
    ];
  } else if (record.id === 3) {
    paramItems.value = [
      { id: 1, name: '容量', type: 'text', options: 'L', required: true, filterable: true, status: 1 },
      { id: 2, name: '能效等级', type: 'select', options: '一级能效,二级能效,三级能效', required: true, filterable: true, status: 1 },
      { id: 3, name: '制冷方式', type: 'radio', options: '风冷,直冷,混合制冷', required: true, filterable: true, status: 1 },
      { id: 4, name: '温控方式', type: 'radio', options: '电脑温控,机械温控', required: false, filterable: false, status: 1 },
      { id: 5, name: '门款式', type: 'select', options: '对开门,三门,多门,双门', required: true, filterable: true, status: 1 },
      { id: 6, name: '颜色', type: 'checkbox', options: '银色,黑色,白色,金色', required: false, filterable: true, status: 1 }
    ];
  } else {
    paramItems.value = [];
  }
  console.log('参数项目数据', paramItems.value);
};

// 添加参数项目
const addParamItem = () => {
  paramEditTitle.value = '新增参数项';
  currentParamItem.value = {
    id: 0,
    name: '',
    type: 'text',
    options: '',
    required: false,
    filterable: false,
    status: 1
  };
  paramEditVisible.value = true;
};

// 编辑参数项
const handleEditParam = (record) => {
  paramEditTitle.value = '编辑参数项';
  currentParamItem.value = { ...record };
  paramEditVisible.value = true;
};

// 处理参数项提交
const handleParamSubmit = (data) => {
  if (currentParamItem.value.id === 0) {
    // 新增参数项
    const newId = paramItems.value.length > 0 ? Math.max(...paramItems.value.map(item => item.id)) + 1 : 1;
    paramItems.value.push({
      id: newId,
      ...data
    });
    Message.success('参数项添加成功');
  } else {
    // 更新参数项
    const index = paramItems.value.findIndex(item => item.id === currentParamItem.value.id);
    if (index !== -1) {
      Object.assign(paramItems.value[index], data);
      Message.success('参数项更新成功');
    }
  }
  paramEditVisible.value = false;
};

// 删除参数项目
const removeParamItem = (id) => {
  paramItems.value = paramItems.value.filter(item => item.id !== id);
  Message.success('参数项删除成功');
};

// 保存参数项目
const saveParams = () => {
  // 验证参数项目
  const emptyParams = paramItems.value.filter(item => !item.name || !item.options);
  if (emptyParams.length > 0) {
    Message.error('参数名称和可选值/单位不能为空');
    return false;
  }

  emit('save', {
    id: props.record.id,
    params: paramItems.value,
    params_count: paramItems.value.length
  });
  
  Message.success('参数项目保存成功');
  emit('update:visible', false);
};

// 取消处理
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};
</script>

<style scoped>
.params-container {
  padding: 0 16px;
}
</style>

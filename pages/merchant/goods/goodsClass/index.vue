<template>
    <div class="ma-content-block lg:flex justify-between p-4 gap-4">
        <div
            class="lg:w-4/12 w-full mb-4 lg:mb-0 border rounded-md p-3 bg-white dark:bg-blackdark-5 dark:border-blackdark-3 shadow">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold">商品分类</h3>
                <a-button type="primary" size="small" @click="handleAddRoot">
                    <template #icon><icon-plus /></template> 添加根分类
                </a-button>
            </div>
            <a-input v-model="filterText" placeholder="搜索分类名称" allow-clear class="mb-2" />
            <div class="tree-container customer-scrollbar">
                <a-tree ref="treeRef" :data="classData"
                    :field-names="{ title: 'name', key: 'id', children: 'children' }" :show-line="true"
                    :block-node="true" @select="handleTreeSelect">
                    <template #title="nodeData">
                        <span class="truncate">{{ nodeData && nodeData.name || '未命名分类' }}</span>
                    </template>
                    <template #extra="nodeData">
                        <a-space size="mini">
                            <a-button type="text" size="mini" @click.stop="handleEdit(nodeData)">
                                <template #icon><icon-edit /></template>
                            </a-button>
                            <a-button type="text" size="mini" @click.stop="handleAddChild(nodeData)">
                                <template #icon><icon-plus /></template>
                            </a-button>
                            <a-popconfirm content="确定要删除此分类及其子分类吗?" @ok="handleDelete(nodeData)">
                                <a-button type="text" status="danger" size="mini" @click.stop>
                                    <template #icon><icon-delete /></template>
                                </a-button>
                            </a-popconfirm>
                        </a-space>
                    </template>
                </a-tree>
            </div>
        </div>

        <div class="lg:w-8/12 w-full border rounded-md p-4 bg-white dark:bg-blackdark-5 dark:border-blackdark-3 shadow">
            <div class="text-lg font-semibold mb-4">
                {{ formMode === 'add' ? '新增分类详情' : formMode === 'edit' ? '编辑分类详情' : '分类详情' }}
                <span v-if="formMode === 'edit'"> (ID: {{ formModel.id }})</span>
            </div>
            <ma-form ref="maFormRef" v-model="formModel" :columns="columns" :options="formOptions" auto-label-width
                @submit="handleSubmit">
                <template #form-parent_id>
                    <a-tree-select v-model="formModel.parent_id" :data="treeSelectData" :disabled="formMode === 'edit'"
                        placeholder="选择父级分类（不选则为根分类）" allow-clear allow-search
                        :field-names="{ key: 'id', title: 'name' }" />
                </template>

                <!-- 关联属性模板 -->
                <template #form-template_id>
                    <a-select v-model="formModel.template_id" placeholder="请选择关联属性模板" allow-clear allow-search>
                        <a-option value="0">无</a-option>
                        <a-option value="1">手机属性</a-option>
                        <a-option value="2">服装属性</a-option>
                        <a-option value="3">家具属性</a-option>
                        <a-option value="4">电脑属性</a-option>
                    </a-select>
                </template>
            </ma-form>
            <div v-if="formMode === 'view'" class="p-4 text-center text-gray-500">
                请在左侧选择商品分类进行编辑，或点击按钮添加新分类。
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue';
import MaForm from '~/components/base/ma-form/index.vue';
import { Message } from '@arco-design/web-vue';
import { IconPlus, IconEdit, IconDelete } from '@arco-design/web-vue/es/icon';
import tool from '@/utils/tool';

definePageMeta({
    name: 'merchant-goods-class',
    path: "/merchant/goods/goodsClass",
});

const treeRef = ref();
const maFormRef = ref();
const filterText = ref('');
const formMode = ref('view');
const classData = ref([]);

const getMockClassData = () => [
    {
        id: 1,
        parent_id: 0,
        name: "服饰鞋包",
        sort: 1,
        status: 1,
        remark: "服装、鞋子、箱包等",
        children: [
            {
                id: 2,
                parent_id: 1,
                name: "男装",
                sort: 10,
                status: 1,
                remark: "男士服装",
                children: [
                    { id: 5, parent_id: 2, name: "衬衫", sort: 1, status: 1, remark: "" },
                    { id: 6, parent_id: 2, name: "裤子", sort: 2, status: 1, remark: "" }
                ],
            },
            {
                id: 3,
                parent_id: 1,
                name: "女装",
                sort: 20,
                status: 1,
                remark: "女士服装",
                children: [],
            },
            {
                id: 4,
                parent_id: 1,
                name: "童装",
                sort: 30,
                status: 2, // Disabled example
                remark: "儿童服装",
                children: [],
            },
        ],
    },
    {
        id: 7,
        parent_id: 0,
        name: "数码家电",
        sort: 100,
        status: 1,
        remark: "手机、电脑、家电等",
        children: [],
    },
];
const initialForm = {
    id: undefined,
    parent_id: 0, 
    name: '',
    sort: 100,
    status: 1, 
    remark: '',
    template_id: '0', // 关联属性模板
    seo_title: '', // SEO标题
    seo_keywords: '', // SEO关键词
    seo_description: '', // SEO描述
};
const formModel = ref({ ...initialForm });

const formOptions = reactive({
    labelWidth: '130px',
    showButtons: true, 
    submitText: '保存',
    resetText: '重置',
});

const columns = reactive([
    {
        title: '分类名称',
        dataIndex: 'name',
        formType: 'input',
        rules: [{ required: true, message: '分类名称必填' }],
        labelWidth: '135px'
    },
    { title: '上级分类', dataIndex: 'parent_id', formType: 'tree-select', hide: false, addDisplay: true, editDisplay: true,
     disabled: computed(() => formMode.value === 'edit'), labelWidth: '135px' },
   
    {
        title: '分类图片',
        dataIndex: 'files',
        formType: 'upload',
        labelWidth: '135px'
    },
    { title: '分类描述', dataIndex: 'remark', formType: 'textarea',labelWidth: '135px' },
    {
        title: '排序',
        dataIndex: 'sort',
        formType: 'input-number',
        min: 0,
        max: 999,
        addDefaultValue: 100,
        labelWidth: '135px'
    },
    {
        title: '状态',
        dataIndex: 'status',
        formType: 'radio',
        addDefaultValue: 1,
        dict: { data: [{ label: '启用', value: 1 }, { label: '禁用', value: 2 }], labelWidth: '135px' },
    },
    // 添加关联属性模板
    { 
        title: '关联属性模板', 
        dataIndex: 'template_id', 
        formType: 'select',
        addDefaultValue: '0',
        labelWidth: '135px'
    },
    // 添加SEO信息分组
    { 
        title: 'SEO信息', 
        formType: 'divider',
    },
    { title: 'Meta Title', dataIndex: 'seo_title', formType: 'input',labelWidth: '135px' },
    { title: 'Meta Keywords', dataIndex: 'seo_keywords', formType: 'input',labelWidth: '135px'},
    { title: 'Meta Description', dataIndex: 'seo_description', formType: 'textarea',labelWidth: '135px'},
]);

const treeSelectData = computed(() => {
    return [{ id: 0, name: '根分类', children: classData.value }];
});

onMounted(() => {
    classData.value = getMockClassData();
    console.log('商品分类数据:', JSON.stringify(classData.value));
});

watch(filterText, (val) => {
});

const filterNode = (value, data) => {
    if (!value) return true;
    // 添加安全检查，确保 data 和 data.name 存在
    return data && data.name ? data.name.includes(value) : false;
};

const handleTreeSelect = (selectedKeys, { node, e }) => {
    if (selectedKeys.length > 0 && node) {
        nextTick(() => {
            formModel.value = { ...node.dataRef }; 
            formMode.value = 'edit';
            maFormRef.value?.clearValidate(); 
        });
    } else {
        resetFormView();
    }
};

const handleAddRoot = () => {
    resetFormView(); 
    formModel.value.parent_id = 0; 
    formMode.value = 'add';
};

const handleAddChild = (parentData) => {
    resetFormView();
    formModel.value.parent_id = parentData.id;
    formMode.value = 'add';
    Message.info(`将在分类 [${parentData.name}] 下添加子分类`);
};

const resetFormView = () => {
    formModel.value = { ...initialForm };
    formMode.value = 'view';
    maFormRef.value?.resetFields();
    maFormRef.value?.clearValidate();
};

const handleSubmit = async (formData, done) => {
    const validationResult = await maFormRef.value.validateForm();
    if (validationResult) {
        console.error('Form validation failed:', validationResult);
        done(false);
        return;
    }

    const dataToSave = { ...formData };

    try {
        if (formMode.value === 'add') {
            const newId = tool.generateId(); 
            const newItem = {
                ...dataToSave,
                id: newId,
                children: [],
                created_at: tool.dateFormat(new Date()), 
                updated_at: tool.dateFormat(new Date()),
            };

            if (newItem.parent_id === 0) {
                classData.value.push(newItem);
            } else {
                const parent = findNodeById(classData.value, newItem.parent_id);
                if (parent) {
                    if (!parent.children) parent.children = [];
                    parent.children.push(newItem);
                } else {
                    console.error("Parent node not found for:", newItem);
                    Message.error("添加失败：未找到父分类");
                    done(false);
                    return;
                }
            }
            classData.value = [...classData.value];
            Message.success(`分类 "${newItem.name}" 添加成功 (Mock)`);
        } else if (formMode.value === 'edit') {
            const nodeToUpdate = findNodeById(classData.value, dataToSave.id);
            if (nodeToUpdate) {
                Object.assign(nodeToUpdate, {
                    ...dataToSave,
                    updated_at: tool.dateFormat(new Date()),
                });
                classData.value = [...classData.value]; 
                Message.success(`分类 "${dataToSave.name}" 更新成功 (Mock)`);
            } else {
                Message.error("更新失败：未找到分类");
                done(false);
                return;
            }
        }
        resetFormView();
        done(true); 

    } catch (e) {
        console.error("Error submitting form:", e);
        Message.error("操作失败");
        done(false); 
    }
};
const handleDelete = (data) => {
    const deleteRecursive = (tree, idToDelete) => {
        for (let i = 0; i < tree.length; i++) {
            if (tree[i].id === idToDelete) {
                tree.splice(i, 1);
                return true; 
            }
            if (tree[i].children && tree[i].children.length > 0) {
                if (deleteRecursive(tree[i].children, idToDelete)) {
                    return true; 
                }
            }
        }
        return false;
    };

    if (deleteRecursive(classData.value, data.id)) {
        classData.value = [...classData.value]; 
        Message.success(`分类 "${data.name}" 及其子分类已删除 (Mock)`);
        resetFormView(); 
    } else {
        Message.error("删除失败：未找到分类");
    }
};
const findNodeById = (tree, id) => {
    for (const node of tree) {
        if (node.id === id) {
            return node;
        }
        if (node.children) {
            const found = findNodeById(node.children, id);
            if (found) {
                return found;
            }
        }
    }
    return null;
};

const handleEdit = (nodeData) => {
    console.log('编辑节点:', nodeData);
    if (nodeData) {
        formModel.value = { ...nodeData };
        formMode.value = 'edit';
        maFormRef.value?.clearValidate();
    }
};

</script>

<style scoped>
.tree-container {
    height: calc(100% - 80px);
    overflow: auto;
}

:deep(.arco-tree-node-selected) .arco-tree-node-title,
:deep(.arco-tree-node-selected) .arco-tree-node-title:hover {
    background-color: rgb(var(--primary-1));
    color: rgb(var(--primary-6));
}

:deep(.arco-tree-node-title-hover) {
    background-color: var(--color-fill-1);
}

:deep(.arco-tree-node-extra) {
    display: flex;
}

</style>
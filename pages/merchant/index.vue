<template>
  <div class="merchant-join-wrapper">
    <!-- 顶部导航 -->
    <div class="join-header bg-red-600 text-white w-full">
      <div class="container mx-auto px-4 max-w-screen-xl">
        <div class="flex justify-between items-center py-3">
          <div class="logo flex items-center">
            <img src="/logo.svg" alt="商家入驻" class="h-8 mr-2" />
            <span class="text-xl font-bold">商家招商</span>
            <span class="text-sm ml-2 opacity-80">诚邀全国</span>
          </div>
          <div class="nav-links">
            <div class="flex space-x-6">
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium">首页</a>
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium" @click="scrollToSection('process')">开店流程</a>
              <!-- <a class="text-white hover:text-yellow-200 cursor-pointer font-medium" @click="scrollToSection('process')">资质费用</a> -->
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium" @click="scrollToSection('benefits')">入驻优势</a>
              <!-- <a class="text-white hover:text-yellow-200 cursor-pointer font-medium">招商联系人</a> -->
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium" @click="scrollToSection('faq')">最新资讯</a>
              <!-- <a class="text-white hover:text-yellow-200 cursor-pointer font-medium">自营及其他业务</a> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主视觉区域 -->
    <div class="banner-section bg-red-600 text-white w-full rounded-bl-3xl rounded-br-3xl">
      <div class="container mx-auto px-4 py-16 relative max-w-screen-xl">
        <!-- 背景图案 -->
        <div class="absolute inset-0 opacity-20">
          <div class="w-32 h-32 rounded-full bg-red-400 absolute top-10 left-20"></div>
          <div class="w-48 h-48 rounded-full bg-red-500 absolute bottom-10 right-40"></div>
          <div class="w-24 h-24 rounded-full bg-red-300 absolute top-40 right-20"></div>
        </div>
        
        <div class="flex flex-col md:flex-row items-center relative z-10">
          <div class="md:w-1/2 mb-8 md:mb-0">
            <h1 class="text-5xl font-bold mb-4">商家·成长加速计划</h1>
            <h2 class="text-4xl font-bold mb-6">报名指南</h2>
            <p class="text-xl mb-8">点击立即报名！</p>
            <div class="flex space-x-4">
              <a-button type="primary" size="large" class="bg-white text-red-600 border-white hover:bg-gray-100 hover:text-red-700 hover:border-gray-100" @click="goToApply">立即入驻</a-button>
              <a-button ghost size="large" class="border-white text-white hover:bg-red-500 hover:border-white" @click="scrollToSection('process')">了解流程</a-button>
            </div>
            
            <div class="flex mt-10 space-x-4">
              <div class="bg-red-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                2000亿扶持·入驻免保证金
              </div>
              <div class="bg-red-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                开店便捷快速
              </div>
              <div class="bg-red-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                成长加速计划
              </div>
              <div class="bg-red-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                同类入驻优先
              </div>
            </div>
          </div>
          
          <div class="md:w-1/2 flex justify-center">
            <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm">
              <h3 class="text-center text-lg font-bold text-gray-800 mb-4">注册成为第三方（POP）卖家</h3>
              <div class="flex justify-around mb-6">
                <div class="text-center">
                  <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2">
                    <i class="iconfont icon-hl-yonghu " style="font-size: 30px;"></i>
                  </div>
                  <div class="text-sm text-gray-600">海量用户</div>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2">
                    <i class="iconfont icon-kaidian " style="font-size: 30px;"></i>
                  </div>
                  <div class="text-sm text-gray-600">便捷开店</div>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2">
                    <i class="iconfont icon-chengzhang" style="font-size: 30px;"></i>
                  </div>
                  <div class="text-sm text-gray-600">高效成长</div>
                </div>
              </div>
              <a-button type="primary" long class="bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700" @click="goToApply">0元入驻开店</a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 入驻流程与资质费用 -->
    <div id="process" class="section-container ">
      <div class="container mx-auto px-4 py-16" style="max-width: 1200px;">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-6">快速开店 成为第三方(POP)卖家</h2>
        </div>
        
        <!-- 删除了快速入驻三步走标题 -->
        
        <!-- 入驻流程内容 -->
        <div class="relative">
          <div class="flex flex-col md:flex-row step-container">
            <!-- 个体工商户卡片 -->
            <div 
              class="step-card rounded-lg shadow-md transition-all duration-500 ease-in-out relative" 
              :class="{'active': hoveredProcessCard === 'submit' || (!hoveredProcessCard && defaultProcessCard === 'submit')}"
              @mouseenter="hoveredProcessCard = 'submit'" 
              @mouseleave="hoveredProcessCard = ''"
              :style="{
                'background-image': (hoveredProcessCard === 'submit' || (!hoveredProcessCard && defaultProcessCard === 'submit')) ? 
                  'linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)), url(https://img12.360buyimg.com/imagetools/jfs/t1/151484/40/23384/446626/662b1030F2666097a/b4aabf4885557e27.png)' : 
                  'linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url(https://img13.360buyimg.com/imagetools/jfs/t1/232059/25/16153/69737/662b1030F3e9094f7/e2e492502d89c1d5.png)'
              }"
            >
              <div class="flex items-center mb-3">
                <i class="iconfont icon-gtgsh mr-2" style="font-size: 30px;"></i>
                <div class="step-title mb-0">个体工商户</div>
              </div>
              <p class="text-gray-700 mb-3">适合营业执照类型为"个体工商户"</p>
              <div class="step-content" v-show="hoveredProcessCard === 'submit' || (!hoveredProcessCard && defaultProcessCard === 'submit')">
                <div class="step-desc">
                  <div class="flex items-center">
                    <!-- <span class="text-blue-600 font-bold text-xl mr-2">01</span> -->
                    <span class="font-medium text-lg">个体工商户</span>
                    <span class="ml-auto text-xs text-blue-500">约15分钟</span>
                  </div>
                  <div class="border-l-2 border-blue-300 pl-4 mt-3 ml-3">
                    <p class="text-orange-500 mb-4">入驻成功限时享新商专属最高800元流量券扶持</p>
                    <div class="mb-4">
                      <p class="font-medium mb-1"><span class="text-blue-600">01</span> <span class="text-gray-700">提交材料</span> <span class="text-xs text-blue-500">约15分钟</span></p>
                      <p class="text-gray-700">上传营业执照、经营者身份证件等相关资质材料</p>
                    </div>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">02</span> 平台审核 <span class="text-xs text-blue-500">约1-3个工作日</span></p>
                      <p class="text-gray-700">平台进行资质审核</p>
                    </div>
                  </div>
                </div>
                
                <div class="step-action mt-4 hidden">
                  <a-button type="primary" size="small" @click="goToApply" class="bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700">立即入驻</a-button>
                </div>
              </div>
              
              <!-- 已将图片作为背景引用 -->
            </div>
            
            <!-- 企业/公司卡片 -->
            <div 
              class="step-card rounded-lg shadow-md transition-all duration-500 ease-in-out relative" 
              :class="{'active': hoveredProcessCard === 'review' || (!hoveredProcessCard && defaultProcessCard === 'review')}"
              @mouseenter="hoveredProcessCard = 'review'" 
              @mouseleave="hoveredProcessCard = ''"
              :style="{
                'background-image': (hoveredProcessCard === 'review' || (!hoveredProcessCard && defaultProcessCard === 'review')) ? 
                  'linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)), url(https://img12.360buyimg.com/imagetools/jfs/t1/151484/40/23384/446626/662b1030F2666097a/b4aabf4885557e27.png)' : 
                  'linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url(https://img13.360buyimg.com/imagetools/jfs/t1/232059/25/16153/69737/662b1030F3e9094f7/e2e492502d89c1d5.png)'
              }"
            >
              <div class="flex items-center mb-3">
                <i class="iconfont icon-qiye mr-2" style="font-size: 30px;"></i>
                <div class="step-title mb-0">企业/公司</div>
              </div>
              <p class="text-gray-700 mb-3">适合营业执照类型为"公司/企业/个人独资企业"等</p>
              <div class="step-content" v-show="hoveredProcessCard === 'review' || (!hoveredProcessCard && defaultProcessCard === 'review')">
                <div class="step-desc">
                  <div class="flex items-center">
                    <!-- <span class="text-blue-600 font-bold text-xl mr-2">02</span> -->
                    <span class="font-medium text-lg">企业/公司</span>
                    <span class="ml-auto text-xs text-blue-500">约1-3个工作日</span>
                  </div>
                  <div class="border-l-2 border-blue-300 pl-4 mt-3 ml-3">
                    <p class="text-orange-500 mb-4">入驻成功限时享新商专属最高800元流量券扶持</p>
                    <div class="mb-4">
                      <p class="font-medium mb-1"><span class="text-blue-600">01</span> <span class="text-gray-700">提交材料</span> <span class="text-xs text-blue-500">约30分钟</span></p>
                      <p class="text-gray-700">上传营业执照、法定代表人身份证件等相关资质材料</p>
                    </div>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">02</span> 平台审核 <span class="text-xs text-blue-500">约1-3个工作日</span></p>
                      <p class="text-gray-700">平台进行资质审核</p>
                    </div>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">03</span> 账户验证 <span class="text-xs text-blue-500">约1-3个工作日</span></p>
                      <p class="text-gray-700">实名认证或对公账户打款验证</p>
                    </div>
                  </div>
                </div>
                
                <div class="step-action mt-4 hidden">
                  <a-button type="primary" size="small" @click="goToApply" class="bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700">立即入驻</a-button>
                </div>
              </div>
              
              <!-- 已将图片作为背景引用 -->
            </div>
            
            <!-- 个人身份卡片 -->
            <div 
              class="step-card rounded-lg shadow-md transition-all duration-500 ease-in-out relative" 
              :class="{'active': hoveredProcessCard === 'operate' || (!hoveredProcessCard && defaultProcessCard === 'operate')}"
              @mouseenter="hoveredProcessCard = 'operate'" 
              @mouseleave="hoveredProcessCard = ''"
              :style="{
                'background-image': (hoveredProcessCard === 'operate' || (!hoveredProcessCard && defaultProcessCard === 'operate')) ? 
                  'linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)), url(https://img12.360buyimg.com/imagetools/jfs/t1/151484/40/23384/446626/662b1030F2666097a/b4aabf4885557e27.png)' : 
                  'linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url(https://img13.360buyimg.com/imagetools/jfs/t1/232059/25/16153/69737/662b1030F3e9094f7/e2e492502d89c1d5.png)'
              }"
            >
              <div class="flex items-center mb-3">
                <i class="iconfont icon-geren mr-2" style="font-size: 30px;"></i>
                <div class="step-title mb-0">个人身份</div>
              </div>
              <p class="text-gray-700 mb-3">无营业执照，个人身份开店</p>
              <div class="step-content" v-show="hoveredProcessCard === 'operate' || (!hoveredProcessCard && defaultProcessCard === 'operate')">
                <div class="step-desc">
                  <div class="flex items-center">
                    <!-- <span class="text-blue-600 font-bold text-xl mr-2">03</span> -->
                    <span class="font-medium text-lg">个人身份</span>
                    <span class="ml-auto text-xs text-blue-500">约1-3个工作日</span>
                  </div>
                  <div class="border-l-2 border-blue-300 pl-4 mt-3 ml-3">
                    
                    <p class="text-gray-700 mb-4">年累计销售额不超过10万元，部分类目不支持</p>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">01</span> 提交材料 <span class="text-xs text-blue-500">约10分钟</span></p>
                      <p class="text-gray-700">上传身份证件后人脸验证，填写店铺信息</p>
                    </div>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">02</span> 平台审核 <span class="text-xs text-blue-500">约1-3个工作日</span></p>
                      <p class="text-gray-700">平台进行资质审核，审核通过后即可成功开店</p>
                    </div>
                  </div>
                </div>
                
                <div class="step-action mt-4">
                  <a-button type="primary" size="small" @click="goToApply" class="bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700">立即入驻</a-button>
                </div>
              </div>
              
              <!-- 已将图片作为背景引用 -->
            </div>
          </div>
          
          <div class="mt-8 text-center">
            <a-button type="primary" size="large" @click="goToApply" class="bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700">立即申请入驻</a-button>
          </div>
        </div>
        

      </div>
    </div>
    <!-- 入驻优势 -->
    <div id="benefits" class="section-container bg-gray-50">
      <div class="container mx-auto px-4 py-16" style="max-width: 1200px;">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-12">入驻优势</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="(benefit, index) in benefits" :key="index" class="benefit-card">
            <div class="bg-white rounded-lg shadow-md p-6 text-center h-full flex flex-col">
              <div class="icon-wrapper mb-4 flex justify-center">
                <component :is="benefit.icon" class="text-5xl text-red-500" />
              </div>
              <h3 class="text-xl font-bold mb-2">{{ benefit.title }}</h3>
              <p class="text-gray-600 flex-grow">{{ benefit.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 入驻条件 -->
    <div id="requirements" class="section-container bg-gray-50">
      <div class="container mx-auto px-4 py-16" style="max-width: 1200px;">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-12">入驻条件</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="requirement-card">
            <div class="bg-white rounded-lg shadow-md p-6 h-full">
              <h3 class="text-xl font-bold mb-4 flex items-center">
                <icon-store class="mr-2 text-red-500" />企业入驻条件
              </h3>
              <ul class="list-disc pl-5 space-y-2">
                <li v-for="(item, index) in enterpriseRequirements" :key="index">{{ item }}</li>
              </ul>
            </div>
          </div>
          <div class="requirement-card">
            <div class="bg-white rounded-lg shadow-md p-6 h-full">
              <h3 class="text-xl font-bold mb-4 flex items-center">
                <icon-user class="mr-2 text-red-500" />个人入驻条件
              </h3>
              <ul class="list-disc pl-5 space-y-2">
                <li v-for="(item, index) in personalRequirements" :key="index">{{ item }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 常见问题 -->
    <div id="faq" class="section-container">
      <div class="container mx-auto px-4 py-16">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-12">常见问题</h2>
        </div>
        <div class="faq-container">
          <a-collapse accordion>
            <a-collapse-item v-for="(item, index) in faqs" :key="index" :header="item.question">
              <p>{{ item.answer }}</p>
            </a-collapse-item>
          </a-collapse>
        </div>
      </div>
    </div>

    <!-- 底部区域已移除 -->
  </div>
  
  <!-- 底部导航栏 -->
  <div class="footer-nav w-full bg-gray-800 text-white py-8 mt-12">
    <div class="container mx-auto px-4 max-w-screen-xl">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <h4 class="text-lg font-bold mb-4">关于我们</h4>
          <ul class="space-y-2">
            <li><a href="#" class="text-gray-300 hover:text-white">公司简介</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white">发展历程</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white">联系我们</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="text-lg font-bold mb-4">商家服务</h4>
          <ul class="space-y-2">
            <li><a href="#" class="text-gray-300 hover:text-white">入驻流程</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white">资质要求</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white">店铺运营</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="text-lg font-bold mb-4">帮助中心</h4>
          <ul class="space-y-2">
            <li><a href="#" class="text-gray-300 hover:text-white">常见问题</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white">商家学院</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white">运营指南</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="text-lg font-bold mb-4">联系方式</h4>
          <ul class="space-y-2">
            <li class="flex items-center"><i class="iconfont icon-phone mr-2"></i> ************</li>
            <li class="flex items-center"><i class="iconfont icon-mail mr-2"></i> <EMAIL></li>
            <li class="flex items-center"><i class="iconfont icon-location mr-2"></i> 北京市朝阳区大望路</li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
        <p>© 2025 商家招商平台 版权所有 | 京ICP备XXXXXXXX号</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

definePageMeta({
  layout: 'merchant-join',
});

// 入驻流程与资质费用
const activeTab = ref('process');

// 卡片悬停状态
const hoveredCard = ref('');
const defaultCard = ref('');

// 入驻流程卡片悬停状态
const hoveredProcessCard = ref('');
const defaultProcessCard = ref('');

// 组件挂载后设置默认展开的卡片
onMounted(() => {
  // 设置默认展开第一个卡片（提交材料）
  defaultCard.value = 'submit';
  defaultProcessCard.value = 'submit';
});

// 商户类型（企业、个体、个人）
const merchantType = ref('enterprise');

const router = useRouter();

// 入驻优势数据
const benefits = ref([
  {
    icon: 'icon-fire',
    title: '海量流量',
    description: '平台拥有庞大的用户基础，为您的店铺带来持续稳定的流量支持'
  },
  {
    icon: 'icon-dashboard',
    title: '专业运营',
    description: '提供专业的运营支持和培训，帮助您快速提升店铺运营能力'
  },
  {
    icon: 'icon-bulb',
    title: '营销工具',
    description: '丰富的营销工具和活动资源，助力商家实现销售增长'
  },
  {
    icon: 'icon-safe',
    title: '安全保障',
    description: '完善的交易安全体系，保障商家权益，提供安心经营环境'
  }
]);

// 入驻流程数据
const steps = ref([
  {
    title: '注册账号',
    description: '注册平台账号，填写基本信息'
  },
  {
    title: '提交资质',
    description: '上传营业执照等相关资质材料'
  },
  {
    title: '签署协议',
    description: '阅读并签署平台入驻协议'
  },
  {
    title: '等待审核',
    description: '平台将在3-5个工作日内完成审核'
  },
  {
    title: '入驻成功',
    description: '审核通过后，即可开始店铺运营'
  }
]);

// 入驻条件数据
const enterpriseRequirements = ref([
  '具有合法的企业营业执照，注册资本不低于10万元',
  '企业成立时间不少于1年，具有良好的经营记录',
  '具备相关行业资质证书和经营许可',
  '具有稳定的供货能力和售后服务能力',
  '无不良信用记录，未被列入经营异常名录'
]);

const personalRequirements = ref([
  '年满18周岁，具有完全民事行为能力',
  '持有有效的身份证件和个体工商户营业执照',
  '具备相关行业经验和专业知识',
  '具有稳定的供货渠道和商品来源',
  '无不良信用记录，未被列入失信被执行人名单'
]);

// 常见问题数据
const faqs = ref([
  {
    question: '入驻需要多长时间？',
    answer: '从提交资料到审核通过，一般需要3-5个工作日。如资料齐全且符合要求，可能会更快。'
  },
  {
    question: '入驻需要缴纳费用吗？',
    answer: '平台入驻基础服务是免费的，但可能需要缴纳一定的保证金，具体金额根据店铺类型和经营品类而定。'
  },
  {
    question: '可以同时经营多个品类吗？',
    answer: '可以，但需要提供相应品类的资质证明和经营许可。不同品类可能有不同的入驻要求和保证金标准。'
  },
  {
    question: '入驻后如何管理店铺？',
    answer: '入驻成功后，您将获得商家管理后台的访问权限，可以通过后台进行商品上传、订单管理、营销活动设置等操作。'
  },
  {
    question: '如何提高店铺的曝光率？',
    answer: '可以通过参与平台活动、优化商品信息、提升服务质量、使用平台推广工具等方式提高店铺曝光率和转化率。'
  }
]);

// 滚动到指定区域
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

// 跳转到申请页面
const goToApply = () => {
  router.push('/merchant/join/apply');
};
</script>

<style scoped>
.merchant-join-container {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  min-height: 100vh;
}

.join-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.banner-section {
  position: relative;
  overflow: hidden;
  background-image: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.section-container {
  scroll-margin-top: 80px;
}

.benefit-card:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

.process-steps {
  max-width: 800px;
  margin: 0 auto;
}

.requirement-card:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 步骤卡片容器样式 */
.step-container {
  display: flex;
  width: 100%;
  height: 400px; /* 固定高度 */
  gap: 12px;
  margin: 0 auto;
}

/* 步骤卡片样式 */
.step-card {
  flex: 1;
  position: relative;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.5s ease;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%; /* 确保高度不变 */
}

/* 卡片激活状态 */
.step-card.active {
  flex: 3;
}

/* 卡片数字标记 */
.step-number {
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

/* 卡片标题 */
.step-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

/* 卡片标题在flex布局中 */
.step-title.mb-0 {
  margin-bottom: 0;
  margin-top: 0;
}

/* 卡片内容区 */
.step-content {
  margin-top: 10px;
}

/* 卡片描述文字 */
.step-desc {
  margin-bottom: 15px;
}

.step-desc p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 5px 0;
}

/* 卡片操作区 */
.step-action {
  margin-bottom: 15px;
}

/* 卡片图片区 */
.step-image {
  max-width: 100%;
  margin-top: 10px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .step-container {
    flex-direction: column;
  }
  
  .step-card {
    margin-bottom: 15px;
  }
  
  .step-card.active {
    flex: 1;
  }
}
</style>

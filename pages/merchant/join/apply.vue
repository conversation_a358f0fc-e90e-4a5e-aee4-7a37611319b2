<template>
  <div class="merchant-apply-container">
    <!-- 顶部导航 -->
    <div class="apply-header bg-red-600 text-white">
      <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-3">
          <div class="logo flex items-center">
            <img src="/logo.svg" alt="商家入驻" class="h-8 mr-2" />
            <span class="text-xl font-bold">商家招商</span>
            <span class="text-sm ml-2 opacity-80">诚邀全国</span>
          </div>
          <div class="nav-links">
            <a-button type="primary" class="bg-white text-red-600 border-white hover:bg-gray-100 hover:text-red-700 hover:border-gray-100" @click="goToJoinPage">返回入驻首页</a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请表单区域 -->
    <div class="apply-form-section">
      <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 class="text-2xl font-bold mb-6 text-center">商家入驻申请</h1>
          
          <!-- 步骤导航 -->
          <a-steps :current="currentStep" class="mb-8">
            <a-step title="选择入驻类型" />
            <a-step title="填写基本信息" />
            <a-step title="上传资质材料" />
            <a-step title="签署协议" />
            <a-step title="提交审核" />
          </a-steps>

          <!-- 步骤内容 -->
          <div class="step-content">
            <!-- 步骤1：选择入驻类型 -->
            <div v-if="currentStep === 1" class="step-container">
              <h2 class="text-xl font-bold mb-6">请选择您的入驻类型</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div 
                  class="merchant-type-card p-6 border rounded-lg cursor-pointer transition-all duration-300"
                  :class="{'border-blue-500 bg-blue-50': merchantType === 'enterprise'}"
                  @click="selectMerchantType('enterprise')"
                >
                  <div class="flex items-center mb-4">
                    <i class="iconfont icon-qiye mr-2" style="font-size: 30px;"></i>
                    <h3 class="text-lg font-bold">企业商家</h3>
                  </div>
                  <p class="text-gray-600 mb-4">适合已注册的企业、公司、工厂等机构</p>
                  <ul class="list-disc pl-5 text-sm text-gray-500">
                    <li>需要提供营业执照等企业资质</li>
                    <li>支持多种经营类目</li>
                    <li>享受企业专属服务支持</li>
                  </ul>
                </div>
                
                <div 
                  class="merchant-type-card p-6 border rounded-lg cursor-pointer transition-all duration-300"
                  :class="{'border-blue-500 bg-blue-50': merchantType === 'personal'}"
                  @click="selectMerchantType('personal')"
                >
                  <div class="flex items-center mb-4">
                    <i class="iconfont icon-geren mr-2" style="font-size: 30px;"></i>
                    <h3 class="text-lg font-bold">个人商家</h3>
                  </div>
                  <p class="text-gray-600 mb-4">适合个体工商户、创业者等个人经营者</p>
                  <ul class="list-disc pl-5 text-sm text-gray-500">
                    <li>需要提供身份证和个体工商户执照</li>
                    <li>支持部分经营类目</li>
                    <li>简化入驻流程，快速开店</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 步骤2：填写基本信息 -->
            <div v-if="currentStep === 2" class="step-container">
              <h2 class="text-xl font-bold mb-6">填写基本信息</h2>
              <a-form :model="formData.basic" layout="vertical">
                <a-form-item field="shopName" label="店铺名称" required>
                  <a-input v-model="formData.basic.shopName" placeholder="请输入店铺名称" />
                </a-form-item>
                
                <a-form-item field="contactName" label="联系人姓名" required>
                  <a-input v-model="formData.basic.contactName" placeholder="请输入联系人姓名" />
                </a-form-item>
                
                <a-form-item field="contactPhone" label="联系电话" required>
                  <a-input v-model="formData.basic.contactPhone" placeholder="请输入联系电话" />
                </a-form-item>
                
                <a-form-item field="email" label="电子邮箱" required>
                  <a-input v-model="formData.basic.email" placeholder="请输入电子邮箱" />
                </a-form-item>
                
                <a-form-item field="province" label="所在地区" required>
                  <a-cascader
                    v-model="formData.basic.region"
                    :options="regionOptions"
                    placeholder="请选择所在地区"
                  />
                </a-form-item>
                
                <a-form-item field="address" label="详细地址" required>
                  <a-textarea v-model="formData.basic.address" placeholder="请输入详细地址" />
                </a-form-item>
                
                <a-form-item field="businessScope" label="经营范围" required>
                  <a-select
                    v-model="formData.basic.businessScope"
                    placeholder="请选择经营范围"
                    multiple
                  >
                    <a-option v-for="(item, index) in businessScopeOptions" :key="index" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-form>
            </div>

            <!-- 步骤3：上传资质材料 -->
            <div v-if="currentStep === 3" class="step-container">
              <h2 class="text-xl font-bold mb-6">上传资质材料</h2>
              
              <div v-if="merchantType === 'enterprise'" class="enterprise-materials">
                <a-form :model="formData.qualification" layout="vertical">
                  <a-form-item field="businessLicense" label="营业执照" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.businessLicense"
                      @change="handleBusinessLicenseUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传营业执照</a-button>
                      </template>
                      <template #extra>
                        <div class="text-gray-500 text-sm">
                          请上传营业执照原件扫描件或清晰照片，支持JPG、PNG格式，大小不超过5MB
                        </div>
                      </template>
                    </a-upload>
                  </a-form-item>
                  
                  <a-form-item field="businessLicenseNumber" label="营业执照注册号" required>
                    <a-input v-model="formData.qualification.businessLicenseNumber" placeholder="请输入营业执照注册号" />
                  </a-form-item>
                  
                  <a-form-item field="legalPerson" label="法定代表人" required>
                    <a-input v-model="formData.qualification.legalPerson" placeholder="请输入法定代表人姓名" />
                  </a-form-item>
                  
                  <a-form-item field="idCardFront" label="法定代表人身份证正面" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.idCardFront"
                      @change="handleIdCardFrontUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传身份证正面</a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                  
                  <a-form-item field="idCardBack" label="法定代表人身份证反面" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.idCardBack"
                      @change="handleIdCardBackUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传身份证反面</a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                  
                  <a-form-item field="bankLicense" label="开户许可证" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.bankLicense"
                      @change="handleBankLicenseUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传开户许可证</a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                  
                  <a-form-item field="otherQualifications" label="其他资质证明（可选）">
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg,application/pdf"
                      :file-list="formData.qualification.otherQualifications"
                      @change="handleOtherQualificationsUpload"
                      multiple
                    >
                      <template #upload-button>
                        <a-button>上传其他资质</a-button>
                      </template>
                      <template #extra>
                        <div class="text-gray-500 text-sm">
                          可上传商标注册证、产品资质证书等其他资质证明，支持JPG、PNG、PDF格式
                        </div>
                      </template>
                    </a-upload>
                  </a-form-item>
                </a-form>
              </div>
              
              <div v-else-if="merchantType === 'personal'" class="personal-materials">
                <a-form :model="formData.qualification" layout="vertical">
                  <a-form-item field="idCardFront" label="身份证正面" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.idCardFront"
                      @change="handleIdCardFrontUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传身份证正面</a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                  
                  <a-form-item field="idCardBack" label="身份证反面" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.idCardBack"
                      @change="handleIdCardBackUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传身份证反面</a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                  
                  <a-form-item field="personalLicense" label="个体工商户营业执照" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.personalLicense"
                      @change="handlePersonalLicenseUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传营业执照</a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                  
                  <a-form-item field="bankCard" label="银行卡照片" required>
                    <a-upload
                      action="/api/v1/master/system/integration/upload/file"
                      :headers="{
                        'Content-Type': 'multipart/form-data'
                      }"
                      accept="image/png,image/jpeg,image/jpg"
                      :file-list="formData.qualification.bankCard"
                      @change="handleBankCardUpload"
                      :limit="1"
                    >
                      <template #upload-button>
                        <a-button>上传银行卡照片</a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                </a-form>
              </div>
            </div>

            <!-- 步骤4：签署协议 -->
            <div v-if="currentStep === 4" class="step-container">
              <h2 class="text-xl font-bold mb-6">签署协议</h2>
              
              <div class="agreement-container border rounded-lg p-4 mb-6 h-80 overflow-y-auto">
                <h3 class="text-lg font-bold mb-4">《商家入驻服务协议》</h3>
                <p class="mb-2">甲方：平台运营方</p>
                <p class="mb-4">乙方：商家</p>
                
                <h4 class="font-bold mt-4 mb-2">一、协议目的</h4>
                <p class="mb-4">本协议是甲方与乙方就乙方入驻甲方电子商务平台，在平台上开设店铺从事商品销售及相关活动所达成的协议。</p>
                
                <h4 class="font-bold mt-4 mb-2">二、入驻条件</h4>
                <p class="mb-4">乙方应当具备相应的经营资质，包括但不限于营业执照、税务登记证、组织机构代码证等证照，以及相关商品的经营许可。</p>
                
                <h4 class="font-bold mt-4 mb-2">三、店铺运营</h4>
                <p class="mb-4">1. 乙方应当遵守国家法律法规和平台规则，不得销售假冒伪劣、侵权或违禁商品。</p>
                <p class="mb-4">2. 乙方应当保证商品信息的真实性和准确性，不得进行虚假宣传。</p>
                <p class="mb-4">3. 乙方应当提供良好的售前、售中和售后服务，及时处理消费者投诉。</p>
                
                <h4 class="font-bold mt-4 mb-2">四、保证金与费用</h4>
                <p class="mb-4">1. 乙方应当按照平台规定缴纳相应的保证金，作为履行协议义务的担保。</p>
                <p class="mb-4">2. 平台将根据不同类目和服务收取相应的技术服务费、佣金等费用。</p>
                
                <h4 class="font-bold mt-4 mb-2">五、违约责任</h4>
                <p class="mb-4">1. 乙方违反本协议约定，甲方有权视情节轻重采取警告、罚款、降级、关闭店铺等处罚措施。</p>
                <p class="mb-4">2. 因乙方违规经营给甲方或第三方造成损失的，乙方应当承担赔偿责任。</p>
                
                <h4 class="font-bold mt-4 mb-2">六、协议期限</h4>
                <p class="mb-4">本协议自乙方确认同意并通过平台审核之日起生效，有效期为一年。期满后，如双方无异议，自动续期一年。</p>
                
                <h4 class="font-bold mt-4 mb-2">七、其他条款</h4>
                <p class="mb-4">1. 本协议未尽事宜，双方可另行协商解决。</p>
                <p class="mb-4">2. 因本协议引起的争议，双方应友好协商解决；协商不成的，提交甲方所在地人民法院诉讼解决。</p>
              </div>
              
              <a-checkbox v-model="formData.agreement.accepted">
                我已阅读并同意《商家入驻服务协议》
              </a-checkbox>
            </div>

            <!-- 步骤5：提交审核 -->
            <div v-if="currentStep === 5" class="step-container">
              <div class="text-center py-8">
                <icon-check-circle class="text-6xl text-green-500 mb-4" />
                <h2 class="text-2xl font-bold mb-4">申请提交成功</h2>
                <p class="text-gray-600 mb-6">您的入驻申请已提交，我们将在3-5个工作日内完成审核</p>
                <p class="text-gray-600 mb-6">审核结果将通过短信和邮件通知您，请保持联系方式畅通</p>
                <div class="flex justify-center">
                  <a-button type="primary" size="large" class="bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700" @click="goToJoinPage">返回入驻首页</a-button>
                </div>
              </div>
            </div>
          </div>
          <!-- 步骤操作按钮 -->
          <div class="step-actions mt-8 flex justify-between" v-if="currentStep < 5">
            <a-button 
              v-if="currentStep > 0" 
              @click="prevStep"
            >
              上一步
            </a-button>
            <div v-else></div>
            
            <a-button 
              type="primary" 
              @click="nextStep"
              :disabled="!canProceed"
            >
              {{ currentStep === 4 ? '提交申请' : '下一步' }}
            </a-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部区域 -->
    <div class="join-footer bg-gray-900 text-gray-400">
      <div class="container mx-auto px-4 py-8">
        <div class="flex flex-wrap justify-between mb-8">
          <div class="w-full md:w-auto mb-6 md:mb-0">
            <div class="flex items-center mb-4">
              <img src="/logo.svg" alt="商家入驻" class="h-6 mr-2" />
              <span class="text-white text-lg font-medium">商家招商平台</span>
            </div>
            <p class="text-sm mb-4">人工智能商城平台</p>
            <p class="text-sm">95001服务热线</p>
          </div>
          
          <div class="flex flex-wrap">
            <div class="w-1/2 md:w-auto px-4 mb-6">
              <h3 class="text-white text-sm font-medium mb-4">关于平台</h3>
              <ul class="space-y-2 text-xs">
                <li><a href="#" class="hover:text-white">平台简介</a></li>
                <li><a href="#" class="hover:text-white">安全保障</a></li>
                <li><a href="#" class="hover:text-white">联系我们</a></li>
              </ul>
            </div>
            
            <div class="w-1/2 md:w-auto px-4 mb-6">
              <h3 class="text-white text-sm font-medium mb-4">商家服务</h3>
              <ul class="space-y-2 text-xs">
                <li><a href="#" class="hover:text-white">商家入驻</a></li>
                <li><a href="#" class="hover:text-white">商家后台</a></li>
                <li><a href="#" class="hover:text-white">运营指南</a></li>
              </ul>
            </div>
            
            <div class="w-1/2 md:w-auto px-4 mb-6">
              <h3 class="text-white text-sm font-medium mb-4">帮助中心</h3>
              <ul class="space-y-2 text-xs">
                <li><a href="#" class="hover:text-white">入驻流程</a></li>
                <li><a href="#" class="hover:text-white">常见问题</a></li>
                <li><a href="#" class="hover:text-white">规则中心</a></li>
              </ul>
            </div>
            
            <div class="w-1/2 md:w-auto px-4 mb-6">
              <h3 class="text-white text-sm font-medium mb-4">关注我们</h3>
              <div class="flex space-x-4">
                <div class="text-center">
                  <img src="/avatar.jpg" alt="微信公众号" class="w-20 h-20 rounded mb-1" />
                  <p class="text-xs">微信公众号</p>
                </div>
                <div class="text-center">
                  <img src="/avatar.jpg" alt="官方小程序" class="w-20 h-20 rounded mb-1" />
                  <p class="text-xs">官方小程序</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="border-t border-gray-800 pt-6 text-xs">
          <div class="flex flex-wrap justify-center mb-4">
            <a href="#" class="mx-2 hover:text-white">关于平台</a>
            <a href="#" class="mx-2 hover:text-white">平台协议</a>
            <a href="#" class="mx-2 hover:text-white">隐私政策</a>
            <a href="#" class="mx-2 hover:text-white">平台规则</a>
            <a href="#" class="mx-2 hover:text-white">广告服务</a>
            <a href="#" class="mx-2 hover:text-white">联系我们</a>
          </div>
          
          <div class="text-center space-y-2">
            <p>商城平台营业执照 91000002000001号</p>
            <p>网络文化经营许可证 粤网文[2022]10001-001号</p>
            <p>互联网药品信息服务资格证书 (粤)-非经营性-2022-0001</p>
            <p>网络文化经营许可证 粤网文[2022]10001-002号</p>
            <p>© 2025 商家入驻平台 版权所有</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';

definePageMeta({
  layout: 'merchant-join',
});

const router = useRouter();
const currentStep = ref(1);
const merchantType = ref('');

// 表单数据
const formData = ref({
  basic: {
    shopName: '',
    contactName: '',
    contactPhone: '',
    email: '',
    region: [],
    address: '',
    businessScope: []
  },
  qualification: {
    businessLicense: [],
    businessLicenseNumber: '',
    legalPerson: '',
    idCardFront: [],
    idCardBack: [],
    bankLicense: [],
    otherQualifications: [],
    personalLicense: [],
    bankCard: []
  },
  agreement: {
    accepted: false
  }
});

// 区域选项（示例数据）
const regionOptions = ref([
  {
    value: '110000',
    label: '北京市',
    children: [
      {
        value: '110100',
        label: '北京市',
        children: [
          { value: '110101', label: '东城区' },
          { value: '110102', label: '西城区' }
        ]
      }
    ]
  },
  {
    value: '120000',
    label: '天津市',
    children: [
      {
        value: '120100',
        label: '天津市',
        children: [
          { value: '120101', label: '和平区' },
          { value: '120102', label: '河东区' }
        ]
      }
    ]
  }
  // 其他省份数据...
]);

// 经营范围选项（示例数据）
const businessScopeOptions = ref([
  { value: '1', label: '服装鞋帽' },
  { value: '2', label: '家用电器' },
  { value: '3', label: '数码产品' },
  { value: '4', label: '家居家装' },
  { value: '5', label: '美妆个护' },
  { value: '6', label: '母婴用品' },
  { value: '7', label: '食品生鲜' },
  { value: '8', label: '图书音像' },
  { value: '9', label: '运动户外' },
  { value: '10', label: '汽车用品' }
]);

// 选择商家类型
const selectMerchantType = (type) => {
  merchantType.value = type;
};

// 上传处理函数
const handleBusinessLicenseUpload = (fileList) => {
  formData.value.qualification.businessLicense = fileList;
};

const handleIdCardFrontUpload = (fileList) => {
  formData.value.qualification.idCardFront = fileList;
};

const handleIdCardBackUpload = (fileList) => {
  formData.value.qualification.idCardBack = fileList;
};

const handleBankLicenseUpload = (fileList) => {
  formData.value.qualification.bankLicense = fileList;
};

const handleOtherQualificationsUpload = (fileList) => {
  formData.value.qualification.otherQualifications = fileList;
};

const handlePersonalLicenseUpload = (fileList) => {
  formData.value.qualification.personalLicense = fileList;
};

const handleBankCardUpload = (fileList) => {
  formData.value.qualification.bankCard = fileList;
};

// 判断是否可以进行下一步
const canProceed = computed(() => {
  if (currentStep.value === 1) {
    return merchantType.value !== '';
  } else if (currentStep.value === 2) {
    // 基本信息验证
    const basic = formData.value.basic;
    return basic.shopName && basic.contactName && basic.contactPhone && 
           basic.email && basic.region.length > 0 && basic.address && 
           basic.businessScope.length > 0;
  } else if (currentStep.value === 3) {
    // 资质材料验证
    const qualification = formData.value.qualification;
    if (merchantType.value === 'enterprise') {
      return qualification.businessLicense.length > 0 && 
             qualification.businessLicenseNumber && 
             qualification.legalPerson && 
             qualification.idCardFront.length > 0 && 
             qualification.idCardBack.length > 0 && 
             qualification.bankLicense.length > 0;
    } else if (merchantType.value === 'personal') {
      return qualification.idCardFront.length > 0 && 
             qualification.idCardBack.length > 0 && 
             qualification.personalLicense.length > 0 && 
             qualification.bankCard.length > 0;
    }
    return false;
  } else if (currentStep.value === 4) {
    // 协议验证
    return formData.value.agreement.accepted;
  }
  return true;
});

// 下一步
const nextStep = () => {
  if (!canProceed.value) {
    Message.error('请完成当前步骤的必填项');
    return;
  }
  
  if (currentStep.value === 4) {
    // 提交申请
    submitApplication();
  }
  
  currentStep.value++;
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

// 提交申请
const submitApplication = () => {
  // 这里可以添加实际的提交逻辑，调用API等
  console.log('提交申请数据:', {
    merchantType: merchantType.value,
    formData: formData.value
  });
  
  // 模拟提交成功
  Message.success('申请提交成功');
};

// 返回入驻首页
const goToJoinPage = () => {
  router.push('/merchant/join');
};
</script>

<style scoped>
.merchant-apply-container {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f7fa;
  min-height: 100vh;
  width: 100%;
}

.apply-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.step-container {
  min-height: 300px;
}

.merchant-type-card {
  transition: all 0.3s ease;
}

.merchant-type-card:hover {
  border-color: #2b85e4;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>

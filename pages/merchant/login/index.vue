<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<script setup>
import { reactive, ref } from "vue";
import { useUserStore } from "~/store/index.js";
import { useTagStore } from "~/store/index.js";
import commonApi from "~/api/common.js";
import { Message } from '@arco-design/web-vue'
definePageMeta({
  layout: false,
  name: "merchantlogin",
});

// request({
//   url: "system/getBingBackgroundImage",
//   timeout: 10000,
//   method: "get",
// }).then((res) => {
//   document.getElementById(
//     "background"
//   ).style.backgroundImage = `url(${res.data.url})`;
// });

const router = useRouter();
const route = useRoute();
const Verify = ref(null);

const loading = ref(false);

// 移除环境判断，统一使用开发环境逻辑
// let isDevelop = import.meta.env.VITE_APP_ENV === "development";

// 直接使用开发环境的数据
const form = reactive({ 
  username: "admin", 
  password: "admin123", 
  code: "",
  captcha: "",
  captchaId: ""
});

// 验证码相关
const captchaImg = ref('');

// 获取验证码
const getCaptcha = async () => {
  try {
    const res = await commonApi.getCaptcha();
    // 检查响应结构
    if (res && res.code === 200) {
      // 如果响应中有 data 属性
      if (res.data ) {
        // 兼容SVG字符串，转为data URI防止URI malformed
        captchaImg.value = 'data:image/svg+xml;utf8,' + encodeURIComponent(res.data.image);
        form.captchaId = res.data.id;
      } else {
        console.error('验证码数据结构不符合预期:', res.data);
      }
    } else {
      console.error('获取验证码失败:', res);
    }
  } catch (error) {
    console.error('获取验证码出错:', error);
  }
};

const userStore = useUserStore();

const redirect = route.query.redirect;

// 页面加载时自动获取验证码
onMounted(() => {
  getCaptcha();
});

const handleSubmit = async ({ values, errors }) => {
  if (loading.value) {
    return;
  }
  loading.value = true;
  
  try {
    // 验证码校验
    if (!form.captcha) {
      Message.error('请输入验证码');
      loading.value = false;
      return;
    }
    
    // 使用RSA加密登录
    if (!errors) {
      const result = await userStore.login(form);
      if (!result) {
        loading.value = false;
        // 登录失败时刷新验证码
        getCaptcha();
        return;
      }
      useTagStore().clearTags();

      // 确保重定向到正确的路径
      try {
        if (redirect && redirect !== "/") {
          await router.push(redirect);
        } else {
          await router.push("/master/dashboard");
        }
      } catch (error) {
        console.error("导航错误:", error);
        // 如果导航失败，尝试使用navigateTo
        await navigateTo("/master/dashboard");
      }
    }
  } catch (error) {
    console.error("登录处理错误:", error);
    window.$message.error("登录失败，请稍后重试");
    // 登录失败时刷新验证码
    getCaptcha();
  } finally {
    loading.value = false;
  }
};
</script>
<template>
  <div id="background" class="fixed"></div>
  <div class="bg-backdrop-layout"></div>
  <div class="login-container">
    <div
      class="login-width md:w-10/12 w-11/12 mx-auto flex justify-between h-full items-center"
    >
      <div
        class="w-6/12 mx-auto left-panel rounded-l pl-5 pr-5 hidden md:block"
      >
        <div class="logo">
          <img  src="/assets/logo.svg"  width="30" /><span>{{ $title }}</span>
        </div>
        <div class="slogan flex justify-end">
          <span>---- {{ $t("sys.login.slogan") }}</span>
        </div>
      </div>

      <div class="md:w-6/12 w-11/12 md:rounded-r mx-auto pl-5 pr-5 pb-10">
        <h2 class="mt-10 text-3xl pb-0 mb-10">{{ $t("sys.login.title") }}</h2>
        <a-form :model="form" @submit="handleSubmit">
          <a-form-item field="username" :hide-label="true">
            <a-input
              v-model="form.username"
              class="w-full"
              size="large"
              allow-clear
            >
              <template #prefix><icon-user /></template>
            </a-input>
          </a-form-item>

          <a-form-item field="password" :hide-label="true">
            <a-input-password v-model="form.password" size="large" allow-clear>
              <template #prefix><icon-lock /></template>
            </a-input-password>
          </a-form-item>

          <!-- 验证码输入框 -->
          <a-form-item field="captcha" :hide-label="true">
            <div class="flex items-center">
              <a-input v-model="form.captcha" size="large" allow-clear placeholder="请输入验证码" class="mr-2">
                <template #prefix><icon-safe /></template>
              </a-input>
              <div class="captcha-img cursor-pointer" @click="getCaptcha" v-if="captchaImg">
                <img :src="captchaImg" alt="验证码" class="captchaImg" />
              </div>
            </div>
          </a-form-item>

          <a-form-item :hide-label="true" class="mt-5">
            <a-button
              html-type="submit"
              type="primary"
              long
              size="large"
              :loading="loading"
            >
              登录商家端
            </a-button>
          </a-form-item>

          <a-divider orientation="center">{{
            $t("sys.login.otherLoginType")
          }}</a-divider>
          <div class="flex w-3/4 pt-2 mx-auto items-stretch justify-around">
            <a-avatar class="other-login wechat"><icon-wechat /></a-avatar>
            <a-avatar class="other-login alipay"
              ><icon-alipay-circle
            /></a-avatar>
            <a-avatar class="other-login qq"><icon-qq /></a-avatar>
            <a-avatar class="other-login weibo"><icon-weibo /></a-avatar>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.captchaImg {
  width: 215px;
  height: 35px;
}
#background {
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
}
.bg-backdrop-layout {
  top: 0;
  left: 0;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 2;
  backdrop-filter: blur(25px);
}
.login-container {
  width: 100%;
  height: 100%;
  position: absolute;
  background-size: cover;
  z-index: 3;
  .login-width {
    max-width: 950px;
    background: #fff;
    padding: 10px;
    height: 500px;
    position: relative;
    top: 50%;
    margin-top: -255px;
    border-radius: 10px;
  }

  .left-panel {
    height: 491px;
    background-image: url(/assets/login_picture.svg);
    background-repeat: no-repeat;
    background-position: center 60px;
    background-size: contain;
  }

  .logo {
    display: flex;
    margin-top: 20px;
    color: #333;
    span {
      font-size: 28px;
      margin-left: 15px;
      color: rgb(var(--primary-6));
    }
  }
  .slogan {
    font-size: 16px;
    line-height: 50px;
  }

  :deep(.arco-input-append) {
    padding: 0 !important;
  }

  .other-login {
    cursor: pointer;
  }

  .qq:hover,
  .alipay:hover {
    background: #165dff;
  }
  .wechat:hover {
    background: #0f9c02;
  }

  .weibo:hover {
    background: #f3ce2b;
  }
}
</style>

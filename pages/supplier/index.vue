<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 p-4">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden">
      <div class="p-6 sm:p-8">
        <div class="flex justify-center mb-6">
          <icon-tool class="text-[64px] text-amber-500" />
        </div>
        <h1 class="text-2xl font-bold text-center text-gray-900 mb-2">
          {{ title }}
        </h1>
        <p class="text-gray-600 text-center mb-6">
          {{ message }}
        </p>
        <div class="flex justify-center">
          <a-button 
            type="primary" 
            @click="goHome" 
            class="bg-amber-500 border-amber-500 hover:bg-amber-600 hover:border-amber-600"
          >
            <template #icon>
              <icon-home />
            </template>
            {{ buttonText }}
          </a-button>
        </div>
      </div>
      <div class="bg-amber-50 px-6 py-4 border-t border-amber-100">
        <div class="flex justify-center items-center gap-2">
          <icon-calendar class="text-amber-700" />
          <p class="text-amber-700 text-sm text-center">
            {{ estimatedTime }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { 
  IconTool, 
  IconHome, 
  IconCalendar 
} from '@arco-design/web-vue/es/icon';
import { Button as AButton } from '@arco-design/web-vue';

definePageMeta({
  layout: 'empty'
});

// Props with default values
const props = defineProps({
  title: {
    type: String,
    default: '页面正在开发中'
  },
  message: {
    type: String,
    default: '我们正在努力开发这个页面，请稍后再来查看。'
  },
  buttonText: {
    type: String,
    default: '返回首页'
  },
  estimatedTime: {
    type: String,
    default: '预计完成时间: 两周内'
  }
});

// Method to navigate back to home page
const goHome = () => {
  window.location.href = '/';
};

</script>

<style scoped>
.provider-container {
  padding: 20px;
}
/* You may need to adjust icon sizing if Arco Design has different default sizes */
:deep(.arco-icon) {
  display: inline-flex;
}
</style>

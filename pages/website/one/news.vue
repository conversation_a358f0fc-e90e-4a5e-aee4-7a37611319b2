<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 新闻页面内容 -->
    <div class="joinus-banner">
      <h1 class="joinus-banner-title">新闻动态</h1>
    </div>
    
    <!-- 选项卡导航 -->
    <div class="joinus-tabs">
      <div class="container">
        <div class="tabs-scroll-container">
          <ul class="nav-list">
            <li class="nav-item-tab" v-for="(item,index) in newsCategoryList" :key="index" :class="{active: currentCategoryId === item.id}">
              <a class="nav-link" href="#" @click.prevent="changeCategory(item.id)">{{ item.name }}</a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 新闻列表 -->
    <div class="news-container">
      <div class="news-grid">
        <!-- 新闻项目 -->
        <div
          class="news-item"
          v-for="(item, index) in newsList"
          :key="index"
          :class="{ 'first-item': index === 0 }"
        >
          <a :href="`/website/one/newsdetail/${item.id}`" class="news-link">
            <div class="news-image">
              <img :src="item.image" :alt="item.title" />
            </div>
            <div class="news-content">
              <h3>{{ item.title }}</h3>
              <p >{{item.summary}}</p>
              <div class="news-date" v-time="item.created_at"></div>
            </div>
          </a>
        </div>
      </div>

      <!-- 分页控件 -->
      <div class="pagination-container">
        <a-pagination
          :current="currentPage"
          :total="totalItems"
          :page-size="pageSize"
          @change="changePage"
          show-less-items
        />
      </div>
    </div>
    
    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
  
<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted, computed } from "vue";
import NavBar from "@/components/website/one/NavBar.vue";
import Footer from "@/components/website/one/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false
});

// 基础URL
const baseUrl = "/website/one/assets";

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = path => {
  return getAssetUrl("images", path);
};



// 计算总页数
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));
const hasMorePages = computed(() => totalPages.value > 5);

// 切换页面
const changePage = page => {
  getNewsList(page);
};

// 获取新闻列表
const newsList = ref([]);
const totalItems = ref(0);
const currentPage = ref(1);
const pageSize = ref(5);

//获取新闻分类
const newsCategoryList = ref([]);
const currentCategoryId = ref(0); // 当前选中的分类ID

const getNewsCategoryList = () => {
    officialApi.newsManagement.getList().then(res => {
        if(res.code == 200){
            newsCategoryList.value = res.data.items;
            // 默认选中第一个分类
            if(newsCategoryList.value.length > 0) {
                currentCategoryId.value = newsCategoryList.value[0].id;
                getNewsList(1);
            }
        }
    })
}

// 切换新闻分类
const changeCategory = (categoryId) => {
    currentCategoryId.value = categoryId;
    currentPage.value = 1; // 切换分类时重置页码
    getNewsList(1);
}

//获取新闻列表
const getNewsList = (page = 1) => {
    // 携带分类ID请求新闻列表
    officialApi.newsManagement.list.getList({
        page: page, 
        pageSize: pageSize.value,
        news_category_id: currentCategoryId.value // 添加分类ID参数
    }).then(res => {
        if(res.code == 200){
            newsList.value = res.data.items;
            totalItems.value = res.data.pageInfo.total;
            currentPage.value = res.data.pageInfo.current;
        }
    })
}

// 页面加载完成后执行的逻辑
onMounted(() => {
  getNewsList(1)
  getNewsCategoryList()
  // 加载CSS  
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;
    
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  loadCSS(`${baseUrl}/css/news.css`, "news-css");
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      if (document.getElementById(id)) {
        resolve();
        return;
      }
      
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        "bootstrap-script"
      )
        .then(() => {
          console.log("页面功能初始化完成");
        })
        .catch(error => {
          console.error("Bootstrap 加载失败:", error);
        });
    } catch (error) {
      console.error("脚本加载错误:", error);
    }
  }, 100);
});
</script>
  
  <style scoped>
/* 页面样式会从外部CSS文件加载 */
.website-container {
  width: 100%;
  overflow-x: hidden;
}

/* 确保第一个新闻项目占据两列 */
.first-item {
  grid-column: span 2;
  width: 100% !important;
}

/* 导航选项卡样式 */
.tabs-scroll-container {
  width: 100%;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  -webkit-overflow-scrolling: touch;
}

.tabs-scroll-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  /* border-bottom: 1px solid #e1e5eb; */
  white-space: nowrap;
  width: max-content;
  min-width: 100%;
}

.nav-item-tab {
  margin-right: 20px;
  display: inline-block;
}

.nav-item-tab a {
  display: block;
  padding: 1rem 2rem;
  color: #333;
  text-decoration: none;
  border-bottom: 3px solid transparent;
}

.nav-item-tab.active a {
  border-bottom: 3px solid #0052d9;
  color: #0052d9;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .first-item {
    grid-column: span 1;
  }
  
  .nav-item-tab a {
    padding: 0.7rem 1.2rem;
    font-size: 14px;
  }
  
  .joinus-banner-title {
    margin-left: 30px;
    font-size: 26px;
  }
  
  .tabs-scroll-container {
    padding-bottom: 5px;
  }
  
  .joinus-tabs {
    position: relative;
  }
}
</style>
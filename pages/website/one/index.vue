<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { onMounted, onBeforeUnmount, ref } from 'vue';
import NavBar from '@/components/website/one/NavBar.vue';
import Footer from '@/components/website/one/Footer.vue';

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = '/website/one/assets';

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = (path) => {
  return getAssetUrl('images', path);
};

// 定义获取CSS URL的函数
const getCssUrl = (path) => {
  return getAssetUrl('css', path);
};

// 定义获取JS URL的函数
const getJsUrl = (path) => {
  return getAssetUrl('js', path);
};
definePageMeta({
    layout: false,
    path: '/website/one/index'
})
// 使用useHead钩子管理头部元数据
useHead({
  title: '首页',
  meta: [
    { charset: 'UTF-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' }
  ],
  link: [
    { rel: 'stylesheet', href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css` },
    // { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});

const jumpToPlans = (id) => {
    navigateTo(`/website/one/plans/${id}`);
}

// 获取首页新闻
const newsList = ref([]);
const newlist = () =>{
    officialApi.newsManagement.list.getList({page:1,pageSize:5}).then(res => {
        if(res.code == 200){
            newsList.value = res.data.items;
        }
    })
}

// 获取供应链数据
const basisData = ref({});
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
        if(res.data.banner_url){
            basisData.value.banner_url = res.data.banner_url.split(',');
        }
        console.log(basisData.value,'xxxxx');
    })
}

// 获取案例列表
const caseList = ref([]);
const getCaseList = () =>{
    officialApi.caseManagement.faceList({page:1,pageSize:100}).then(res => {
        if(res.code == 200){
            caseList.value = res.data.items.filter(item => item.status == 1).slice(0, 10);
        }
    })
}

const enterpriseInforList= ref([]);
const getEnterpriseInformations = () =>{
    officialApi.getEnterpriseInformations({page:1,pageSize:3,cate_name:'公司简介'}).then(res => {
        if(res.code == 200){
            enterpriseInforList.value = res.data.items;
        }
    })
}


// 轮播图相关变量
const carouselInstance = ref(null);
const isVideoPlaying = ref(false);
const activeVideoElement = ref(null);
const videoItems = ref([]);

// 视频播放结束事件处理函数
const videoEnded = (event) => {
  console.log('视频播放结束');
  isVideoPlaying.value = false;
  activeVideoElement.value = null;
  
  // 视频播放完成后，允许轮播图自动切换到下一个
  setTimeout(() => {
    if (carouselInstance.value) {
      carouselInstance.value.next();
    }
  }, 100);
};

// 视频播放开始时暂停轮播
const videoPlay = (event) => {
  console.log('视频开始播放');
  isVideoPlaying.value = true;
  activeVideoElement.value = event.target;
  
  // 确保轮播图暂停
  if (carouselInstance.value) {
    carouselInstance.value.pause();
  }
};

// 页面加载完成后执行的逻辑
onMounted(() => {
    newlist()
    getData()
    getCaseList()
    getEnterpriseInformations()
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, 'style-css');
  loadCSS(`${baseUrl}/css/index.css`, 'index-css');
  loadCSS(`${baseUrl}/css/home-news.css`, 'home-news-css');

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = (e) => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(`${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`, 'bootstrap-script')
        .then(() => {
          // 初始化轮播图
          const mainCarousel = document.getElementById('mainCarousel');
          if (mainCarousel && window.bootstrap) {
            // 初始化轮播图，确保循环播放
            const carousel = new window.bootstrap.Carousel(mainCarousel, {
              interval: 5000, // 设置为5秒切换一次
              wrap: true, // 确保循环播放
              ride: 'carousel', // 设置为自动轮播
              pause: false, // 禁用默认的暂停行为
              keyboard: true // 启用键盘控制
            });
            
            // 保存轮播实例引用
            carouselInstance.value = carousel;
            
            // 检查当前激活的轮播项是否包含视频
            const activeItem = mainCarousel.querySelector('.carousel-item.active');
            const activeVideo = activeItem ? activeItem.querySelector('video') : null;
            
            if (activeVideo) {
              // 如果当前项是视频，保持暂停状态
              isVideoPlaying.value = true;
              activeVideoElement.value = activeVideo;
            } else {
              // 如果当前项不是视频，开始自动轮播
              carousel.cycle();
            }
            
            // 收集所有视频元素引用
            videoItems.value = Array.from(mainCarousel.querySelectorAll('video'));
            
            // 监听轮播图切换后事件，处理视频播放
            mainCarousel.addEventListener('slid.bs.carousel', (event) => {
              // 获取当前激活的轮播项
              const activeItem = mainCarousel.querySelector('.carousel-item.active');
              // 获取当前激活项中的视频元素
              const activeVideo = activeItem ? activeItem.querySelector('video') : null;
              
              // 暂停所有视频
              videoItems.value.forEach(video => {
                if (video !== activeVideo) {
                  video.pause();
                  video.currentTime = 0; // 重置视频时间
                }
              });
              
              // 如果当前项包含视频，尝试播放它
              if (activeVideo) {
                console.log('当前轮播项包含视频，尝试播放');
                activeVideo.currentTime = 0; // 重置视频时间
                activeVideoElement.value = activeVideo;
                
                // 尝试播放视频
                const playPromise = activeVideo.play();
                
                if (playPromise !== undefined) {
                  playPromise.then(() => {
                    console.log('视频开始播放');
                    isVideoPlaying.value = true;
                    // 暂停轮播自动切换
                    if (carouselInstance.value) {
                      carouselInstance.value.pause();
                    }
                  }).catch(error => {
                    console.error('视频播放失败:', error);
                    isVideoPlaying.value = false;
                    // 如果视频无法播放，继续轮播
                    if (carouselInstance.value) {
                      carouselInstance.value.cycle();
                    }
                  });
                }
              } else {
                // 如果当前项不包含视频，继续轮播
                isVideoPlaying.value = false;
                activeVideoElement.value = null;
                if (carouselInstance.value) {
                  carouselInstance.value.cycle();
                }
              }
            });
            
            // 监听轮播图滑动到最后一项后的事件
            mainCarousel.addEventListener('slid.bs.carousel', (event) => {
              // 获取当前激活的项目索引
              const activeIndex = Array.from(mainCarousel.querySelectorAll('.carousel-item')).findIndex(item => item.classList.contains('active'));
              
              // 获取轮播项总数
              const totalItems = mainCarousel.querySelectorAll('.carousel-item').length;
              
              console.log(`当前轮播项: ${activeIndex + 1}/${totalItems}`);
              
              // 如果是最后一项，确保下一次会回到第一项
              if (activeIndex === totalItems - 1) {
                console.log('已到达最后一项，准备从头开始');
                // 强制在最后一项结束后跳转到第一项
                setTimeout(() => {
                  carousel.to(0); // 跳转到第一个轮播项
                  console.log('已重置到第一项');
                }, carousel._config.interval); // 使用当前设置的间隔时间
              }
            });
          }
          
          // 添加滚动特效
          window.addEventListener('scroll', function() {
            const newsTitle = document.querySelector('.home-news-title');
            if (newsTitle) {
              const rect = newsTitle.getBoundingClientRect();
              if (rect.top < window.innerHeight * 0.8) {
                newsTitle.classList.add('animate__fadeInDown');
              }
            }
          });
          
          console.log('页面功能初始化完成');
        })
        .catch(error => {
          console.error('Bootstrap 加载失败:', error);
        });
    } catch (error) {
      console.error('初始化页面功能失败:', error);
    }
  }, 500);


});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 清除轮播图事件监听器
  const mainCarousel = document.getElementById('mainCarousel');
  if (mainCarousel) {
    mainCarousel.removeEventListener('slide.bs.carousel', () => {});
    mainCarousel.removeEventListener('slid.bs.carousel', () => {});
  }
  
  // 清除所有脚本
  ['bootstrap-script', 'components-script', 'vision-script', 'index-script'].forEach(id => {
    const script = document.getElementById(id);
    if (script) {
      document.body.removeChild(script);
    }
  })
  
  // 清除所有样式
  ['style-css', 'index-css', 'home-news-css'].forEach(id => {
    const link = document.getElementById(id);
    if (link) {
      document.head.removeChild(link);
    }
  });
});
</script>

<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl"  />

    <div id="mainCarousel" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-inner">
            <div class="carousel-item" :class="{active: index === 0}" v-for="(item,index) in basisData.banner_url" :key="index">
                <img :src="item" class="d-block w-100" alt="轮播图1" v-if="item.indexOf('mp4') == -1"/>
                <video class="d-block w-100" autoplay muted playsinline @play="videoPlay" @ended="videoEnded" v-else>
                    <source :src="item" type="video/mp4">
                    您的浏览器不支持视频标签
                </video>
                <div class="carousel-overlay"></div>
                <!-- <div class="carousel-caption">
                    <h2 class="main-title">引领商业向善新生态，谱写产业互联新篇章</h2>
                    <p class="sub-title">拥抱变化、勇于创新、砥砺同行、多方共益</p>
                </div> -->
            </div>
            <!-- <div class="carousel-item">
                <video class="d-block w-100" autoplay muted playsinline>
                    <source :src="getImageUrl('adbg2.mp4')" type="video/mp4">
                </video>
                <div class="carousel-overlay"></div>
                <div class="carousel-caption">
                    <h2 class="main-title">破界·重塑·智启新程——2025战略跃迁盛典</h2>
                    <p class="sub-title">突破边界·聚能进化·智创未来</p>
                </div>
            </div> -->
        </div>
        <!-- 添加左右切换箭头 -->
        <button class="carousel-control-prev" type="button" data-bs-target="#mainCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">上一张</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#mainCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">下一张</span>
        </button>
    </div>

    <div class="home-page-container" style="padding-top: 0;margin: 0px;">
        <div class="news-box">
            <div class="news">
                <div class="news-container">
                    <div class="home-news-title animate__animated animate__fadeInDown" style="padding-top: 0; height: 3rem; margin-bottom: 0;">新闻动态</div>
                    <div class="news-grid">
                        <!-- 新闻项目1 -->
                        <div class="news-item" v-for="(item,index) in newsList" :key="index">
                            <a :href="`/website/one/newsdetail/${item.id}`" class="news-link">
                                <div class="news-image">
                                    <img :src="item.image" alt="新闻图片" />
                                </div>
                                <div class="news-content">
                                    <h3>{{item.title}}</h3>
                                    <p >{{item.summary}}</p>
                                    <div class="news-date" v-time="item.created_at"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 供应链模块 -->
    <div class="supply-container">
        <div class="supply">  
            <div class="supply-title">行业特性精准适配的供应链解决方案</div>
            <div class="supply-grid-container" :style="{ backgroundImage: 'url(' + getImageUrl('supply_bg.png') + ')' }">
                <div class="supply-grid">
                    <div class="supply-item" @click="jumpToPlans(item.id)" v-for="(item,index) in caseList" :key="index">
                        <div class="default-content">
                            <a-image :src="item.icon" :preview="false"/>
                            <span class="item-title">{{item.title}}</span>
                        </div>
                        <div class="hover-content">
                            <div style="margin-bottom: 10px;">
                                <a-image :src="item.icon" :preview="false" />
                                <span class="item-title">{{item.title}}</span>
                            </div>
                            <div >{{item.description}}</div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 
    <!-- 公司信息模块 -->
    <div class="company-info-container">
        <div class="company-info-title">公司信息</div>
        <div class="company-info-cards">
            <div class="company-info-card" v-for="(item,index) in enterpriseInforList" :key="index">
                <div class="card-image">
                    <img :src="item.cover_image" />
                    <div class="card-title">{{item.title}}</div>
                    <div class="card-overlay">
                        <h3>{{item.title}}</h3>
                        <p>{{item.description}}</p>
                        <a href="/website/one/profile" class="btn btn-light" :style="{zIndex: 100}">了解更多</a>
                    </div>
                </div>
            </div>
         
        </div>
    </div>

    <!-- 企业愿景模块 -->
    <div class="vision-container">
        <div class="container h-100">
            <div class="row h-100">
                <div class="col-lg-6 vision-left">
                    <h2 class="vision-title">企业愿景</h2>
                    <p class="vision-content">{{basisData.vision}}</p>
                    <div class="vision-separator"></div>
                    
                    <h2 class="vision-title">企业使命</h2>
                    <p class="vision-content">{{basisData.mission}}</p>
                    <div class="vision-separator"></div>
                    
                    <h2 class="vision-title">核心价值观</h2>
                    <p class="vision-content">{{basisData.core_values}}</p>
                    <div class="vision-separator"></div>
                </div>
                <div class="col-lg-6 h-100">
                    <div class="vision-image-container">
                        <a-image :src="getImageUrl('vision1.png')" alt="企业愿景" class="vision-image" :preview="false"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
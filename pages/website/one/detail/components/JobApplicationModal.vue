<template>
  <a-modal
    :visible="visible"
    title="申请职位"
    :mask-closable="false"
    :width="modalWidth"
    :popup-container="popupContainer"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
    class="job-application-modal"
  >
    <a-form 
      :model="formState" 
      :rules="rules" 
      ref="formRef"
      :layout="screenWidth < 768 ? 'vertical' : 'horizontal'"
      :label-col-props="{ span: screenWidth < 768 ? 24 : 6 }"
      :wrapper-col-props="{ span: screenWidth < 768 ? 24 : 16 }"
    >
      <a-form-item field="submitter_name" label="姓名" required>
        <a-input v-model="formState.submitter_name" placeholder="请输入您的姓名" />
      </a-form-item>
      
      <a-form-item field="email" label="邮箱" >
        <a-input v-model="formState.email" placeholder="请输入您的邮箱" />
      </a-form-item>
      
      <a-form-item field="phone" label="电话" required>
        <a-input v-model="formState.phone" placeholder="请输入您的电话号码" />
      </a-form-item>

      <!-- <a-form-item field="message_location" label="职位名称" >
        <a-input v-model="formState.message_location" placeholder="请输入职位名称" />
      </a-form-item> -->
      
      <a-form-item field="message_details" label="求职信" >
        <a-textarea 
          v-model="formState.message_details" 
          placeholder="请输入您的求职信..." 
          :auto-size="{ minRows: 2, maxRows: 6 }" 
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitting">提交申请</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import recruitmentApi from '@/api/master/officialWebsiteModule';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  jobTitle: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'submit', 'cancel']);

const formRef = ref(null);
const submitting = ref(false);
const screenWidth = ref(0);
// 使用 ma-upload 组件，不需要维护文件列表
const popupContainer = ref(null);

// 表单数据
const formState = reactive({
  submitter_name: '',
  email: '',
  phone: '',
  message_location: '',
  message_details: ''
});

// 表单验证规则
const rules = {
  submitter_name: [
    { required: true, message: '请输入姓名' },
    { maxLength: 50, message: '姓名不能超过50个字符' }
  ],
  phone: [
    { required: true, message: '请输入电话' },
    { match: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
  ],

};

// 响应式布局处理
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};

// 监听窗口大小变化
onMounted(() => {
  // 安全地初始化客户端专有变量
  popupContainer.value = document.body;
  screenWidth.value = window.innerWidth;
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', updateScreenWidth);
  
  console.log('弹窗组件挂载，visible值为：', props.visible);
});

onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('resize', updateScreenWidth);
});


// 动态计算弹窗宽度
const modalWidth = computed(() => {
  // 移动端时占屏幕80%
  if (screenWidth.value < 768) {
    return `${window.innerWidth * 0.8}px`;
  }
  // PC端固定宽度
  return 600;
});

// ma-upload 组件内部已处理上传逻辑，此处不需要自定义上传处理

// 处理取消
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单为初始值
  Object.keys(formState).forEach(key => {
    formState[key] = '';
  });
};

// 手机号码校验函数
const validatePhone = phone => {
  // 中国手机号码校验正则表达式，11位数字，以1开头
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};


// 提交申请
const handleSubmit = async () => {
  if (!formRef.value) return;

  
  try {
    await formRef.value.validate();
    if (!validatePhone(formState.phone)) {
    return;
  }
    submitting.value = true;
    
    // 构造提交数据
    const submitData = {
      ...formState,
    };
    submitData.message_location = props.jobTitle;
    // 这里应替换为实际的API调用
    recruitmentApi.messageManagement.create(submitData).then(res => {
      if(res.code == 200){
        Message.success('申请提交成功！');
        emit('submit', submitData);
        // 只有在API调用成功后才关闭弹窗
        emit('update:visible', false);
        resetForm();
      }
    })
    
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped>
:deep(.arco-form-item){
  margin-bottom: 8px !important;
}
.job-application-modal :deep(.arco-upload-list-item) {
  margin-top: 0;
}

.job-application-modal :deep(.arco-modal) {
  z-index: 9999 !important;
}

.job-application-modal :deep(.arco-modal-mask) {
  z-index: 9998 !important;
}

/* 移动端优化 */
@media (max-width: 767px) {
  .job-application-modal :deep(.arco-modal) {
    max-height: 80vh;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 10vh;
    left: 5%;
    right: 5%;
    width: 90% !important;
  }
  
  .job-application-modal :deep(.arco-modal-body) {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
    padding: 12px 12px 5px;
    -webkit-overflow-scrolling: touch;
  }

  .job-application-modal :deep(.arco-modal-content) {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .job-application-modal :deep(.arco-modal-header) {
    padding: 16px 12px 10px;
    height: auto;
    min-height: 46px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
  
  .job-application-modal :deep(.arco-modal-title) {
    font-size: 16px;
    line-height: 1.4;
    padding-right: 40px;
    font-weight: 600;
  }

  .job-application-modal :deep(.arco-modal-close-btn) {
    top: 15px;
    right: 10px;
  }

  .job-application-modal :deep(.arco-modal-footer) {
    padding: 10px 12px;
    margin-top: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
  }
  
  .job-application-modal :deep(.arco-form-item) {
    margin-bottom: 8px;
  }
  
  .job-application-modal :deep(.arco-form-item-label-col) {
    padding-bottom: 3px;
  }

  .job-application-modal :deep(.arco-textarea-wrapper) {
    max-height: 80px;
  }

  .job-application-modal :deep(.arco-upload-list) {
    margin-top: 3px;
  }

  .job-application-modal :deep(.arco-form) {
    padding-right: 3px;
  }
}
</style>

<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

   <!-- 横幅 Banner -->
   <div class="joinus-banner">
      <h1 class="joinus-banner-title">产品中心</h1>
    </div>

    <!-- 产品分类页面主体 -->
    <div class="product-page-container">
      <div class="product-category-section">
        <div class="category-sidebar">
          <h3 class="category-title">产品分类</h3>
          <ul class="category-list">
            <!-- 动态渲染分类 -->
            <li class="category-parent" v-for="category in categoryList" :key="category.id">
              <div class="category-header"
                   :class="{ active: activeCategory === category.id.toString() }"
                   @click="selectCategory(category.id, category.name)">
                <span class="category-name">{{ category.name }}</span>
                <span class="category-toggle"
                      v-if="category.children && category.children.length > 0"
                      @click.stop="toggleCategory(category.id)"
                >{{ expandedCategories.includes(category.id) ? '−' : '+' }}</span>
              </div>
              <ul class="subcategory-list" v-if="expandedCategories.includes(category.id) && category.children && category.children.length > 0">
                <li
                  v-for="subCategory in category.children"
                  :key="subCategory.id"
                  class="category-item"
                  :class="{ active: activeCategory === `${category.id}-${subCategory.id}` }"
                >
                  <div class="category-header"
                       @click="selectSubCategory(category.id, subCategory.id, category.name, subCategory.name)">
                    <span class="category-name">{{ subCategory.name }}</span>
                    <span class="category-toggle"
                          v-if="subCategory.children && subCategory.children.length > 0"
                          @click.stop="toggleSubCategory(category.id, subCategory.id)"
                    >{{ expandedSubCategories.includes(`${category.id}-${subCategory.id}`) ? '−' : '+' }}</span>
                  </div>
                  <!-- 三级分类 -->
                  <ul class="third-level-list" v-if="expandedSubCategories.includes(`${category.id}-${subCategory.id}`) && subCategory.children && subCategory.children.length > 0">
                    <li 
                      v-for="thirdCategory in subCategory.children" 
                      :key="thirdCategory.id" 
                      class="third-level-item" 
                      :class="{ active: activeCategory === `${category.id}-${subCategory.id}-${thirdCategory.id}` }" 
                      @click.stop="setActiveCategory(`${category.id}-${subCategory.id}-${thirdCategory.id}`, category.name, subCategory.name, thirdCategory.name)"
                    >
                      <span class="category-name">{{ thirdCategory.name }}</span>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
        </div>

        <div class="product-display-section">

          <!-- 产品网格 -->
          <div class="product-grid">
            <div v-for="product in displayedProducts" :key="product.id" class="product-card" @click="goToProductDetail(product.id)">
              <div class="product-image">
                <img :src="product.image" :preview="false" class="product-img" fit="cover"/>
              </div>
              <div class="product-info">
                <h3 class="product-name">{{ product.name }}</h3>
              </div>
            </div>
          </div>
          <!-- 无数据时显示 -->
          <div v-if="displayedProducts.length === 0" class="no-data">
            <p>暂无产品数据</p>
          </div>

          <!-- 分页控件 -->
          <div class="pagination-container" v-if="totalProducts > 0">
            <a-pagination 
              :current="currentPage" 
              :total="totalProducts" 
              :page-size="pageSize"
              @change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <Footer />
  </div>
</template>


<script setup>
import procuctApi from '@/api/master/officialWebsiteModule';
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import NavBar from '@/components/website/one/NavBar.vue';
import Footer from '@/components/website/one/Footer.vue';

// 定义页面元信息
definePageMeta({
  layout: false,
  path: '/website/one/product'
});

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = '/website/one/assets';

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = (path) => {
  return getAssetUrl('images', path);
};

// 使用useHead钩子管理头部元数据
useHead({
  title: '产品中心',
  meta: [
    { charset: 'UTF-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' }
  ],
  link: [
    { rel: 'stylesheet', href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css` },
    { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});

// 产品分类和筛选状态
const categoryList = ref([]); // 分类列表
const activeCategory = ref(''); // 当前选中的分类ID
const activeCategoryName = ref(''); // 当前选中的分类名称
const activeSubCategoryName = ref(''); // 当前选中的子分类名称
const activeThirdCategoryName = ref(''); // 当前选中的三级分类名称
const currentPage = ref(1);
const pageSize = ref(9);
const totalProducts = ref(0);

// 展开的分类列表
const expandedCategories = ref([]);
// 展开的二级分类列表
const expandedSubCategories = ref([]);

// 选择一级分类
const selectCategory = (categoryId, categoryName) => {
  setActiveCategory(categoryId.toString(), categoryName);
};

// 切换分类展开/收起状态（只负责展开收起，不选择分类）
const toggleCategory = (categoryId) => {
  const index = expandedCategories.value.indexOf(categoryId);
  if (index === -1) {
    // 如果分类未展开，则展开它
    expandedCategories.value.push(categoryId);
  } else {
    // 如果分类已展开，则收起它
    expandedCategories.value.splice(index, 1);
  }
};

// 选择二级分类
const selectSubCategory = (categoryId, subCategoryId, categoryName, subCategoryName) => {
  const subCategoryKey = `${categoryId}-${subCategoryId}`;
  setActiveCategory(subCategoryKey, categoryName, subCategoryName);
};

// 切换二级分类展开/收起状态（只负责展开收起，不选择分类）
const toggleSubCategory = (categoryId, subCategoryId) => {
  const subCategoryKey = `${categoryId}-${subCategoryId}`;
  const index = expandedSubCategories.value.indexOf(subCategoryKey);

  if (index === -1) {
    // 如果二级分类未展开，则展开它
    expandedSubCategories.value.push(subCategoryKey);
  } else {
    // 如果二级分类已展开，则收起它
    expandedSubCategories.value.splice(index, 1);
  }
};

// 设置活动分类
const setActiveCategory = (category, parentName = '', subName = '', thirdName = '') => {
  activeCategory.value = category;
  activeCategoryName.value = parentName;
  activeSubCategoryName.value = subName;
  activeThirdCategoryName.value = thirdName;
  currentPage.value = 1; // 重置页码
  
  // 确保父分类已展开
  if (category.includes('-')) {
    const parentCategoryId = category.split('-')[0];
    if (!expandedCategories.value.includes(parentCategoryId)) {
      expandedCategories.value.push(parentCategoryId);
    }
    
    // 如果是三级分类，确保二级分类也已展开
    const parts = category.split('-');
    if (parts.length > 2) {
      const subCategoryKey = `${parts[0]}-${parts[1]}`;
      if (!expandedSubCategories.value.includes(subCategoryKey)) {
        expandedSubCategories.value.push(subCategoryKey);
      }
    }
  }
  
  // 切换分类后重新加载产品列表
  getProductsByCategory();
};

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
  getProductsByCategory(); // 切换页码后重新加载产品列表
};

// 跳转到产品详情页
const goToProductDetail = (id) => {
  navigateTo(`/website/one/productdetail/${id}`);
};

// 产品数据
const allProducts = ref([]);
const productParams = ref({
  page: 1,
  pageSize: 9,
  category_id: ''
});

// 根据当前分类和页码计算显示的产品
const displayedProducts = computed(() => {
  return allProducts.value;
});

// 获取产品分类
const getProductList = async () => {
  try {
    const response = await procuctApi.productManagement.getList({page: 1, pageSize: 100});
    if (response.code === 200) {
      categoryList.value = response.data.items;
      
      // 如果有分类数据，默认选中第一个一级分类
      if (categoryList.value.length > 0) {
        const firstCategory = categoryList.value[0];
        // 直接选中第一个一级分类，不自动展开或选择子分类
        setActiveCategory(firstCategory.id.toString(), firstCategory.name);
      }
    }
  } catch (error) {
    console.error('获取产品分类失败:', error);
  }
};

// 根据分类获取产品列表
const getProductsByCategory = async () => {
  try {
    // 解析当前选中的分类ID
    let categoryId = '';
    if (activeCategory.value) {
      const parts = activeCategory.value.split('-');
      // 使用最后一个分类ID（可能是三级分类）
      categoryId = parts[parts.length - 1];
    }
    
    // 更新请求参数
    productParams.value = {
      page: currentPage.value,
      pageSize: pageSize.value,
      category_id: categoryId
    };
    
    // 调用API获取产品列表
    const response = await procuctApi.productManagement.list.getList(productParams.value);
    if (response.code === 200) {
      allProducts.value = response.data.items;
      totalProducts.value = response.data.pageInfo?.total || 0;
    }
  } catch (error) {
    console.error('获取产品列表失败:', error);
  }
};

// 页面加载完成后执行的逻辑
onMounted(() => {
  // 获取产品分类数据
  getProductList();
  
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, 'style-css');
  loadCSS(`${baseUrl}/css/product.css`, 'product-css');

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = (e) => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(`${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`, 'bootstrap-script')
        .then(() => {
          console.log('页面功能初始化完成');
        })
        .catch(error => {
          console.error('Bootstrap 加载失败:', error);
        });
    } catch (error) {
      console.error('初始化页面功能失败:', error);
    }
  }, 500);
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 清除所有脚本
  ['bootstrap-script'].forEach(id => {
    const script = document.getElementById(id);
    if (script) {
      document.body.removeChild(script);
    }
  });
  
  // 清除所有样式
  ['style-css', 'product-css'].forEach(id => {
    const link = document.getElementById(id);
    if (link) {
      document.head.removeChild(link);
    }
  });
});
</script>

<style scoped>
.joinus-banner{
    position: relative;
    width: 100%;
    height: 450px;
    background-image: url('/website/one/assets/images/product.png');
    background-size: cover;
    background-position: center;
    margin-top: 56px;
    display: flex;
    align-items: center;
}

.joinus-banner-title {
    color: white;
    font-weight: 500;
    font-size: 36px;
    margin-left: 283px;
}
/* 产品页面样式 */
.product-page-container {
  min-height: 100vh;
  background-color: white;
}
/* 确保图片容器有明确的尺寸 */
.product-image-container {
  width: 100%;
  height: 200px; /* 设置固定高度或根据需要调整 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 使 a-image 组件填充整个容器 */
.full-size-image {
  width: 100%;
  height: 100%;
}

/* 确保 a-image 组件内的实际图片元素也填充整个组件 */
.full-size-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图片比例并填充整个区域 */
  object-position: center; /* 居中显示图片 */
}

.product-category-section {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 侧边栏分类样式 */
.category-sidebar {
  width: 200px;
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-right: 20px;
}

.category-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* 父分类样式 */
.category-parent {
  margin-bottom: 5px;
}

.category-header {
  padding: 10px 0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  transition: all 0.3s ease;
  font-weight: 500;
}

.category-header.active {
  color: #1890ff;
  font-weight: bold;
}

.category-toggle {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
  transition: all 0.3s ease;
}

/* 子分类样式 */
.subcategory-list {
  list-style: none;
  padding: 0 0 0 15px;
  margin: 5px 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.category-item {
  padding: 8px 0;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item.active > .category-header {
  color: #1890ff;
  font-weight: bold;
}

.third-level-item {
  color: #666 !important;
  cursor: pointer;
  padding: 8px 0;
  font-size: 14px;
  transition: all 0.3s ease;
  font-weight: normal !important;
}

.third-level-item.active {
  color: #1890ff !important;
  font-weight: bold !important;
}


/* 产品展示区域样式 */
.product-display-section {
  flex: 1;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.filter-group {
  margin-right: 30px;
}

.filter-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.filter-options {
  display: flex;
}

.filter-option {
  cursor: pointer;
  margin-right: 10px;
  color: #1890ff;
}

.filter-option.active {
  font-weight: bold;
}

/* 产品网格样式 */
.product-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

/* 产品卡片样式 */
.product-card {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.product-image {
  width: 100%;
  height: 280px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-img {
  width: 100%;
  height: 100%;
  display: block;
}

:deep(.arco-image) {
  width: 100%;
  height: 100%;
  display: block;
}

:deep(.arco-image-img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
  display: block;
}

.product-card:hover :deep(.arco-image-img) {
  transform: scale(1.05);
}

.product-info {
  padding: 15px;
  text-align: center;
  border-top: 1px solid #eee;
}

.product-name {
  font-size: 16px;
  color: #333;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 分页控件样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 992px) {
  .product-page-container {
    padding: 20px;
  }
  
  .product-category-section {
    flex-direction: column;
  }
  
  .category-sidebar {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .product-display-section {
    width: 100%;
  }
  
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .product-image {
    height: 200px;
  }
}

@media screen and (max-width: 576px) {
  .product-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    flex-wrap: wrap;
  }
  
  .filter-group {
    margin-bottom: 10px;
    width: 50%;
  }
}

@media (max-width: 992px) {
    .joinus-banner {
        height: 350px;
        margin-top: 60px;
    }
    
    .joinus-banner-title {
        margin-left: 50px;
        font-size: 30px;
    }
    
}

@media (max-width: 768px) {
    .joinus-banner {
        height: 250px;
    }
    
    .joinus-banner-title {
        margin-left: 30px;
        font-size: 26px;
    }
  
}

@media (max-width: 576px) {
    .joinus-banner {
        height: 200px;
    }
    
    .joinus-banner-title {
        margin-left: 20px;
        font-size: 22px;
    }
    
 
}
</style>

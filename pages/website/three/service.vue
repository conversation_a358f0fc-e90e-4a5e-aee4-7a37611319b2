<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/three/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/three/service">核心服务</a>
            </li>
          </ol>
        </nav>
      </div>
    </div>
    <!-- 页面主要内容 -->
    <main class="main-content">
      <!-- 核心服务 -->
      <section class="core-services ">
        <div class="container mx-auto px-4">
          <h2 class="text-left text-3xl font-bold mb-12 ">核心服务</h2>
          
          <div class="services-container">
            <!-- 专利申请服务 -->
            <div class="service-item flex flex-col md:flex-row mb-5" v-for="(item,index) in enterpriseInformations" :key="item.id">
              <div class="service-image md:w-2/3">
                <img
                  :preview="false"
                  :src="item.cover_image"
                  :alt="item.title"
                  :width="isMobile ? '100%' : 650"
                  :height="isMobile ? 'auto' : 338"
                  class="responsive-image"
                />
              </div>
              <div class="service-content md:w-1/2 p-8 flex flex-col justify-center relative" :class="`service-content-${index+1}`">
                <h3 class="text-2xl font-bold mb-4" :class="index == 0?'text-gray-700':'text-white'">{{item.title}}</h3>
                <p :class="index == 0?'text-gray-700':'text-white'">
                  {{item.description}}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
  
  
<script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { onMounted, onBeforeUnmount, ref, onUnmounted, reactive } from "vue";
import NavBar from "@/components/website/three/NavBar.vue";
import Footer from "@/components/website/three/Footer.vue";

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = "/website/one/assets";

definePageMeta({
  layout: false,
  path: "/website/three/service"
});

// 使用useHead钩子管理头部元数据
useHead({
  title: "服务支持",
  meta: [
    { charset: "UTF-8" },
    { name: "viewport", content: "width=device-width, initial-scale=1.0" }
  ],
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});
// 服务支持 
const enterpriseInformations = ref([]);

const getEnterpriseInformations = () => {
  officialApi.getEnterpriseInformations({page:1,pageSize:3,cate_name:'服务支持'}).then(res => {
        if(res.code == 200){
          enterpriseInformations.value = res.data.items;
        }
    })
}

// 页面加载完成后执行的逻辑
onMounted(() => {
  getEnterpriseInformations()
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = e => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }


});


</script>
  
<style scoped lang="less">
.core-services{
  padding: 50px 0;
  max-width: 1200px;
  margin: 0 auto;
}
.website-container{
  width: 100%;
  overflow-x: hidden;
  background-color: white;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}
  /* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #F8F8F8  ;
  padding: 15px 0;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  text-decoration: none;
  color: #666;
}

.breadcrumb-item.active {
  color: #666;
}

.service-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.25rem;
  transition: all 0.3s ease;

  @media (min-width: 48rem) {
    flex-direction: row;

    &:nth-child(even) {
      flex-direction: row-reverse;
    }
  }
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  .service-content {
    position: relative;
    z-index: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;
    transition: all 0.3s ease;

    @media (min-width: 48rem) {
      padding: 32px;
      height: 21.125rem;
    }
    
    &:hover {
      transform: translateY(-3px);
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
    }

    &.service-content-1::before {
      background-color: #f3f4f6; /* bg-gray-100 */
    }

    &.service-content-2::before {
      background-color: #333333;
    }

    &.service-content-3::before {
      background-color: #e70012;
    }
  }
}

.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
  transition: transform 0.4s ease;

  @media (min-width: 48rem) {
    width: 40.625rem;
    height: 21.125rem;
    object-fit: cover;
  }
}

.service-image {
  overflow: hidden;
}

.service-item:hover .responsive-image {
  transform: scale(1.05);
}

.carousel-overlay {
  background-color: none !important;
}

.website-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 确保 Bootstrap 和 Tailwind 的兼容性 */
.container {
  width: 100%;
  padding-right: 0.9375rem;
  padding-left: 0.9375rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 36rem) {
  .container {
    max-width: 33.75rem;
  }
}

@media (min-width: 48rem) {
  .container {
    max-width: 45rem;
  }
}

@media (min-width: 62rem) {
  .container {
    max-width: 60rem;
  }
}

@media (min-width: 75rem) {
  .container {
    max-width: 71.25rem;
  }
}






</style>
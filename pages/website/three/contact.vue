<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />
    <!-- 面包屑 -->
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/three/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/three/contact">联系我们</a>
            </li>
          </ol>
        </nav>
      </div>
    </div>
    
    <!-- 页面主要内容 -->
    <main class="main-content">
      <!-- 联系我们模块 -->
      <section class="contact-section py-5">
        <div class="container">
          <h2 class="section-title text-center mb-5">联系我们</h2>
          <div class="row justify-content-center contact-info-row">
            <div class="col-6 col-md-3 text-center mb-4">
              <div class="contact-item">
                <h5>电话</h5>
                <p>{{ basisData?.service_phone }}</p>
              </div>
            </div>
            <div class="col-6 col-md-3 text-center mb-4">
              <div class="contact-item">
                <h5>公司名称</h5>
                <p>{{basisData?.company_name}}</p>
              </div>
            </div>
            <div class="col-6 col-md-3 text-center mb-4">
              <div class="contact-item">
                <h5>地址</h5>
                <p>{{basisData?.company_address}}</p>
              </div>
            </div>
            <div class="col-6 col-md-3 text-center mb-4">
              <div class="contact-item">
                <h5>平台</h5>
                <div class="wechat-container" @mouseenter="showQrcode = true" @mouseleave="showQrcode = false" @touchstart="toggleQrcode">
                  <i class="iconfont icon-weixin" style="top: 1px; font-size: 24px;background-color: #F8F8F8;"></i>
                  <div class="qrcode-popup" v-show="showQrcode">
                    <a-image :src="basisData.wechat_qrcode" alt="微信二维码" width="120" height="120" :preview="false"/>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
          <!-- 加入我们 -->
          <section class="join-us" >
        <div class="container mx-auto px-4 ">
          <div class="join-us-container">
          
            
            <div class="join-us-form">
              <h3 class="form-title">加入我们</h3>
              
              <div class="form-group">
                <span style="color: red;">* &nbsp;</span>
                <span class="form-label">姓名 </span>
                <a-input v-model="joinForm.submitter_name" placeholder="请输入姓名" />
              </div>
              
              <div class="form-group">
                <span style="color: red;">* &nbsp;</span>
                <span class="form-label">手机</span>
                <a-input v-model="joinForm.phone" placeholder="请输入手机" />
              </div>
              
              <div class="form-group">
                <div class="form-label">邮箱</div>
                <a-input v-model="joinForm.email" placeholder="请输入邮箱" />
              </div>
              
              <div class="form-group">
                <div class="form-label">留言</div>
                <a-textarea v-model="joinForm.message_details" placeholder="请输入留言" :auto-size="{ minRows: 3, maxRows: 5 }" />
              </div>
              
              <a-button type="primary" class="submit-btn" @click="submitJoinForm">提交申请</a-button>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
    
<script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { onMounted, onBeforeUnmount, ref, onUnmounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import NavBar from "@/components/website/three/NavBar.vue";
import Footer from "@/components/website/three/Footer.vue";

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = "/website/one/assets";

definePageMeta({
  layout: false,
  path: "/website/three/contact"
});

// 使用useHead钩子管理头部元数据
useHead({
  title: "联系我们",
  meta: [
    { charset: "UTF-8" },
    { name: "viewport", content: "width=device-width, initial-scale=1.0" }
  ],
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});


const basisData = ref({});
const showQrcode = ref(false);
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
    })
}

// 移动端触摸切换二维码显示
const toggleQrcode = () => {
  showQrcode.value = !showQrcode.value;
};
// 页面加载完成后执行的逻辑
onMounted(() => {
  getData()
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = e => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }
});

// 加入我们表单数据
const joinForm = reactive({});

// 手机号码校验函数
const validatePhone = (phone) => {
  // 中国手机号码校验正则表达式，11位数字，以1开头
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 提交加入我们表单
const submitJoinForm = () => {
  // 表单验证
  if (!joinForm.submitter_name) {
    Message.warning("请填写姓名");
    return;
  }
  
  // 手机号码格式校验
  if (!validatePhone(joinForm.phone)) {
    Message.warning("请填写正确的手机号");
    return;
  }
  
  officialApi.messageManagement.create(joinForm).then(res => {
    if(res.code == 200){
      Message.success('申请提交成功！');
      // 重置表单
      joinForm.submitter_name = '';
      joinForm.phone = '';
      joinForm.email = '';
      joinForm.message_details = '';
    }else{
      Message.error(res.message);
    }
  })
};


</script>
    
<style scoped lang="less">
.website-container{
  width: 100%;
  overflow-x: hidden;
  background-color: white;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}
/* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #f8f8f8;
  padding: 15px 0;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  text-decoration: none;
  color: #666;
}

.breadcrumb-item.active {
  color: #666;
}


.news-list-container {
  padding: 50px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.news-item {
  width: 100%;
  display: block;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-item-content {
  background-color: #F8F8F8;
  display: flex;
  align-items: center;
  padding: 35px;
}

.news-date {
  align-items: baseline;
  margin-bottom: 10px;
}

.news-date-day {
  font-weight: 600;
  font-size: 30px;
  color: #a5a5a5;
  line-height: 1;
  margin-right: 5px;
}

.news-date-year {
  font-weight: 400;
  font-size: 16px;
  color: #a5a5a5;
  margin-top: 4px;
  margin-bottom: 5px;
}

.news-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
}

.news-item-image {
  width: 240px;
  height: 140px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.news-item-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-item-image :deep(.arco-image) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.news-item:hover .news-item-image :deep(img) {
  transform: scale(1.05);
}

.news-item-title {
  font-weight: 600;
  font-size: 24px;
  color: #262626;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.news-item:hover .news-item-title {
  color: #E70012 ;
}

.news-item-desc {
  font-weight: 400;
  font-size: 16px;
  color: #7f7f7f;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}


.service-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.25rem;
  
  @media (min-width: 48rem) {
    flex-direction: row;
    
    &:nth-child(even) {
      flex-direction: row-reverse;
    }
  }
  
  .service-content {
    position: relative;
    z-index: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;
    
    @media (min-width: 48rem) {
      padding: 32px;
      height: 21.125rem;
    }
    
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
    }
    
    &.service-content-1::before {
      background-color: #f3f4f6; /* bg-gray-100 */
    }
    
    &.service-content-2::before {
      background-color: #333333;
    }
    
    &.service-content-3::before {
      background-color: #E70012;
    }
  }
}

.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
  
  @media (min-width: 48rem) {
    width: 40.625rem;
    height: 21.125rem;
    object-fit: cover;
  }
}
.company-advantages {
  background-image: url("/website/three/assets/images/server_bg.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.advantage-image-wrapper {
  width: 241px;
  height: 187px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.advantage-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: relative;
}

.container-title {
  font-weight: 600;
  font-size: 2.75rem;
  color: #ffffff;
  line-height: 3.875rem;
  text-align: center;
  margin-bottom: 2.5rem;

  @media (max-width: 48rem) {
    font-size: 2rem;
    line-height: 2.75rem;
    margin-bottom: 1.875rem;
  }
}

.advantages-container {
  @media (max-width: 48rem) {
    flex-direction: column;
  }
}

.advantage-content {
  width: 100%;
  height: 14.375rem; /* 固定高度，仅在桌面端生效 */
  display: flex;
  flex-direction: column;
  text-align: left;

  @media (max-width: 48rem) {
    height: auto;
    min-height: auto;
    margin-bottom: 0;
  }

  .advantage-number {
    font-weight: 600;
    font-size: 3.125rem;
    color: #ffffff;

    @media (max-width: 48rem) {
      font-size: 2.25rem;
    }
  }

  .advantage-title {
    font-weight: 600;
    font-size: 1.625rem;
    color: #ffffff;
    margin-bottom: .625rem;

    @media (max-width: 48rem) {
      font-size: 1.375rem;
    }
  }

  .advantage-content {
    font-weight: 400;
    font-size: 1rem;
    color: #ffffff;

    @media (max-width: 48rem) {
      font-size: .875rem;
    }
  }
}

.carousel-overlay {
  background-color: none !important;
}
.website-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}


.hero-section {
  background-image: url("/placeholder/hero-bg.jpg");
  background-size: cover;
  background-position: center;
  height: 31.25rem;
  position: relative;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background-color: rgba(0, 0, 0, 0.5); */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保 Bootstrap 和 Tailwind 的兼容性 */
.container {
  width: 100%;
  padding-right: .9375rem;
  padding-left: .9375rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 36rem) {
  .container {
    max-width: 33.75rem;
  }
}

@media (min-width: 48rem) {
  .container {
    max-width: 45rem;
  }
}

@media (min-width: 62rem) {
  .container {
    max-width: 60rem;
  }
}

@media (min-width: 75rem) {
  .container {
    max-width: 76.25rem;
  }
}

/* 成功案例样式 */
.success-cases {
  background-color: #f8f9fa;
}

.cases-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  
  @media (max-width: 48rem) {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  @media (max-width: 36rem) {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }
}

.case-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 .25rem .375rem rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  line-height: 0;
  
  &:hover {
    transform: translateY(-0.3125rem);
    
    .case-overlay {
      opacity: 1;
      
      .case-content {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
  
  .case-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
    line-height: 0;
    vertical-align: middle;
  }
  
  &:hover .case-image {
    transform: scale(1.05);
  }
}

.case-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 24px;
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .case-content {
    transform: translateY(1.25rem);
    transition: all 0.3s ease;
    opacity: 0;
  }
  
  h3 {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: bold;
    color: #ffffff;
    
    @media (max-width: 48rem) {
      font-size: 16px;
    }
  }
  
  p {
    color: #ffffff;
    font-size: 14px;
    margin: 0;
    
    @media (max-width: 48rem) {
      font-size: 12px;
    }
    
    @media (max-width: 36rem) {
      display: none;
    }
  }
}

/* 加入我们样式 */
.join-us {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  color: #ffffff;
}

/* 联系我们模块样式 */
.contact-section {
  @media (max-width: 767px) {
    padding: 2rem 0;
  }
  
  .section-title {
    @media (max-width: 767px) {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }
  
  .contact-info-row {
    @media (max-width: 767px) {
      margin: 0 -8px;
    }
  }
  
  .contact-item {
    padding: 1rem;
    height: 100%;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    @media (max-width: 767px) {
      padding: 0.75rem 0.5rem;
    }
    
    h5 {
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      @media (max-width: 767px) {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
      }
    }
    
    p {
      margin-bottom: 0;
      
      @media (max-width: 767px) {
        font-size: 0.8rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    // &:hover {
    //   transform: translateY(-5px);
    //   box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    // }
  }
}

.join-us-container {
  display: flex;
  flex-direction: column;
  
  @media (min-width: 48rem) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.join-us-content {
  flex: 1;
  margin-bottom: 2rem;
  
  @media (min-width: 48rem) {
    margin-bottom: 0;
    margin-right: 3rem;
  }
}

.join-us-title {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
  
  @media (max-width: 48rem) {
    font-size: 2rem;
  }
}

.join-us-desc {
  font-size: 1rem;
  line-height: 1.6;
  margin-top: 1rem;
  max-width: 30rem;
}

.join-us-form {
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 100%;
  text-align: center;

}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.form-group {
  text-align: left;
  margin-bottom: 1.25rem;
}

.form-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.submit-btn {
  width: 100%;
  height: 3rem;
  font-size: 1rem;
  font-weight: 500;
  background-color: #E70012;
  border-color: #E70012;
  
  &:hover {
    background-color: darken(#E70012, 10%);
    border-color: darken(#E70012, 10%);
  }
}

</style>

<style scoped>
.wechat-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.qrcode-popup {
  position: absolute;
  top: -130px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  
  @media (max-width: 767px) {
    top: -140px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
  }
}
</style>
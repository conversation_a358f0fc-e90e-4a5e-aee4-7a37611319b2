<template>
  <div class="website-container">
    <!-- 使用动态组件渲染不同模板 -->
    <component :is="currentTemplate" v-if="currentTemplate"></component>
    <div v-else class="loading-container">
      <a-spin tip="页面加载中..."></a-spin>
    </div>
  </div>
</template>
  
  <script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { ref, defineAsyncComponent, onMounted } from "vue";

definePageMeta({
  layout: false,
  path: "/website/index"
});

// 定义当前模板组件
const currentTemplate = ref(null);
const templateId = ref(null);
const isLoading = ref(true);

// 模板组件映射表
const templateMap = {
  1: defineAsyncComponent(() => import("./one/index.vue")),
  2: defineAsyncComponent(() => import("./two/index.vue")),
  3: defineAsyncComponent(() => import("./three/index.vue")),
  4: defineAsyncComponent(() => import("./four/index.vue"))
};

// 获取网站配置并加载对应模板
const getConfigWebsite = async () => {
  try {
    const res = await officialApi.basisManagement.getData();
    if (res.code == 200) {
      templateId.value = res.data?.now_type;
      // 根据模板ID设置当前模板组件
      if (templateId.value >= 1 && templateId.value <= 4) {
        currentTemplate.value = templateMap[templateId.value];
      } else {
        // 默认使用模板1
        currentTemplate.value = templateMap[1];
      }
    }
  } catch (error) {
    console.error("获取网站配置失败:", error);
  } finally {
    isLoading.value = false;
  }
};

// 在组件挂载时获取配置
onMounted(() => {
  getConfigWebsite();
});
</script>
  
  <style scoped>
.website-container {
  width: 100%;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}
</style>
  
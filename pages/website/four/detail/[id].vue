<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 职位详情内容 -->
    <div class="container job-detail-container">
      <h1 class="job-detail-title">{{ jobDetail.position_name }}</h1>
      <div class="job-detail-tags">
        <span style="margin-right: 10px;">{{jobDetail.position_address}}</span>
        |
        更新于
        <span v-time="jobDetail.created_at"></span>
      </div>

      <div class="job-detail-section">
        <h3>岗位职责</h3>
        <ul>{{ jobDetail.position_duty }}</ul>
      </div>

      <div class="job-detail-section">
        <h3>岗位要求</h3>
        <ul>{{ jobDetail.position_requirement }}</ul>
      </div>

      <div class="job-detail-buttons">
        <a-button class="apply-btn" @click="applyJob">申请职位</a-button>
        <a-button class="share-btn" @click="shareJob">分享职位</a-button>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl" />

    <!-- 申请职位弹窗 -->
    <JobApplicationModal
      :visible="applicationModalVisible"
      @update:visible="(val) => applicationModalVisible = val"
      :job-id="jobId"
      :job-title="jobDetail.position_name || ''"
      @submit="handleApplicationSubmit"
    />
  </div>
</template>
  
  <script setup>
import recruitmentApi from "@/api/master/officialWebsiteModule";
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import NavBar from "@/components/website/four/NavBar.vue";
import Footer from "@/components/website/four/Footer.vue";
import JobApplicationModal from "./components/JobApplicationModal.vue";

// 定义页面元信息
definePageMeta({
  layout: false
});

// 获取路由参数
const route = useRoute();
const jobId = computed(() => route.params.id);

// 基础URL
const baseUrl = "/website/one/assets";

// 职位详情数据
const jobDetail = ref({});

// 控制申请职位弹窗显示
const applicationModalVisible = ref(false);

// 在实际应用中，这里应该根据ID从API获取职位详情
const fetchJobDetail = async id => {
  console.log("获取职位ID:", id);
  recruitmentApi.recruitManagement.faceDetail(id).then(res => {
    if (res.code == 200) {
      jobDetail.value = res.data;
    }
  });
};

// 申请职位
const applyJob = () => {
  console.log("点击申请职位按钮");
  applicationModalVisible.value = true;
  console.log("设置弹窗显示状态：", applicationModalVisible.value);
};

// 处理申请提交
const handleApplicationSubmit = formData => {
  console.log("收到职位申请:", formData);
  // 这里可以添加实际的API调用处理申请提交
};

// 分享职位
const shareJob = () => {};

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = path => {
  return getAssetUrl("images", path);
};

// 页面加载完成后执行的逻辑
onMounted(() => {
  // 获取职位详情
  fetchJobDetail(jobId.value);

  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  loadCSS(`${baseUrl}/css/detail.css`, "detail-css");
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      if (document.getElementById(id)) {
        resolve();
        return;
      }

      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        "bootstrap-script"
      )
        .then(() => {
          console.log("页面功能初始化完成");
        })
        .catch(error => {
          console.error("Bootstrap 加载失败:", error);
        });
    } catch (error) {
      console.error("脚本加载错误:", error);
    }
  }, 100);
});
</script>
  
<style scoped>
.job-detail-section h3:before {
 
  background-color: #F44217 !important;
}
/* 页面样式会从外部CSS文件加载 */
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: #f5f7fa;
}

.job-detail-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.job-detail-tags {
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.job-detail-section {
  margin-bottom: 25px;
}

.job-detail-section h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.job-detail-section ul {
  padding-left: 20px;
}

.job-detail-section li {
  margin-bottom: 10px;
  line-height: 1.6;
}

.job-detail-buttons {
  margin-top: 30px;
  display: flex;
  gap: 15px;
}

.apply-btn,
.share-btn {
  padding: 0px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.apply-btn {
  background-color: #F44217;
  color: white;
}

.apply-btn:hover {
  background-color: #F44217;
  color: white;
}

.share-btn {
  background-color: #f5f5f5;
  color: #333;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .job-detail-container {
    padding: 20px 15px;
  }

  .job-detail-title {
    font-size: 20px;
  }

  .job-detail-buttons {
    flex-direction: column;
  }

  .apply-btn,
  .share-btn {
    width: 100%;
  }
}
</style>

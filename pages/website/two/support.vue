<template>
    <div class="website-container">
        <!-- 使用导航栏组件 -->
        <NavBar :base-url="baseUrl2" />
        
        <!-- 页面主体内容 -->
        <div class="page-content">
            <!-- 全方位提供支持 -->
            <div class="support-section">
                <div class="container mx-auto px-4 sm:px-6 md:px-20 lg:px-40 py-8 md:py-16 flex flex-wrap items-center">
                    <div class="w-full md:w-1/2 mb-8 md:mb-0 md:pr-12 content-block">
                        <h2 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">{{enterpriseInformations[0]?.title}}</h2>
                        <p class="text-gray-600 mb-4">{{enterpriseInformations[0]?.description}}</p>
                    </div>
                    <div class="w-full md:w-1/2 flex justify-center md:pl-8 mt-4 md:mt-0 image-container">
                        <img v-if="enterpriseInformations[0]" class="support-image" :src="enterpriseInformations[0].cover_image" />
                    </div>
                </div>
            </div>
            
            <!-- 售前咨询 -->
            <div class="support-section">
                <div class="container mx-auto px-4 sm:px-6 md:px-20 lg:px-40 py-8 md:py-16 flex flex-wrap items-center md:flex-row-reverse">
                    <div class="w-full md:w-1/2 mb-8 md:mb-0 md:pl-12 content-block">
                        <h2 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">{{enterpriseInformations[1]?.title}}</h2>
                        <p class="text-gray-600 mb-4">{{enterpriseInformations[1]?.description}}</p>
                    </div>
                    <div class="w-full md:w-1/2 flex justify-center md:pr-8 mt-4 md:mt-0 image-container">
                        <img v-if="enterpriseInformations[1]" class="support-image" :src="enterpriseInformations[1].cover_image" />
                    </div>
                </div>
            </div>
            
            <!-- 落地效果保障 -->
            <div class="support-section">
                <div class="container mx-auto px-4 sm:px-6 md:px-20 lg:px-40 py-8 md:py-16 flex flex-wrap items-center">
                    <div class="w-full md:w-1/2 mb-8 md:mb-0 md:pr-12 content-block">
                        <h2 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">{{enterpriseInformations[2]?.title}}</h2>
                        <p class="text-gray-600 mb-4">{{enterpriseInformations[2]?.description}}</p>
                    </div>
                    <div class="w-full md:w-1/2 flex justify-center md:pl-8 mt-4 md:mt-0 image-container">
                        <img v-if="enterpriseInformations[2]" class="support-image" :src="enterpriseInformations[2].cover_image" />
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- 添加页脚组件 -->
        <Footer :base-url="baseUrl2" />
    </div>
</template>

<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted } from "vue";
import NavBar from "@/components/website/two/NavBar.vue";
import Footer from "@/components/website/two/Footer.vue";

// 定义页面元信息
definePageMeta({
    layout: false,
    path: '/website/two/support'
});

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";


//企业信息
const enterpriseInformations = ref([]);
const getEnterpriseInformations = () =>{
  
    officialApi.getEnterpriseInformations({page:1,pageSize:3,cate_name:'服务支持'}).then(res => {
        if(res.code == 200){
            enterpriseInformations.value = res.data.items;
        }
    })
}
// 页面加载完成后执行的逻辑
onMounted(() => {
    getEnterpriseInformations()
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  link: [
    { rel: 'stylesheet', href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css` },
    { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});
</script>

<style scoped>
.website-container {
    width: 100%;
    overflow-x: hidden;
    background-color: white;
    padding-top: 80px; /* 为固定导航栏留出空间 */
}

.support-page {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.support-section {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.support-section:hover {
    transform: translateY(-3px);
}

.support-section h2 {
    color: #333;
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.support-section:hover h2 {
    color: #1890ff;
}

.support-section:nth-child(2){
    background: #E7F1FB;
}

/* 响应式图片样式 */
.support-image {
    width: 100%;
    height: 100%;
    max-width: 604px;
    max-height: 340px;
    object-fit: cover; /* 改为cover以填满容器 */
    border-radius: 0 !important; /* 强制去掉边角弧度 */
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    overflow: hidden;
    transition: all 0.3s ease;
}


/* .support-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #1890ff;
} */

@media (max-width: 768px) {
    .support-section {
        padding: 1rem 0;
    }
    
    .support-section h2::after {
        width: 40px;
        bottom: -8px;
    }
    
    .support-image {
        max-width: 100%;
        height: auto;
    }
    
    .support-section:hover {
        transform: translateY(-3px);
    }
}
/* 内容块动画效果 */
.content-block {
    transition: all 0.3s ease;
}

.support-section:hover .content-block p {
    color: #666;
}

/* 图片容器动画效果 */
.image-container {
    transition: all 0.3s ease;
    overflow: hidden;
}
</style>

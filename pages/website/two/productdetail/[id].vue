<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/two/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/two/product">产品中心</a>
            </li>
            <li class="breadcrumb-item active">产品详情</li>
          </ol>
        </nav>
      </div>
    </div>

    <!-- 产品详情主体 -->
    <div class="product-detail-container">
      <div class="container">
        <!-- 产品基本信息 -->
        <div class="product-basic-info">
          <div class="product-image">
            <a-image :src="detailProduct.image" :alt="detailProduct.name" fit="cover" :preview="false"/>
          </div>
          <div class="product-info">
            <h1 class="product-name">{{ detailProduct.name }}</h1>
            <div class="product-desc">{{ detailProduct.description }}</div>
            <div class="product-brand">{{detailProduct.specification}}</div>
          </div>
        </div>
        <a-divider />
        <!-- 产品图片展示 -->
        <div class="product-images-section">
          <div v-html="detailProduct.detail_images"></div>
          <!-- <a-image :src="detailProduct.detail_images" alt="产品详情图" fit="cover" class="detail-image" :preview="false" /> -->
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>

<script setup>
import procuctApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted, computed } from "vue";
import NavBar from "@/components/website/two/NavBar.vue";
import Footer from "@/components/website/two/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: '/website/two/productdetail/:id'
});

// 获取路由参数
const route = useRoute();
const productId = computed(() => route.params.id);

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";


const detailProduct = ref({});
const getdetailProduct = async () => {
  const { data } = await procuctApi.productManagement.list.detail(productId.value);
  detailProduct.value = data;
};
// 页面加载完成后执行的逻辑
onMounted(() => {
  getdetailProduct(productId.value);
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});
</script>
  
  <style scoped>
/* 网站容器样式 */
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: white;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}

/* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #F0F5FF ;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5eb;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  color: #666 !important;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #666 !important;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 产品详情容器 */
.product-detail-container {
  padding: 30px 0 50px;
}

/* 产品基本信息样式 */
.product-basic-info {
  display: flex;
  margin-bottom: 40px;
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
}

.product-image {
  flex: 0 0 40%;
  padding-right: 30px;
  text-align: center;
}

.product-image :deep(img) {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

.product-info {
  flex: 0 0 60%;
  padding-left: 20px;
}

.product-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.4;
}

.product-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

/* 产品特性样式 */
.product-features {
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.feature-icon {
  color: #1a65ff;
  margin-right: 10px;
}

.feature-text {
  font-size: 14px;
  color: #333;
}

/* 产品标签样式 */
.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
}

.tag {
  background-color: #f0f5ff;
  color: #1a65ff;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
}

/* 产品详情部分样式 */
.product-detail-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.section-title:after {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 80px;
  height: 2px;
  background-color: #1a65ff;
}

/* 规格参数表格样式 */
.specs-table {
  width: 100%;
  border-collapse: collapse;
}

.specs-table tr {
  border-bottom: 1px solid #eee;
}

.specs-table tr:last-child {
  border-bottom: none;
}

.specs-table td {
  padding: 12px 10px;
  font-size: 14px;
}

.spec-name {
  width: 120px;
  color: #666;
  font-weight: 500;
}

.spec-value {
  color: #333;
}

/* 产品图片展示区域 */
.product-images-section {
  margin-bottom: 40px;
  text-align: center;
}

.detail-image {
  max-width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .product-basic-info {
    flex-direction: column;
    padding: 20px;
  }
  
  .product-image {
    flex: 0 0 100%;
    padding-right: 0;
    margin-bottom: 20px;
  }
  
  .product-info {
    flex: 0 0 100%;
    padding-left: 0;
  }
}

@media (max-width: 768px) {
  .product-name {
    font-size: 20px;
  }
  
  .product-desc {
    font-size: 13px;
  }
  
  .feature-text {
    font-size: 13px;
  }
  
  .section-title {
    font-size: 18px;
  }
  
  .specs-table td {
    padding: 10px 8px;
    font-size: 13px;
  }
  
  .spec-name {
    width: 100px;
  }
}
</style>
  
<template>
  <a-modal
    :visible="modelValue"
    :title="'修改报价'"
    :mask-closable="false"
    :footer="false"
    width="400px"
    @cancel="handleCancel"
  >
    <div class="p-4">
      <a-form :model="formData" layout="vertical">
        <a-form-item label="当前报价" field="quote">
          <a-input-number
            v-model="formData.quote"
            :min="0"
            :precision="2"
            :step="10"
            style="width: 100%"
            placeholder="请输入报价"
          >
            <template #prefix>¥</template>
          </a-input-number>
        </a-form-item>
        
        <div class="text-gray-500 text-sm mb-4">
          <div>参考价格：¥{{ record.price }}</div>
          <div>数量：{{ record.quantity || 1 }}</div>
          <div>总金额：¥{{ calculateAmount() }}</div>
        </div>
        
        <div class="flex justify-end gap-2">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSubmit">确定</a-button>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件事件
const emit = defineEmits(['update:modelValue', 'success']);

// 表单数据
const formData = reactive({
  quote: 0
});

// 监听记录变化，更新表单数据
watch(() => props.record, (newVal) => {
  if (newVal && newVal.quote) {
    formData.quote = parseFloat(newVal.quote);
  }
}, { immediate: true, deep: true });

// 计算总金额
const calculateAmount = () => {
  const quantity = props.record.quantity || 1;
  return (formData.quote * quantity).toFixed(2);
};

// 取消操作
const handleCancel = () => {
  emit('update:modelValue', false);
};

// 提交操作
const handleSubmit = () => {
  if (formData.quote <= 0) {
    // 使用 Arco Design 的消息提示
    window.$message.error('报价必须大于0');
    return;
  }
  
  // 触发成功事件，传递更新后的报价
  emit('success', {
    id: props.record.id,
    quote: formData.quote.toFixed(2),
    amount: calculateAmount()
  });
  
  // 关闭弹窗
  emit('update:modelValue', false);
};
</script>

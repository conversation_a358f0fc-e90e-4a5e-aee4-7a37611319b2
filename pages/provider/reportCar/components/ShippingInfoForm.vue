<!--
 - 收货信息表单组件
 - 用于在报备车页面中添加收货人信息
-->
<template>
  <div class="shipping-info-form bg-white p-4 rounded-md mt-4 border">
    <div class="form-header flex items-center mb-4">
      <div class="form-title text-base font-medium">确认供货信息</div>
      <div class="flex-grow"></div>
      <a-button 
        type="text" 
        @click="isCollapsed = !isCollapsed"
        class="text-blue-600"
      >
        {{ isCollapsed ? '展开' : '折叠' }}
        <template #icon>
          <icon-down v-if="isCollapsed" />
          <icon-up v-else />
        </template>
      </a-button>
    </div>
    
    <div class="form-content" v-show="!isCollapsed">
      <!-- 收货人信息 -->
      <div class="form-section mb-4">
        <div class="section-title mb-2">
          <span class="text-red-500">*</span>
          <span class="text-gray-700">收货人信息</span>
        </div>
        
        <div class="section-content bg-gray-50 p-4 rounded">
          <!-- 收货地址 -->
          <div class="form-item mb-4">
            <div class="item-label mb-1">
              <a-link class="ml-2" type="primary">添加收货地址</a-link>
            </div>
            
            <!-- 地址列表 -->
            <div class="address-list grid grid-cols-2 gap-4">
              <div v-for="(address, index) in addressList" :key="index" 
                class="address-card rounded-lg border p-4 relative" 
                :class="{'address-card-active': address.isDefault}">
                <div class="flex justify-between items-start">
                  <div class="address-info">
                    <div class="flex items-center mb-2">
                      <span class="font-medium text-lg">{{ address.name || address.address }}</span>
                      <span class="ml-3 text-gray-500">{{ address.phone }}</span>
                    </div>
                    <div class="text-gray-600 mb-1">{{ address.address }}</div>
                    <div class="text-gray-500">{{ address.detail }}</div>
                  </div>
                  <div class="address-tag" v-if="address.isDefault">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">默认</span>
                  </div>
                </div>
                <div class="address-actions flex justify-end mt-3">
                  <a-button type="text" class="text-blue-600" @click="handleSetDefault(index)" v-if="!address.isDefault">设为默认</a-button>
                  <a-button type="text" class="text-gray-500 ml-2" @click="handleEdit(index)">编辑</a-button>
                  <a-button type="text" class="text-red-500 ml-2" @click="handleDeleteAddress(index)">删除</a-button>
                </div>
              </div>
              
              <!-- 添加收货地址 -->
              <div class="add-address mt-2">
                <a-button type="primary" @click="handleAddAddress" class="w-full">
                  <template #icon>
                    <icon-plus />
                  </template>
                  添加收货地址
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 地址编辑模态框 -->
  <a-modal
    v-model:visible="showAddressModal"
    :title="editingIndex === -1 ? '新增收货地址' : '编辑收货地址'"
    @cancel="resetAddressForm"
    @before-ok="handleAddressSubmit"
  >
    <a-form :model="addressForm" layout="vertical">
      <a-form-item field="name" label="收货人" validate-trigger="blur" required>
        <a-input v-model="addressForm.name" placeholder="请输入收货人姓名" />
      </a-form-item>
      
      <a-form-item field="phone" label="手机号码" validate-trigger="blur" required>
        <a-input v-model="addressForm.phone" placeholder="请输入手机号码" />
      </a-form-item>
      
      <a-form-item field="address" label="收货地址" validate-trigger="blur" required>
        <a-input v-model="addressForm.address" placeholder="请输入收货地址" />
      </a-form-item>
      
      <a-form-item field="detail" label="详细信息" validate-trigger="blur">
        <a-textarea v-model="addressForm.detail" placeholder="请输入详细信息（选填）" />
      </a-form-item>
      
      <a-form-item>
        <a-checkbox v-model="addressForm.isDefault" :disabled="editingIndex === -1 && addressList.length === 0">
          设为默认收货地址
        </a-checkbox>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconDelete, IconPlus, IconDown, IconUp } from '@arco-design/web-vue/es/icon';

// 折叠状态
const isCollapsed = ref(false);

// 模态框相关状态
const showAddressModal = ref(false);
const editingIndex = ref(-1);

// 地址表单
const addressForm = reactive({
  name: '',
  phone: '',
  address: '',
  detail: '',
  isDefault: false
});

// 地址列表
const addressList = reactive([
  {
    name: '哈哈哈',
    address: '北京市海淀区中关村1',
    detail: '13068019036',
    phone: '18593415401',
    isDefault: true
  },
  {
    name: '测试',
    address: '上海 市嘉区 嘉定区 11',
    detail: '',
    phone: '18345678901',
    isDefault: false
  },
  {
    name: '测试',
    address: '上海县 崇明县 1丁堡',
    detail: '',
    phone: '18121111112',
    isDefault: false
  },
  {
    name: '肉肉肉',
    address: '北京 市辖区 丰台区 ********',
    detail: '',
    phone: '18593415401',
    isDefault: false
  },
  {
    name: '5263',
    address: '内蒙古 兴安盟 扎赉特旗 扎赉打算的1',
    detail: '',
    phone: '19635856325',
    isDefault: false
  }
]);

// 预计下单时间
const orderTime = ref('');

// 备注
const remark = ref('');

// 设置默认地址
const handleSetDefault = (index) => {
  addressList.forEach((item, i) => {
    item.isDefault = i === index;
  });
  Message.success('已设置为默认地址');
};

// 删除地址
const handleDeleteAddress = (index) => {
  addressList.splice(index, 1);
  Message.success('地址已删除');
};

// 打开添加地址模态框
const handleAddAddress = () => {
  // 重置表单
  resetAddressForm();
  // 显示模态框
  showAddressModal.value = true;
};

// 编辑地址
const handleEdit = (index) => {
  // 设置当前编辑的地址索引
  editingIndex.value = index;
  const address = addressList[index];
  
  // 填充表单数据
  Object.assign(addressForm, {
    name: address.name || '',
    phone: address.phone || '',
    address: address.address || '',
    detail: address.detail || '',
    isDefault: address.isDefault
  });
  
  // 显示模态框
  showAddressModal.value = true;
};

// 重置地址表单
const resetAddressForm = () => {
  Object.assign(addressForm, {
    name: '',
    phone: '',
    address: '',
    detail: '',
    isDefault: false
  });
  editingIndex.value = -1;
};

// 处理地址表单提交
const handleAddressSubmit = () => {
  // 表单验证
  if (!addressForm.name) {
    Message.error('请输入收货人姓名');
    return false;
  }
  
  if (!addressForm.phone) {
    Message.error('请输入手机号码');
    return false;
  }
  
  if (!/^1[3-9]\d{9}$/.test(addressForm.phone)) {
    Message.error('请输入正确的手机号码');
    return false;
  }
  
  if (!addressForm.address) {
    Message.error('请输入收货地址');
    return false;
  }
  
  try {
    // 准备数据
    const addressData = {
      name: addressForm.name,
      phone: addressForm.phone,
      address: addressForm.address,
      detail: addressForm.detail || '',
      isDefault: addressForm.isDefault
    };
    
    if (editingIndex.value === -1) {
      // 新增地址
      addressList.push(addressData);
      Message.success('添加地址成功');
    } else {
      // 编辑地址
      Object.assign(addressList[editingIndex.value], addressData);
      Message.success('更新地址成功');
    }
    
    // 如果设置为默认地址，需要将其他地址设置为非默认
    if (addressData.isDefault) {
      addressList.forEach((item, idx) => {
        if (editingIndex.value === -1) {
          // 新增时，最后一个是新增的地址
          item.isDefault = (idx === addressList.length - 1);
        } else {
          // 编辑时
          item.isDefault = (idx === editingIndex.value);
        }
      });
    }
    
    return true;
  } catch (error) {
    console.error('保存地址失败:', error);
    Message.error('保存地址失败，请稍后再试');
    return false;
  }
};
</script>

<style scoped>
.shipping-info-form {
  border: 1px solid #e5e7eb;
}

.address-card {
  transition: all 0.3s;
  border-color: #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.address-card:hover {
  border-color: #d1e9ff;
  box-shadow: 0 2px 8px rgba(0, 120, 212, 0.1);
}

.address-card-active {
  border-color: #2080f0;
  background-color: #f0f7ff;
}

.address-actions button {
  transition: all 0.2s;
}

.address-actions button:hover {
  opacity: 0.8;
}
</style>

<!--
 - 商品备注组件
 - 用于在报备车底部添加备注信息
-->
<template>
  <div>
    <div class="remark-section bg-white p-4 rounded-md mt-4 border">
    <!-- 预计下单时间 -->
    <div class="mb-4">
      <div class="mb-2">
        <span class="text-gray-700 font-medium">预计下单时间</span>
      </div>
      <a-date-picker 
        v-model="orderTime" 
        style="width: 100%" 
        placeholder="请选择下单时间"
        show-time
      />
    </div>
    
    <!-- 备注信息 -->
    <div>
      <div class="mb-2">
        <span class="text-gray-700 font-medium">备注</span>
        <span class="text-gray-400 text-xs ml-1">({{ remarkLength }} / 200)</span>
      </div>
      <a-textarea 
        v-model="remarkContent" 
        placeholder="请输入备注" 
        allow-clear 
        :max-length="200"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </div>
    
    <!-- 已选择商品统计信息卡片 -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
      <div class="text-sm p-1">
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-1">
            <div class="flex justify-end">
              <span class="text-gray-500 w-24">已选择商品：</span>
              <span class="font-medium text-primary">{{ selectedCount }} 件</span>
            </div>
            <!-- <div class="flex">
              <span class="text-gray-500 w-24">平均单价：</span>
              <span class="font-medium">¥{{ selectedCount > 0 ? (parseFloat(selectedTotalAmount) / selectedCount).toFixed(2) : '0.00' }}</span>
            </div> -->
          </div>
          <div class="space-y-1">
            <div class="flex justify-end">
              <span class="text-gray-500 w-24">金额总计：</span>
              <span class="font-medium text-red-500">¥{{ selectedTotalAmount }}</span>
            </div>
            <!-- <div class="flex">
              <span class="text-gray-500 w-24">总库存量：</span>
              <span class="font-medium">{{ totalStock }}</span>
            </div> -->
          </div>
        </div>
      </div>
    </div>
    
    <!-- 提交按钮 -->
    <div class="mt-4 flex justify-end">
      <a-button type="primary" @click="handleSubmit">
        提交
      </a-button>
    </div>
    
  </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconDelete } from '@arco-design/web-vue/es/icon';

// 接收父组件传递的选中行数据
const props = defineProps({
  selectedRows: {
    type: Array,
    default: () => []
  }
});

// 预计下单时间
const orderTime = ref('');

// 备注内容
const remarkContent = ref('');

// 备注字数计算
const remarkLength = computed(() => {
  return remarkContent.value ? remarkContent.value.length : 0;
});

// 计算已选择的商品数量
const selectedCount = computed(() => {
  return props.selectedRows.length;
});

// 计算已选择商品的总金额
const selectedTotalAmount = computed(() => {
  if (!props.selectedRows.length) return '0.00';
  
  const total = props.selectedRows.reduce((sum, item) => {
    return sum + parseFloat(item.amount || 0);
  }, 0);
  
  return total.toFixed(2);
});

// 计算总库存量
const totalStock = computed(() => {
  return props.selectedRows.reduce((sum, item) => sum + (parseInt(item.stock) || 0), 0);
});

// 备注列表
const remarkList = reactive([
  { content: '商品已确认，等待发货', time: '2025-06-05 14:30' },
  { content: '客户要求尽快发货', time: '2025-06-05 10:15' }
]);

// 监听备注内容变化，当备注内容不为空时自动添加到列表
watch(remarkContent, (newValue, oldValue) => {
  if (newValue && !oldValue && remarkList.length === 0) {
    // 当用户开始输入并且列表为空时，不执行任何操作
    // 这里可以添加其他逻辑如果需要
  }
});

// 添加备注
const handleAddRemark = () => {
  if (!remarkContent.value.trim()) {
    Message.warning('请输入备注内容');
    return;
  }
  
  // 获取当前时间
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  
  // 格式化时间
  const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}`;
  
  // 添加到列表
  remarkList.unshift({
    content: remarkContent.value,
    time: formattedTime
  });
  
  // 清空输入框
  remarkContent.value = '';
  
  // 提示成功
  Message.success('备注添加成功');
};

// 删除备注
const handleDelete = (index) => {
  remarkList.splice(index, 1);
  Message.success('备注删除成功');
};

// 提交表单
const handleSubmit = () => {
  // 验证预计下单时间
  if (!orderTime.value) {
    Message.warning('请选择预计下单时间');
    return;
  }
  
  // 如果有备注内容但未添加到列表，自动添加
  if (remarkContent.value.trim()) {
    handleAddRemark();
  }
  
  // 这里可以添加提交到后端的逻辑
  // ...
  
  Message.success('提交成功');
  
  // 触发父组件的事件
  emit('submit', {
    orderTime: orderTime.value,
    remarks: remarkList
  });
};

// 定义事件
const emit = defineEmits(['submit']);

// 导出数据，供父组件使用
defineExpose({
  orderTime,
  remarkContent,
  remarkList,
  selectedCount,
  selectedTotalAmount
});
</script>

<style scoped>
.remark-section {
  border: 1px solid #e5e7eb;
}

.remark-item {
  transition: all 0.3s;
}

.remark-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
</style>

<template>
  <div class="claim-record-container">
    <div class="page-content">
      <!-- 详情抽屉 -->
      <ClaimRecordDetailDrawer
        v-model:visible="drawerVisible"
        :record-data="currentRecord"
        @close="handleDrawerClose"
      />
      
      <div class="search-area">
        <!-- 状态标签列表 -->
        <div class="status-tabs-container">
          <a-tabs type="line" :active-key="activeStatus" @change="selectStatusTab">
            <a-tab-pane v-for="tab in statusTabs" :key="tab.key" :title="tab.name">
              <template #title>
                <span class="status-tab-title">{{ tab.name }}</span>
                <span class="status-tab-count">{{ tab.count || 0 }}</span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </div>
        
        <!-- 搜索表单和表格 -->
        <ma-crud
          ref="crudRef"
          :options="crudOptions"
          :columns="columns"
          :search="search"
          v-model:search-params="searchParams"
          :data="tableData"
          :loading="loading"
        >
          <!-- 操作列 -->
          <template #operation="{ record }">
            <a-space>
              <a-link @click="handleViewAuditRecord(record)">审核记录</a-link>
              <a-link @click="handleView(record)">查看详情</a-link>
            </a-space>
          </template>
          
          <!-- 认款状态列 -->
          <template #claimStatus="{ record }">
            <a-tag :color="getClaimStatusColor(record.claimStatus)">
              {{ record.claimStatus }}
            </a-tag>
          </template>
        </ma-crud>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";
import ClaimRecordDetailDrawer from "./components/ClaimRecordDetailDrawer.vue";

// 定义页面路由元信息
definePageMeta({
  name: 'provider-finance-claim-record',
  title: "认款申请记录",
  icon: "icon-file-text",
});

// 路由
const router = useRouter();

// 抽屉可见性
const drawerVisible = ref(false);
const currentRecord = ref({});

// 查看详情
const handleView = (record) => {
  currentRecord.value = record;
  drawerVisible.value = true;
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
};

// 查看审核记录
const handleViewAuditRecord = (record) => {
  Message.info(`查看审核记录：${record.claimNo}`);
  // 这里可以实现查看审核记录的逻辑
};

// 选择状态标签
const selectStatusTab = (key) => {
  activeStatus.value = key;
  
  // 根据选中的标签设置搜索参数
  if (key === 'all') {
    searchParams.value.claimStatus = '';
  } else {
    const statusMap = {
      'auditing': '待审核',
      'rejected': '审核驳回',
      'approved': '审核通过',
      'abnormal': '单据异常',
    };
    searchParams.value.claimStatus = statusMap[key];
  }
  
  // 重置页码并重新加载数据
  currentPage.value = 1;
  loadData();
};

// 获取认款状态颜色
const getClaimStatusColor = (status) => {
  const colorMap = {
    '待审核': 'blue',
    '审核驳回': 'red',
    '审核通过': 'green',
    '单据异常': 'orange',
  };
  return colorMap[status] || 'gray';
};

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);
// 当前页码
const currentPage = ref(1);
// 每页条数
const pageSize = ref(10);
// 总条数
const total = ref(0);

// 生成模拟数据
const getMockClaimRecordData = () => {
  const generateClaimRecordItem = (index) => {
    const statusOptions = ['待审核', '审核驳回', '审核通过', '单据异常'];
    const orderSourceOptions = ['君网-官网', '君网-小程序', '天猫-旗舰店', '天猫-专卖店', '京东-自营', '京东-POP'];
    const merchantNames = ['上海某某科技有限公司', '北京某某贸易有限公司', '广州某某电子商务有限公司', '深圳某某网络科技有限公司'];
    const applicants = ['张三', '李四', '王五', '赵六', '钱七'];
    
    const statusKey = statusOptions[Math.floor(Math.random() * statusOptions.length)];
    const orderSource = orderSourceOptions[Math.floor(Math.random() * orderSourceOptions.length)];
    const merchantName = merchantNames[Math.floor(Math.random() * merchantNames.length)];
    const applicant = applicants[Math.floor(Math.random() * applicants.length)];
    
    // 生成申请时间（过去30天内的随机时间）
    const applyDate = new Date();
    applyDate.setDate(applyDate.getDate() - Math.floor(Math.random() * 30));
    const applyTime = applyDate.toISOString().split('T')[0];
    
    // 生成审核时间（申请时间后1-3天）
    const auditDate = new Date(applyDate);
    auditDate.setDate(auditDate.getDate() + Math.floor(Math.random() * 3) + 1);
    const auditTime = auditDate.toISOString().split('T')[0];
    
    // 生成随机金额（1000-10000之间）
    const amount = (Math.random() * 9000 + 1000).toFixed(2);
    
    return {
      id: `CR${100000 + index}`,
      claimNo: `CL${new Date().getFullYear()}${String(Math.floor(Math.random() * 1000000)).padStart(6, '0')}`,
      claimStatus: statusKey,
      orderSource: orderSource,
      merchantName: merchantName,
      claimAmount: amount,
      applicant: applicant,
      applyTime: applyTime,
      auditTime: auditTime,
      remark: Math.random() > 0.5 ? `这是第${index+1}条认款申请记录的备注信息` : '',
    };
  };
  
  return Array.from({ length: 100 }, (_, index) => generateClaimRecordItem(index));
};

// 加载数据
const loadData = () => {
  console.log('加载数据');
  loading.value = true;
  
  // 生成模拟数据
  const allData = getMockClaimRecordData();
  
  // 根据搜索参数过滤
  let filteredData = allData;
  
  // 如果有状态筛选
  if (activeStatus.value !== 'all') {
    const statusMap = {
      'auditing': '待审核',
      'rejected': '审核驳回',
      'approved': '审核通过',
      'abnormal': '单据异常',
    };
    filteredData = filteredData.filter(item => item.claimStatus === statusMap[activeStatus.value]);
  }
  
  // 其他搜索条件过滤
  Object.keys(searchParams.value).forEach(key => {
    if (searchParams.value[key] && key !== 'claimStatus') {
      // 处理金额范围筛选
      if (key === 'minAmount') {
        filteredData = filteredData.filter(item => {
          const amount = parseFloat(item.claimAmount);
          return amount >= parseFloat(searchParams.value.minAmount);
        });
      } else if (key === 'maxAmount') {
        filteredData = filteredData.filter(item => {
          const amount = parseFloat(item.claimAmount);
          return amount <= parseFloat(searchParams.value.maxAmount);
        });
      } else {
        filteredData = filteredData.filter(item => {
          if (typeof item[key] === 'string') {
            return item[key].includes(searchParams.value[key]);
          }
          return item[key] === searchParams.value[key];
        });
      }
    }
  });
  
  // 更新状态标签数量
  statusTabs.forEach(tab => {
    if (tab.key === 'all') {
      tab.count = allData.length;
    } else {
      const statusMap = {
        'auditing': '待审核',
        'rejected': '审核驳回',
        'approved': '审核通过',
        'abnormal': '单据异常',
      };
      tab.count = allData.filter(item => item.claimStatus === statusMap[tab.key]).length;
    }
  });
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  
  tableData.value = filteredData.slice(start, end);
  total.value = filteredData.length;
  crudOptions.pagination.total = filteredData.length;
  loading.value = false;
};

// 当前选中的状态标签
const activeStatus = ref('all');

// 状态标签列表
const statusTabs = [
  { key: 'all', name: '全部', count: 0 },
  { key: 'auditing', name: '待审核', count: 0 },
  { key: 'rejected', name: '审核驳回', count: 0 },
  { key: 'approved', name: '审核通过', count: 0 },
  { key: 'abnormal', name: '单据异常', count: 0 },
];

// 表格配置
const crudOptions = reactive({
  rowKey: 'id',
  showIndex: true,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100],
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "审核时间",
    dataIndex: "auditTime",
    align: "center",
    width: 120,
    search: true,
    formType: "range",
    searchField: "auditTime",
    placeholder: "请选择审核时间",
  },
  {
    title: "认款申请单号",
    dataIndex: "claimNo",
    align: "center",
    width: 150,
    search: true,
    formType: "input",
    searchField: "claimNo",
    placeholder: "请输入认款申请单号",
  },
  {
    title: "认款申请单状态",
    dataIndex: "claimStatus",
    align: "center",
    width: 100,
    search: true,
    formType: "select",
    searchField: "claimStatus",
    slotName: "claimStatus",
    dict: {
      data: [
        { label: '待审核', value: '待审核' },
        { label: '审核驳回', value: '审核驳回' },
        { label: '审核通过', value: '审核通过' },
        { label: '单据异常', value: '单据异常' },
      ],
    },
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    align: "center",
    width: 100,
    search: true,
    formType: "cascader",
    searchField: "orderSource",
    dict: {
      data: [
        { 
          value: '君网', 
          label: '君网',
          children: [
            { value: '君网-官网', label: '君网-官网' },
            { value: '君网-小程序', label: '君网-小程序' },
          ]
        },
        { 
          value: '天猫', 
          label: '天猫',
          children: [
            { value: '天猫-旗舰店', label: '天猫-旗舰店' },
            { value: '天猫-专卖店', label: '天猫-专卖店' },
          ]
        },
        { 
          value: '京东', 
          label: '京东',
          children: [
            { value: '京东-自营', label: '京东-自营' },
            { value: '京东-POP', label: '京东-POP' },
          ]
        },
      ],
    },
  },
  {
    title: "商户名称",
    dataIndex: "merchantName",
    align: "center",
    width: 150,
    search: true,
    formType: "input",
    searchField: "merchantName",
    placeholder: "请输入商户名称",
  },
  {
    title: "申请认款金额",
    dataIndex: "claimAmount",
    align: "center",
    width: 120,
    search: true,
    formType: "between",
    searchField: "claimAmount",
    placeholder: ["最小金额", "最大金额"],
  },
  {
    title: "申请人",
    dataIndex: "applicant",
    align: "center",
    width: 100,
    search: true,
    formType: "input",
    searchField: "applicant",
    placeholder: "请输入申请人",
  },
  {
    title: "备注",
    dataIndex: "remark",
    align: "center",
    width: 200,
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 180,
    fixed: "right",
    slotName: "operation",
  },
]);

// 搜索表单配置
const search = reactive({
  labelWidth: 80,
  formProps: {
    layout: "inline",
  },
  schemas: [],
});

// 搜索参数
const searchParams = ref({
  auditTime: "",
  claimStatus: "", // 保留这个字段用于状态标签切换
  claimNo: "",
  merchantName: "",
  orderSource: "",
  claimAmount: "",
  applicant: "",
});

// 页面加载时获取数据
onMounted(() => {
  console.log('页面加载完成，开始获取数据');
  // 初始化表格数据
  loadData();
});

// 监听分页变化
watch([currentPage, pageSize], () => {
  loadData();
});

// 监听搜索参数变化
watch(() => searchParams.value, () => {
  currentPage.value = 1;
  loadData();
}, { deep: true });
</script>

<style scoped lang="less">
.claim-record-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  
  .page-header {
    margin-bottom: 16px;
    
    h1 {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
    }
  }
  
  .page-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.search-area {
  padding: 16px;
}

.status-tabs-container {
  margin-bottom: 16px;
  
  :deep(.arco-tabs-nav) {
    margin-bottom: 0;
  }
  
  :deep(.arco-tabs-nav-tab) {
    padding: 0;
  }
  
  .status-tab-title {
    margin-right: 4px;
  }
  
  .status-tab-count {
    display: inline-block;
    min-width: 16px;
    padding: 0 4px;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    background-color: #f2f3f5;
    border-radius: 8px;
  }
}
</style>

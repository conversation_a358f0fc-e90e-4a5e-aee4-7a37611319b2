<template>
  <div class="claim-container">
    <div class="page-content">
      <!-- 详情抽屉 -->
      <ClaimDetailDrawer
        v-model:visible="drawerVisible"
        :claim-data="currentClaim"
        @close="handleDrawerClose"
      />
      
      <!-- 认款申请弹窗 -->
      <ClaimApplicationModal
        v-model:visible="applicationModalVisible"
        :order-data="currentClaim"
        @submit="handleApplicationSubmit"
      />
      
      <!-- 认款备注弹窗 -->
      <ClaimRemarkModal
        v-model:visible="remarkModalVisible"
        :claim-data="currentClaim"
        @submit="handleRemarkSubmit"
      />
      
      <div class="search-area">
        <!-- 状态标签列表 -->
        <div class="status-tabs-container">
          <a-tabs type="line" :active-key="activeStatus" @change="selectStatusTab">
            <a-tab-pane v-for="tab in statusTabs" :key="tab.key" :title="tab.name">
              <template #title>
                <span class="status-tab-title">{{ tab.name }}</span>
                <span class="status-tab-count">{{ tab.count || 0 }}</span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </div>
        
        <!-- 搜索表单和表格 -->
        <ma-crud
          ref="crudRef"
          :options="crudOptions"
          :columns="columns"
          :search="search"
          v-model:search-params="searchParams"
          :data="tableData"
          :loading="loading"
        >
          <!-- 操作列 -->
          <template #operation="{ record }">
            <a-space>
              <!-- 待认款状态：显示认款申请和认款备注按钮 -->
              <template v-if="record.claimStatus === '待认款'">
                <a-link @click="handleApplyClaim(record)" type="primary">认款申请</a-link>
                <a-link @click="handleRemark(record)">认款备注</a-link>
              </template>
              
              <!-- 待审核、审核通过、无需认款、单据异常状态：显示查看详情和认款备注按钮 -->
              <template v-else-if="['待审核', '审核通过', '无需认款', '单据异常'].includes(record.claimStatus)">
                <a-link @click="handleView(record)">查看详情</a-link>
                <a-link @click="handleRemark(record)">认款备注</a-link>
              </template>
              
              <!-- 审核驳回状态：只显示查看详情按钮 -->
              <template v-else-if="record.claimStatus === '审核驳回'">
                <a-link @click="handleView(record)">查看详情</a-link>
              </template>
            </a-space>
          </template>
          
          <!-- 认款状态列 -->
          <template #claimStatus="{ record }">
            <a-tag :color="getClaimStatusColor(record.claimStatus)">
              {{ record.claimStatus }}
            </a-tag>
          </template>
        </ma-crud>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";
import ClaimDetailDrawer from "./components/ClaimDetailDrawer.vue";
import ClaimApplicationModal from "./components/ClaimApplicationModal.vue";
import ClaimRemarkModal from "./components/ClaimRemarkModal.vue";

// 定义页面路由元信息
definePageMeta({
  name: 'provider-finance-claim',
  title: "认款申请",
  icon: "icon-check-circle",
});

// 路由
const router = useRouter();

const route = useRoute();
// 抽屉可见性
const drawerVisible = ref(false);
const currentClaim = ref({});

// 认款申请弹窗可见性
const applicationModalVisible = ref(false);

// 认款备注弹窗可见性
const remarkModalVisible = ref(false);

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);
// 当前页码
const currentPage = ref(1);
// 每页条数
const pageSize = ref(10);
// 总条数
const total = ref(0);

// 当前选中的状态标签
const activeStatus = ref(route.query.activeStatus || 'all');

// 状态标签列表
const statusTabs = [
  { key: 'all', name: '全部', count: 0 },
  { key: 'pending', name: '待认款', count: 0 },
  { key: 'auditing', name: '待审核', count: 0 },
  { key: 'rejected', name: '审核驳回', count: 0 },
  { key: 'approved', name: '审核通过', count: 0 },
  { key: 'unnecessary', name: '无需认款', count: 0 },
  { key: 'abnormal', name: '单据异常', count: 0 },
];

// 表格配置
const crudOptions = reactive({
  rowKey: 'id',
  showIndex: true,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100],
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "下单时间",
    dataIndex: "orderTime",
    align: "center",
    width: 120,
    search: true,
    formType: "range",
    searchField: "orderTime",
    placeholder: "请选择下单时间",
  },
  {
    title: "认款状态",
    dataIndex: "claimStatus",
    align: "center",
    width: 100,
    slotName: "claimStatus",
    search: false,
    searchField: "claimStatus",
    component: "Select",
    componentProps: {
      placeholder: "请选择认款状态",
      options: [
        { label: "待认款", value: "待认款" },
        { label: "待审核", value: "待审核" },
        { label: "审核驳回", value: "审核驳回" },
        { label: "审核通过", value: "审核通过" },
        { label: "无需认款", value: "无需认款" },
        { label: "单据异常", value: "单据异常" },
      ],
    },
  },
  {
    title: "订单编号",
    dataIndex: "orderNo",
    align: "center",
    width: 150,
    search: true,
    formType: "input",
    searchField: "orderNo",
    placeholder: "请输入订单编号",
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    align: "center",
    width: 100,
    search: true,
    formType: "cascader",
    searchField: "orderSource",
    dict: {
      data: [
        { 
          value: '君网', 
          label: '君网', 
          children: [
            { value: '君网BG', label: '君网BG' },
            { value: '君网C端', label: '君网C端' },
          ] 
        },
        { value: '线下订单', label: '线下订单' },
      ],
    },
    dataIndex: "orderStatus",
    align: "center",
    width: 100,
    search: false,
    searchField: "orderStatus",
    component: "Select",
    componentProps: {
      placeholder: "请选择订单状态",
      options: [
        { label: "待付款", value: "待付款" },
        { label: "待发货", value: "待发货" },
        { label: "已发货", value: "已发货" },
        { label: "已完成", value: "已完成" },
        { label: "已取消", value: "已取消" },
      ],
    },
  },
  {
    title: "订单实付金额",
    dataIndex: "actualAmount",
    search: true,
    formType: "inputNumberrange",
    searchField: "actualAmount",
    align: "center",
    width: 120,
  },
  {
    title: "跟单员",
    dataIndex: "merchandiser",
    align: "center",
    width: 100,
    search: false,
    searchField: "merchandiser",
    placeholder: "请输入跟单员",
  },
  {
    title: "收货人",
    dataIndex: "receiver",
    align: "center",
    width: 100,
    search: true,
    formType: "input",
    searchField: "receiver",
    placeholder: "请输入收货人",
  },
  {
    title: "收货地址",
    search: true,
    formType: "input",
    searchField: "receiverAddress",
    dataIndex: "receiverAddress",
    align: "center",
    width: 200,
  },
  {
    title: "认款申请人",
    dataIndex: "applicant",
    search: false,
    align: "center",
    width: 100,
  },
  {
    title: "认款申请单号",
    dataIndex: "claimNo",
    align: "center",
    width: 120,
    search: true,
    searchField: "claimNo",
    placeholder: "请输入认款申请单号",
  },
  {
    title: "认款申请时间",
    dataIndex: "applyTime",
    align: "center",
    width: 120,
    search: false,
    searchField: "applyTime",
    component: "DatePicker",
    componentProps: {
      placeholder: "请选择认款申请时间",
      style: { width: "100%" },
    },
  },
  {
    title: "付款人名称",
    dataIndex: "payerName",
    align: "center",
    width: 120,
    search: true,
    formType: "input",
    searchField: "payerName",
    placeholder: "请输入付款人名称",
  },
  {
    title: "认款进度",
    dataIndex: "claimProgress",
    align: "center",
    width: 100,
    search: false,
    searchField: "claimProgress",
    component: "Select",
    componentProps: {
      placeholder: "请选择认款进度",
      options: [
        { label: "已付款", value: "已付款" },
        { label: "部分付款", value: "部分付款" },
        { label: "未付款", value: "未付款" },
      ],
    },
  },
  {
    title: "认款备注",
    dataIndex: "remark",
    align: "center",
    width: 200,
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 180,
    fixed: "right",
    slotName: "operation",
  },
]);

// 搜索表单配置 - 不再使用这种方式配置搜索项
const search = reactive({
  labelWidth: 80,
  formProps: {
    layout: "inline",
  },
  schemas: [],
});

// 查看详情
const handleView = (record) => {
  currentClaim.value = record;
  drawerVisible.value = true;
};

// 关闭抽屉
const handleDrawerClose = () => {
  currentClaim.value = {};
};

// 跳转到认款申请页面
const handleApplyClaim = (record) => {
  // 将订单数据存储到localStorage，以便在新页面中获取
  localStorage.setItem('claimOrderData', JSON.stringify(record));
  // 跳转到认款申请页面
  router.push(`/provider/finance/claim/application?orderId=${record.id}`);
};

// 提交认款申请
const handleApplicationSubmit = (data) => {
  console.log('提交认款申请', data);
  Message.success('认款申请提交成功');
  loadData();
};

// 打开认款备注弹窗
const handleRemark = (record) => {
  currentClaim.value = record;
  remarkModalVisible.value = true;
};

// 提交认款备注
const handleRemarkSubmit = (data) => {
  console.log('提交认款备注', data);
  Message.success('认款备注保存成功');
  loadData();
};

// 选择状态标签
const selectStatusTab = (key) => {
  console.log('状态标签变化：', key);
  activeStatus.value = key;
  
  // 更新搜索参数
  if (key === 'all') {
    searchParams.value.claimStatus = '';
  } else {
    searchParams.value.claimStatus = key;
  }
  
  // 重置分页到第一页
  currentPage.value = 1;
  
  // 重新加载数据
  loadData();
};

// 搜索参数
const searchParams = ref({
  orderTime: "",
  claimStatus: "", // 保留这个字段用于状态标签切换
  orderNo: "",
  merchantName: "",
  orderStatus: "",
  actualAmount: "",
  receiver: "",
  claimNo: "",
  claimTime: "",
  payerName: "",
});

// 获取认款状态颜色
const getClaimStatusColor = (status) => {
  switch (status) {
    case '待认款':
      return 'blue';
    case '待审核':
      return 'orange';
    case '审核驳回':
      return 'red';
    case '审核通过':
      return 'green';
    case '无需认款':
      return 'gray';
    case '单据异常':
      return 'magenta';
    default:
      return 'default';
  }
};

// 生成模拟数据
const getMockClaimData = () => {
  const statusMap = {
    'pending': '待认款',
    'auditing': '待审核',
    'rejected': '审核驳回',
    'approved': '审核通过',
    'unnecessary': '无需认款',
    'abnormal': '单据异常',
  };
  
  const orderSourceOptions = ['君网BG', '君网C端', '线下订单'];
  const orderStatusOptions = ['待付款', '待发货', '已发货', '已完成', '已取消'];
  const claimProgressOptions = ['已付款', '部分付款', '未付款'];
  
  // 生成单条认款数据
  const generateClaimItem = (index) => {
    const id = `CL${String(index + 1).padStart(6, '0')}`;
    const orderNo = `OR${String(Math.floor(Math.random() * 1000000)).padStart(6, '0')}`;
    const now = new Date();
    const orderTime = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    
    // 随机选择状态
    const statusKeys = Object.keys(statusMap);
    const statusKey = statusKeys[Math.floor(Math.random() * statusKeys.length)];
    const claimStatus = statusMap[statusKey];
    
    // 根据状态决定是否有申请信息
    const hasApplied = claimStatus !== '待认款';
    const applyTime = hasApplied ? new Date(orderTime.getTime() + Math.random() * 10 * 24 * 60 * 60 * 1000) : null;
    
    return {
      id,
      orderNo,
      orderTime: orderTime.toISOString().split('T')[0],
      merchantName: `商户${index + 1}`,
      orderSource: orderSourceOptions[Math.floor(Math.random() * orderSourceOptions.length)],
      orderStatus: orderStatusOptions[Math.floor(Math.random() * orderStatusOptions.length)],
      actualAmount: (Math.random() * 10000).toFixed(2),
      merchandiser: `跟单员${index % 5 + 1}`,
      receiver: `收货人${index % 10 + 1}`,
      receiverAddress: `北京市朝阳区朝阳路${index % 50 + 1}号`,
      claimStatus,
      claimNo: hasApplied ? `CL${String(Math.floor(Math.random() * 1000000)).padStart(6, '0')}` : '',
      applicant: hasApplied ? `申请人${index % 8 + 1}` : '',
      applyTime: hasApplied ? applyTime.toISOString().split('T')[0] : '',
      payerName: hasApplied ? `付款人${index % 15 + 1}` : '',
      claimProgress: hasApplied ? claimProgressOptions[Math.floor(Math.random() * claimProgressOptions.length)] : '',
      remark: Math.random() > 0.7 ? `认款备注信息${index}` : '',
      auditor: ['approved', 'rejected', 'unnecessary', 'abnormal'].includes(statusKey) ? `审核人${index % 3 + 1}` : '',
      auditTime: ['approved', 'rejected', 'unnecessary', 'abnormal'].includes(statusKey) && applyTime ? 
        new Date(applyTime.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0] : '',
      completeTime: ['approved', 'unnecessary', 'abnormal'].includes(statusKey) && applyTime ? 
        new Date(applyTime.getTime() + 48 * 60 * 60 * 1000).toISOString().split('T')[0] : '',
    };
  };
  
  return Array.from({ length: 100 }, (_, index) => generateClaimItem(index));
};

// 加载数据
const loadData = () => {
  console.log('加载数据');
  loading.value = true;
  
  // 生成模拟数据
  const allData = getMockClaimData();
  
  // 根据搜索参数过滤
  let filteredData = allData;
  
  // 如果有状态筛选
  if (activeStatus.value !== 'all') {
    const statusMap = {
      'pending': '待认款',
      'auditing': '待审核',
      'rejected': '审核驳回',
      'approved': '审核通过',
      'unnecessary': '无需认款',
      'abnormal': '单据异常',
    };
    filteredData = filteredData.filter(item => item.claimStatus === statusMap[activeStatus.value]);
  }
  
  // 其他搜索条件过滤
  Object.keys(searchParams.value).forEach(key => {
    if (searchParams.value[key] && key !== 'claimStatus') {
      // 处理金额范围筛选
      if (key === 'minAmount') {
        filteredData = filteredData.filter(item => {
          const amount = parseFloat(item.actualAmount);
          return amount >= parseFloat(searchParams.value.minAmount);
        });
      } else if (key === 'maxAmount') {
        filteredData = filteredData.filter(item => {
          const amount = parseFloat(item.actualAmount);
          return amount <= parseFloat(searchParams.value.maxAmount);
        });
      } else {
        filteredData = filteredData.filter(item => {
          if (typeof item[key] === 'string') {
            return item[key].includes(searchParams.value[key]);
          }
          return item[key] === searchParams.value[key];
        });
      }
    }
  });
  
  // 更新状态标签数量
  statusTabs.forEach(tab => {
    if (tab.key === 'all') {
      tab.count = allData.length;
    } else {
      const statusMap = {
        'pending': '待认款',
        'auditing': '待审核',
        'rejected': '审核驳回',
        'approved': '审核通过',
        'unnecessary': '无需认款',
        'abnormal': '单据异常',
      };
      tab.count = allData.filter(item => item.claimStatus === statusMap[tab.key]).length;
    }
  });
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  
  tableData.value = filteredData.slice(start, end);
  total.value = filteredData.length;
  crudOptions.pagination.total = filteredData.length;
  loading.value = false;
};

// 页面加载时获取数据
onMounted(() => {
  console.log('页面加载完成，开始获取数据');
  // 初始化表格数据
  loadData();
});

// 监听分页变化
watch([currentPage, pageSize], () => {
  loadData();
});

// 监听搜索参数变化
watch(() => searchParams.value, () => {
  currentPage.value = 1;
  loadData();
}, { deep: true });
</script>

<style scoped lang="less">
.claim-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  
  .page-header {
    margin-bottom: 16px;
    
    h1 {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
    }
  }
  
  .page-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.search-area {
  padding: 16px;
}

.status-tabs-container {
  margin-bottom: 16px;
  
  :deep(.arco-tabs-nav) {
    margin-bottom: 0;
  }
  
  :deep(.arco-tabs-nav-tab) {
    padding: 0;
  }
  
  .status-tab-title {
    margin-right: 4px;
  }
  
  .status-tab-count {
    display: inline-block;
    min-width: 16px;
    padding: 0 4px;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    background-color: #f2f3f5;
    border-radius: 8px;
  }
}
</style>

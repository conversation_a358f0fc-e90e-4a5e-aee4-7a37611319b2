<template>
  <div class="claim-remark-upload">
    <a-form-item field="remark" label="认款申请备注">
      <a-textarea 
        v-model="remark" 
        placeholder="请输入申请备注，最多不超过250个字" 
        :max-length="250" 
        show-word-limit 
        :auto-size="{ minRows: 4, maxRows: 6 }" 
        @change="handleRemarkChange"
      />
    </a-form-item>
    
    <a-form-item field="attachments" label="附件上传">
      <a-upload
        :custom-request="handleUploadAttachment"
        :file-list="fileList"
        :limit="5"
        @change="handleUploadChange"
        @remove="handleRemoveFile"
        multiple
      >
        <template #upload-button>
          <div class="upload-btn">
            <a-button>
              <template #icon>
                <icon-upload />
              </template>
              上传附件
            </a-button>
            <div class="upload-tip text-gray-500 text-xs mt-2">
              可支持png、jpg、jpeg、pdf文件，最多上传5个文件，文件最大5M
            </div>
          </div>
        </template>
      </a-upload>
    </a-form-item>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ remark: '', attachments: [] })
  }
});

const emit = defineEmits(['update:modelValue']);

// 备注内容
const remark = ref(props.modelValue.remark || '');

// 文件列表
const fileList = ref(props.modelValue.attachments || []);

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  remark.value = newVal.remark || '';
  fileList.value = newVal.attachments || [];
}, { deep: true });

// 处理备注变更
const handleRemarkChange = (value) => {
  updateModelValue();
};

// 自定义上传请求
const handleUploadAttachment = (options) => {
  const { file, onSuccess, onError } = options;
  
  // 模拟上传
  setTimeout(() => {
    // 检查文件类型和大小
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!validTypes.includes(file.type)) {
      Message.error('只支持 jpg、png、jpeg、pdf 格式的文件');
      onError();
      return;
    }
    
    if (file.size > maxSize) {
      Message.error('文件大小不能超过5MB');
      onError();
      return;
    }
    
    // 模拟成功上传
    const fileUrl = URL.createObjectURL(file);
    onSuccess({ url: fileUrl, name: file.name, uid: Date.now() });
  }, 500);
};

// 处理上传变化
const handleUploadChange = (fileList) => {
  updateModelValue();
};

// 移除文件
const handleRemoveFile = (file) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    updateModelValue();
  }
};

// 更新v-model值
const updateModelValue = () => {
  emit('update:modelValue', {
    remark: remark.value,
    attachments: fileList.value
  });
};
</script>

<style scoped lang="less">
.claim-remark-upload {
  .upload-btn {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>

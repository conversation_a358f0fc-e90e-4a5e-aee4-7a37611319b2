<template>
  <div class="claim-receipt-tabs">
    <div class="tab-header">
      <div 
        class="tab-item" 
        :class="{ 'active': activeTab === 'system' }"
        @click="switchTab('system')"
      >
        <div class="tab-icon">1</div>
        <span>关联系统收款记录</span>
      </div>
      <div 
        class="tab-item" 
        :class="{ 'active': activeTab === 'manual' }"
        @click="switchTab('manual')"
      >
        <div class="tab-icon">2</div>
        <span>手动添加收款记录</span>
      </div>
    </div>
    
    <div class="tab-content">
      <!-- 关联系统收款记录 -->
      <div v-if="activeTab === 'system'" class="system-records">
        <a-table :data="systemRecords" :bordered="true" :pagination="false">
          <template #columns>
            <a-table-column title="收款单号" data-index="receiptNo" align="center" />
            <a-table-column title="交易日期" data-index="tradeDate" align="center" />
            <a-table-column title="付款人名称" data-index="payerName" align="center" />
            <a-table-column title="付款人账号" data-index="payerAccount" align="center" />
            <a-table-column title="付款人开户行" data-index="payerBank" align="center" />
            <a-table-column title="交易流水号" data-index="tradeNo" align="center" />
            <a-table-column title="收款账号" data-index="receiverAccount" align="center" />
            <a-table-column title="收款银行" data-index="receiverBank" align="center" />
            <a-table-column title="收款金额" data-index="amount" align="center" />
            <a-table-column title="操作" align="center">
              <template #cell="{ record }">
                <a-link status="danger" @click="handleRemove(record)">删除</a-link>
              </template>
            </a-table-column>
          </template>
          <template #footer>
            <div class="table-footer">
              <div class="total-amount">
                <span class="label">合计：</span>
                <span class="value">{{ totalSystemAmount }}</span>
              </div>
            </div>
          </template>
        </a-table>
        <div class="empty-tip" v-if="!systemRecords.length">
          <div class="add-button">
            <a-button type="primary" @click="handleAddSystemRecord">添加系统收款记录</a-button>
          </div>
        </div>
      </div>
      
      <!-- 手动添加收款记录 -->
      <div v-if="activeTab === 'manual'" class="manual-records">
        <div class="empty-tip" v-if="!manualRecords.length">
          <a-empty description="请点击添加手动收款记录" />
          <div class="add-button">
            <a-button type="primary" @click="handleAddManualRecord">添加手动收款记录</a-button>
          </div>
        </div>
        <a-table v-else :data="manualRecords" :bordered="true" :pagination="false">
          <template #columns>
            <a-table-column title="收款单号" data-index="receiptNo" align="center" />
            <a-table-column title="交易日期" data-index="tradeDate" align="center" />
            <a-table-column title="付款人名称" data-index="payerName" align="center" />
            <a-table-column title="付款人账号" data-index="payerAccount" align="center" />
            <a-table-column title="付款人开户行" data-index="payerBank" align="center" />
            <a-table-column title="交易流水号" data-index="tradeNo" align="center" />
            <a-table-column title="收款金额" data-index="amount" align="center" />
            <a-table-column title="操作" align="center">
              <template #cell="{ record }">
                <a-link status="danger" @click="handleRemoveManual(record)">删除</a-link>
              </template>
            </a-table-column>
          </template>
          <template #footer>
            <div class="table-footer">
              <div class="total-amount">
                <span class="label">合计：</span>
                <span class="value">{{ totalManualAmount }}</span>
              </div>
            </div>
          </template>
        </a-table>
        <div class="add-more" v-if="manualRecords.length">
          <a-link @click="handleAddManualRecord">
            <icon-plus />
            添加更多收款记录
          </a-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 定义属性
const props = defineProps({
  systemRecords: {
    type: Array,
    default: () => []
  },
  manualRecords: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits([
  'update:activeTab', 
  'remove-system', 
  'remove-manual', 
  'add-system',
  'add-manual'
]);

// 当前激活的标签
const activeTab = ref('system');

// 切换标签
const switchTab = (tab) => {
  activeTab.value = tab;
  emit('update:activeTab', tab);
};

// 计算系统记录总金额
const totalSystemAmount = computed(() => {
  if (!props.systemRecords || props.systemRecords.length === 0) {
    return '0.00';
  }
  
  const total = props.systemRecords.reduce((sum, item) => {
    const amount = parseFloat(item.amount) || 0;
    return sum + amount;
  }, 0);
  
  return total.toFixed(2);
});

// 计算手动记录总金额
const totalManualAmount = computed(() => {
  if (!props.manualRecords || props.manualRecords.length === 0) {
    return '0.00';
  }
  
  const total = props.manualRecords.reduce((sum, item) => {
    const amount = parseFloat(item.amount) || 0;
    return sum + amount;
  }, 0);
  
  return total.toFixed(2);
});

// 删除系统记录
const handleRemove = (record) => {
  emit('remove-system', record);
};

// 删除手动记录
const handleRemoveManual = (record) => {
  emit('remove-manual', record);
};

// 添加系统记录
const handleAddSystemRecord = () => {
  emit('add-system');
};

// 添加手动记录
const handleAddManualRecord = () => {
  emit('add-manual');
};
</script>

<style scoped lang="less">
.claim-receipt-tabs {
  margin-bottom: 24px;
  
  .tab-header {
    display: flex;
    margin-bottom: 16px;
    border-bottom: 1px solid #e5e6eb;
    
    .tab-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      position: relative;
      margin-right: 24px;
      
      &.active {
        color: rgb(var(--primary-6));
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: rgb(var(--primary-6));
        }
      }
      
      .tab-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #f2f3f5;
        color: #4e5969;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        margin-right: 8px;
        
        .active & {
          background-color: rgb(var(--primary-6));
          color: #fff;
        }
      }
    }
  }
  
  .tab-content {
    .empty-tip {
      padding: 40px 0;
      text-align: center;
      
      .add-button {
        margin-top: 16px;
      }
    }
    
    .add-more {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
    
    .table-footer {
      display: flex;
      justify-content: flex-end;
      padding: 8px 16px;
      
      .total-amount {
        display: flex;
        align-items: center;
        
        .label {
          font-size: 14px;
          color: #1d2129;
          margin-right: 8px;
        }
        
        .value {
          font-size: 16px;
          font-weight: 500;
          color: #f53f3f;
        }
      }
    }
  }
}
</style>

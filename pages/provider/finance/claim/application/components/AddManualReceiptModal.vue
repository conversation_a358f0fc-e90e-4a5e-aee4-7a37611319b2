<template>
  <a-modal
    :visible="visible"
    :title="'选择收款记录'"
    :mask-closable="false"
    :unmount-on-close="false"
    @cancel="handleCancel"
    @before-ok="handleConfirm"
    width="600px"
  >
    <a-form :model="formData" ref="formRef" :rules="rules" layout="vertical">
      <div class="grid grid-cols-2 gap-4">
        <a-form-item field="payerName" label="付款人名称">
          <a-input v-model="formData.payerName" placeholder="请输入付款人..." allow-clear :max-length="20" />
          <div class="text-right text-xs text-gray-400">{{ formData.payerName?.length || 0 }} / 20</div>
        </a-form-item>
        
        <a-form-item field="payerAccount" label="付款人账号">
          <a-input v-model="formData.payerAccount" placeholder="请输入付款人..." allow-clear :max-length="20" />
          <div class="text-right text-xs text-gray-400">{{ formData.payerAccount?.length || 0 }} / 20</div>
        </a-form-item>
        
        <a-form-item field="amount" label="收款金额">
          <a-input v-model="formData.amount" placeholder="请输入收款金额" allow-clear :max-length="20" />
          <div class="text-right text-xs text-gray-400">{{ formData.amount?.length || 0 }} / 20</div>
        </a-form-item>
        
        <a-form-item field="payerBank" label="付款人开户行">
          <a-input v-model="formData.payerBank" placeholder="请输入付款人..." allow-clear :max-length="20" />
          <div class="text-right text-xs text-gray-400">{{ formData.payerBank?.length || 0 }} / 20</div>
        </a-form-item>
        
        <a-form-item field="tradeDate" label="交易日期">
          <a-date-picker
            v-model="formData.tradeDate"
            style="width: 100%"
            placeholder="请选择日期"
          />
        </a-form-item>
        
        <a-form-item field="receiptVoucher" label="收款凭证">
          <div class="flex items-center">
            <a-upload
              :file-list="fileList"
              :custom-request="handleUpload"
              @change="handleFileChange"
              :limit="1"
              :show-file-list="false"
            >
              <a-button type="outline">
                <template #icon>
                  <icon-upload />
                </template>
                上传收款凭证
              </a-button>
            </a-upload>
            <div class="ml-2 text-xs text-gray-400">
              可支持png、jpg、jpeg、pdf文件，最多上传1个文件，文件最大5M
            </div>
          </div>
        </a-form-item>
      </div>
    </a-form>
    
    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button @click="handleCancel">取 消</a-button>
        <a-button type="primary" @click="handleOk">确 认</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { IconUpload } from '@arco-design/web-vue/es/icon';
import { Message } from '@arco-design/web-vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'confirm']);

// 表单引用
const formRef = ref(null);

// 文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  payerName: '',
  payerAccount: '',
  amount: '',
  payerBank: '',
  tradeDate: null,
  receiptVoucher: null
});

// 表单验证规则
const rules = {
  // 这里所有字段都是可选的，根据图片显示
};

// 处理文件上传
const handleUpload = (options) => {
  const { file, onSuccess, onError } = options;
  
  // 文件类型验证
  const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'];
  if (!validTypes.includes(file.type)) {
    Message.error('只支持png、jpg、jpeg、pdf格式文件');
    onError();
    return;
  }
  
  // 文件大小验证（5MB）
  if (file.size > 5 * 1024 * 1024) {
    Message.error('文件大小不能超过5MB');
    onError();
    return;
  }
  
  // 模拟上传成功
  setTimeout(() => {
    formData.receiptVoucher = file;
    onSuccess();
  }, 500);
};

// 处理文件变更
const handleFileChange = (fileList) => {
  fileList.value = fileList;
};

// 取消
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 确认提交
const handleOk = async () => {
  // 格式化日期
  const formattedData = {
    ...formData,
    tradeDate: formData.tradeDate ? formatDate(formData.tradeDate) : '',
  };
  
  emit('confirm', formattedData);
  resetForm();
  emit('update:visible', false);
};

// 确认提交（原有方法保留，但不再使用）
const handleConfirm = async (done) => {
  try {
    const formattedData = {
      ...formData,
      tradeDate: formData.tradeDate ? formatDate(formData.tradeDate) : '',
    };
    
    emit('confirm', formattedData);
    resetForm();
    done();
  } catch (error) {
    done(false);
  }
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  formData.tradeDate = null;
  formData.receiptVoucher = null;
  fileList.value = [];
  formRef.value?.resetFields();
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  
  try {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    return '';
  }
};
</script>

<style scoped lang="less">
// 弹窗样式
.arco-form-item {
  margin-bottom: 0;
}

.arco-upload {
  display: inline-block;
}

.arco-modal-footer {
  border-top: 1px solid #e5e6eb;
  padding: 16px 20px;
}
</style>

<template>
  <a-drawer
    :visible="visible"
    :width="600"
    :title="'产品线详情 - ' + (productLineData?.relatedBrand || '')"
    @cancel="close"
    unmountOnClose
  >
    <template #footer>
      <div class="flex justify-end">
        <a-button @click="close">关闭</a-button>
      </div>
    </template>

    <div class="product-line-detail-container">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-title">基本信息</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">关联品牌</div>
            <div class="info-value">{{ productLineData?.relatedBrand || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">产品线类型</div>
            <div class="info-value">{{ productLineData?.productLineType || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">是否自主品牌</div>
            <div class="info-value">
              <a-tag :color="productLineData?.isOwnBrand ? 'green' : 'gray'">
                {{ productLineData?.isOwnBrand ? '是' : '否' }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">状态</div>
            <div class="info-value">
              <a-tag :color="getStatusColor(productLineData?.status)">
                {{ getStatusText(productLineData?.status) }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类信息 -->
      <div class="detail-section">
        <div class="section-title">分类信息</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">一级分类</div>
            <div class="info-value">{{ productLineData?.categoryLevel1 || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">二级分类</div>
            <div class="info-value">{{ productLineData?.categoryLevel2 || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">三级分类</div>
            <div class="info-value">{{ productLineData?.categoryLevel3 || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">平台</div>
            <div class="info-value">{{ getPlatformText(productLineData?.platform) }}</div>
          </div>
        </div>
      </div>

      <!-- 资质信息 -->
      <div class="detail-section">
        <div class="section-title">资质信息</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">资质到期时间</div>
            <div class="info-value">{{ productLineData?.qualificationExpireDate || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">资质状态</div>
            <div class="info-value">
              <a-tag :color="getStatusColor(productLineData?.status)">
                {{ getStatusText(productLineData?.status) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">创建时间</div>
            <div class="info-value">{{ productLineData?.createTime || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">更新时间</div>
            <div class="info-value">{{ productLineData?.updateTime || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="detail-section">
        <div class="section-title">操作日志</div>
        <a-timeline>
          <a-timeline-item v-for="(log, index) in getOperationLogs()" :key="index">
            <div class="log-item">
              <div class="log-time">{{ log.time }}</div>
              <div class="log-content">{{ log.content }}</div>
              <div class="log-operator">操作人：{{ log.operator }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  productLineData: {
    type: Object,
    default() {
      return null;
    }
  }
});

const emit = defineEmits(['update:visible', 'close']);

// 关闭抽屉
const close = () => {
  emit('update:visible', false);
  emit('close');
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    valid: "已生效",
    invalid: "无效",
    expired: "资质过期",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    valid: "green",
    invalid: "gray",
    expired: "red",
  };
  return colorMap[status] || "gray";
};

// 获取平台文本
const getPlatformText = (platform) => {
  const platformMap = {
    julingBG: "君网BG",
    jd: "京东",
    mall: "商城",
  };
  return platformMap[platform] || platform || "-";
};

// 获取操作日志（模拟数据）
const getOperationLogs = () => {
  const logs = [];
  if (!props.productLineData) return logs;
  
  const id = props.productLineData.id || '';
  const idNum = parseInt(id.replace(/\D/g, '') || '1');
  
  // 创建日期
  const createDate = props.productLineData.createTime || '2023-01-01';
  
  // 更新日期
  const updateDate = props.productLineData.updateTime || '2023-01-02';
  
  // 创建日志
  logs.push({
    time: `${createDate} 10:30:00`,
    content: '创建产品线',
    operator: '系统'
  });
  
  // 根据状态添加不同的日志
  if (props.productLineData.status === 'valid') {
    logs.push({
      time: `${updateDate} 15:45:00`,
      content: '产品线资质审核通过',
      operator: getAuditor(idNum)
    });
  } else if (props.productLineData.status === 'invalid') {
    logs.push({
      time: `${updateDate} 15:45:00`,
      content: '产品线资质审核不通过，原因：资质信息不完整',
      operator: getAuditor(idNum)
    });
  } else if (props.productLineData.status === 'expired') {
    // 先添加审核通过的日志
    logs.push({
      time: `${updateDate} 15:45:00`,
      content: '产品线资质审核通过',
      operator: getAuditor(idNum)
    });
    
    // 再添加过期的日志
    const expireDate = props.productLineData.qualificationExpireDate || '2023-12-31';
    logs.push({
      time: `${expireDate} 00:00:00`,
      content: '产品线资质已过期',
      operator: '系统'
    });
  }
  
  return logs;
};

// 获取审核人（模拟数据）
const getAuditor = (idNum) => {
  const auditors = ['张审核', '李审核', '王审核', '赵审核', '钱审核'];
  return auditors[idNum % auditors.length];
};
</script>

<style scoped lang="less">
.product-line-detail-container {
  padding: 0 16px;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f2f3f5;
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #165dff;
    border-radius: 2px;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #1d2129;
}

.log-item {
  margin-bottom: 8px;
}

.log-time {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.log-content {
  font-size: 14px;
  color: #1d2129;
  margin-bottom: 4px;
}

.log-operator {
  font-size: 12px;
  color: #86909c;
}
</style>

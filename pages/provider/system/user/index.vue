<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">
        {{ record.status == 1 ? '正常' : '停用' }}
      </template>
      <!-- 自定义列 - 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>
      <template #dept_id="{ record }">
        <div>{{ record.dept_name }}</div>
      </template>
      <template #role_id="{ record }">
        <div>{{ record.role_name }}</div>
      </template>
      
      <!-- <template #operationBeforeExtend="{ record }">
        <a-button type="text" size="small" @click="changePassword(record.id)">
            <template #icon><icon-refresh /></template>
            重置密码
          </a-button>
      </template> -->
     
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message,Modal } from "@arco-design/web-vue";
import systemApi from '@/api/master/system';
import MaButtonMenu from "~/components/master/layout/ma-buttonMenu.vue";
const userApi = systemApi.user;
definePageMeta({
  name: 'provider-system-user',
  path: '/provider/system/user'
})


const roleData = ref([]);
const deptData = ref([]);
const crudRef = ref();

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const res = await systemApi.role.getList();
   
    if (res.code == '200') {
      roleData.value = res.data.items || [];
    } 
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
};

// 获取部门列表
const fetchDeptList = async () => {
  try {
    const res = await systemApi.dept.getDeptList();
    if (res.code == '200') {
      deptData.value = res.data.items || [];
    }  
  } catch (error) {
    console.error('获取部门列表失败:', error);
  }
};

// 页面初始化
onMounted(() => {
  // 获取角色和部门数据
  fetchRoleList();
  fetchDeptList();
});

// CRUD配置
const crud = reactive({
  // API配置
  api: userApi.getList,
  showIndex: false,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  add: { show: true, api: userApi.create,},
  edit: { show: true, api: userApi.update },
  delete: {
      show: true,
      api: userApi.delete,
    },
      // 添加前处理参数
  beforeAdd: (params) => {
    console.log('添加前原始参数:', params); 
    params.deptId = params.dept_id;
    params.roleId = params.role_id;
    delete params.role_id;
    delete params.dept_id;
    delete params.created_at;
    return params;
  },


  // 编辑前处理参数
  beforeEdit: (params) => {
    console.log('编辑前原始参数:', params);
    params.deptId = params.dept_id;
    params.roleId = params.role_id;
    delete params.role_id;
    delete params.dept_id;
    delete params.created_at;
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: '用户ID',
    dataIndex: 'id',
    // addDisplay: false, 
    // editDisplay: false,
  },
  {
    title: '用户名称',
    dataIndex: 'username',
    search: true,
    // addDisplay: false, 
    // editDisplay: false,
    commonRules: [{ required: true, message: '用户名必填' }],
  },
  {
    title: '用户昵称',
    dataIndex: 'nickname',
    search: true,
    commonRules: [{ required: true, message: '用户昵称必填' }],
  },
  {
    title: '所属部门',
    dataIndex: 'dept_id',
    formType: 'select',
    commonRules: [{ required: true, message: '部门必选' }],
    dict: { data: deptData, props: { label: 'name', value: 'id' } },
    hide: true,
  },
  {
    title: '所属角色',
    dataIndex: 'role_id',
    formType: 'select',
    dict: { data: roleData, props: { label: 'name', value: 'id' } }
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    search: true,
    commonRules: [{required: true, match: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码' }]
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    search: true,
    commonRules: [{ required: true,type: 'email', message: '请输入正确的邮箱' }]
  },
  {
    title: '密码',
    dataIndex: 'password',
    formType: "input-password",
    search: false,
    hide: true,
  },
  {
    title: '状态', dataIndex: 'status', search: true, 
    formType: 'radio',
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 }
      ]
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    addDisplay: false, 
    editDisplay: false,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'center',
  },
]);

// 修改密码
const changePassword = async () => {
  Modal.info({
        title: '提示',
        content: '确定将该用户密码重置为 123456 吗？',
        simple: false,
        onBeforeOk: (done) => {
          resetPassword(id)
          done(true)
        }
      })
      return
};
const resetPassword = async (id) => {
    const response = await userApi.updatePassword({
      id: id,
      new_password: '123456'
    });
    if (response.code === 200) {
      Message.success('密码重置成功');
    } else {
      Message.error('密码重置失败');
    }
  }

</script>

<script>
export default { name: "provider-system-user" };
</script>

<style scoped></style>

<template>
  <div class="provider-join-wrapper">
    <!-- 顶部导航 -->
    <div class="join-header bg-blue-600 text-white w-full">
      <div class="container mx-auto px-4 max-w-screen-xl">
        <div class="flex justify-between items-center py-3">
          <div class="logo flex items-center">
            <a-image src="/logo.svg" alt="服务商入驻" class="h-8 mr-2" />
            <span class="text-xl font-bold">服务商招募</span>
            <span class="text-sm ml-2 opacity-80">诚邀全国</span>
          </div>
          <div class="nav-links">
            <div class="flex space-x-6">
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium">首页</a>
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium" @click="scrollToSection('process')">入驻流程</a>
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium" @click="scrollToSection('benefits')">入驻优势</a>
              <a class="text-white hover:text-yellow-200 cursor-pointer font-medium" @click="scrollToSection('faq')">常见问题</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主视觉区域 -->
    <div class="banner-section bg-blue-600 text-white w-full rounded-bl-3xl rounded-br-3xl">
      <div class="container mx-auto px-4 py-16 relative max-w-screen-xl">
        <!-- 背景图案 -->
        <div class="absolute inset-0 opacity-20">
          <div class="w-32 h-32 rounded-full bg-blue-400 absolute top-10 left-20"></div>
          <div class="w-48 h-48 rounded-full bg-blue-500 absolute bottom-10 right-40"></div>
          <div class="w-24 h-24 rounded-full bg-blue-300 absolute top-40 right-20"></div>
        </div>
        
        <div class="flex flex-col md:flex-row items-center relative z-10">
          <div class="md:w-1/2 mb-8 md:mb-0">
            <h1 class="text-5xl font-bold mb-4">服务商·能力共享计划</h1>
            <h2 class="text-4xl font-bold mb-6">入驻指南</h2>
            <p class="text-xl mb-8">成为平台认证服务商，共享亿级流量！</p>
            <div class="flex space-x-4">
              <a-button type="primary" size="large" class="bg-white text-blue-600 border-white hover:bg-gray-100 hover:text-blue-700 hover:border-gray-100" @click="goToApply">立即入驻</a-button>
              <a-button ghost size="large" class="border-white text-white hover:bg-blue-500 hover:border-white" @click="scrollToSection('process')">了解流程</a-button>
            </div>
            
            <div class="flex mt-10 space-x-4">
              <div class="bg-blue-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                专业服务认证
              </div>
              <div class="bg-blue-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                流量扶持政策
              </div>
              <div class="bg-blue-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                技能培训体系
              </div>
              <div class="bg-blue-500 bg-opacity-50 rounded-full px-4 py-2 text-sm">
                品牌增值服务
              </div>
            </div>
          </div>
          
          <div class="md:w-1/2 flex justify-center">
            <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm">
              <h3 class="text-center text-lg font-bold text-gray-800 mb-4">注册成为认证服务商</h3>
              <div class="flex justify-around mb-6 ">
                <div class="text-center ml-2 mr-2">
                  <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                    <i class="iconfont icon-hl-yonghu " style="font-size: 24px;"></i>
                  </div>
                  <p class="text-sm text-gray-600">海量用户</p>
                </div>
                <div class="text-center ml-2 mr-2">
                  <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                    <i class="iconfont icon-kaidian " style="font-size: 24px;"></i>
                  </div>
                  <p class="text-sm text-gray-600">便捷开店</p>
                </div>
                <div class="text-center ml-2 mr-2">
                  <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                    <i class="iconfont icon-chengzhang" style="font-size: 24px;"></i>
                  </div>
                  <p class="text-sm text-gray-600" >高效成长</p>
                </div>
              </div>
              <a-button type="primary" long class="bg-blue-600 border-blue-600 hover:bg-blue-700 hover:border-blue-700" @click="goToApply">免费入驻申请</a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 入驻流程与资质费用 -->
    <div id="process" class="section-container">
      <div class="container mx-auto px-4 py-16" style="max-width: 1200px;">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-6">快速入驻 成为认证服务商</h2>
        </div>
        
        <!-- 入驻流程内容 -->
        <div class="relative">
          <div class="flex flex-col md:flex-row step-container">
            <!-- 个人服务商卡片 -->
            <div 
              class="step-card rounded-lg shadow-md transition-all duration-500 ease-in-out relative" 
              :class="{'active': hoveredProcessCard === 'personal' || (!hoveredProcessCard && defaultProcessCard === 'personal')}"
              @mouseenter="hoveredProcessCard = 'personal'" 
              @mouseleave="hoveredProcessCard = ''"
              :style="{
                'background-image': (hoveredProcessCard === 'personal' || (!hoveredProcessCard && defaultProcessCard === 'personal')) ? 
                  'linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)), url(https://img12.360buyimg.com/imagetools/jfs/t1/151484/40/23384/446626/662b1030F2666097a/b4aabf4885557e27.png)' : 
                  'linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url(https://img13.360buyimg.com/imagetools/jfs/t1/232059/25/16153/69737/662b1030F3e9094f7/e2e492502d89c1d5.png)'
              }"
            >
              <div class="flex items-center mb-3">
                <i class="iconfont icon-geren mr-2" style="font-size: 30px;"></i>
                <div class="step-title mb-0">个人服务商</div>
              </div>
              <p class="text-gray-700 mb-3">适合个人提供专业技能服务</p>
              <div class="step-content" v-show="hoveredProcessCard === 'personal' || (!hoveredProcessCard && defaultProcessCard === 'personal')">
                <div class="step-desc">
                  <div class="flex items-center">
                    <span class="font-medium text-lg">个人服务商</span>
                    <span class="ml-auto text-xs text-blue-500">约1-2个工作日</span>
                  </div>
                  <div class="border-l-2 border-blue-300 pl-4 mt-3 ml-3">
                    <p class="text-blue-500 mb-4">入驻成功可享新人专属流量扶持</p>
                    <div class="mb-4">
                      <p class="font-medium mb-1"><span class="text-blue-600">01</span> <span class="text-gray-700">提交材料</span> <span class="text-xs text-blue-500">约30分钟</span></p>
                      <p class="text-gray-700">上传身份证件、资格证书等相关资质材料</p>
                    </div>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">02</span> 平台审核 <span class="text-xs text-blue-500">约1-2个工作日</span></p>
                      <p class="text-gray-700">平台进行资质审核</p>
                    </div>
                  </div>
                </div>
                
                <div class="step-action mt-4">
                  <a-button type="primary" size="small" @click="goToApply" class="bg-blue-600 border-blue-600 hover:bg-blue-700 hover:border-blue-700">立即入驻</a-button>
                </div>
              </div>
            </div>
            
            <!-- 企业服务商卡片 -->
            <div 
              class="step-card rounded-lg shadow-md transition-all duration-500 ease-in-out relative" 
              :class="{'active': hoveredProcessCard === 'enterprise' || (!hoveredProcessCard && defaultProcessCard === 'enterprise')}"
              @mouseenter="hoveredProcessCard = 'enterprise'" 
              @mouseleave="hoveredProcessCard = ''"
              :style="{
                'background-image': (hoveredProcessCard === 'enterprise' || (!hoveredProcessCard && defaultProcessCard === 'enterprise')) ? 
                  'linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)), url(https://img12.360buyimg.com/imagetools/jfs/t1/151484/40/23384/446626/662b1030F2666097a/b4aabf4885557e27.png)' : 
                  'linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url(https://img13.360buyimg.com/imagetools/jfs/t1/232059/25/16153/69737/662b1030F3e9094f7/e2e492502d89c1d5.png)'
              }"
            >
              <div class="flex items-center mb-3">
                <i class="iconfont icon-qiye mr-2" style="font-size: 30px;"></i>
                <div class="step-title mb-0">企业服务商</div>
              </div>
              <p class="text-gray-700 mb-3">适合企业提供专业服务解决方案</p>
              <div class="step-content" v-show="hoveredProcessCard === 'enterprise' || (!hoveredProcessCard && defaultProcessCard === 'enterprise')">
                <div class="step-desc">
                  <div class="flex items-center">
                    <span class="font-medium text-lg">企业服务商</span>
                    <span class="ml-auto text-xs text-blue-500">约2-3个工作日</span>
                  </div>
                  <div class="border-l-2 border-blue-300 pl-4 mt-3 ml-3">
                    <p class="text-blue-500 mb-4">入驻成功可享企业专属流量扶持政策</p>
                    <div class="mb-4">
                      <p class="font-medium mb-1"><span class="text-blue-600">01</span> <span class="text-gray-700">提交材料</span> <span class="text-xs text-blue-500">约30分钟</span></p>
                      <p class="text-gray-700">上传营业执照、资质证书等相关材料</p>
                    </div>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">02</span> 平台审核 <span class="text-xs text-blue-500">约2-3个工作日</span></p>
                      <p class="text-gray-700">平台进行资质审核</p>
                    </div>
                    <div class="mb-4">
                      <p class="text-gray-700 font-medium mb-1"><span class="text-blue-600">03</span> 签约授权 <span class="text-xs text-blue-500">约1个工作日</span></p>
                      <p class="text-gray-700">签署服务协议并完成授权</p>
                    </div>
                  </div>
                </div>
                
                <div class="step-action mt-4">
                  <a-button type="primary" size="small" @click="goToApply" class="bg-blue-600 border-blue-600 hover:bg-blue-700 hover:border-blue-700">立即入驻</a-button>
                </div>
              </div>
            </div>
          </div>
          
          <div class="mt-8 text-center">
            <a-button type="primary" size="large" @click="goToApply" class="bg-blue-600 border-blue-600 hover:bg-blue-700 hover:border-blue-700">立即申请入驻</a-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 入驻优势 -->
    <div id="benefits" class="section-container bg-gray-50">
      <div class="container mx-auto px-4 py-16" style="max-width: 1200px;">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-12">入驻优势</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="(benefit, index) in benefits" :key="index" class="benefit-card">
            <div class="bg-white rounded-lg shadow-md p-6 text-center h-full flex flex-col">
              <div class="icon-wrapper mb-4 flex justify-center">
                <component :is="benefit.icon" class="text-5xl text-blue-500" />
              </div>
              <h3 class="text-xl font-bold mb-2">{{ benefit.title }}</h3>
              <p class="text-gray-600 flex-grow">{{ benefit.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 入驻条件 -->
    <div id="requirements" class="section-container bg-gray-50">
      <div class="container mx-auto px-4 py-16" style="max-width: 1200px;">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-12">入驻条件</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="requirement-card">
            <div class="bg-white rounded-lg shadow-md p-6 h-full">
              <h3 class="text-xl font-bold mb-4 flex items-center">
                <icon-store class="mr-2 text-blue-500" />企业服务商条件
              </h3>
              <ul class="list-disc pl-5 space-y-2">
                <li v-for="(item, index) in enterpriseRequirements" :key="index">{{ item }}</li>
              </ul>
            </div>
          </div>
          <div class="requirement-card">
            <div class="bg-white rounded-lg shadow-md p-6 h-full">
              <h3 class="text-xl font-bold mb-4 flex items-center">
                <icon-user class="mr-2 text-blue-500" />个人服务商条件
              </h3>
              <ul class="list-disc pl-5 space-y-2">
                <li v-for="(item, index) in personalRequirements" :key="index">{{ item }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 常见问题 -->
    <div id="faq" class="section-container">
      <div class="container mx-auto px-4 py-16">
        <div class="section-title">
          <h2 class="text-3xl font-bold text-center mb-12">常见问题</h2>
        </div>
        <div class="faq-container">
          <a-collapse accordion>
            <a-collapse-item v-for="(item, index) in faqs" :key="index" :header="item.question">
              <p>{{ item.answer }}</p>
            </a-collapse-item>
          </a-collapse>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="footer-nav w-full bg-gray-800 text-white py-8 mt-12">
      <div class="container mx-auto px-4 max-w-screen-xl">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h4 class="text-lg font-bold mb-4">关于我们</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-white">公司简介</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white">发展历程</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white">联系我们</a></li>
            </ul>
          </div>
          
          <div>
            <h4 class="text-lg font-bold mb-4">服务商服务</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-white">入驻流程</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white">资质要求</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white">服务标准</a></li>
            </ul>
          </div>
          
          <div>
            <h4 class="text-lg font-bold mb-4">帮助中心</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-white">常见问题</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white">服务商学院</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white">运营指南</a></li>
            </ul>
          </div>
          
          <div>
            <h4 class="text-lg font-bold mb-4">联系方式</h4>
            <ul class="space-y-2">
              <li class="flex items-center"><i class="iconfont icon-phone mr-2"></i> ************</li>
              <li class="flex items-center"><i class="iconfont icon-mail mr-2"></i> <EMAIL></li>
              <li class="flex items-center"><i class="iconfont icon-location mr-2"></i> 北京市朝阳区大望路</li>
            </ul>
          </div>
        </div>
        
        <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>© 2025 服务商招募平台 版权所有 | 京ICP备XXXXXXXX号</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

definePageMeta({
  layout: 'empty'
});

// 入驻流程与资质费用
const activeTab = ref('process');

// 卡片悬停状态
const hoveredCard = ref('');
const defaultCard = ref('');

// 入驻流程卡片悬停状态
const hoveredProcessCard = ref('');
const defaultProcessCard = ref('');

// 组件挂载后设置默认展开的卡片
onMounted(() => {
  // 设置默认展开第一个卡片（个人服务商）
  defaultCard.value = 'personal';
  defaultProcessCard.value = 'personal';
});

const router = useRouter();

// 入驻优势数据
const benefits = ref([
  {
    icon: 'icon-fire',
    title: '流量曝光',
    description: '平台提供专属流量入口，帮助服务商快速获得高质量客户资源'
  },
  {
    icon: 'icon-dashboard',
    title: '品牌背书',
    description: '获得平台官方认证标识，提升服务可信度和品牌影响力'
  },
  {
    icon: 'icon-bulb',
    title: '技能培训',
    description: '免费参与平台专业培训，提升服务能力和专业水平'
  },
  {
    icon: 'icon-safe',
    title: '收益保障',
    description: '完善的服务保障体系，确保服务商权益，提供稳定收益'
  }
]);

// 入驻条件数据
const enterpriseRequirements = ref([
  '具有合法的企业营业执照，注册资本不低于50万元',
  '企业成立时间不少于2年，具有良好的服务记录',
  '具备相关行业资质证书和专业人员证书',
  '具有稳定的服务能力和唯一的服务优势',
  '无不良信用记录，未被列入经营异常名录'
]);

const personalRequirements = ref([
  '年满18周岁，具有完全民事行为能力',
  '持有有效的身份证件和相关专业资格证书',
  '具备相关行业经验和专业技能',
  '能够提供稳定的服务和良好的服务体验',
  '无不良信用记录，未被列入失信被执行人名单'
]);

// 常见问题数据
const faqs = ref([
  {
    question: '入驻需要多长时间？',
    answer: '从提交资料到审核通过，个人服务商一般需要1-2个工作日，企业服务商需要2-3个工作日。如资料齐全且符合要求，可能会更快。'
  },
  {
    question: '入驻需要缴纳费用吗？',
    answer: '平台入驻基础服务是免费的，但可能需要缴纳一定的质量保证金，具体金额根据服务类型和服务范围而定。'
  },
  {
    question: '可以同时提供多种服务吗？',
    answer: '可以，但需要提供相应服务类型的资质证明和专业能力证明。不同服务类型可能有不同的入驻要求和质量保证金标准。'
  },
  {
    question: '入驻后如何接单？',
    answer: '入驻成功后，您将获得服务商管理后台的访问权限，可以通过后台进行服务发布、订单管理、客户沟通等操作。'
  },
  {
    question: '如何提高服务的曝光率？',
    answer: '可以通过参与平台活动、优化服务信息、提升服务质量、获取更多好评、使用平台推广工具等方式提高服务曝光率和转化率。'
  }
]);

// 滚动到指定区域
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

// 跳转到申请页面
const goToApply = () => {
  router.push('/provider/join/apply');
};
</script>

<style scoped>
.provider-join-wrapper {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  min-height: 100vh;
}

.join-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.banner-section {
  position: relative;
  overflow: hidden;
  background-image: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
}

.section-container {
  scroll-margin-top: 80px;
}

.benefit-card:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

.process-steps {
  max-width: 800px;
  margin: 0 auto;
}

.requirement-card:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 步骤卡片容器样式 */
.step-container {
  display: flex;
  width: 100%;
  height: 400px; /* 固定高度 */
  gap: 12px;
  margin: 0 auto;
}

/* 步骤卡片样式 */
.step-card {
  flex: 1;
  position: relative;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.5s ease;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%; /* 确保高度不变 */
}

/* 卡片激活状态 */
.step-card.active {
  flex: 3;
}

/* 卡片标题 */
.step-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

/* 卡片标题在flex布局中 */
.step-title.mb-0 {
  margin-bottom: 0;
  margin-top: 0;
}

/* 卡片内容区 */
.step-content {
  margin-top: 10px;
}

/* 卡片描述文字 */
.step-desc {
  margin-bottom: 15px;
}

.step-desc p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 5px 0;
}

/* 卡片操作区 */
.step-action {
  margin-bottom: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .step-container {
    flex-direction: column;
    height: auto;
  }
  
  .step-card {
    margin-bottom: 15px;
    height: auto;
    min-height: 200px;
  }
  
  .step-card.active {
    flex: 1;
  }
}
</style>

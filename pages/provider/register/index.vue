<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";

definePageMeta({
  layout: false,
  name: "providerregister",
  path: "/provider/register",
  title: "服务商注册"
});

// 当前步骤
const currentStep = ref(1);

// 表单引用
const basicFormRef = ref(null);
const companyFormRef = ref(null);

// 基础信息表单数据
const basicForm = reactive({
  username: "",
  nickname: "",
  password: "",
  confirmPassword: "",
  email: "",
  phone: "",
  verificationCode: ""
});

// 企业信息表单数据
const companyForm = reactive({
  // 基本信息
  companyName: "",

  // 营业执照
  businessLicense: "",
  businessLicenseList: [],

  // 法人身份证
  idCardFrontList: [],
  idCardBackList: [],

  // 纳税评级
  taxLevel: "",
  taxCertificateList: [],

  // 办公地址
  province: "",
  detailAddress: "",

  // 联系人信息
  contacts: [
    {
      position: "", // 职务
      name: "", // 姓名
      phone: "", // 电话
      email: "" // 邮箱
    }
  ],

  // 银行信息
  bankName: "", // 开户银行
  bankBranch: "", // 开户网点
  bankAccount: "", // 对公银行账号
  bankCertificateList: [] // 开户证明
});

// 处理营业执照上传
const handleBusinessLicenseChange = fileList => {
  companyForm.businessLicenseList = fileList;
  if (fileList && fileList.length > 0) {
    companyForm.businessLicense = fileList[0].url || "";
  } else {
    companyForm.businessLicense = "";
  }
};

// 处理法人身份证正面上传
const handleIdCardFrontChange = fileList => {
  companyForm.idCardFrontList = fileList;
};

// 处理法人身份证背面上传
const handleIdCardBackChange = fileList => {
  companyForm.idCardBackList = fileList;
};

// 处理纳税评级证明上传
const handleTaxCertificateChange = fileList => {
  companyForm.taxCertificateList = fileList;
};

// 处理银行开户证明上传
const handleBankCertificateChange = fileList => {
  companyForm.bankCertificateList = fileList;
};

// 添加联系人
const addContact = () => {
  companyForm.contacts.push({
    position: "",
    name: "",
    phone: "",
    email: ""
  });
};

// 删除联系人
const removeContact = index => {
  if (companyForm.contacts.length > 1) {
    companyForm.contacts.splice(index, 1);
  } else {
    Message.warning("至少需要保留一个联系人");
  }
};

// 短信验证码相关
const countdown = ref(0);
const timer = ref(null);

// 获取短信验证码
const getVerificationCode = async () => {
  if (countdown.value > 0) return;

  // 验证手机号
  if (!basicForm.phone || !/^1[3-9]\d{9}$/.test(basicForm.phone)) {
    Message.error("请输入正确的手机号");
    return;
  }

  try {
    // 这里应该调用发送短信验证码的API
    // 模拟发送成功
    countdown.value = 60;
    Message.success("验证码已发送到您的手机");

    // 倒计时
    timer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer.value);
      }
    }, 1000);
  } catch (error) {
    console.error("发送验证码失败:", error);
    Message.error("发送验证码失败，请稍后重试");
  }
};

// 下一步
const nextStep = async () => {
  try {
    if (currentStep.value === 1) {
      // 验证基础信息表单
      if (basicFormRef.value) {
        await basicFormRef.value.validate();
      } else {
        // 如果表单引用不存在，直接跳过验证
        console.warn("表单引用不存在，跳过验证");
      }

      // 验证密码是否一致
      if (basicForm.password !== basicForm.confirmPassword) {
        Message.error("两次输入的密码不一致");
        return;
      }

      // 切换到下一步
      currentStep.value = 2;
      console.log("切换到企业信息步骤，当前步骤：", currentStep.value);
    } else if (currentStep.value === 2) {
      // 验证企业信息表单
      if (companyFormRef.value) {
        await companyFormRef.value.validate();
      } else {
        // 如果表单引用不存在，直接跳过验证
        console.warn("表单引用不存在，跳过验证");
      }

      // 模拟提交注册信息
      // 这里应该调用注册的API

      // 切换到完成步骤
      currentStep.value = 3;
      console.log("切换到完成注册步骤，当前步骤：", currentStep.value);
    }
  } catch (error) {
    console.error("表单验证失败:", error);
    Message.error("表单验证失败，请检查必填项");
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
    console.log("返回上一步，当前步骤：", currentStep.value);
  }
};

// 返回登录页
const goToLogin = () => {
  navigateTo("/provider/login");
};

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});
</script>
<template>
  <div class="register-container">
    <div class="register" v-if="currentStep===1">注册新账号</div>
    <div  v-if="currentStep===2" class="Improve">完善企业信息</div>
    <div class="register-content">
      <!-- 步骤条 -->
      <!-- <a-steps :current="currentStep" label-placement="vertical" style="margin-bottom: 20px;">
        <a-step title="基础信息" />
        <a-step title="企业信息" />
        <a-step title="完成注册" />
      </a-steps> -->

      <!-- 表单内容 -->
      <div class="form-container">
        <!-- 基础信息表单 -->
        <div v-show="currentStep === 1" class="form-step">
          <a-form auto-label-width :model="basicForm" layout="horizontal" ref="basicFormRef">
            <a-form-item
              field="username"
              label="用户名"
              :rules="[{ required: true, message: '用户名必填' }]"
            >
              <a-input v-model="basicForm.username" placeholder="请输入登录账号" />
              <div class="form-tip">登录账号将作为您的唯一标识</div>
            </a-form-item>
            <a-form-item
              field="password"
              label="登录密码"
              :rules="[{ required: true, message: '登录密码必填' }]"
            >
              <a-input-password v-model="basicForm.password" placeholder="********" />
            </a-form-item>

            <a-form-item
              field="confirmPassword"
              label="确认密码"
              :rules="[{ required: true, message: '确认密码必填' }]"
            >
              <a-input-password v-model="basicForm.confirmPassword" placeholder="请再一次输入登录密码" />
            </a-form-item>
            <!-- <a-form-item field="nickname" label="昵称" :rules="[{ required: true, message: '昵称必填' }]">
              <a-input v-model="basicForm.nickname" placeholder="请输入昵称" />
            </a-form-item> -->
            <!-- <a-form-item field="password" label="密码" :rules="[{ required: true, message: '密码必填' }]">
              <a-input-password v-model="basicForm.password" placeholder="请输入密码" />
            </a-form-item> -->

            <!-- <a-form-item field="email" label="邮箱">
              <a-input v-model="basicForm.email" placeholder="请输入邮箱地址" />
            </a-form-item> -->

            <a-form-item field="phone" label="手机号" :rules="[{ required: true, message: '手机号必填' }]">
              <a-input v-model="basicForm.phone" placeholder="请输入手机号码" />
            </a-form-item>

            <a-form-item
              field="verificationCode"
              label="短信验证码"
              :rules="[{ required: true, message: '短信验证码必填' }]"
            >
              <div class="verification-code">
                <a-input
                  v-model="basicForm.verificationCode"
                  placeholder="请输入短信验证码"
                  style="width:371px"
                />
                <a-button
                  type="primary"
                  class="get-code-btn"
                  @click="getVerificationCode"
                  :disabled="countdown > 0"
                >{{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}</a-button>
              </div>
            </a-form-item>

          

            <div class="form-actions" style="justify-content: flex-end;">
              <!-- <a-button @click="goToLogin" style="margin-right: 20px;">返回登录</a-button> -->
              <a-button type="primary" @click="nextStep" style="width:502px">立即注册</a-button>
            </div>
          </a-form>
        </div>

        <!-- 企业信息表单 -->
        <div v-show="currentStep === 2" class="form-step">
      
          <a-form auto-label-width :model="companyForm" layout="horizontal" ref="companyFormRef">
            <a-form-item
              field="companyName"
              label="企业名称"
              :rules="[{ required: true, message: '企业名称必填' }]"
            >
              <a-input v-model="companyForm.companyName" placeholder="请输入企业名称" />
            </a-form-item>

            <!-- 1. 上传营业执照 -->
            <a-form-item
              field="businessLicense"
              label="营业执照"
              :rules="[{ required: true, message: '营业执照必填' }]"
            >
              <a-upload
                list-type="picture-card"
                :limit="1"
                :file-list="companyForm.businessLicenseList"
                @change="handleBusinessLicenseChange"
                class="custom-upload"
              >
                <template #upload-button>
                  <div class="upload-placeholder">
                    <icon-plus class="upload-icon" />
                    <div class="upload-text">营业执照</div>
                  </div>
                </template>
              </a-upload>
              <div class="upload-tip">请上传营业执照正本的扫描件或清晰照片</div>
            </a-form-item>

            <!-- 2. 上传法人身份证 -->
            <a-form-item
              field="legalIdCard"
              label="法人身份证"
              :rules="[{ required: true, message: '法人身份证必填' }]"
            >
              <div class="id-card-upload">
                <a-upload
                  list-type="picture-card"
                  :limit="1"
                  :file-list="companyForm.idCardFrontList"
                  @change="handleIdCardFrontChange"
                  class="custom-upload"
                >
                  <template #upload-button>
                    <div class="upload-placeholder">
                      <icon-plus class="upload-icon" />
                      <div class="upload-text">人像面</div>
                    </div>
                  </template>
                </a-upload>
                <a-upload
                  list-type="picture-card"
                  :limit="1"
                  :file-list="companyForm.idCardBackList"
                  @change="handleIdCardBackChange"
                  class="custom-upload"
                >
                  <template #upload-button>
                    <div class="upload-placeholder">
                      <icon-plus class="upload-icon" />
                      <div class="upload-text">国徽面</div>
                    </div>
                  </template>
                </a-upload>
              </div>
            </a-form-item>

            <!-- 3. 选择纳税评级证明等级 -->
            <a-form-item
              field="taxLevel"
              label="纳税评级等级"
              :rules="[{ required: true, message: '请选择纳税评级等级' }]"
            >
              <a-radio-group v-model="companyForm.taxLevel">
                <a-radio value="A">A</a-radio>
                <a-radio value="B">B</a-radio>
                <a-radio value="C">C</a-radio>
                <a-radio value="D">D</a-radio>
                <a-radio value="E">E</a-radio>
                <a-radio value="M">M</a-radio>
              </a-radio-group>
            </a-form-item>

            <!-- 4. 上传评级证明 -->
            <a-form-item
              field="taxCertificate"
              label="评级证明"
              :rules="[{ required: true, message: '评级证明必填' }]"
            >
              <a-upload
                list-type="picture-card"
                :limit="1"
                :file-list="companyForm.taxCertificateList"
                @change="handleTaxCertificateChange"
                class="custom-upload"
              >
                <template #upload-button>
                  <div class="upload-placeholder">
                    <icon-plus class="upload-icon" />
                    <div class="upload-text">评级证明</div>
                  </div>
                </template>
              </a-upload>
            </a-form-item>

            <!-- 5. 办公地址 -->
            <a-form-item
              field="officeAddress"
              label="办公地址"
              :rules="[{ required: true, message: '办公地址必填' }]"
            >
              <a-select
                v-model="companyForm.province"
                placeholder="省市区"
                style="width: 100%; margin-bottom: 10px"
              >
                <a-option value="北京市">北京市</a-option>
                <a-option value="上海市">上海市</a-option>
                <a-option value="广东省">广东省</a-option>
                <!-- 更多省份选项 -->
              </a-select>
              <a-input v-model="companyForm.detailAddress" placeholder="详细地址" />
            </a-form-item>

            <!-- 6. 联系人 -->
            <a-form-item
              field="contacts"
              label="联系人"
              :rules="[{ required: true, message: '至少添加一个联系人' }]"
            >
              <div class="contacts-list">
                <div
                  v-for="(contact, index) in companyForm.contacts"
                  :key="index"
                  class="contact-item"
                >
                  <div class="contact-header">
                    <span>联系人 {{index + 1}}</span>
                    <a-button
                      v-if="index > 0"
                      type="text"
                      status="danger"
                      @click="removeContact(index)"
                    >
                      <template #icon>
                        <icon-delete />
                      </template>
                      删除
                    </a-button>
                  </div>
                  <div class="contact-form">
                    <a-form-item
                      field="contacts[${index}].position"
                      label="职务"
                      :rules="[{ required: true, message: '职务必填' }]"
                    >
                      <a-input v-model="contact.position" placeholder="职务" />
                    </a-form-item>
                    <a-form-item
                      field="contacts[${index}].name"
                      label="姓名"
                      :rules="[{ required: true, message: '姓名必填' }]"
                    >
                      <a-input v-model="contact.name" placeholder="姓名" />
                    </a-form-item>
                    <a-form-item
                      field="contacts[${index}].phone"
                      label="电话"
                      :rules="[{ required: true, message: '电话必填' }]"
                    >
                      <a-input v-model="contact.phone" placeholder="电话" />
                    </a-form-item>
                    <a-form-item field="contacts[${index}].email" label="邮箱">
                      <a-input v-model="contact.email" placeholder="邮箱" />
                    </a-form-item>
                  </div>
                </div>
                <div class="add-contact">
                  <a-button type="outline" @click="addContact">
                    <template #icon>
                      <icon-plus />
                    </template>
                    添加联系人
                  </a-button>
                </div>
              </div>
            </a-form-item>

            <!-- 7. 银行信息 -->
            <a-form-item
              field="bankInfo"
              label="银行信息"
              :rules="[{ required: true, message: '银行信息必填' }]"
            >
              <a-form-item
                field="bankName"
                label="开户银行"
                :rules="[{ required: true, message: '开户银行必填' }]"
              >
                <a-input v-model="companyForm.bankName" placeholder="请输入开户银行" />
              </a-form-item>
              <a-form-item
                field="bankBranch"
                label="开户网点"
                :rules="[{ required: true, message: '开户网点必填' }]"
              >
                <a-input v-model="companyForm.bankBranch" placeholder="请输入开户网点" />
              </a-form-item>
              <a-form-item
                field="bankAccount"
                label="对公银行账号"
                :rules="[{ required: true, message: '对公银行账号必填' }]"
              >
                <a-input v-model="companyForm.bankAccount" placeholder="请输入对公银行账号" />
              </a-form-item>
              <a-form-item
                field="bankCertificate"
                label="开户证明"
                :rules="[{ required: true, message: '开户证明必填' }]"
              >
                <a-upload
                  list-type="picture-card"
                  :limit="1"
                  :file-list="companyForm.bankCertificateList"
                  @change="handleBankCertificateChange"
                  class="custom-upload"
                >
                  <template #upload-button>
                    <div class="upload-placeholder">
                      <icon-plus class="upload-icon" />
                      <div class="upload-text">开户证明</div>
                    </div>
                  </template>
                </a-upload>
              </a-form-item>
            </a-form-item>

            <div class="form-actions">
              <a-button @click="prevStep">上一步</a-button>
              <a-button type="primary" @click="nextStep">提交</a-button>
            </div>
          </a-form>
        </div>

        <!-- 完成注册 -->
        <div v-show="currentStep === 3" class="form-step">
          <div class="register-success">
            <icon-check-circle class="success-icon" />
            <h2>注册成功</h2>
            <p>您的服务商账号已创建成功，请等待平台资质认证审核</p>
            <div class="form-actions" style="justify-content:flex-end">
              <!-- <a-button @click="prevStep">上一步</a-button> -->
              <a-button type="primary" @click="goToLogin">返回登录</a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  flex-direction: column;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}
.register{
    position: absolute;
    font-size: 20px;
    top: 173px;
    left: 563px;
    font-weight: 400;
}

.Improve{
  margin-left: -675px;
    font-size: 20px;
    font-weight: 400;
    margin-bottom: 15px;
}

.register-content {
  width: 100%;
  max-width: 800px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.steps-wrapper {
  margin-bottom: 40px;

  :deep(.arco-steps-item-title) {
    font-weight: 500;
  }
  ::v-deep .arco-steps-item-node {
    display: inline-block;
    // margin-right: 12px;
    font-weight: 500;
    font-size: 16px;
    // vertical-align: top;
  }
}
::v-deep .arco-form-item-content-flex {
  display: flex;
  align-items: flex-start;
  justify-items: flex-start;
  flex-direction: column;
}
.step-icon {
  background-color: #e8f3ff;
  color: #4080ff;

  &.active {
    background-color: #4080ff;
    color: #fff;
  }
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.form-step {
  min-height: 400px;
  display: flex;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.verification-code {
  display: flex;
  gap: 10px;

  .get-code-btn {
    white-space: nowrap;
    min-width: 120px;
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.id-card-upload {
  display: flex;
  gap: 20px;
}

.contacts-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  width: 100%;
  //   background-color: #fafafa;
}

.contact-item {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e5e6eb;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
}

.contact-form {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
}

.add-contact {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 自定义上传组件样式 */
.custom-upload {
  :deep(.arco-upload-picture-card) {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
    transition: border-color 0.3s;

    &:hover {
      border-color: #4080ff;
    }
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    color: #999;
    border: 1px dashed;
  }

  .upload-icon {
    font-size: 24px;
    color: #c0c4cc;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 12px;
    color: #909399;
  }
}

.register-success {
  text-align: center;
  padding: 40px 0;

  .success-icon {
    font-size: 60px;
    color: #00b42a;
    margin-bottom: 16px;
  }

  h2 {
    font-size: 24px;
    margin-bottom: 16px;
    color: #1d2129;
  }

  p {
    font-size: 16px;
    color: #4e5969;
    margin-bottom: 30px;
  }
}

:deep(.arco-form-item-label-col) {
  label::before {
    margin-right: 4px;
  }
}
</style>

<template>
  <div class="font-sans">
    <div>
      <!-- 导航栏 -->
      <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <div class="flex items-center">
                <img
                  :src="logoImage"
                  width="160"
                  height="32"
                  fit="contain"
                  class="cursor-pointer md:w-[200px]"
                />
              </div>
            </div>
            
            <!-- 移动端菜单按钮 -->
            <div class="flex items-center md:hidden">
              <a-button class="mr-2" type="text" @click="showMobileMenu = !showMobileMenu">
                <template #icon>
                  <icon-menu />
                </template>
              </a-button>
            </div>
            
            <!-- 桌面端导航 -->
            <div class="hidden md:flex items-center space-x-4">
              <a href="#home" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">首页</a>
              <a href="#process" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">入驻流程</a>
              <a href="#benefits" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">入驻优势</a>
              <a href="#contact" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">联系我们</a>
              <a-button
                type="primary"
                class="bg-gradient-to-r from-red-500 to-orange-500 border-none font-semibold"
                @click="showLoginModal"
              >登录</a-button>
              <a-button
                class="border-orange-500 text-orange-500 hover:bg-gradient-to-r hover:from-red-500 hover:to-orange-500 hover:text-white hover:border-none font-semibold"
                @click="showRegisterModal"
              >注册</a-button>
            </div>
          </div>
        </div>
        
        <!-- 移动端下拉菜单 -->
        <div v-if="showMobileMenu" class="md:hidden bg-white shadow-lg py-2 px-4 absolute w-full">
          <div class="flex flex-col space-y-2">
            <a href="#home" class="text-gray-700 hover:text-red-600 py-2 block" @click="showMobileMenu = false">首页</a>
            <a href="#process" class="text-gray-700 hover:text-red-600 py-2 block" @click="showMobileMenu = false">入驻流程</a>
            <a href="#benefits" class="text-gray-700 hover:text-red-600 py-2 block" @click="showMobileMenu = false">入驻优势</a>
            <a href="#contact" class="text-gray-700 hover:text-red-600 py-2 block" @click="showMobileMenu = false">联系我们</a>
            <div class="flex space-x-2 py-2">
              <a-button
                type="primary"
                class="bg-gradient-to-r from-red-500 to-orange-500 border-none font-semibold flex-1"
                @click="showLoginModal(); showMobileMenu = false"
              >登录</a-button>
              <a-button
                class="border-orange-500 text-orange-500 hover:bg-gradient-to-r hover:from-red-500 hover:to-orange-500 hover:text-white hover:border-none font-semibold flex-1"
                @click="showRegisterModal(); showMobileMenu = false"
              >注册</a-button>
            </div>
          </div>
        </div>
      </nav>

      <!-- 主页面内容 -->
      <div id="mainContent">
        <!-- Hero Section -->
        <section
          id="home"
          class="min-h-screen flex items-center pt-16 pb-8"
          :style="{ backgroundImage: `url(${bgImage})`, backgroundRepeat: 'no-repeat', backgroundPosition: 'center center', backgroundSize: 'cover' }"
        >
          <div class="relative z-10 w-full">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
              <div class="flex flex-col lg:flex-row items-center justify-between">
                <div class="w-full lg:w-1/2 text-white mb-8 lg:mb-0 text-center lg:text-left">
                  <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                    来平台
                    <br />
                    <span class="text-yellow-300">共赢成功</span>
                  </h1>
                  <p class="text-lg sm:text-xl mb-8 opacity-90">携手优质服务商，共建生态圈，实现互利共赢</p>
                  <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start">
                    <a-button
                      type="warning"
                      size="large"
                      class="text-base sm:text-lg font-semibold px-8 w-full sm:w-auto"
                      @click="showRegisterModal"
                    >立即入驻</a-button>
                    <a-button
                      type="warning"
                      size="large"
                      class="text-base sm:text-lg font-semibold px-8 w-full sm:w-auto text-white bg-gradient-to-r from-red-500 to-orange-500 text-white border-none"
                      @click="scrollToSection('process')"
                    >了解流程</a-button>
                  </div>
                </div>
                <div class="w-full lg:w-1/2 flex justify-center mt-8 lg:mt-0">
                  <div class="bg-white rounded-2xl p-4 sm:p-8 shadow-2xl max-w-md w-full">
                    <div class="text-center mb-6">
                      <h2 class="text-xl sm:text-2xl font-bold text-gray-800 mb-2">快速入驻</h2>
                      <p class="text-gray-600 text-sm sm:text-base">加入我们，开启商业新篇章</p>
                      <div class="grid grid-cols-3 gap-2 sm:gap-4 mt-6">
                        <div class="text-center">
                          <div class="text-xl sm:text-2xl font-bold text-red-600 mb-1">1000+</div>
                          <span class="text-xs sm:text-sm text-gray-600">合作伙伴</span>
                        </div>
                        <div class="text-center">
                          <div class="text-xl sm:text-2xl font-bold text-red-600 mb-1">98%</div>
                          <span class="text-xs sm:text-sm text-gray-600">满意度</span>
                        </div>
                        <div class="text-center">
                          <div class="text-xl sm:text-2xl font-bold text-red-600 mb-1">24H</div>
                          <span class="text-xs sm:text-sm text-gray-600">共同成长</span>
                        </div>
                      </div>
                    </div>
                    <a-button
                      type="primary"
                      long
                      size="large"
                      class="bg-gradient-to-r from-red-600 to-orange-500"
                      @click="showRegisterModal"
                    >立即入驻</a-button>
                    <p class="text-xs sm:text-sm text-gray-500 text-center mt-4">成为优质服务商 共创美好</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 入驻流程 Section -->
        <section id="process" class="py-12 sm:py-20 bg-gray-50">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-10 sm:mb-16">
              <h2 class="text-3xl sm:text-4xl font-bold text-gray-800 mb-3 sm:mb-4">入驻流程</h2>
              <p class="text-lg sm:text-xl text-gray-600">简单四步，快速入驻</p>
            </div>
            <!-- 步骤列表 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 mb-10 sm:mb-16">
              <!-- 步骤 1 -->
              <div class="text-center relative step-line">
                <div
                  class="w-14 h-14 sm:w-16 sm:h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4"
                >
                  <span class="text-xl sm:text-2xl font-bold text-white">1</span>
                </div>
                <h3 class="text-lg sm:text-xl font-bold text-gray-800 mb-2">账号注册</h3>
                <p class="text-sm sm:text-base text-gray-600">使用可在线编辑手机号，快速注册优质服务商账号</p>
                <div class="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-500">约需 2 分钟</div>
              </div>
              <!-- 步骤 2 -->
              <div class="text-center relative step-line">
                <div
                  class="w-14 h-14 sm:w-16 sm:h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4"
                >
                  <span class="text-xl sm:text-2xl font-bold text-white">2</span>
                </div>
                <h3 class="text-lg sm:text-xl font-bold text-gray-800 mb-2">提交资料</h3>
                <p class="text-sm sm:text-base text-gray-600">填写企业信息、产品信息、经营信息</p>
                <div class="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-500">预计 2 个工作日</div>
              </div>
              <!-- 步骤 3 -->
              <div class="text-center relative step-line">
                <div
                  class="w-14 h-14 sm:w-16 sm:h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4"
                >
                  <span class="text-xl sm:text-2xl font-bold text-white">3</span>
                </div>
                <h3 class="text-lg sm:text-xl font-bold text-gray-800 mb-2">等待审核</h3>
                <p class="text-sm sm:text-base text-gray-600">平台审核信息及初审，机构进行复审核</p>
                <div class="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-500">预计 5 个工作日</div>
              </div>
              <!-- 步骤 4 -->
              <div class="text-center relative step-line">
                <div
                  class="w-14 h-14 sm:w-16 sm:h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4"
                >
                  <span class="text-xl sm:text-2xl font-bold text-white">4</span>
                </div>
                <h3 class="text-lg sm:text-xl font-bold text-gray-800 mb-2">开始合作</h3>
                <p class="text-sm sm:text-base text-gray-600">审核通过后即可开始合作，实现共赢</p>
                <div class="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-500">立即开始</div>
              </div>
            </div>
            <!-- 详细说明卡片 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mt-10 sm:mt-16">
              <!-- 账号注册 -->
              <div class="bg-white p-4 sm:p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-3 sm:mb-4">
                  <icon-user-add class="text-red-600 text-xl sm:text-2xl mr-2" />
                  <h4 class="text-base sm:text-lg font-bold">账号注册</h4>
                </div>
                <div class="text-xs sm:text-sm text-gray-600">
                  <p class="mb-2">01 企业信息</p>
                  <p class="text-xs text-gray-500 mb-3 sm:mb-4">请填写好企业名称、注册地址、联系方式、法人入口信息等</p>
                </div>
              </div>
              <!-- 提交资料 -->
              <div class="bg-white p-4 sm:p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-3 sm:mb-4">
                  <icon-file class="text-red-600 text-xl sm:text-2xl mr-2" />
                  <h4 class="text-base sm:text-lg font-bold">提交资料</h4>
                </div>
                <div class="text-xs sm:text-sm text-gray-600">
                  <p class="mb-2">02 产品信息</p>
                  <p class="text-xs text-gray-500 mb-3 sm:mb-4">请填写好产品、服务、产品相关信息，包括但不限于产品基本信息及在主要电商平台的发展情况</p>
                  <p class="mb-2">03 经营信息</p>
                  <p class="text-xs text-gray-500">请填写好经营规模、运营团队、仓储能力等相关信息，包括线上线下经营规模、自营品牌团队规模等经营信息</p>
                </div>
              </div>
              <!-- 等待审核 -->
              <div class="bg-white p-4 sm:p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-3 sm:mb-4">
                  <icon-unordered-list class="text-red-600 text-xl sm:text-2xl mr-2" />
                  <h4 class="text-base sm:text-lg font-bold">等待审核</h4>
                </div>
                <div class="text-xs sm:text-sm text-gray-600">
                  <p class="mb-2">01 机构审核</p>
                  <p class="text-xs text-gray-500 mb-3 sm:mb-4">信息审核通过后进行资质证明文件审核过程，机构审核预计 1-2 小时</p>
                  <p class="mb-2">02 类型审核</p>
                  <p class="text-xs text-gray-500 mb-3 sm:mb-4">类型审核根据品牌及品牌类型进行分类审核，预计 2-3 天</p>
                  <p class="mb-2">03 类型经理审核</p>
                  <p class="text-xs text-gray-500">类型经理联系品牌类型经理进行价格审核，预计 2-3 天</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 入驻优势 Section -->
        <section id="benefits" class="py-12 sm:py-20 bg-white">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-10 sm:mb-16">
              <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">入驻优势</h2>
              <p class="text-lg sm:text-xl text-gray-600">与我们合作的五大价值</p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8">
              <div
                class="bg-gray-50 rounded-xl p-4 sm:p-8 hover:bg-gray-100 hover:transform hover:translate-y-[-2px] transition-all duration-300 cursor-pointer"
              >
                <div class="flex items-center mb-4 sm:mb-6">
                  <div
                    class="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4"
                  >
                    <icon-apps class="text-xl sm:text-2xl text-blue-600" />
                  </div>
                  <h3 class="text-lg sm:text-xl font-bold text-gray-900">市场资源</h3>
                </div>
                <p class="text-sm sm:text-base text-gray-600 leading-relaxed">平台活跃用户超过100万，每月GMV过10亿，为服务商提供广阔的市场空间</p>
              </div>
              <div
                class="bg-gray-50 rounded-xl p-4 sm:p-8 hover:bg-gray-100 hover:transform hover:translate-y-[-2px] transition-all duration-300 cursor-pointer"
              >
                <div class="flex items-center mb-4 sm:mb-6">
                  <div
                    class="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4"
                  >
                    <icon-copyright class="text-xl sm:text-2xl text-red-600" />
                  </div>
                  <h3 class="text-lg sm:text-xl font-bold text-gray-900">品牌保护</h3>
                </div>
                <p class="text-sm sm:text-base text-gray-600 leading-relaxed">完善的品牌保护机制，打击价格恶意竞价和代购行为，维护品牌形象</p>
              </div>
              <div
                class="bg-gray-50 rounded-xl p-4 sm:p-8 hover:bg-gray-100 hover:transform hover:translate-y-[-2px] transition-all duration-300 cursor-pointer"
              >
                <div class="flex items-center mb-4 sm:mb-6">
                  <div
                    class="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4"
                  >
                    <icon-gift class="text-xl sm:text-2xl text-purple-600" />
                  </div>
                  <h3 class="text-lg sm:text-xl font-bold text-gray-900">资金支持</h3>
                </div>
                <p class="text-sm sm:text-base text-gray-600 leading-relaxed">提供多元化的资金支持方案，包括贷款、财务管理等服务，助力企业发展</p>
              </div>
              <div
                class="bg-gray-50 rounded-xl p-4 sm:p-8 hover:bg-gray-100 hover:transform hover:translate-y-[-2px] transition-all duration-300 cursor-pointer"
              >
                <div class="flex items-center mb-4 sm:mb-6">
                  <div
                    class="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4"
                  >
                    <icon-bar-chart class="text-xl sm:text-2xl text-red-600" />
                  </div>
                  <h3 class="text-lg sm:text-xl font-bold text-gray-900">运营支持</h3>
                </div>
                <p class="text-sm sm:text-base text-gray-600 leading-relaxed">提供专业的运营指导、数据分析、营销推广等服务，助力商家提升经营能力</p>
              </div>
              <div
                class="bg-gray-50 rounded-xl p-4 sm:p-8 hover:bg-gray-100 hover:transform hover:translate-y-[-2px] transition-all duration-300 cursor-pointer"
              >
                <div class="flex items-center mb-4 sm:mb-6">
                  <div
                    class="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4"
                  >
                    <icon-send class="text-xl sm:text-2xl text-green-600" />
                  </div>
                  <h3 class="text-lg sm:text-xl font-bold text-gray-900">物流优势</h3>
                </div>
                <p class="text-sm sm:text-base text-gray-600 leading-relaxed">与多家物流公司合作，提供优惠的物流费率和仓储服务，降低运营成本</p>
              </div>
              <div
                class="bg-gray-50 rounded-xl p-4 sm:p-8 hover:bg-gray-100 hover:transform hover:translate-y-[-2px] transition-all duration-300 cursor-pointer"
              >
                <div class="flex items-center mb-4 sm:mb-6">
                  <div
                    class="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4"
                  >
                    <icon-customer-service class="text-xl sm:text-2xl text-red-600" />
                  </div>
                  <h3 class="text-lg sm:text-xl font-bold text-gray-900">专业服务</h3>
                </div>
                <p class="text-sm sm:text-base text-gray-600 leading-relaxed">配备专属客服经理，7*24小时响应服务，解决商家经营问题</p>
              </div>
            </div>
          </div>
        </section>

        <!-- 联系我们 Section -->
        <section id="contact" class="py-20 bg-gray-50">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
              <h2 class="text-4xl font-bold text-gray-900 mb-4">联系我们</h2>
              <p class="text-xl text-gray-600">我们期待与您的合作</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div
                class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div
                  class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6 mx-auto"
                >
                  <icon-phone class="text-2xl text-red-600" />
                      </div>
                <h3 class="text-xl font-bold text-gray-900 text-center mb-4">客服电话</h3>
                <p class="text-gray-600 text-center">************</p>
                <p class="text-gray-600 text-center">周一至周日 9:00-18:00</p>
                    </div>
              <div
                class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                      <div
                  class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6 mx-auto"
                >
                  <icon-email class="text-2xl text-red-600" />
                </div>
                <h3 class="text-xl font-bold text-gray-900 text-center mb-4">商务邮箱</h3>
                <p class="text-gray-600 text-center"><EMAIL></p>
                <p class="text-gray-600 text-center">欢迎商务合作咨询</p>
              </div>
              <div
                class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div
                  class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6 mx-auto"
                >
                  <icon-location class="text-2xl text-red-600" />
                </div>
                <h3 class="text-xl font-bold text-gray-900 text-center mb-4">公司地址</h3>
                <p class="text-gray-600 text-center">广东省深圳市南山区科技园</p>
                <p class="text-gray-600 text-center">科技大厦 10楼</p>
              </div>
            </div>
          </div>
        </section>

        <!-- Footer -->
        <footer class="bg-gray-900 text-white py-10 sm:py-16">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
              <div class="mb-6 sm:mb-0">
                <h3 class="text-lg sm:text-xl font-bold mb-3 sm:mb-4">关于我们</h3>
                <p class="text-sm sm:text-base text-gray-400 mb-4">我们是一家专注于电商服务的平台，致力于为品牌商家提供全方位的电商解决方案。</p>
                <div class="flex space-x-4">
                  <a href="#" class="text-gray-400 hover:text-white">
                    <icon-facebook class="text-lg sm:text-xl" />
                  </a>
                  <a href="#" class="text-gray-400 hover:text-white">
                    <icon-twitter class="text-lg sm:text-xl" />
                  </a>
                  <a href="#" class="text-gray-400 hover:text-white">
                    <icon-instagram class="text-lg sm:text-xl" />
                  </a>
                  <a href="#" class="text-gray-400 hover:text-white">
                    <icon-linkedin class="text-lg sm:text-xl" />
                  </a>
                </div>
              </div>
              <div class="mb-6 sm:mb-0">
                <h3 class="text-lg sm:text-xl font-bold mb-3 sm:mb-4">快速链接</h3>
                <ul class="space-y-1 sm:space-y-2">
                  <li>
                    <a href="#" class="text-sm sm:text-base text-gray-400 hover:text-white">首页</a>
                  </li>
                  <li>
                    <a href="#about" class="text-sm sm:text-base text-gray-400 hover:text-white">关于我们</a>
                  </li>
                  <li>
                    <a href="#process" class="text-sm sm:text-base text-gray-400 hover:text-white">入驻流程</a>
                  </li>
                  <li>
                    <a href="#benefits" class="text-sm sm:text-base text-gray-400 hover:text-white">入驻优势</a>
                  </li>
                  <li>
                    <a href="#contact" class="text-sm sm:text-base text-gray-400 hover:text-white">联系我们</a>
                  </li>
                </ul>
              </div>
              <div class="mb-6 sm:mb-0">
                <h3 class="text-lg sm:text-xl font-bold mb-3 sm:mb-4">帮助中心</h3>
                <ul class="space-y-1 sm:space-y-2">
                  <li>
                    <a href="#" class="text-sm sm:text-base text-gray-400 hover:text-white">常见问题</a>
                  </li>
                  <li>
                    <a href="#" class="text-sm sm:text-base text-gray-400 hover:text-white">入驻指南</a>
                  </li>
                  <li>
                    <a href="#" class="text-sm sm:text-base text-gray-400 hover:text-white">运营指南</a>
                  </li>
                  <li>
                    <a href="#" class="text-sm sm:text-base text-gray-400 hover:text-white">平台规则</a>
                  </li>
                  <li>
                    <a href="#" class="text-sm sm:text-base text-gray-400 hover:text-white">隐私政策</a>
                  </li>
                </ul>
              </div>
              <div>
                <h3 class="text-lg sm:text-xl font-bold mb-3 sm:mb-4">联系方式</h3>
                <ul class="space-y-1 sm:space-y-2">
                  <li class="flex items-center">
                    <icon-phone class="text-gray-400 mr-2 text-sm sm:text-base" />
                    <span class="text-sm sm:text-base text-gray-400">************</span>
                  </li>
                  <li class="flex items-center">
                    <icon-email class="text-gray-400 mr-2 text-sm sm:text-base" />
                    <span class="text-sm sm:text-base text-gray-400"><EMAIL></span>
                  </li>
                  <li class="flex items-center">
                    <icon-location class="text-gray-400 mr-2 text-sm sm:text-base" />
                    <span class="text-sm sm:text-base text-gray-400">广东省深圳市南山区科技园</span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="border-t border-gray-800 mt-8 sm:mt-12 pt-6 sm:pt-8">
              <p class="text-center text-xs sm:text-sm text-gray-400">&copy; 2023 服务商平台. 所有权利均已保留.</p>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <!-- 注册弹窗组件 -->
    <RegisterDialog ref="registerDialogRef" />
    <LoginDialog ref="loginDialogRef" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import RegisterDialog from "~/components/provider/RegisterDialog.vue";
import LoginDialog from "~/components/provider/LoginDialog.vue";
let showMobileMenu=ref(false)
const bgImage = new URL(
  "@/assets/images/provider/base-map.jpg",
  import.meta.url
).href;

const logoImage = new URL(
  "@/assets/images/provider/InvestmentpromotionplatformIcon.png",
  import.meta.url
).href;

const router = useRouter();
definePageMeta({
  layout: false,
  name: "provider-index",
  path: "/provider/index"
});

// 登录弹窗状态
const showLoginModal = () => {
  loginDialogRef.value.open();
};

// 注册弹窗相关
const registerDialogRef = ref(null);
const loginDialogRef = ref(null);

const showRegisterModal = () => {
  registerDialogRef.value.open();
};

// 滚动到指定区域
const scrollToSection = sectionId => {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};
</script>

<style lang="less" scoped>
.gradient-bg {
  background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
}

.step-line::after {
  content: "";
  position: absolute;
  top: 50%;
  right: -25px;
  width: 50px;
  height: 2px;
  background: #e53e3e;
  transform: translateY(-50%);
}

.step-line:last-child::after {
  display: none;
}

.hero-pattern {
  background-image: url("/assets/images/pattern.png");
  background-repeat: repeat;
  background-size: 150px;
  background-blend-mode: soft-light;
}

.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

// 动画效果
.transition-shadow {
  transition: box-shadow 0.3s ease-in-out;
}

.hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover\:bg-gray-100:hover {
  background-color: #f3f4f6;
  transform: translateY(-2px);
  transition: all 0.3s ease-in-out;
}

// 响应式调整
@media (max-width: 768px) {
  .hero-pattern {
    background-size: 100px;
  }

  .text-5xl {
    font-size: 2.5rem;
  }

  .text-xl {
    font-size: 1.1rem;
  }
}
</style>
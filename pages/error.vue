<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->

<template>
  <div class="page max-7xl mx-auto text-center">
    <div class="bg mx-auto">
      <img src="@/assets/404.svg" />
      <div class="mt-2">{{ $t('sys.notFoundPage') }}</div>
    </div>
    <div class="mt-5"><a-button type="primary" @click="jump">{{ $t('sys.goHome') }}</a-button></div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';

definePageMeta({
  name: 'error',
  layout: false
})

// 导航函数
const navigateTo = (path) => {
  window.location.href = path;
};

const jump = () => {
  console.log('当前路径:', window.location.pathname);
  const currentPath = window.location.pathname;
  const route = useRoute();
  const redirectPath = route.query.redirect || '';
  
  // 根据URL判断用户所在的系统（商家端或管理端）
  if (currentPath.includes('/merchant/') || redirectPath.includes('/merchant/')) {
    console.log('跳转到商家仪表盘');
    navigateTo('/merchant/dashboard');
  } else {
    console.log('跳转到管理仪表盘');
    navigateTo('/master/dashboard');
  }
}
</script>

<style scoped lang="less">
.page {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -200px;
  margin-left: -195px;
}

.bg,
.bg img {
  width: 390px;
}
</style>

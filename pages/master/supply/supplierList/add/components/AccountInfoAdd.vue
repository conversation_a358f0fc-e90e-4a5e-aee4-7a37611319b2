<template>
  <div class="account-info-add">
    <div class="add-header">
      <h4>账户信息</h4>
    </div>
    <div class="add-content">
      <a-form 
        ref="formRef"
        :model="formData" 
        :rules="rules"
        layout="vertical"
        :label-col-props="{ span: 24 }"
        :wrapper-col-props="{ span: 24 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="付款条件" field="payment_terms">
              <a-select 
                v-model="formData.payment_terms" 
                placeholder="请选择付款条件"
                allow-clear
                allow-create
              >
                <a-option value="货到付款">货到付款</a-option>
                <a-option value="月结30天">月结30天</a-option>
                <a-option value="月结60天">月结60天</a-option>
                <a-option value="月结90天">月结90天</a-option>
                <a-option value="预付30%，发货前70%">预付30%，发货前70%</a-option>
                <a-option value="预付50%，发货前50%">预付50%，发货前50%</a-option>
                <a-option value="信用证付款">信用证付款</a-option>
                <a-option value="承兑汇票90天">承兑汇票90天</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发票类型" field="invoice_type">
              <a-select 
                v-model="formData.invoice_type" 
                placeholder="请选择发票类型"
                allow-clear
              >
                <a-option value="增值税专用发票">增值税专用发票</a-option>
                <a-option value="增值税普通发票">增值税普通发票</a-option>
                <a-option value="电子发票">电子发票</a-option>
                <a-option value="收据">收据</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="税率(%)" field="tax_rate">
              <a-input-number 
                v-model="formData.tax_rate" 
                placeholder="请输入税率"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="开户名" field="account_name">
              <a-input 
                v-model="formData.account_name" 
                placeholder="请输入开户名"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="结算方式" field="settlement_method">
              <a-select 
                v-model="formData.settlement_method" 
                placeholder="请选择结算方式"
                allow-clear
              >
                <a-option value="银行转账">银行转账</a-option>
                <a-option value="现金支付">现金支付</a-option>
                <a-option value="支票支付">支票支付</a-option>
                <a-option value="信用证">信用证</a-option>
                <a-option value="承兑汇票">承兑汇票</a-option>
                <a-option value="网银支付">网银支付</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="开户账号" field="account_number">
              <a-input 
                v-model="formData.account_number" 
                placeholder="请输入开户账号"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="开户银行" field="bank_name">
              <a-input 
                v-model="formData.bank_name" 
                placeholder="请输入开户银行"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="注册资本(万元)" field="registered_capital">
              <a-input-number 
                v-model="formData.registered_capital" 
                placeholder="请输入注册资本"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 保存按钮 -->
      <div v-if="showSaveButton" class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存账户信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const formRef = ref();
const saving = ref(false);

// 表单数据
const formData = reactive({ ...props.formData });

// 表单验证规则
const rules = {
  payment_terms: [
    { required: true, message: '请选择付款条件' }
  ],
  invoice_type: [
    { required: true, message: '请选择发票类型' }
  ],
  account_name: [
    { required: true, message: '请输入开户名' }
  ],
  account_number: [
    { pattern: /^\d{10,25}$/, message: '账号格式不正确' }
  ]
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  Object.assign(formData, newVal);
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    const result = await formRef.value.validate();
    return result;
  } catch (error) {
    console.error('账户信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.account-info-add {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.add-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.add-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.add-content {
  margin-top: 16px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>

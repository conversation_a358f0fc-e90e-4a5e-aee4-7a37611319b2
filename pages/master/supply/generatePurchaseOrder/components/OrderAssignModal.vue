<template>
  <a-modal
    v-model:visible="visible"
    title="订单指派"
    width="1200px"
    :footer="false"
    @cancel="handleCancel"
    class="order-assign-modal"
  >
    <div class="modal-content">
      <!-- 订单信息 -->
      <div class="section order-info">
        <h3 class="section-title">订单信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">订单编号</span>
            <span class="value">{{ orderData?.orderNumber || '202505242124227703002' }}</span>
          </div>
          <div class="info-item">
            <span class="label">订单金额</span>
            <span class="value">¥{{ orderData?.totalAmount || '1.00' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收货地址</span>
            <span class="value">{{ orderData?.address || '一天津市红桥区中华...' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系人</span>
            <span class="value">{{ orderData?.recipientName || '张氏' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话</span>
            <span class="value">{{ orderData?.recipientPhone || '14852463265' }}</span>
          </div>
          <div class="info-item">
            <span class="label">订单创建时间</span>
            <span class="value">{{ orderData?.createdAt || '2025-06-13 10:...' }}</span>
          </div>
          <div class="info-item full-width">
            <span class="label">客户备注</span>
            <span class="value">{{ orderData?.remark || '' }}</span>
          </div>
        </div>
      </div>

      <!-- 商品信息 -->
      <div class="section product-info">
        <h3 class="section-title">商品信息</h3>
        <a-table
          :columns="productColumns"
          :data="productData"
          :pagination="false"
          class="product-table"
        >
          <template #productInfo="{ record }">
            <div class="product-cell">
              <a-image
                :src="record.imageUrl"
                width="60"
                height="45"
                class="product-image"
              />
              <div class="product-details">
                <div class="product-name">{{ record.productName }}</div>
                <div class="product-sku">SKU: {{ record.sku }}</div>
                <div class="product-code">商品编号: {{ record.productCode }}</div>
                <div class="product-spec">规格: {{ record.specifications }}</div>
              </div>
            </div>
          </template>
          <template #price="{ record }">
            <span>¥{{ record.price }}</span>
          </template>
          <template #total="{ record }">
            <span>¥{{ record.total }}</span>
          </template>
        </a-table>
      </div>

      <!-- 派单信息 -->
      <div class="section assign-info">
        <h3 class="section-title">派单信息</h3>
        <div class="assign-options">
          <a-radio-group v-model="assignType" class="assign-radio-group">
            <a-radio value="company" class="assign-radio">
              <span class="radio-text">按公司指派</span>
            </a-radio>
            <a-radio value="warehouse" class="assign-radio">
              <span class="radio-text">按仓库指派</span>
            </a-radio>
            <a-radio value="business" class="assign-radio">
              <span class="radio-text">业务员</span>
            </a-radio>
          </a-radio-group>
          <div class="business-select" v-if="assignType === 'business'">
            <a-select v-model="selectedBusiness" placeholder="请选择业务员" style="width: 200px;">
              <a-option value="business1">商户名称</a-option>
              <a-option value="business2">商户名称2</a-option>
            </a-select>
          </div>
          <a-button type="primary" class="search-btn">查询</a-button>
        </div>
      </div>

      <!-- 供应商列表 -->
      <div class="section supplier-list">
        <a-table
          :columns="supplierColumns"
          :data="supplierData"
          :pagination="paginationConfig"
          row-key="id"
          class="supplier-table"
        >
          <template #selection="{ record }">
            <a-radio
              :value="record.id"
              v-model="selectedSupplier"
              @change="handleSupplierSelect(record)"
            />
          </template>
          <template #rate="{ record }">
            <span class="rate-text">{{ record.rate }}%</span>
          </template>
          <template #action="{ record }">
            <a-button type="text" size="small" class="detail-btn">详情</a-button>
          </template>
        </a-table>
      </div>

      <!-- 其它信息 -->
      <div class="section other-info">
        <h3 class="section-title">其它信息</h3>
        <div class="other-grid">
          <div class="other-item">
            <span class="label">订单类型</span>
            <span class="value">{{ orderData?.orderType || '普通订单' }}</span>
          </div>
          <div class="other-item">
            <span class="label">商品白名单</span>
            <span class="value">{{ orderData?.productWhitelist || '是' }}</span>
          </div>
          <div class="other-item">
            <span class="label">商品成本价</span>
            <span class="value">¥{{ orderData?.costPrice || '1.00' }}</span>
          </div>
          <div class="other-item">
            <span class="label">系统白名单</span>
            <span class="value">{{ orderData?.systemWhitelist || '是' }}</span>
          </div>
        </div>
        <div class="remark-section">
          <div class="remark-label">订单备注</div>
          <a-textarea
            v-model="orderRemark"
            placeholder="请输入订单备注"
            :rows="3"
            class="remark-textarea"
          />
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit">确定</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const assignType = ref('company')
const selectedBusiness = ref('')
const selectedSupplier = ref('')
const orderRemark = ref('')

// 商品表格列定义
const productColumns = [
  {
    title: '商品名称',
    slotName: 'productInfo',
    width: 300
  },
  {
    title: '单价',
    slotName: 'price',
    width: 100,
    align: 'center'
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 80,
    align: 'center'
  },
  {
    title: '总价',
    slotName: 'total',
    width: 100,
    align: 'center'
  }
]

// 供应商表格列定义
const supplierColumns = [
  {
    title: '选择',
    slotName: 'selection',
    width: 60,
    align: 'center'
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    width: 200
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 120
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 140
  },
  {
    title: '回款',
    slotName: 'rate',
    width: 80,
    align: 'center'
  },
  {
    title: '操作',
    slotName: 'action',
    width: 80,
    align: 'center'
  }
]

// 模拟商品数据
const productData = computed(() => {
  if (props.orderData?.products) {
    return props.orderData.products.map(product => ({
      ...product,
      total: (parseFloat(product.price || 0) * parseInt(product.quantity || 0)).toFixed(2)
    }))
  }
  
  return [{
    id: 1,
    imageUrl: '/placeholder.png',
    productName: '达雷 便携式立体声TZ 231 台式 白色蓝牙音箱-1',
    sku: 'c336131b db7a 4a19 bea2 7f230042d4/4',
    productCode: '0801040333',
    specifications: 'TZ-231',
    price: '1.00',
    quantity: 1,
    total: '1.00'
  }]
})

// 模拟供应商数据
const supplierData = ref([
  {
    id: 1,
    customerName: '广东八元科技设备有限公司',
    contactPerson: '洪小姐',
    contactPhone: '13826113341',
    rate: '0'
  },
  {
    id: 2,
    customerName: '广东八元科技设备有限公司',
    contactPerson: '黄诗雅',
    contactPhone: '13826113247',
    rate: '0'
  },
  {
    id: 3,
    customerName: '广东八元科技设备有限公司',
    contactPerson: '方运琴',
    contactPhone: '17710866511',
    rate: '0'
  },
  {
    id: 4,
    customerName: '广东八元科技设备有限公司',
    contactPerson: '刘科起',
    contactPhone: '13826112104',
    rate: '0'
  },
  {
    id: 5,
    customerName: '广东八元科技设备有限公司',
    contactPerson: '黄文君',
    contactPhone: '13826118730',
    rate: '0'
  }
])

// 分页配置
const paginationConfig = {
  total: 60,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showJumper: true,
  showPageSize: true
}

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleSupplierSelect = (supplier) => {
  console.log('选择供应商:', supplier)
}

const handleSubmit = () => {
  if (!selectedSupplier.value) {
    Message.warning('请选择供应商')
    return
  }
  
  const submitData = {
    orderId: props.orderData?.id,
    assignType: assignType.value,
    selectedBusiness: selectedBusiness.value,
    selectedSupplier: selectedSupplier.value,
    orderRemark: orderRemark.value
  }
  
  emit('submit', submitData)
  Message.success('订单指派成功')
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
  // 重置表单
  assignType.value = 'company'
  selectedBusiness.value = ''
  selectedSupplier.value = ''
  orderRemark.value = ''
}
</script>

<style scoped>
.order-assign-modal :deep(.arco-modal-body) {
  padding: 0;
}

.modal-content {
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
}

.section {
  margin-bottom: 24px;
  background: white;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e6eb;
}

/* 订单信息样式 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 24px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item .label {
  color: #666;
  font-size: 14px;
  min-width: 80px;
  margin-right: 8px;
}

.info-item .value {
  color: #333;
  font-size: 14px;
  flex: 1;
}

/* 商品信息样式 */
.product-table {
  background: white;
}

.product-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  border-radius: 4px;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.product-sku,
.product-code,
.product-spec {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

/* 派单信息样式 */
.assign-options {
  display: flex;
  align-items: center;
  gap: 16px;
}

.assign-radio-group {
  display: flex;
  gap: 24px;
}

.assign-radio .radio-text {
  font-size: 14px;
}

.business-select {
  margin-left: 16px;
}

.search-btn {
  margin-left: auto;
}

/* 供应商列表样式 */
.supplier-table {
  background: white;
}

.rate-text {
  color: #1890ff;
}

.detail-btn {
  color: #f53f3f;
}

/* 其它信息样式 */
.other-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 24px;
  margin-bottom: 16px;
}

.other-item {
  display: flex;
  align-items: center;
}

.other-item .label {
  color: #666;
  font-size: 14px;
  min-width: 100px;
  margin-right: 8px;
}

.other-item .value {
  color: #333;
  font-size: 14px;
}

.remark-section {
  margin-top: 16px;
}

.remark-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.remark-textarea {
  width: 100%;
}

/* 底部按钮样式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e6eb;
  margin-top: 20px;
}
</style>

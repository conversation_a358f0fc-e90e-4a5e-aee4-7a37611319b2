<template>
  <a-modal
    v-model:visible="visible"
    title="申请采购"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    class="purchase-application-modal"
  >
    <a-form
      :model="formData"
      layout="vertical"
      ref="formRef"
      class="purchase-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="主要负责人"
              field="mainResponsible"
              :rules="[{ required: true, message: '请输入主要负责人' }]"
            >
              <a-input
                v-model="formData.mainResponsible"
                placeholder="请输入主要负责人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="联系电话"
              field="contactPhone"
              :rules="[
                { required: true, message: '请输入联系电话' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
              ]"
            >
              <a-input
                v-model="formData.contactPhone"
                placeholder="请输入联系电话"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="采购部门负责人"
              field="departmentHead"
              :rules="[{ required: true, message: '请输入采购部门负责人' }]"
            >
              <a-input
                v-model="formData.departmentHead"
                placeholder="请输入采购部门负责人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="计划交付"
              field="plannedDelivery"
              :rules="[{ required: true, message: '请选择计划交付时间' }]"
            >
              <a-date-picker
                v-model="formData.plannedDelivery"
                placeholder="请选择计划交付时间"
                style="width: 100%"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item
          label="备注信息"
          field="remarks"
        >
          <a-textarea
            v-model="formData.remarks"
            placeholder="请输入备注信息"
            :rows="4"
          />
        </a-form-item>
      </div>

      <!-- 附件上传 -->
      <div class="form-section">
        <h4 class="section-title">附件上传</h4>
        
        <a-form-item label="相关文件">
          <a-upload
            ref="uploadRef"
            :file-list="fileList"
            :show-file-list="true"
            :auto-upload="false"
            :multiple="true"
            :limit="5"
            accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
            @change="handleFileChange"
            @remove="handleFileRemove"
            class="upload-area"
          >
            <template #upload-button>
              <div class="upload-button">
                <div class="upload-icon">
                  <icon-cloud-upload />
                </div>
                <div class="upload-text">
                  <div class="upload-title">点击上传文件或拖拽文件到此处</div>
                  <div class="upload-subtitle">
                    支持格式：PDF、Word、Excel、图片等，单个文件不超过10MB，最多5个文件
                  </div>
                </div>
              </div>
            </template>
          </a-upload>
        </a-form-item>

        <!-- 文件列表 -->
        <div v-if="fileList.length > 0" class="file-list">
          <div
            v-for="(file, index) in fileList"
            :key="index"
            class="file-item"
          >
            <div class="file-info">
              <icon-file class="file-icon" />
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">({{ formatFileSize(file.size) }})</span>
            </div>
            <a-button
              type="text"
              size="small"
              @click="removeFile(index)"
              class="remove-btn"
            >
              <icon-delete />
            </a-button>
          </div>
        </div>
      </div>

      <!-- 提示信息 -->
      <a-alert
        type="info"
        message="申请提醒"
        class="alert-info"
      >
        <template #description>
          <div class="alert-content">
            <p>• 请确保填写的信息准确无误，提交后将进入审核流程</p>
            <p>• 计划交付时间将作为采购计划的重要参考</p>
            <p>• 如有特殊要求，请在备注中详细说明</p>
            <p>• 相关文件将有助于加快审核进度</p>
          </div>
        </template>
      </a-alert>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconCloudUpload, IconFile, IconDelete } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref()
const uploadRef = ref()

// 表单数据
const formData = reactive({
  mainResponsible: '',
  contactPhone: '',
  departmentHead: '',
  plannedDelivery: '',
  remarks: ''
})

// 文件列表
const fileList = ref([])

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理文件变化
const handleFileChange = (fileList, fileItem) => {
  console.log('文件变化:', fileList, fileItem)
}

// 处理文件移除
const handleFileRemove = (fileItem) => {
  console.log('移除文件:', fileItem)
}

// 移除文件
const removeFile = (index) => {
  fileList.value.splice(index, 1)
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)}${units[index]}`
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      return
    }

    // 构建提交数据
    const submitData = {
      orderId: props.orderData?.id,
      orderNumber: props.orderData?.orderNumber,
      ...formData,
      attachments: fileList.value.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        url: file.url || '' // 实际项目中这里应该是上传后的文件URL
      })),
      submittedAt: new Date().toISOString(),
      submittedBy: '当前用户' // 实际项目中从用户状态获取
    }

    emit('submit', submitData)
    Message.success('申请采购提交成功')
    visible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  fileList.value = []
  formRef.value?.resetFields()
}
</script>

<style scoped>
.purchase-application-modal :deep(.arco-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

.purchase-form {
  padding: 8px 0;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e6eb;
}

/* 上传区域样式 */
.upload-area {
  width: 100%;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-button:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.upload-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-right: 16px;
}

.upload-text {
  text-align: left;
}

.upload-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.upload-subtitle {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

/* 文件列表样式 */
.file-list {
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 16px;
  color: #666;
  margin-right: 8px;
}

.file-name {
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.remove-btn {
  color: #f53f3f;
  padding: 4px;
}

.remove-btn:hover {
  background-color: #fff2f0;
}

/* 提示信息样式 */
.alert-info {
  margin-top: 16px;
}

.alert-content p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .upload-button {
    flex-direction: column;
    text-align: center;
  }
  
  .upload-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .upload-text {
    text-align: center;
  }
}
</style>

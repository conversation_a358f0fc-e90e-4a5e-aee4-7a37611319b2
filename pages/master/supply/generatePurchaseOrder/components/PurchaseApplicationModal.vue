<template>
  <a-modal
    v-model:visible="visible"
    title="申请采购"
    width="900px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    class="purchase-application-modal"
  >
    <a-form
      :model="formData"
      layout="vertical"
      ref="formRef"
      class="purchase-form"
    >
      <!-- 基本信息 -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="采购员"
            field="sourceSupplier"
            :rules="[{ required: true, message: '请输入采购员' }]"
          >
            <a-input
              v-model="formData.sourceSupplier"
              placeholder="供应商编码"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="业务员备注"
            field="businessManager"
          >
            <a-input
              v-model="formData.businessManager"
              placeholder="请输入业务员备注"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="实际收货人"
            field="actualReceiver"
            :rules="[{ required: true, message: '请输入实际收货人' }]"
          >
            <a-input
              v-model="formData.actualReceiver"
              placeholder="请输入实际收货人"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="订单备注"
            field="orderRemark"
          >
            <a-textarea
              v-model="formData.orderRemark"
              placeholder="请输入订单备注"
              :rows="1"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="联系电话"
            field="contactPhone"
            :rules="[
              { required: true, message: '请输入联系电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]"
          >
            <a-input
              v-model="formData.contactPhone"
              placeholder="请输入联系电话"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="客户备注"
            field="customerRemark"
          >
            <a-textarea
              v-model="formData.customerRemark"
              placeholder="客户备注"
              :rows="1"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item
        label="实际收货地址"
        field="actualAddress"
        :rules="[{ required: true, message: '请输入实际收货地址' }]"
      >
        <a-textarea
          v-model="formData.actualAddress"
          placeholder="请输入实际收货地址"
          :rows="2"
        />
      </a-form-item>

      <!-- 建议供应商编号 -->
      <a-form-item label="建议供应商编号">
        <a-table
          :columns="supplierColumns"
          :data="supplierData"
          :pagination="false"
          class="supplier-table"
          size="small"
        >
          <template #productCode="{ record }">
            <span>{{ record.productCode }}</span>
          </template>
          <template #productName="{ record }">
            <span>{{ record.productName }}</span>
          </template>
          <template #suggestedSupplier="{ record }">
            <span><a-input v-model="record.suggestedSupplier" /></span>
          </template>
        </a-table>
      </a-form-item>

      <!-- 附件上传 -->
      <a-form-item label="附件上传">
        <div class="upload-container">
          <a-upload
            ref="uploadRef"
            :file-list="fileList"
            :show-file-list="false"
            :auto-upload="false"
            :multiple="true"
            :limit="5"
            accept=".doc,.docx,.xls,.xlsx,.pdf,.png,.jpg"
            @change="handleFileChange"
            class="upload-area"
            drag
          >
            <template #upload-button>
              <div class="upload-button">
                <div class="upload-icon">
                  <icon-upload />
                </div>
                <div class="upload-text">
                  <div class="upload-title">将文件拖拽到此处或点击上传</div>
                </div>
              </div>
            </template>
          </a-upload>
          <div class="upload-tip">
            支持格式类型：doc、xlsx、xls、pdf、png、jpg 可上传多个，大小不超过2MB
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconUpload, IconFile, IconDelete } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref()
const uploadRef = ref()

// 表单数据
const formData = reactive({
  sourceSupplier: '',
  businessManager: '',
  actualReceiver: '',
  orderRemark: '',
  contactPhone: '',
  customerRemark: '',
  actualAddress: ''
})

// 供应商表格列定义
const supplierColumns = [
  {
    title: '商品编码',
    slotName: 'productCode',
    width: 120
  },
  {
    title: '商品名称',
    slotName: 'productName',
    width: 300
  },
  {
    title: '建议供应商',
    slotName: 'suggestedSupplier',
    width: 200
  }
]

// 模拟供应商数据
const supplierData = ref([
  {
    id: 1,
    productCode: '0803002409',
    productName: '美化 半力/jdeli 7901 钢铁侠头盔 蓝牙音响',
    suggestedSupplier: '广东线上工厂生产供应商-测试'
  }
])

// 文件列表
const fileList = ref([])

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
    initFormData()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理文件变化
const handleFileChange = (fileList, fileItem) => {
  console.log('文件变化:', fileList, fileItem)
}

// 处理文件移除
const handleFileRemove = (fileItem) => {
  console.log('移除文件:', fileItem)
}

// 移除文件
const removeFile = (index) => {
  fileList.value.splice(index, 1)
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)}${units[index]}`
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      return
    }

    // 构建提交数据
    const submitData = {
      orderId: props.orderData?.id,
      orderNumber: props.orderData?.orderNumber,
      ...formData,
      attachments: fileList.value.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        url: file.url || '' // 实际项目中这里应该是上传后的文件URL
      })),
      submittedAt: new Date().toISOString(),
      submittedBy: '当前用户' // 实际项目中从用户状态获取
    }

    emit('submit', submitData)
    Message.success('申请采购提交成功')
    visible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  fileList.value = []
  formRef.value?.resetFields()
}

// 初始化表单数据
const initFormData = () => {
  if (props.orderData) {
    formData.actualReceiver = props.orderData.recipientName || ''
    formData.contactPhone = props.orderData.recipientPhone || ''
    formData.actualAddress = props.orderData.address || ''
    formData.customerRemark = props.orderData.remark || ''

    // 根据订单商品数据初始化供应商表格
    if (props.orderData.products && props.orderData.products.length > 0) {
      supplierData.value = props.orderData.products.map((product, index) => ({
        id: index + 1,
        productCode: product.goodsSkuId || product.sku || '',
        productName: product.productName || '',
        suggestedSupplier: '广东线上工厂生产供应商-测试'
      }))
    }
  }
}
</script>

<style scoped>
.purchase-application-modal :deep(.arco-modal-body) {
  max-height: 80vh;
  overflow-y: auto;
}

.purchase-form {
  padding: 8px 0;
}

/* 供应商表格样式 */
.supplier-table {
  background: white;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  width: 100%;
}

.supplier-table :deep(.arco-table-th) {
  background-color: #f7f8fa;
  font-weight: 500;
}

/* 上传区域样式 */
.upload-container {
  width: 100%;
}

.upload-area {
  width: 100%;
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.upload-button:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.upload-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 12px;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

/* 表单项样式调整 */
.purchase-form :deep(.arco-form-item-label) {
  font-weight: 500;
  color: #333;
}

.purchase-form :deep(.arco-textarea) {
  resize: vertical;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .purchase-application-modal {
    width: 95% !important;
  }

  .upload-button {
    padding: 24px 16px;
  }

  .upload-icon {
    font-size: 36px;
  }
}
</style>

<template>
  <div class="p-6 bg-white rounded">
    <!-- OCR配置 -->
    <div>
      <a-card :body-style="{ padding: '24px' }">
        <div class="text-2xl font-semibold text-gray-800 mb-2">阿里云OCR配置</div>
        <div class="mb-4 text-sm text-gray-500">请填写阿里云OCR服务的相关配置信息</div>
        <a-form :model="formModel" layout="vertical" class="mt-2">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 是否启用OCR服务 -->
            <a-form-item field="enabled" label="启用OCR服务">
              <a-switch v-model="formModel.enabled" />
              <div class="text-xs text-gray-500 mt-1">开启后可使用OCR文字识别功能</div>
            </a-form-item>

            <!-- 阿里云AccessKey ID -->
            <a-form-item field="accessKeyId" label="阿里云AccessKey ID">
              <a-input
                v-model="formModel.accessKeyId"
                placeholder="请输入阿里云AccessKey ID"
                :disabled="!formModel.enabled"
              />
              
            </a-form-item>

            <!-- 阿里云AccessKey Secret -->
            <a-form-item field="accessKeySecret" label="阿里云AccessKey Secret">
              <a-input-password
                v-model="formModel.accessKeySecret"
                placeholder="请输入阿里云AccessKey Secret"
                :disabled="!formModel.enabled"
              />
              
            </a-form-item>

            <!-- 阿里云区域 -->
            <a-form-item field="region" label="阿里云区域">
              <a-select
                v-model="formModel.region"
                placeholder="请选择阿里云区域"
                :disabled="!formModel.enabled"
              >
                <a-option value="cn-hangzhou">华东1（杭州）</a-option>
                <a-option value="cn-shanghai">华东2（上海）</a-option>
                <a-option value="cn-beijing">华北2（北京）</a-option>
                <a-option value="cn-shenzhen">华南1（深圳）</a-option>
                <a-option value="cn-guangzhou">华南2（广州）</a-option>
                <a-option value="cn-chengdu">西南1（成都）</a-option>
                <a-option value="ap-southeast-1">亚太东南1（新加坡）</a-option>
                <a-option value="us-west-1">美国西部1（硅谷）</a-option>
                <a-option value="us-east-1">美国东部1（弗吉尼亚）</a-option>
              </a-select>
              
            </a-form-item>
          </div>

          <!-- 测试连接 -->
          <div class="mt-8" v-if="formModel.enabled">
            <div class="text-lg font-medium text-gray-800 mb-3">测试连接</div>
            <a-card class="test-connection-card">
              <div class="grid grid-cols-1 gap-5">
                <a-form-item field="testImageUrl" label="测试图片URL">
                  <a-input
                    v-model="testImageUrl"
                    placeholder="请输入图片URL地址，用于测试OCR识别功能"
                  />

                </a-form-item>
                <a-form-item field="ocrType" label="识别类型">
                  <a-select v-model="ocrType" placeholder="请选择识别类型">
                    <a-option value="general">通用文字识别</a-option>
                    <a-option value="idcard">身份证识别</a-option>
                    <a-option value="driving">驾驶证识别</a-option>
                    <a-option value="business">营业执照识别</a-option>
                    <a-option value="bankcard">银行卡识别</a-option>
                  </a-select>
                </a-form-item>
                <div class="flex justify-end space-x-3 mt-2">
                  <a-button @click="testConnection" :loading="testingConnection" size="medium">
                    <template #icon><icon-link /></template>
                    测试连接
                  </a-button>
                  <a-button
                    type="primary"
                    @click="testOCR"
                    :loading="testingOCR"
                    :disabled="!testImageUrl || !ocrType"
                    size="medium"
                  >
                    <template #icon><icon-search /></template>
                    测试识别
                  </a-button>
                </div>
              </div>
            </a-card>
          </div>
        </a-form>

        <div class="flex justify-end mt-6">
          <a-button type="primary" @click="submitForm" :loading="saving" size="medium">
            <template #icon><icon-save /></template>
            保存设置
          </a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconSave, IconLink, IconSearch } from '@arco-design/web-vue/es/icon';
import systemApi from '@/api/master/system';
import { useUserStore } from '@/store/index.js';

definePageMeta({
  name: 'system-config-ocrConfig',
  path: '/master/system/config/ocrConfig',
  title: 'OCR配置'
});

const configApi = systemApi.configuration;
const userStore = useUserStore();

// 表单数据
const formModel = ref({
  enabled: false,
  accessKeyId: '',
  accessKeySecret: '',
  region: 'cn-hangzhou'
});

// 测试相关数据
const testImageUrl = ref('');
const ocrType = ref('general');
const testingConnection = ref(false);
const testingOCR = ref(false);
const saving = ref(false);

// 获取设置
const fetchSettings = async () => {
  try {
    const res = await configApi.objectTypes('OCR');
    if (res.code === 200 && res.data) {
      // 处理后端返回的enabled字段，可能是布尔值true或字符串'true'
      formModel.value = {
        enabled: res.data.enabled === true || res.data.enabled === 'true',
        accessKeyId: res.data.accessKeyId || '',
        accessKeySecret: res.data.accessKeySecret || '',
        region: res.data.region || 'cn-hangzhou'
      };
      console.log('OCR配置加载成功:', formModel.value);
    }
  } catch (error) {
    console.error('获取OCR配置失败:', error);
    Message.error('获取OCR配置失败');
  }
};

// 测试连接
const testConnection = async () => {
  if (!formModel.value.enabled) {
    Message.warning('请先启用OCR服务');
    return;
  }

  if (!formModel.value.accessKeyId || !formModel.value.accessKeySecret) {
    Message.warning('请先填写AccessKey信息');
    return;
  }

  testingConnection.value = true;
  try {
    // 先保存当前配置
    await submitForm();

    // 调用后端的测试连接接口
    const response = await fetch('/api/v1/master/system/configure/ocrConfig/test/connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userStore.getToken('master')}`
      }
    });

    const result = await response.json();

    if (result.code === 200) {
      Message.success(result.message || 'OCR服务连接测试成功');
    } else {
      Message.error(result.message || 'OCR服务连接测试失败');
    }
  } catch (error) {
    console.error('测试连接失败:', error);
    Message.error('OCR服务连接测试失败');
  } finally {
    testingConnection.value = false;
  }
};

// 测试OCR识别
const testOCR = async () => {
  if (!testImageUrl.value) {
    Message.warning('请输入测试图片URL');
    return;
  }

  if (!ocrType.value) {
    Message.warning('请选择识别类型');
    return;
  }

  testingOCR.value = true;
  try {
    // 先保存当前配置
    await submitForm();

    // 调用后端的OCR识别测试接口
    const response = await fetch('/api/v1/master/system/configure/ocrConfig/test/recognize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userStore.getToken('master')}`
      },
      body: JSON.stringify({
        imageUrl: testImageUrl.value,
        ocrType: ocrType.value,
        side: 'face'
      })
    });

    const result = await response.json();

    if (result.code === 200) {
      Message.success('OCR识别测试成功，请查看控制台输出结果');
      console.log('OCR识别结果:', result.data);
    } else {
      Message.error(result.message || 'OCR识别测试失败');
      console.error('OCR识别失败:', result);
    }
  } catch (error) {
    console.error('OCR识别测试失败:', error);
    Message.error('OCR识别测试失败');
  } finally {
    testingOCR.value = false;
  }
};

// 提交表单
const submitForm = async () => {
  saving.value = true;
  try {
    Message.loading('正在保存设置，请稍候...');

    // 组装要提交的数据，将布尔值转换为字符串
    const configData = {
      enabled: formModel.value.enabled.toString(),
      accessKeyId: formModel.value.accessKeyId,
      accessKeySecret: formModel.value.accessKeySecret,
      region: formModel.value.region
    };

    // 分别更新每个配置项
    const updatePromises = Object.entries(configData).map(([key, value]) => {
      return configApi.update('OCR', key, {
        configValue: value,
        isDefault: true
      });
    });

    await Promise.all(updatePromises);

    Message.success('OCR配置保存成功');
    await fetchSettings(); // 重新获取配置
  } catch (error) {
    console.error('保存OCR配置失败:', error);
    Message.error('保存配置失败，请稍后重试');
  } finally {
    saving.value = false;
  }
};

onMounted(() => {
  fetchSettings();
});
</script>

<style lang="less" scoped>
:deep(.arco-form-item-extra) {
  margin: 6px 0px !important;
  color: #86909c;
}

:deep(.arco-form-item-label-col) {
  margin-bottom: 8px !important;
}

:deep(.arco-card) {
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

:deep(.arco-input-wrapper) {
  width: 100%;
}

:deep(.arco-form-item) {
  margin-bottom: 16px !important;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: #1d2129;
}

:deep(.arco-select-view) {
  width: 100%;
}

.test-connection-card {
  border: 1px solid #e5e6eb;
}

:deep(.arco-btn) {
  height: 36px;
  font-weight: 500;
}
</style>
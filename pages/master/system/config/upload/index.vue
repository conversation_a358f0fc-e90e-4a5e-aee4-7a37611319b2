<template>
  <div class="p-5 bg-white rounded">
    <a-tabs default-active-key="sms">
      <!-- 左侧：存储方式 -->
      <div class="w-full" style="width: 25%;">
        <a-card bordered :body-style="{ padding: '16px' }">
          <div style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;">存储方式</div>
          <div class="mb-3 text-sm text-gray-500">选择文件存储的位置，只能选择一种存储方式</div>
          <div class="storage-options">
            <div
              v-for="item in storageList"
              :key="item.value"
              :class="['storage-option-card', { active: formModel.storage_mode === item.value }]"
              @click="formModel.storage_mode = item.value"
            >
              <div class="storage-content">
                <a-radio :model-value="formModel.storage_mode === item.value"></a-radio>
                <component :is="item.icon" class="storage-icon" />
                <div class="storage-text">{{ item.label }}</div>
              </div>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 右侧：云配置和上传限制 -->
      <div class="w-full flex flex-col gap-6" style="width: 74%;margin-left: 20px">
        <!-- 本地存储配置 -->
        <a-card
          v-if="getStorageConfigKeyByValue(formModel.storage_mode) === 'local'"
          :body-style="{ padding: '16px' }"
        >
          <div
            style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;"
          >{{ getStorageLabelByValue(formModel.storage_mode) }}</div>
          <div
            class="mb-3 text-sm text-gray-500"
          >请填写{{ getStorageLabelByValue(formModel.storage_mode) }}的相关配置信息</div>

          <a-form :model="formModel.local" layout="vertical" class="local-config-form">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 动态生成其他未预设的表单项 -->
              <template v-if="configData && configData.local">
                <a-form-item
                  v-for="(value, key) in configData.local"
                  :key="key"
                  :field="key"
                  :label="key"
                >
                  <a-input
                    v-if="typeof value !== 'boolean'"
                    v-model="formModel.local[key]"
                    :placeholder="'请输入' + key"
                  />
                  <a-switch v-else v-model="formModel.local[key]" />
                </a-form-item>
              </template>
            </div>
          </a-form>
        </a-card>

        <!-- OSS配置 -->
        <a-card
          v-if="getStorageConfigKeyByValue(formModel.storage_mode) !== 'local'"
          :body-style="{ padding: '16px' }"
        >
          <div
            style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;"
          >{{ getStorageLabelByValue(formModel.storage_mode) }}配置</div>
          <div
            class="mb-3 text-sm text-gray-500"
          >请填写{{ getStorageLabelByValue(formModel.storage_mode) }}的相关配置信息</div>

          <template v-if="getStorageConfigKeyByValue(formModel.storage_mode) === 'aliyun'">
            <a-form :model="formModel.aliyun" layout="vertical" class="oss-config-form">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 动态生成其他未预设的表单项 -->
                <template v-if="configData && configData.aliyun">
                  <a-form-item
                    v-for="(value, key) in configData.aliyun"
                    :key="key"
                    :field="key"
                    :label="key"
                  >
                    <a-input
                      v-if="typeof value !== 'boolean'"
                      v-model="formModel.aliyun[key]"
                      :placeholder="'请输入' + key"
                    />
                    <a-switch v-else v-model="formModel.aliyun[key]" />
                  </a-form-item>
                </template>
              </div>
            </a-form>
          </template>

          <template v-else-if="getStorageConfigKeyByValue(formModel.storage_mode) === 'tencent'">
            <a-form :model="formModel.tencent" layout="vertical" class="oss-config-form">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 动态生成其他未预设的表单项 -->
                <template v-if="configData && configData.tencent">
                  <a-form-item
                    v-for="(value, key) in configData.tencent"
                    :key="key"
                    :field="key"
                    :label="key"
                  >
                    <a-input
                      v-if="typeof value !== 'boolean'"
                      v-model="formModel.tencent[key]"
                      :placeholder="'请输入' + key"
                    />
                    <a-switch v-else v-model="formModel.tencent[key]" />
                  </a-form-item>
                </template>
              </div>
            </a-form>
          </template>

          <template v-else-if="getStorageConfigKeyByValue(formModel.storage_mode) === 'qiniu'">
            <a-form :model="formModel.qiniu" layout="vertical" class="oss-config-form">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 动态生成其他未预设的表单项 -->
                <template v-if="configData && configData.qiniu">
                  <a-form-item
                    v-for="(value, key) in configData.qiniu"
                    :key="key"
                    :field="key"
                    :label="key"
                  >
                    <a-input
                      v-if="typeof value !== 'boolean'"
                      v-model="formModel.qiniu[key]"
                      :placeholder="'请输入' + key"
                    />
                    <a-switch v-else v-model="formModel.qiniu[key]" />
                  </a-form-item>
                </template>
              </div>
            </a-form>
          </template>
        </a-card>

        <!-- 上传限制 -->
        <a-card :body-style="{ padding: '16px' }">
          <div style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;">上传限制</div>
          <div class="mb-3 text-sm text-gray-500">设置允许上传的文件类型和大小限制</div>

          <a-radio-group v-model="activeLimitTab" type="button" class="mb-4">
            <a-radio value="image">图片限制</a-radio>
            <a-radio value="video">视频限制</a-radio>
            <a-radio value="file">文件限制</a-radio>
          </a-radio-group>

          <div v-if="activeLimitTab === 'image'" class="limit-content">
            <a-form :model="formModel" layout="vertical" class="limit-form">
              <a-form-item field="allow_image_ext" label="允许的文件类型" hide-asterisk>
                <a-input v-model="formModel.allow_image_ext" placeholder="请输入允许上传的图片类型" />
                <template #extra>多个类型请用英文逗号分隔</template>
              </a-form-item>
              <a-form-item field="upload_image_size_limit" label="文件大小限制 (MB)" hide-asterisk>
                <a-input-number
                  v-model="formModel.upload_image_size_limit"
                  placeholder="请输入图片大小限制"
                  :min="1"
                />
              </a-form-item>
            </a-form>
          </div>

          <div v-if="activeLimitTab === 'video'" class="limit-content">
            <a-form :model="formModel" layout="vertical" class="limit-form">
              <a-form-item field="allow_video_ext" label="允许的文件类型" hide-asterisk>
                <a-input v-model="formModel.allow_video_ext" placeholder="请输入允许上传的视频类型" />
                <template #extra>多个类型请用英文逗号分隔</template>
              </a-form-item>
              <a-form-item field="upload_video_size_limit" label="文件大小限制 (MB)" hide-asterisk>
                <a-input-number
                  v-model="formModel.upload_video_size_limit"
                  placeholder="请输入视频大小限制"
                  :min="1"
                />
              </a-form-item>
            </a-form>
          </div>

          <div v-if="activeLimitTab === 'file'" class="limit-content">
            <a-form :model="formModel" layout="vertical" class="limit-form">
              <a-form-item field="allow_file_ext" label="允许的文件类型" hide-asterisk>
                <a-input v-model="formModel.allow_file_ext" placeholder="请输入允许上传的文件类型" />
                <template #extra>多个类型请用英文逗号分隔</template>
              </a-form-item>
              <a-form-item field="upload_file_size_limit" label="文件大小限制 (MB)" hide-asterisk>
                <a-input-number
                  v-model="formModel.upload_file_size_limit"
                  placeholder="请输入文件大小限制"
                  :min="1"
                />
              </a-form-item>
            </a-form>
          </div>
        </a-card>
        <!-- 保存按钮 -->
        <div class="flex justify-end">
          <a-button type="primary" @click="submitForm">
            <template #icon>
              <icon-save />
            </template>
            保存设置
          </a-button>
        </div>
      </div>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import systemApi from "@/api/master/system";
definePageMeta({
  name: "system-config-upload",
  path: "/master/system/config/upload"
});

const configApi = systemApi.configuration;
// 存储方式列表，将在configure函数中动态加载
const storageList = ref([]);

// 云存储配置区块标题
const ossConfigTitleMap = ref({});

// 初始化存储方式标题映射
const initOssConfigTitleMap = () => {
  storageList.value.forEach(item => {
    ossConfigTitleMap.value[item.value] = item.label;
  });
};
// 各云存储表单项label
const ossConfigLabelMap = ref({});

// 初始化存储方式表单配置
const initOssConfigLabelMap = () => {
  // 清空存储映射
  ossConfigLabelMap.value = {};

  // 如果没有configData，直接返回
  if (!configData.value) {
    return;
  }

  // 为每个存储方式初始化空映射对象
  storageList.value.forEach(item => {
    ossConfigLabelMap.value[item.value] = {};
  });
};

// 获取表单字段标签 - 直接返回字段名称
const getFieldLabel = fieldName => {
  return fieldName;
};

const activeLimitTab = ref("image"); // 默认激活图片限制

// 调整 formModel 以匹配新的 UI 结构
const formModel = reactive({
  storage_mode: "",
  allow_image_ext: "",
  upload_image_size_limit: "", // 单位 MB
  allow_video_ext: "",
  upload_video_size_limit: "",
  allow_file_ext: "",
  upload_file_size_limit: "",
  // 本地存储配置项
  local: {
    basePath: "",
    baseUrl: "",
    maxSize: ""
  },
  // 阿里云OSS配置项
  aliyun: {
    region: "",
    endpoint: "",
    accessKeyId: "",
    accessKeySecret: "",
    bucket: ""
  },
  // 腾讯云OSS配置项
  tencent: {
    region: "",
    secretId: "",
    secretKey: "",
    bucket: "",
    appId: ""
  },
  // 七牛云OSS配置项
  qiniu: {
    region: "",
    accessKey: "",
    secretKey: "",
    bucket: "",
    domain: "",
    isPrivate: false
  }
});

// 全局配置数据缓存
const configData = ref(null);

//获取默认设置
const configure = async () => {
  const res = await configApi.configure("Upload");
  if (res.code === 200 && res.data) {
    // 接口返回的是数组，直接使用
    const storageTypesArray = res.data;
    configData.value = {};

    // 将数组数据转换为对象格式，方便后续使用
    storageTypesArray.forEach(item => {
      // 解析config_value字符串为对象
      let configValue = {};
      try {
        configValue = JSON.parse(item.config_value);
      } catch (e) {
        console.error("解析config_value失败:", e);
      }

      // 将解析后的配置存储到configData中
      configData.value[item.config_key] = configValue;
    });

    // 构建存储方式列表
    storageList.value = storageTypesArray.map(item => ({
      value: getStorageModeValue(item.config_key), // 将config_key转换为前端使用的value
      label: item.name,
      icon: getStorageIcon(item.config_key),
      configKey: item.config_key,
      isDefault: item.is_default === 1
    }));

    // 设置默认选中的存储方式
    const defaultStorage = storageList.value.find(item => item.isDefault);
    if (defaultStorage) {
      formModel.storage_mode = defaultStorage.value;
    }
  }
};

// 根据config_key获取前端使用的value
const getStorageModeValue = configKey => {
  // 生成唯一的值，使用configKey和随机字符串组合
  return `storage_${configKey}_${Math.random()
    .toString(36)
    .substring(2, 8)}`;
};

//根据config_key获取图标;
const getStorageIcon = configKey => {
  return configKey === "local" ? "icon-home" : "icon-cloud";
};

// 根据value获取存储方式的configKey
const getStorageConfigKeyByValue = value => {
  const storage = storageList.value.find(item => item.value === value);
  return storage ? storage.configKey : "";
};

// 根据value获取存储方式的名称
const getStorageLabelByValue = value => {
  const storage = storageList.value.find(item => item.value === value);
  return storage ? storage.label : "";
};

// 这里已经在前面定义了getFieldLabel函数，不需要重复定义

// 获取设置
const fetchSettings = async () => {
  try {
    // 获取配置数据
    const res = await configApi.objectTypes("Upload");

    if (res.code === 200 && res.data) {
      // 缓存数据供切换存储模式时使用
      configData.value = res.data;
      console.log("API返回数据:", JSON.stringify(configData.value));

      // 输出每种存储方式的字段，便于调试
      if (configData.value.local)
        console.log("本地存储字段:", Object.keys(configData.value.local));
      if (configData.value.aliyun)
        console.log("阿里云字段:", Object.keys(configData.value.aliyun));
      if (configData.value.tencent)
        console.log("腔讯云字段:", Object.keys(configData.value.tencent));
      if (configData.value.qiniu)
        console.log("七牛云字段:", Object.keys(configData.value.qiniu));

      // 初始化存储方式标题映射
      initOssConfigTitleMap();

      // 初始化表单配置（现在基于API返回的数据）
      initOssConfigLabelMap();

      // 加载当前存储模式的配置
      loadStorageModeConfig(formModel.storage_mode);

      // 加载上传限制数据
      loadUploadLimitConfig();
    } else {
      Message.warning("获取配置数据格式不正确");
    }
  } catch (error) {
    console.error("获取设置失败", error);
    Message.error("获取设置失败");
  }
};

// 加载上传限制配置
const loadUploadLimitConfig = () => {
  if (!configData.value) return;

  // 首先检查当前存储模式的配置中是否有 uploadLimit
  const currentStorageType = getStorageConfigKeyByValue(formModel.storage_mode);
  const currentConfig = configData.value[currentStorageType];

  // 根据用户需求：如果接口返回值有uploadLimit的话 则根据根据uploadLimit里面的值回显数据
  // 否则上传限制模块里面的值 都默认为空
  if (currentConfig && currentConfig.uploadLimit) {
    console.log("加载当前存储类型的上传限制:", currentConfig.uploadLimit);
    // 如果当前存储模式的配置中有 uploadLimit，使用它
    formModel.allow_image_ext = currentConfig.uploadLimit.allow_image_ext || "";
    formModel.upload_image_size_limit =
      currentConfig.uploadLimit.upload_image_size_limit || 0;
    formModel.allow_video_ext = currentConfig.uploadLimit.allow_video_ext || "";
    formModel.upload_video_size_limit =
      currentConfig.uploadLimit.upload_video_size_limit || 0;
    formModel.allow_file_ext = currentConfig.uploadLimit.allow_file_ext || "";
    formModel.upload_file_size_limit =
      currentConfig.uploadLimit.upload_file_size_limit || 0;
  } else {
    console.log("当前存储类型没有上传限制设置，清空表单数据");
    // 如果没有任何 uploadLimit，清空表单数据
    formModel.allow_image_ext = "";
    formModel.upload_image_size_limit = 0;
    formModel.allow_video_ext = "";
    formModel.upload_video_size_limit = 0;
    formModel.allow_file_ext = "";
    formModel.upload_file_size_limit = 0;
  }
};

// 根据存储模式加载对应配置
const loadStorageModeConfig = mode => {
  if (!configData.value) return;

  // 获取存储方式的configKey
  const configKey = getStorageConfigKeyByValue(mode);

  // 根据选择的存储模式加载对应配置
  switch (configKey) {
    case "local":
      // 本地存储配置
      if (configData.value.local) {
        // 复制配置，但排除 uploadLimit
        const { uploadLimit, ...localConfig } = configData.value.local;
        formModel.local = { ...localConfig };
      }
      break;

    case "aliyun":
      // 阿里云配置
      if (configData.value.aliyun) {
        // 复制配置，但排除 uploadLimit
        const { uploadLimit, ...aliyunConfig } = configData.value.aliyun;
        formModel.aliyun = { ...aliyunConfig };
      }
      break;

    case "tencent":
      // 腾讯云配置
      if (configData.value.tencent) {
        // 复制配置，但排除 uploadLimit
        const { uploadLimit, ...tencentConfig } = configData.value.tencent;
        formModel.tencent = { ...tencentConfig };
      }
      break;

    case "qiniu":
      // 七牛云配置
      if (configData.value.qiniu) {
        // 复制配置，但排除 uploadLimit
        const { uploadLimit, ...qiniuConfig } = configData.value.qiniu;
        formModel.qiniu = { ...qiniuConfig };
      }
      break;
  }

  // 加载上传限制数据
  loadUploadLimitConfig();
};

// 监听存储模式变化
watch(
  () => formModel.storage_mode,
  newMode => {
    if (configData.value) {
      loadStorageModeConfig(newMode);
    }
  }
);

// 提交表单
const submitForm = async () => {
  try {
    // 准备上传限制对象
    const uploadLimit = {
      allow_image_ext: formModel.allow_image_ext, // 保持字符串格式
      upload_image_size_limit: formModel.upload_image_size_limit,
      allow_video_ext: formModel.allow_video_ext, // 保持字符串格式
      upload_video_size_limit: formModel.upload_video_size_limit,
      allow_file_ext: formModel.allow_file_ext, // 保持字符串格式
      upload_file_size_limit: formModel.upload_file_size_limit
    };

    let dataToSave;
    let storageConfig;

    // 根据当前选择的存储方式，只提交对应的配置
    const configKey = getStorageConfigKeyByValue(formModel.storage_mode);
    switch (configKey) {
      case "local":
        // 本地存储配置
        storageConfig = { ...formModel.local };
        break;

      case "aliyun":
        // 阿里云配置
        storageConfig = { ...formModel.aliyun };
        break;

      case "tencent":
        // 腾讯云配置
        storageConfig = { ...formModel.tencent };
        break;

      case "qiniu":
        // 七牛云配置
        storageConfig = { ...formModel.qiniu };
        break;

      default:
        // 默认情况下使用空对象
        storageConfig = {};
    }

    // 获取接口期望的存储模式格式
    const apiStorageMode =
      getStorageConfigKeyByValue(formModel.storage_mode) || "local";

    // 获取当前选中存储方式的名称
    const storageName = getStorageLabelByValue(formModel.storage_mode) || "";

    // 组装最终要提交的数据
    dataToSave = {
      storage_mode: apiStorageMode, // 使用转换后的存储模式
      configValue: { ...storageConfig, uploadLimit: uploadLimit }, // 上传限制配置
      isDefault: true, // 设置为默认存储方式
      name: storageName // 存储方式的名称
    };

    console.log("准备保存设置:", dataToSave);

    const data = { ...dataToSave };
    delete data.storage_mode;
    // 调用更新后的 API 接口保存配置
    const res = await configApi.update("Upload", apiStorageMode, data);
    if (res.code === 200) {
      fetchSettings();
      configure();
      Message.success(res.message);
    } else {
      Message.error(res.message);
    }
  } catch (error) {
    console.error("保存上传设置失败:", error);
    Message.error("保存失败，请稍后重试");
  }
};

onMounted(() => {
  fetchSettings();
  configure();
});
</script>

<style lang="less" scoped>
// 卡片样式优化
:deep(.arco-card) {
  border-radius: 4px;
}

:deep(.arco-card-header) {
  border-bottom: none;
  padding-bottom: 8px;
}

:deep(.arco-card-header-title) {
  font-weight: 600;
  font-size: 16px;
}

:deep(.arco-card-header-extra) {
  color: var(--color-text-3);
  font-size: 12px;
}

// 存储方式大卡片单选
// 存储方式卡片样式
.storage-options {
  width: 100%;
}

.storage-option-card {
  width: 100%;
  height: 54px;
  display: flex;
  align-items: center;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 16px;
  margin-bottom: 15px;
  cursor: pointer;
  background: #fff;
  transition: border-color 0.2s;
  position: relative;
}

.storage-option-card.active {
  border-color: rgb(var(--primary-6));
  background: #f7f8fa;
}

.storage-option-card:hover {
  border-color: rgb(var(--primary-6));
}

.storage-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.storage-icon {
  font-size: 18px;
  margin: 0 8px;
  color: #86909c;
}

.storage-text {
  font-size: 14px;
  line-height: 1.2;
}

.storage-option-card .arco-icon {
  font-size: 18px;
  color: #86909c;
}

// 上传限制 tabs 优化
.limit-tabs {
  :deep(.arco-tabs-nav::before) {
    display: none; // 隐藏 tabs 导航下划线
  }

  :deep(.arco-tabs-tab) {
    padding: 6px 16px; // 调整 tab 内边距
    font-size: 14px;
  }
}

// 上传限制表单优化
.limit-form {
  :deep(.arco-form-item) {
    margin-bottom: 16px; // 减小表单项之间的间距
  }

  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    color: var(--color-text-2); // 标签颜色变浅
    font-weight: normal; // 标签不加粗
  }

  :deep(.arco-form-item-extra) {
    font-size: 12px; // 减小提示文字大小
    margin-top: 4px;
  }
}
:deep(.arco-form-item-extra) {
  margin: 8px 0px !important;
}
:deep(.arco-form-item-label-col) {
  margin-bottom: 10px !important;
}
:deep(.arco-form-item) {
  margin-bottom: 0px !important;
}
:deep(.arco-radio-button-content) {
  padding: 5px 26px !important;
}
</style>

<template>
  <div class="p-5 bg-white rounded">
    <a-tabs default-active-key="alipay">
      <a-tab-pane key="alipay" title="支付宝支付">
        <a-alert class="mb-4">配置支付宝支付</a-alert>
        <ma-form
          ref="alipayMaFormRef"
          v-model="alipayForm"
          :columns="alipayColumns"
          :style="{ width: '600px' }"
          @submit="saveAlipayConfig"
        />
      </a-tab-pane>
      <a-tab-pane key="wechat" title="微信支付">
        <a-alert class="mb-5">配置微信支付</a-alert>
        <ma-form
          ref="wechatMaFormRef"
          v-model="wechatForm"
          :columns="wechatColumns"
          :style="{ width: '600px' }"
          @submit="saveWechatConfig"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import paymentConfigApi from "@/api/master/paymentConfig.js";

definePageMeta({
  name: "system-config-paymentsettings",
  path: "/master/system/config/paymentsettings"
});

// --- 支付宝支付 ---
const alipayMaFormRef = ref();
const alipayForm = ref({
  enabled: false,
  app_id: "",
  private_key: "",
  public_key: "",
  alipay_public_key: "",
  alipay_cert: ""
});

const alipayColumns = ref([
  {
    title: "是否启用",
    dataIndex: "enabled",
    formType: "switch",
    labelWidth: "200px",
  },
  {
    title: "支付宝应用ID",
    dataIndex: "app_id",
    formType: "input",
    placeholder: "请输入支付宝应用ID",
    labelWidth: "200px",
    rules: [{ required: true, message: "支付宝应用ID必填" }],
    hide: !alipayForm.value.enabled
  },
  {
    title: "应用私钥",
    dataIndex: "private_key",
    formType: "textarea",
    placeholder: "请输入应用私钥",
    labelWidth: "200px",
    rules: [{ required: true, message: "应用私钥必填" }],
    hide: !alipayForm.value.enabled
  },
  {
    title: "应用公钥",
    dataIndex: "public_key",
    formType: "textarea",
    placeholder: "请输入应用公钥内容",
    hide: !alipayForm.value.enabled,
    labelWidth: "200px",
    rows: 4
  },
  // {
  //   title: "商品视频",
  //   dataIndex: "video",
  //   formType: "upload",
  //   type: "file",
  //   multiple: false,
  //   limit: 1,
  //   accept: ".mp4,.mov",
  //   tip: "最多上传1个视频，大小不超过50MB",
  //   returnType: "url",
  // },
  {
    title: "支付宝公钥",
    dataIndex: "alipay_public_key",
    formType: "textarea",
    placeholder: "请输入支付宝公钥内容",
    hide: !alipayForm.value.enabled,
    labelWidth: "200px",
    rows: 4,
    rules: [{ required: true, message: "支付宝公钥必填" }]
  },
  {
    title: "支付宝根证书",
    dataIndex: "alipay_cert",
    formType: "textarea",
    placeholder: "请输入支付宝根证书内容",
    hide: !alipayForm.value.enabled,
    labelWidth: "200px",
    rows: 4
  }
]);

// 监听支付宝启用状态变化，动态更新列的 hide 属性
watch(
  () => alipayForm.value.enabled,
  newValue => {
    alipayColumns.value.forEach(col => {
      if (col.dataIndex !== "enabled") {
        col.hide = !newValue;
      }
    });
  }
);

const saveAlipayConfig = async data => {
  console.log("提交的支付宝支付配置数据:", data);
  try {
    // 处理文件上传字段，将URL转换为文件内容
    const configData = await processFileFields(data, ['public_key', 'alipay_public_key', 'alipay_cert']);

    // 添加配置名称和环境
    configData.environment = "sandbox"; // 默认沙箱环境

    let result;

    if (alipayConfigId.value) {
      // 如果已有配置ID，更新现有配置（不设置配置名称，保持原有名称）
      result = await paymentConfigApi.update(alipayConfigId.value, configData);
    } else {
      // 如果没有配置ID，创建新配置
      configData.config_name = configData.config_name || "支付宝支付配置";
      result = await paymentConfigApi.saveAlipayConfig(configData);
    }

    if (result.code === 200) {
      Message.success("支付宝支付配置保存成功");
      // 重新加载配置
      await loadConfigs();
    } else {
      Message.error(result.message || "支付宝支付配置保存失败");
    }
  } catch (error) {
    console.error("保存支付宝配置失败:", error);
    Message.error("支付宝支付配置保存失败");
  }
};

// --- 微信支付 ---
const wechatMaFormRef = ref();
const wechatForm = ref({
  enabled: false,
  mch_id: "",
  api_v3_key: "",
  mch_private_key: "",
  mch_public_key: "",
  wechat_pay_cert: "",
  wechat_pay_public_key_id: "",
  native_callback_url: "https://freshpeakpet.com"
});

// 微信支付表单列定义
const wechatColumns = ref([
  {
    title: "是否启用",
    dataIndex: "enabled",
    formType: "switch",
    labelWidth: "200px"
  },
  {
    title: "商户号",
    dataIndex: "mch_id",
    formType: "input",
    placeholder: "请输入商户号",
    labelWidth: "200px",
    rules: [{ required: true, message: "商户号必填" }],
    hide: !wechatForm.value.enabled
  },
  {
    title: "APIv3密钥",
    dataIndex: "api_v3_key",
    formType: "input",
    placeholder: "请输入APIv3密钥",
    labelWidth: "200px",
    rules: [{ required: true, message: "APIv3密钥必填" }],
    hide: !wechatForm.value.enabled
  },
  {
    title: "商户私钥",
    dataIndex: "mch_private_key",
    formType: "textarea",
    placeholder: "请输入商户私钥内容",
    hide: !wechatForm.value.enabled,
    labelWidth: "200px",
    rows: 4,
    rules: [{ required: true, message: "商户私钥必填" }]
  },
  {
    title: "商户公钥",
    dataIndex: "mch_public_key",
    formType: "textarea",
    placeholder: "请输入商户公钥内容",
    hide: !wechatForm.value.enabled,
    labelWidth: "200px",
    rows: 4
  },
  {
    title: "微信支付公钥",
    dataIndex: "wechat_pay_cert",
    formType: "textarea",
    placeholder: "请输入微信支付公钥内容",
    hide: !wechatForm.value.enabled,
    labelWidth: "200px",
    rows: 4,
    rules: [{ required: true, message: "微信支付公钥必填" }]
  },
  {
    title: "微信支付公钥ID",
    dataIndex: "wechat_pay_public_key_id",
    formType: "input",
    placeholder: "请输入微信支付公钥ID",
    labelWidth: "200px",
    hide: !wechatForm.value.enabled
  },
  {
    title: "Native支付回调链接",
    dataIndex: "native_callback_url",
    formType: "input",
    placeholder: "请输入Native支付回调链接",
    labelWidth: "200px",
    hide: !wechatForm.value.enabled
  }
]);

// 监听微信支付启用状态变化，动态更新列的 hide 属性
watch(
  () => wechatForm.value.enabled,
  newValue => {
    wechatColumns.value.forEach(col => {
      if (col.dataIndex !== "enabled") {
        col.hide = !newValue;
      }
    });
  }
);

const saveWechatConfig = async data => {
  console.log("提交的微信支付配置数据:", data);
  try {
    // 处理文件上传字段，将URL转换为文件内容
    const configData = await processFileFields(data, ['mch_private_key', 'mch_public_key', 'wechat_pay_cert']);

    // 添加配置名称和环境
    configData.environment = "sandbox"; // 默认沙箱环境

    let result;

    if (wechatConfigId.value) {
      // 如果已有配置ID，更新现有配置（不设置配置名称，保持原有名称）
      result = await paymentConfigApi.update(wechatConfigId.value, configData);
    } else {
      // 如果没有配置ID，创建新配置
      configData.config_name = configData.config_name || "微信支付配置";
      result = await paymentConfigApi.saveWechatConfig(configData);
    }

    if (result.code === 200) {
      Message.success("微信支付配置保存成功");
      // 重新加载配置
      await loadConfigs();
    } else {
      Message.error(result.message || "微信支付配置保存失败");
    }
  } catch (error) {
    console.error("保存微信配置失败:", error);
    Message.error("微信支付配置保存失败");
  }
};

// 安全的Base64解码函数（浏览器兼容）
const safeBase64Decode = (base64String) => {
  try {
    if (!base64String) return '';
    // 使用浏览器原生的atob函数进行Base64解码
    const binaryString = atob(base64String);
    // 将二进制字符串转换为UTF-8字符串
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new TextDecoder('utf-8').decode(bytes);
  } catch (error) {
    console.error('Base64解码失败:', error);
    return base64String; // 如果解码失败，返回原字符串
  }
};

// 处理文件字段，现在直接使用文本内容
const processFileFields = async (data, fileFields) => {
  const processedData = { ...data };

  // 现在文件字段直接是文本内容，不需要特殊处理
  // 只需要确保字段存在且不为空
  for (const field of fileFields) {
    if (processedData[field]) {
      // 去除首尾空白字符
      processedData[field] = processedData[field].trim();
    }
  }

  return processedData;
};

// 存储配置ID，用于更新
const alipayConfigId = ref(null);
const wechatConfigId = ref(null);

// 加载配置数据
const loadConfigs = async () => {
  try {
    // 加载支付宝配置
    const alipayResult = await paymentConfigApi.getDefault('alipay');
    if (alipayResult.code === 200 && alipayResult.data) {
      const alipayConfig = alipayResult.data;
      alipayConfigId.value = alipayConfig.id;
      alipayForm.value = {
        enabled: alipayConfig.enabled === 1,
        app_id: alipayConfig.config_data?.app_id || '',
        private_key: alipayConfig.config_data?.private_key || '',
        public_key: safeBase64Decode(alipayConfig.public_key),
        alipay_public_key: safeBase64Decode(alipayConfig.alipay_public_key),
        alipay_cert: safeBase64Decode(alipayConfig.alipay_cert)
      };
    }

    // 加载微信配置
    const wechatResult = await paymentConfigApi.getDefault('wechat');
    if (wechatResult.code === 200 && wechatResult.data) {
      const wechatConfig = wechatResult.data;
      wechatConfigId.value = wechatConfig.id;
      wechatForm.value = {
        enabled: wechatConfig.enabled === 1,
        mch_id: wechatConfig.config_data?.mch_id || '',
        api_v3_key: wechatConfig.config_data?.api_v3_key || '',
        mch_private_key: safeBase64Decode(wechatConfig.private_key),
        mch_public_key: safeBase64Decode(wechatConfig.public_key),
        wechat_pay_cert: safeBase64Decode(wechatConfig.wechat_pay_cert),
        wechat_pay_public_key_id: wechatConfig.config_data?.wechat_pay_public_key_id || '',
        native_callback_url: wechatConfig.config_data?.native_callback_url || 'https://v4api.ioa.8080bl.com/api/v1/master/wechat-pay/notify'
      };
    }
  } catch (error) {
    console.error('加载配置失败:', error);
    // 如果没有配置，使用默认值，不显示错误
  }
};

// 页面加载时获取配置
onMounted(() => {
  loadConfigs();
});
</script>

<style scoped>
/* 可添加样式 */
</style>
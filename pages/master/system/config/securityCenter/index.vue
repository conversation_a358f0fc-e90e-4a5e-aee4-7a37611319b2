<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 规则类型列 -->
      <template #rule_type="{ record }">
        <a-tag :color="record.rule_type === 1 ? 'red' : 'green'">
          {{ record.rule_type === 1 ? '黑名单' : '白名单' }}
        </a-tag>
      </template>

      <!-- 匹配类型列 -->
      <template #match_type="{ record }">
        <span>{{ getMatchTypeText(record.match_type) }}</span>
      </template>

      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="record.status === 1 ? 'green' : 'red'">
          {{ record.status === 1 ? '启用' : '禁用' }}
        </a-tag>
      </template>

      <!-- 命中次数列 -->
      <template #hit_count="{ record }">
        <a-statistic
          :value="parseInt(record.hit_count)"
          :value-style="{ fontSize: '14px' }"
        />
      </template>

      <!-- 最后命中时间列 -->
      <template #last_hit_at="{ record }">
        <span v-if="record.last_hit_at">
          {{ formatTime(record.last_hit_at) }}
        </span>
        <span v-else class="text-gray-400">从未命中</span>
      </template>

      <!-- 创建时间列 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>
    </MaCrud>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import dayjs from 'dayjs'
import firewallApi from '~/api/master/system/config/securityCenter/firewall.js'

// 页面标题
definePageMeta({
  name: 'system-config-securityCenter',
  path: '/master/system/config/securityCenter',
  title: '防火墙管理'
})

const crudRef = ref()

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center',
    editDisplay: false,
    width: 80
  },
  {
    title: '规则名称',
    dataIndex: 'rule_name',
    align: 'center',
    search: true,
    commonRules: [{ required: true, message: '规则名称必填' }],
    width: 150
  },
  {
    title: 'IP地址',
    dataIndex: 'ip_address',
    align: 'center',
    search: true,
    commonRules: [
      { required: true, message: 'IP地址必填' },
      {
        validator: (value, callback) => {
          if (!isValidIpAddress(value)) {
            callback('IP地址格式不正确')
          } else {
            callback()
          }
        }
      }
    ],
    width: 150
  },
  {
    title: '子网掩码',
    dataIndex: 'ip_mask',
    align: 'center',
    formType: 'input',
    help: 'IP段匹配时填写，如：*************',
    width: 120
  },
  {
    title: '规则类型',
    dataIndex: 'rule_type',
    align: 'center',
    search: true,
    formType: 'radio',
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '黑名单(禁止)', value: 1 },
        { label: '白名单(允许)', value: 2 }
      ]
    },
    commonRules: [{ required: true, message: '规则类型必选' }],
    width: 100
  },
  {
    title: '匹配类型',
    dataIndex: 'match_type',
    align: 'center',
    formType: 'select',
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '精确匹配', value: 1 },
        { label: 'IP段匹配', value: 2 },
        { label: '通配符匹配', value: 3 }
      ]
    },
    commonRules: [{ required: true, message: '匹配类型必选' }],
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center',
    search: true,
    formType: 'radio',
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    commonRules: [{ required: true, message: '状态必选' }],
    width: 80
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    align: 'center',
    formType: 'input-number',
    addDefaultValue: 0,
    formProps: {
      min: 0,
      max: 999,
      placeholder: '数值越大优先级越高'
    },
    width: 80
  },
  {
    title: '命中次数',
    dataIndex: 'hit_count',
    align: 'center',
    addDisplay: false,
    editDisplay: false,
    width: 100
  },
  {
    title: '最后命中时间',
    dataIndex: 'last_hit_at',
    align: 'center',
    addDisplay: false,
    editDisplay: false,
    width: 160
  },
  {
    title: '备注',
    dataIndex: 'remark',
    formType: 'textarea',
    formProps: {
      rows: 3,
      placeholder: '请输入备注信息'
    },
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    formType: 'range',
    search: true,
    addDisplay: false,
    editDisplay: false,
    width: 160
  }
]

// CRUD 配置
const crud = reactive({
  api: firewallApi.getList,
  showIndex: true,
  pageLayout: 'fixed',
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: '100px',
  add: {
    show: true,
    api: firewallApi.create
  },
  edit: {
    show: true,
    api: firewallApi.update
  },
  delete: {
    show: true,
    api: firewallApi.delete
  },
  beforeSearch: (params) => {
    // 如果有创建时间参数，转换为时间戳
    if (params.created_at) {
      params.startTime = new Date(params.created_at[0]).getTime()
      params.endTime = new Date(params.created_at[1]).getTime()
      delete params.created_at
    } else {
      delete params.startTime
      delete params.endTime
    }
    return params
  }
})

// 工具函数
const getMatchTypeText = (type) => {
  const typeMap = {
    1: '精确匹配',
    2: 'IP段匹配',
    3: '通配符匹配'
  }
  return typeMap[type] || '未知'
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  return dayjs(parseInt(timestamp)).format('YYYY-MM-DD HH:mm:ss')
}

const isValidIpAddress = (ip) => {
  // IPv4 正则表达式
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  // 支持通配符的IPv4格式
  const wildcardIpv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|\*)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|\*)$/

  return ipv4Regex.test(ip) || wildcardIpv4Regex.test(ip)
}


</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
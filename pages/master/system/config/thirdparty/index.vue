
<template>
  <div class="p-5 bg-white rounded">
    <a-tabs default-active-key="mobile">
      <a-tab-pane key="mobile" title="手机登录">
        <a-alert class="mb-4">配置手机号+验证码/密码登录方式。</a-alert>
        <ma-form
          ref="mobileMaFormRef"
          v-model="mobileForm"
          :columns="mobileColumns"
          :style="{ width: '600px' }"
          @submit="saveMobileConfig"
        />
      </a-tab-pane>
      <a-tab-pane key="wechat" title="微信登录(公众号)">
        <a-alert class="mb-5">配置微信开放平台或公众号扫码/授权登录。</a-alert>
        <ma-form
          ref="wechatMaFormRef"
          v-model="wechatForm"
          :columns="wechatColumns"
          :style="{ width: '600px' }"
          @submit="saveWechatConfig"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, watch } from "vue"; // 引入 watch
import { Message } from "@arco-design/web-vue";

definePageMeta({
  name: "system-config-thirdparty",
  path: "/master/system/config/thirdparty"
});

// --- 手机登录 ---
const mobileMaFormRef = ref();
const mobileForm = ref({
  enabled: false // 是否启用
  // 可能还有其他手机登录相关配置，例如使用的短信服务商等
});
const mobileColumns = ref([
  {
    title: "是否启用",
    dataIndex: "enabled",
    formType: "switch",
    labelWidth: "150px"
  }
  // 添加其他手机登录配置项...
]);
const saveMobileConfig = async data => {
  console.log("提交的手机登录配置数据:", data);
  try {
    // await api.saveMobileConfig(data);
    Message.success("手机登录配置保存成功");
  } catch (error) {
    Message.error("手机登录配置保存失败");
  }
};

// --- 微信登录 ---
const wechatMaFormRef = ref();
const wechatForm = ref({
  enabled: false, // 是否启用
  app_id: "",
  app_secret: ""
});

// 微信表单列定义
const wechatColumns = ref([
  {
    title: "是否启用",
    dataIndex: "enabled",
    formType: "switch",
    labelWidth: "200px"
  },
  {
    title: "App ID",
    dataIndex: "app_id",
    formType: "input",
    placeholder: "请输入微信开放平台或公众号的 App ID",
    labelWidth: "200px",
    rules: [{ required: true, message: "App ID 必填" }],
    hide: !wechatForm.value.enabled // 初始隐藏状态
  },
  {
    title: "TOKEN",
    dataIndex: "token",
    formType: "input",
    placeholder: "请输入微信开放平台或公众号的 TOKEN",
    labelWidth: "200px",
    rules: [{ required: true, message: "TOKEN 必填" }],
    hide: !wechatForm.value.enabled // 初始隐藏状态
  },
  {
    title: "encodingAESKey",
    dataIndex: "encoding_aes_key",
    formType: "input",
    placeholder: "请输入微信开放平台或公众号的 encodingAESKey",
    labelWidth: "200px",
    rules: [{ required: true, message: "encodingAESKey 必填" }],
    hide: !wechatForm.value.enabled // 初始隐藏状态
  },
  {
    title: "App Secret",
    dataIndex: "app_secret",
    formType: "input-password",
    placeholder: "请输入微信开放平台或公众号的 App Secret",
    labelWidth: "200px",
    rules: [{ required: true, message: "App Secret 必填" }],
    hide: !wechatForm.value.enabled // 初始隐藏状态
  }
]);

// 监听微信启用状态变化，动态更新列的 hide 属性
watch(
  () => wechatForm.value.enabled,
  newValue => {
    const appIdColumn = wechatColumns.value.find(
      col => col.dataIndex === "app_id"
    );
    const appSecretColumn = wechatColumns.value.find(
      col => col.dataIndex === "app_secret"
    );
    if (appIdColumn) appIdColumn.hide = !newValue;
    if (appSecretColumn) appSecretColumn.hide = !newValue;
  }
);

const saveWechatConfig = async data => {
  console.log("提交的微信登录配置数据:", data);
  try {
    // await api.saveWechatConfig(data);
    Message.success("微信登录配置保存成功");
  } catch (error) {
    Message.error("微信登录配置保存失败");
  }
};

// TODO: 添加获取配置的逻辑，并在 onMounted 中调用
</script>

<style scoped>
/* 可添加样式 */
</style>
<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">
        {{ record.status == 1 ? '正常' : '停用' }}
      </template>
       <!-- 自定义列 - 创建时间 -->
       <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>
      
    </MaCrud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import systemApi from '@/api/master/system';
const deptApi = systemApi.dept;

definePageMeta({
  name: 'master-system-permission-dept',
  path: '/master/system/permission/dept'
})

const crudRef = ref();

// 表格列定义
const columns = [
  {
    title: '部门名称',
    dataIndex: 'name',
    align: 'center',
    search: true, 
    commonRules: [{ required: true, message: '部门名称必填' }],
  },
  {
    title: '部门负责人',
    dataIndex: 'leader',
    align: 'center',
    search: true, 
  },
  {
    title: '手机号码',
    dataIndex: 'phone',
    align: 'center',
    commonRules: [{required: true, match: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码' }]
  },
  {
    title: '排序',
    dataIndex: 'sort',
    align: 'center',
  },
  {
    title: '状态', dataIndex: 'status', search: true, 
    formType: 'radio',
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 }
      ]
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    formType: "range",
    search: true, 
    addDisplay: false, 
    editDisplay: false,
    form:false
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'center',
  }, 
];

// CRUD 配置
const crud = reactive({
  // 使用本地数据源而不是直接调用API
  api: deptApi.getDeptList,
  showIndex: true,
  pageLayout: 'fixed',
  // rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: '100px',
  add: { show: true,api:deptApi.createDept },
  edit: { show: true,api:deptApi.updateDept }, // 使用自定义编辑
  delete: { show: true,api:deptApi.deleteDept }, // 使用自定义删除
  //export: { show: true },//导出
    // 搜索前处理参数
    beforeSearch: (params) => {
    // 如果有创建时间参数，转换为时间戳
    if(params.created_at){
      params.startTime =new Date(params.created_at[0]).getTime()/1000;
      params.endTime =new Date(params.created_at[1]).getTime()/1000;
      delete params.created_at
    }else{
      delete params.startTime
      delete params.endTime
    }
    return params;
  },
});


</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}
</style>

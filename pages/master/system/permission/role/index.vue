<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block lg:flex justify-between p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
       <!-- 状态列 -->
       <template #status="{ record }">
        {{ record.status == 1 ? '正常' : '停用' }}
      </template>
        <!-- 自定义列 - 创建时间 -->
        <template #createdAt="{ record }">
        <div v-time="record.createdAt"></div>
      </template>
      <template #dataScope="{ record }">
        {{
          {
            1: '全部数据权限',
            2: '自定义数据权限',
            3: '本部门数据权限',
            4: '本部门及以下数据权限',
            5: '本人数据权限',
          }[record.data_scope]
        }}
      </template>
      <!-- 自定义菜单树选择器 -->
      <template #form-menuIds>
        <div style="height: 300px; width: 100%; border: 1px solid #e5e6eb; padding: 8px; overflow: auto;">
          <a-tree 
            :data="treeData" 
            :field-names="{ title: 'name', key: 'id', children: 'children' }" 
            checkable
            :checked-keys="selectedMenuIds"
            @check="handleTreeCheck"
            :default-expanded-keys="[]"
            :check-strictly="editMode"
          />
        </div>
      </template>
      <template #deptIds="{ record }">
        <span v-for="(item,index) in record.depts" :key="item.id">
          <em v-if="index">,</em>
          {{ item.name }}
          </span>
      </template>

    </ma-crud>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { Message } from '@arco-design/web-vue'
import MaForm from '~/components/base/ma-form/index.vue' // 导入 MaForm
import systemApi from '@/api/master/system';
const roleApi = systemApi.role;
definePageMeta({
  name: "master-system-permission-role",
  path: '/master/system/permission/role',
})

const crudRef = ref()
const deptData = ref([]);

// 获取部门列表
const fetchDeptList = async () => {
  try {
    const res = await systemApi.dept.getDeptList();
    if (res.code == '200') {
      // 确保部门ID是字符串类型
      deptData.value = (res.data.items || []).map(item => ({
        ...item,
        id: String(item.id) // 确保 ID 是字符串类型
      }));
      console.log(deptData.value,'deptData')
    }  
  } catch (error) {
    console.error('获取部门列表失败:', error);
  }
};

const treeData = ref([]);
const selectedMenuIds = ref([]);
const editMode = ref(false); // 是否处于编辑模式

// 找出所有节点的子节点
const findAllChildrenIds = (nodes) => {
  const result = {};
  
  const traverse = (nodes, parentId = null) => {
    if (!Array.isArray(nodes)) return [];
    
    const childrenIds = [];
    
    nodes.forEach(node => {
      childrenIds.push(node.id);
      
      if (node.children && node.children.length > 0) {
        const subChildrenIds = traverse(node.children, node.id);
        childrenIds.push(...subChildrenIds);
      }
    });
    
    if (parentId) {
      result[parentId] = childrenIds;
    }
    
    return childrenIds;
  };
  
  traverse(nodes);
  return result;
};

// 获取菜单树数据
const fetchMenuTree = async () => {
  try {
    const res = await systemApi.menu.tree();
    if (res.code == '200') {    
      const extractMenuData = (data) => {
        const result = data.map(item => {
          return {
            id: item.id,
            name: item.name,
            children: item.children ? extractMenuData(item.children) : []
          }
        })
        return result;
      }
      treeData.value = extractMenuData(res.data || []);
      console.log(treeData.value, 'treeData');
    } else {
      Message.error('获取菜单树失败');
    }
  } catch (error) {
    console.error('获取菜单树错误:', error);
    Message.error('获取菜单树失败');
  }
};

// 工具函数：收集完整的菜单权限链（选中节点及其所有父节点）
const collectFullMenuIds = (checkedKeys) => {
  // 构建父子关系映射
  const parentMap = new Map();
  const buildRelationship = (nodes, parentId = null) => {
    if (!Array.isArray(nodes)) return;
    
    nodes.forEach(node => {
      if (parentId) {
        parentMap.set(node.id, parentId);
      }
      
      if (node.children && node.children.length > 0) {
        buildRelationship(node.children, node.id);
      }
    });
  };
  
  buildRelationship(treeData.value);
  
  // 递归获取父节点
  const getParentIds = (nodeId) => {
    const parentIds = [];
    let currentId = nodeId;
    
    while(parentMap.has(currentId)) {
      const parentId = parentMap.get(currentId);
      parentIds.push(parentId);
      currentId = parentId;
    }
    
    return parentIds;
  };
  
  // 获取所有选中节点及其父节点
  const allMenuIds = new Set(checkedKeys);
  checkedKeys.forEach(nodeId => {
    const parentIds = getParentIds(nodeId);
    parentIds.forEach(id => allMenuIds.add(id));
  });
  
  // 转换为数组并返回
  return Array.from(allMenuIds);
};

// 处理树节点选择
const handleTreeCheck = (checkedKeys, info) => {
  // 获取当前点击的节点信息
  const nodeKey = info.node.key;
  const checked = info.checked;
  console.log('用户操作:', checked ? '选中' : '取消选中', '节点:', nodeKey);
  console.log('当前选中的节点列表:', checkedKeys);
  
  // 更新选中的菜单ID
  selectedMenuIds.value = checkedKeys;
  
  // 如果是编辑模式（严格模式），需要手动处理父子节点关系
  if (editMode.value) {
    // 获取当前节点ID和选中状态
    const nodeId = info.node.id;
    const isChecked = info.checked;
    
    // 获取所有子节点的映射
    const childrenMap = findAllChildrenIds(treeData.value);
    
    // 如果当前节点有子节点，且被选中，则将所有子节点也选中
    if (childrenMap[nodeId] && isChecked) {
      selectedMenuIds.value = [...new Set([...selectedMenuIds.value, ...childrenMap[nodeId]])];
    }
    
    // 如果当前节点有子节点，且被取消选中，则将所有子节点也取消选中
    if (childrenMap[nodeId] && !isChecked) {
      selectedMenuIds.value = selectedMenuIds.value.filter(id => !childrenMap[nodeId].includes(id));
    }
  }
  
  console.log('最终选中的节点:', selectedMenuIds.value);
};

// 页面初始化
onMounted(() => {
  fetchDeptList();
  fetchMenuTree();
});
const crud = reactive({
  api: roleApi.getList,
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 150,
  add: { show: true, api: roleApi.create,  },
  edit: { show: true, api: roleApi.update, },
  delete: { show: true, api: roleApi.delete, auth: ['user_add'], },
  // 新增前的钩子，用于清空选中的菜单
  beforeOpenAdd: () => {
    console.log('新增前清空菜单');
    selectedMenuIds.value = [];
    // 新增时直接使用级联模式，提供更好的操作体验
    editMode.value = false;
    return true; // 必须返回true才会打开新增弹窗
  },
  // 编辑前处理参数
  beforeEdit: (params) => {
    console.log('编辑前原始参数:', params);
    // 编辑前处理参数
    params.dataScope = params.data_scope;
    delete params.data_scope;
    
    // 收集完整的菜单权限链（选中节点及其所有父节点）
    params.menuIds = collectFullMenuIds(selectedMenuIds.value);
    
    // 处理部门IDs
    if (params.dataScope == 2) {
      // 如果是自定义数据权限，确保有deptIds
      if (!params.deptIds || !Array.isArray(params.deptIds) || params.deptIds.length === 0) {
        // 如果没有deptIds，尝试从原始数据中获取
        if (params.depts && Array.isArray(params.depts)) {
          params.deptIds = params.depts.map(dept => String(dept.id));
          console.log('从原始数据中获取部门IDs:', params.deptIds);
        }
      }
    } else {
      // 如果不是自定义数据权限，删除deptIds字段
      delete params.deptIds;
    }
    
    // 删除原始depts字段，不需要提交给后端
    delete params.depts;
    
    console.log('编辑前处理后参数:', params);
    return params;
  },
  
  // 编辑前的钩子，用于设置选中的菜单和部门
  beforeOpenEdit: (record) => {
    console.log('编辑前的记录:', record);
    console.log('编辑前的菜单数据:', record.menus);
    console.log('编辑前的menuIds数据:', record.menuIds);
  
    // 设置为编辑模式
    editMode.value = true;
  
    // 清空已选中的菜单ID
    selectedMenuIds.value = [];
  
    // 先尝试使用 record.menus
    if (record.menus && Array.isArray(record.menus)) {
      // 提取菜单ID
      const menuIds = record.menus.map(menu => String(menu.id));
      console.log('从 menus 提取的菜单ID:', menuIds);
  
      // 更新选中的菜单ID
      selectedMenuIds.value = menuIds;
    } 
    // 如果 menus 不存在，尝试使用 record.menuIds
    else if (record.menuIds) {
      let menuIds = [];
      
      if (Array.isArray(record.menuIds)) {
        menuIds = record.menuIds.map(id => String(id));
      } else if (typeof record.menuIds === 'string') {
        // 如果是逗号分隔的字符串，尝试分割
        menuIds = record.menuIds.split(',').map(id => String(id.trim()));
      } else if (record.menuIds) {
        // 如果是单个值，转换为数组
        menuIds = [String(record.menuIds)];
      }
      
      console.log('从 menuIds 提取的菜单ID:', menuIds);
      selectedMenuIds.value = menuIds;
    }
  
    // 设置选中的部门ID
    if (record.depts && Array.isArray(record.depts)) {
      // 提取部门ID
      const deptIds = record.depts.map(dept => dept.id);
      console.log('部门ID:', deptIds);
  
      // 更新表单中的部门ID
      setTimeout(() => {
        // 直接设置 record.deptIds 值，ma-crud 会自动使用这个值
        record.deptIds = deptIds;
      }, 100);
    }
    return true;
    
    return true; // 必须返回true才会打开编辑弹窗
  },
  // 添加前处理参数
  beforeAdd: (params) => {
    console.log('添加前原始参数:', params);
    params.created_at = Math.floor(Date.now() / 1000);
    if (params.date_range && Array.isArray(params.date_range)) {
      params.start_time = new Date(params.date_range[0]).getTime() / 1000;
      params.end_time = new Date(params.date_range[1]).getTime() / 1000;
      delete params.date_range;
    }
    params.dataScope = params.data_scope;
    delete params.data_scope;
    delete params.created_at;
    
    // 收集完整的菜单权限链（选中节点及其所有父节点）
    params.menuIds = collectFullMenuIds(selectedMenuIds.value);
    
    // 处理部门IDs
    if (params.dataScope == 2) {
      // 如果是自定义数据权限，确保有deptIds字段
      if (!params.deptIds || !Array.isArray(params.deptIds) || params.deptIds.length === 0) {
        // 如果没有选择部门，设置为空数组
        params.deptIds = [];
      }
    } else {
      // 如果不是自定义数据权限，删除deptIds字段
      delete params.deptIds;
    }
    
    console.log('添加前处理后参数:', params);
    return params;
  },  
})

const columns = reactive([
  { title: 'ID', dataIndex: 'id', width: 80, hide: true },
  {
    title: '角色名称', dataIndex: 'name', search: true, commonRules: [{ required: true, message: '角色名称必填' }],
  },
  {
    title: '数据范围', dataIndex: 'data_scope',
    formType: 'select',
    hide: true,
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '全部数据权限', value: 1 },
        { label: '自定义数据权限', value: 2 },
        { label: '本部门数据权限', value: 3 },
        { label: '本部门及以下数据权限', value: 4 },
        { label: '本人数据权限', value: 5 },
      ]
    },
    events: {
      change: (val) => {
        console.log('数据范围变化：', val);
      }
    },
    // 根据数据范围控制部门字段的显示隐藏
    onControl: (value, maFormObject) => {
      if (!maFormObject) return;
      console.log('数据范围变化：', value);
      
      const service = maFormObject.getColumnService();
      if (!service) return;
      
      const field = service.get('deptIds');
      if (field && typeof field.setAttr === 'function') {
        // 只有当选择自定义数据权限(value === 2)时才显示部门选择
        const isCustomScope = parseInt(value) === 2;
        field.setAttr('display', isCustomScope);
        
        // 调整必填规则
        const rules = isCustomScope 
          ? [{ required: true, message: '部门必选' }] 
          : [];
        field.setAttr('rules', rules);
      }
    }
  },
  {
    title: '部门',
    dataIndex: 'deptIds',
    formType: 'select',
    multiple: true,
  
    dict: { data: deptData, props: { label: 'name', value: 'id' } },
    // 根据数据范围控制显示隐藏
    editDisplay: (form) => form && form.data_scope == 2,
    addDisplay: (form) => form && form.data_scope == 2
  },
  {
    title: '菜单', 
    dataIndex: 'menuIds',
    formType: 'input',  // 使用输入框类型，但会被插槽覆盖
    hide: true,

    // 在表格中隐藏该列
    // commonRules: [{ required: true, message: '请选择菜单' }]
  },
  {
    title: '状态', dataIndex: 'status', search: true,
    formType: 'radio',
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 }
      ]
    }
  },
  {
    title: '排序',
    dataIndex: 'sort',
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'center',
    form: false
  },
  {
    title: '创建人',
    dataIndex: 'created_by',
    addDisplay: false,
    editDisplay: false,
    hide: true,
    form: false
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    formType: "range",
    addDisplay: false,
    editDisplay: false,
    form: false
  },

])
</script>

<script>
export default { name: "master-system-permission-role" }
</script>

<style scoped></style>

<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
 <template>
  <div class="ma-content-block p-4">
    <!-- 内容区域 -->
    <div class="flex">
      <!-- 左侧导航区域 -->
      <div class="w-3/12 mr-4" style="width: 65%;">
        <!-- 三个大类标签页 -->
        <div class="bg-white rounded shadow-sm mb-4">
          <div class="tab-container flex border-b border-gray-200">
            <div 
              class="category-tab cursor-pointer text-sm font-medium whitespace-nowrap"
              :class="{'text-blue-500 border-b-2 border-blue-500': currentCategory === 0}"
              @click="switchCategory(0)"
            >
              <span class="tab-text">军政企</span>
            </div>
            <div 
              class="category-tab cursor-pointer text-sm font-medium whitespace-nowrap"
              :class="{'text-blue-500 border-b-2 border-blue-500': currentCategory === 1}"
              @click="switchCategory(1)"
            >
              <span class="tab-text">主流电商</span>
            </div>
            <div 
              class="category-tab cursor-pointer text-sm font-medium whitespace-nowrap"
              :class="{'text-blue-500 border-b-2 border-blue-500': currentCategory === 2}"
              @click="switchCategory(2)"
            >
              <span class="tab-text">ERP</span>
            </div>
          </div>
        </div>
        
        <!-- 小类列表 -->
        <div class="bg-white rounded shadow-sm">
          <div class="platform-list">
            <div 
              v-for="platform in platformCategories[currentCategory].platforms" 
              :key="platform.id"
              class="platform-item flex items-center h-14 px-4 py-2 cursor-pointer hover:bg-gray-100 border-b border-gray-200"
              :class="{'bg-blue-50 border-l-4 border-blue-500 pl-3': currentPlatform === platform.id}"
              @click="selectPlatform(platform.id)"
            >
              <div class="platform-icon flex items-center justify-center w-8 h-8 mr-4 flex-shrink-0">
                <i class="iconfont text-xl" :class="platform.icon"></i>
              </div>
              <div class="platform-name flex-grow text-base whitespace-nowrap overflow-hidden text-ellipsis">{{ platform.name }}</div>
              <div class="platform-count text-sm font-medium text-gray-500 ml-3">{{ platform.count }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-grow bg-white rounded shadow-sm p-4">
        <!-- CRUD 组件 -->
        <ma-crud :options="crud" :columns="columns" ref="crudRef">
          <!-- 状态列 -->
          <template #status="{ record }">
            <a-tag :color="record.status == 1 ? 'green' : 'red'">
              {{ record.status == 1 ? '正常' : '异常' }}
            </a-tag>
          </template>
          
          <!-- 同步时间列 -->
          <template #sync_time="{ record }">
            <div v-time="record.sync_time"></div>
          </template>

          <!-- 操作列 -->
          <template #operationBeforeExtend="{ record }">
            <a-button type="text" size="small" @click="openDetails(record)" class="mr-2">
              <template #icon>
                <icon-eye />
              </template>
              详情
            </a-button>
            <a-button type="text" size="small" @click="openEdit(record)">
              <template #icon>
                <icon-edit />
              </template>
              编辑
            </a-button>
          </template>
        </ma-crud>
      </div>
    </div>

    <!-- 授权详情抽屉组件 -->
    <DetailDrawer 
      v-model:visible="detailVisible" 
      :record="detailRecord"
      @edit="handleDetailEdit"
    />
    
    <!-- 授权编辑抽屉组件 -->
    <EditDrawer
      v-model:visible="editVisible"
      :record="editRecord"
      @save="handleSave"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import { IconUser, IconEye, IconSearch, IconRefresh, IconPlus, IconEdit } from '@arco-design/web-vue/es/icon';
// import upstreamApi from "@/api/master/upstream"; // 暂时注释掉API导入
import DetailDrawer from './components/DetailDrawer.vue';
import EditDrawer from './components/EditDrawer.vue';

// 定义页面元数据
definePageMeta({
  name: "master-upstream-authorization_old",
  path: "/master/upstream/authorization_old"
});

// 平台分类数据
const platformCategories = reactive([
  { 
    name: '军政企', 
    platforms: [
      { id: 'army', name: '兵工自采', icon: 'icon-binggong', count: '0/0' },
      { id: 'weapon', name: '齐心自采', icon: 'icon-jundui', count: '0/0' },
      { id: 'bangwei', name: '邦威自采', icon: 'icon-bangwei', count: '0/0' },
      { id: 'leading', name: '云中鹤自采', icon: 'icon-yunzhonghe', count: '0/0' },
      { id: 'sengxin', name: '申合信自采', icon: 'icon-lingxian', count: '0/0' },
      { id: 'deli', name: '得力自采', icon: 'icon-deli', count: '0/0' },
      { id: 'jiansu', name: '江苏政采', icon: 'icon-jiansu', count: '0/0' },
      { id: 'zhonyang', name: '中央集采', icon: 'icon-zhonyang', count: '0/0' },
      { id: 'nangfang', name: '南网采购', icon: 'icon-nangfang', count: '0/0' },
      { id: 'shenzheng', name: '深圳政采', icon: 'icon-shenzheng', count: '0/0' },
      { id: 'guangdong', name: '广东省采', icon: 'icon-guangdong', count: '0/0' },
      { id: 'tielu', name: '铁路采购', icon: 'icon-tielu ', count: '0/0' },
    ] 
  },
  { 
    name: '主流电商', 
    platforms: [
      { id: 'jd_hufu', name: '京东(虎符)', icon: 'icon-jingdong', count: '7/7' },
      { id: 'jd_vc_moni', name: '京东VC(模拟)', icon: 'icon-jingdong', count: '9/12' },
      { id: 'jd_vc_zr', name: '京东VC(自研入鼎)', icon: 'icon-jingdong', count: '5/6' },
      { id: 'taobao', name: '淘宝', icon: 'icon-taobao', count: '3/3' },
      { id: 'tmall', name: '天猫', icon: 'icon-tianmao', count: '2/4' },
      { id: 'douyin', name: '抖店', icon: 'icon-doudian', count: '4/5' },
      { id: 'pdd', name: '拼多多', icon: 'icon-pdd', count: '2/3' },
    ] 
  },
  { 
    name: 'ERP', 
    platforms: [
      { id: 'kingdee8', name: '金蝶8.0', icon: 'icon-jine', count: '1/1' },
      { id: 'kingdee9', name: '金蝶9.0', icon: 'icon-jine', count: '0/1' },
      { id: 'kingdeeK3', name: '金蝶 K3', icon: 'icon-jine', count: '2/2' },
      { id: 'yongyouU8', name: '用友U8', icon: 'icon-yongyou', count: '3/3' },
    ] 
  }
]);

// ERP平台数据
const erpPlatforms = reactive([
  { id: 'erp1', name: 'SAP', icon: '/assets/images/platforms/erp1.png', count: '1/1' },
  { id: 'erp2', name: 'Oracle', icon: '/assets/images/platforms/erp2.png', count: '0/1' },
  { id: 'erp3', name: 'Kingdee', icon: '/assets/images/platforms/erp3.png', count: '2/2' },
  { id: 'erp4', name: 'Yonyou', icon: '/assets/images/platforms/erp4.png', count: '3/3' },
  { id: 'erp5', name: 'Infor', icon: '/assets/images/platforms/erp5.png', count: '1/2' },
]);

// 当前主标签页，平台或ERP
const mainTab = ref('platform');

// 当前选中的分类索引
const currentCategory = ref(0); // 默认选中军政企

// 当前选中的平台
const currentPlatform = ref('');



// 切换分类
function switchCategory(index) {
  console.log('切换到分类:', index, platformCategories[index].name);
  currentCategory.value = index;
  
  // 如果当前分类有平台，默认选中第一个
  if (platformCategories[index].platforms.length > 0) {
    currentPlatform.value = platformCategories[index].platforms[0].id;
  }
}

// 选择平台
function selectPlatform(platformId) {
  console.log('选择平台:', platformId);
  currentPlatform.value = platformId;
  
  // 根据平台ID过滤数据
  filterDataByPlatform(platformId);
}

// 根据平台ID过滤数据
function filterDataByPlatform(platformId) {
  // 过滤数据
  const filteredData = mockData.filter(item => item.platform_id === platformId);
  
  // 更新数据源
  crud.data = filteredData;
  
  // 如果有引用，刷新表格
  if (crudRef.value) {
    crudRef.value.refresh();
  }
}

// 页面初始化
onMounted(() => {
  // 默认选中主流电商分类的第一个平台
  const defaultCategory = currentCategory.value;
  const defaultPlatform = platformCategories[defaultCategory].platforms[0].id;
  
  // 设置当前选中的平台
  currentPlatform.value = defaultPlatform;
  
  // 加载数据
  filterDataByPlatform(defaultPlatform);
});

// 详情弹窗相关状态
const detailVisible = ref(false);
const detailRecord = ref({});

// 编辑弹窗相关状态
const editVisible = ref(false);
const editRecord = ref({});

// 过滤掉 hide、序号和操作相关字段的列用于详情展示
const detailColumns = computed(() =>
  columns.filter(
    col =>
      !col.hide &&
      col.dataIndex !== "__index" &&
      col.dataIndex !== "__operation"
  )
);

// 打开详情弹窗
function openDetails(record) {
  detailRecord.value = { ...record };
  detailVisible.value = true;
}

// 打开编辑弹窗
function openEdit(record) {
  editRecord.value = { ...record };
  editVisible.value = true;
}

// 关闭详情抽屉
function closeDetail() {
  detailVisible.value = false;
}

// 确认按钮处理函数
function handleOk() {
  detailVisible.value = false;
}

// 日期格式化函数
function formatDate(dateString) {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (e) {
    return dateString;
  }
}

// 详情页编辑按钮处理函数
function handleDetailEdit() {
  // 打开编辑抽屉
  editRecord.value = { ...detailRecord.value };
  editVisible.value = true;
  // 关闭详情抽屉
  detailVisible.value = false;
}

// 编辑抽屉保存按钮处理函数
function handleSave(record) {
  // 更新数据
  const index = mockData.findIndex(item => item.id === record.id);
  if (index !== -1) {
    mockData[index] = { ...mockData[index], ...record };
    Message.success('保存成功');
  } else {
    Message.error('未找到要编辑的记录');
  }
  
  // 关闭编辑抽屉
  editVisible.value = false;
}

const crudRef = ref();
// const authApi = upstreamApi.authorization; // 暂时注释掉API调用

// 模拟数据
const mockData = reactive([
  {
    id: '1001',
    subject_name: '天猫旗舰店',
    channel_name: '天猫',
    status: 1,
    sync_time: '2025-01-15 10:30:00',
    platform_id: 'amazon'
  },
  {
    id: '1002',
    subject_name: '京东自营店',
    channel_name: '京东',
    status: 1,
    sync_time: '2025-02-20 14:15:00',
    platform_id: 'mercado'
  },
  {
    id: '1003',
    subject_name: '拍拍官方店',
    channel_name: '拍拍',
    status: 0,
    sync_time: '2025-03-10 09:45:00',
    platform_id: 'shopee'
  },
  {
    id: '2001',
    subject_name: '广东八灵科技发展有限公司',
    channel_name: '兵工自采',
    status: 1,
    sync_time: '2025-06-09 15:18:00',
    platform_id: 'army'
  },
  {
    id: '2002',
    subject_name: '广州好帮手信息科技有限公司',
    channel_name: '兵工自采',
    status: 1,
    sync_time: '2025-06-09 15:18:00',
    platform_id: 'army'
  },
  {
    id: '2003',
    subject_name: '深圳腾讯公司',
    channel_name: '兵工自采',
    status: 0,
    sync_time: '2025-06-09 15:18:00',
    platform_id: 'army'
  }
]);

// 页面初始化
onMounted(() => {
  // 初始化加载数据
});

// CRUD配置
const crud = reactive({
  // 使用模拟数据
  data: mockData,
  showIndex: false,
  pageLayout: "fixed",
  rowSelection: false,
  operationColumn: true,
  operationColumnWidth: 200,
  add: { 
    show: true, 
    text: '添加授权',
    // 模拟添加操作
    handler: (record) => {
      // 添加平台ID参数
      record.platform_id = currentPlatform.value;
      record.id = Date.now().toString();
      record.sync_time = new Date().toISOString().replace('T', ' ').substring(0, 19);
      mockData.push(record);
      Message.success('添加授权成功');
      return Promise.resolve({ code: 200, data: record, message: '添加授权成功' });
    }
  },
  edit: { 
    show: false, 
    // 模拟编辑操作
    handler: (record) => {
      const index = mockData.findIndex(item => item.id === record.id);
      if (index !== -1) {
        mockData[index] = { ...mockData[index], ...record };
        Message.success('编辑授权成功');
        // 编辑成功后关闭抽屉
        if (detailVisible.value && detailRecord.value.id === record.id) {
          detailVisible.value = false;
        }
        return Promise.resolve({ code: 200, data: record, message: '编辑授权成功' });
      }
      return Promise.reject({ code: 400, message: '未找到记录' });
    }
  },
  delete: {
    show: true,
    // 模拟删除操作
    handler: (ids) => {
      const idArray = Array.isArray(ids) ? ids : [ids];
      idArray.forEach(id => {
        const index = mockData.findIndex(item => item.id === id);
        if (index !== -1) {
          mockData.splice(index, 1);
        }
      });
      Message.success('删除授权成功');
      return Promise.resolve({ code: 200, message: '删除授权成功' });
    }
  },
  // 模拟数据过滤
  api: (params) => {
    // 根据当前选中的平台过滤数据
    let filteredData = mockData.filter(item => item.platform_id === currentPlatform.value);
    
    // 应用其他过滤条件
    if (params.id) {
      filteredData = filteredData.filter(item => item.id === params.id);
    }
    
    if (params.subject_name) {
      filteredData = filteredData.filter(item => item.subject_name.includes(params.subject_name));
    }
    
    if (params.channel_name) {
      filteredData = filteredData.filter(item => item.channel_name.includes(params.channel_name));
    }
    
    if (params.status !== undefined) {
      filteredData = filteredData.filter(item => item.status === Number(params.status));
    }
    
    // 模拟分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pageData = filteredData.slice(start, end);
    
    return Promise.resolve({
      code: 200,
      data: {
        items: pageData,
        pageInfo: {
          total: filteredData.length,
          currentPage: page,
          totalPage: Math.ceil(filteredData.length / pageSize)
        }
      },
      message: '获取数据成功'
    });
  }
});

// 渠道名称下拉选项
const channelOptions = ref([
  { label: '天猫', value: '天猫' },
  { label: '京东', value: '京东' },
  { label: '拍拍', value: '拍拍' },
  { label: '兵工自采', value: '兵工自采' },
]);

// 表格列配置
const columns = reactive([
  {
    title: "Id",
    dataIndex: "id",
    search: true,
    commonRules: [{ required: true, message: "Id必填" }]
  },
  {
    title: "状态",
    dataIndex: "status",
    search: true,
    formType: "select",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "正常", value: 1 },
        { label: "异常", value: 0 }
      ]
    }
  },
  {
    title: "主体名称",
    dataIndex: "subject_name",
    search: true,
    commonRules: [{ required: true, message: "主体名称必填" }]
  },
  {
    title: "渠道名称",
    dataIndex: "channel_name",
    search: true,
    commonRules: [{ required: true, message: "渠道名称必填" }]
  },
  {
    title: "同步时间",
    dataIndex: "sync_time",
    search: false,
    formType: "date-picker",
    component: 'a-date-picker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss'
    }
  }
]);
</script>

<script>
export default { name: "master-upstream-authorization" };
</script>

<style scoped>
/* 左侧导航样式 */
.left-sidebar {
  border: 1px solid #ebedf0;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tab-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  position: relative;
}

.tab-item:hover {
  background-color: #f5f5f5;
}

.category-tab {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  padding-left: 1.5rem;
}

.category-tab:hover {
  background-color: #f5f5f5;
}

.platform-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.platform-item:hover {
  background-color: #f5f5f5;
}

.platform-item.active {
  background-color: #f0f7ff;
  border-left-color: #1890ff;
}

/* 详情抽屉样式 */
.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .text-gray-500 {
  width: 100px;
  flex-shrink: 0;
}

/* 平台图标样式 */
.platform-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.platform-icon img {
  max-width: 100%;
  max-height: 100%;
}

/* 平台项样式 */
.platform-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  height: 56px;
  display: flex;
  align-items: center;
}

.platform-item:hover {
  background-color: #f5f5f5;
}

/* 平台名称样式 */
.platform-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 平台计数样式 */
.platform-count {
  font-weight: 500;
}

/* 平台图标样式 */
.platform-icon .iconfont {
  font-size: 20px;
}

/* 平台列表样式 */
.platform-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 标签容器样式 */
.tab-container {
  display: flex;
  width: 100%;
}

/* 标签页样式 */
.category-tab {
  flex: 1;
  width: 33.33%;
  min-width: 80px; /* 调整最小宽度，使标签页不要太大 */
  padding: 8px 4px; /* 减小内边距，使标签页不要太大 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  white-space: nowrap;
  box-sizing: border-box;
  position: relative;
}

/* 确保标签页内容在切换时不会改变布局 */
.category-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: transparent;
}

/* 标签页文字样式 */
.tab-text {
  display: block;
  width: 100%;
  text-align: center;
  margin: 0 auto;
}
</style>
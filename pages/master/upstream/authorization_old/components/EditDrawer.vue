<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <!-- 授权编辑抽屉 -->
  <a-drawer 
    :width="650" 
    :visible="visible" 
    @cancel="closeDrawer" 
    :unmount-on-close="true"
  >
    <template #title>
      编辑授权信息
    </template>
    
    <div class="auth-edit-container">
      <a-form :model="formData" layout="vertical">
        <!-- 基础信息 -->
        <div class="section mb-4 pb-4 border-b border-gray-200">
          <h4 class="text-base font-medium mb-4 text-blue-500">基础信息</h4>
          
          <a-form-item field="store_name" label="主体名称">
            <a-input v-model="formData.store_name" placeholder="请输入主体名称" />
          </a-form-item>
          
          <a-form-item field="invoice_title" label="发票抬头">
            <a-input v-model="formData.invoice_title" placeholder="请输入发票抬头" />
          </a-form-item>
        </div>
        
        <!-- 渠道绑定 -->
        <div class="section mb-4 pb-4 border-b border-gray-200">
          <h4 class="text-base font-medium mb-4 text-blue-500">渠道绑定</h4>
          
          <a-form-item field="channel_id" label="绑定渠道">
            <a-select v-model="formData.channel_id" placeholder="请选择渠道" :loading="channelLoading">
              <a-option v-for="channel in channelOptions" :key="channel.id" :value="channel.id">
                <div class="flex items-center">
                  <i class="iconfont" :class="channel.iconUrl"></i>
                  <span class="ml-2">{{ channel.name }}</span>
                </div>
              </a-option>
            </a-select>
          </a-form-item>
        </div>
        
        <!-- 同步设置 -->
        <div class="section mb-4 pb-4 border-b border-gray-200">
          <h4 class="text-base font-medium mb-4 text-blue-500">同步设置</h4>
          
          <a-form-item field="auto_sync" label="自动同步">
            <a-switch v-model="formData.auto_sync" />
          </a-form-item>
          
          <div class="grid grid-cols-2 gap-4">
            <a-form-item field="order_sync_interval" label="订单同步(同步间隔秒)">
              <a-input-number 
                v-model="formData.order_sync_interval" 
                :min="0" 
                :step="1" 
                placeholder="输入0代表不同步"
                :disabled="!formData.auto_sync" 
              />
            </a-form-item>
            
            <a-form-item field="product_sync_interval" label="商品同步(同步间隔秒)">
              <a-input-number 
                v-model="formData.product_sync_interval" 
                :min="0" 
                :step="1" 
                placeholder="输入0代表不同步"
                :disabled="!formData.auto_sync" 
              />
            </a-form-item>
            
            <a-form-item field="invoice_sync_interval" label="发票同步(同步间隔秒)">
              <a-input-number 
                v-model="formData.invoice_sync_interval" 
                :min="0" 
                :step="1" 
                placeholder="输入0代表不同步"
                :disabled="!formData.auto_sync" 
              />
            </a-form-item>
            
            <a-form-item field="report_sync_interval" label="报备同步(同步间隔秒)">
              <a-input-number 
                v-model="formData.report_sync_interval" 
                :min="0" 
                :step="1" 
                placeholder="输入0代表不同步"
                :disabled="!formData.auto_sync" 
              />
            </a-form-item>
          </div>
        </div>
        
        <!-- 交互信息 -->
        <div class="section mb-4 pb-4 border-b border-gray-200">
          <h4 class="text-base font-medium mb-4 text-blue-500">交互信息</h4>
          
          <a-form-item field="account" label="账号">
            <a-input v-model="formData.account" placeholder="请输入账号" />
          </a-form-item>
          
          <a-form-item field="password" label="密码">
            <a-input-password v-model="formData.password" placeholder="请输入密码" />
          </a-form-item>
          
          <a-form-item field="login_url" label="登录网址">
            <a-input v-model="formData.login_url" placeholder="请输入登录网址" />
          </a-form-item>
          
          <a-form-item field="cookies" label="Cookies">
            <a-textarea v-model="formData.cookies" placeholder="请输入Cookies" :auto-size="{ minRows: 3, maxRows: 5 }" />
          </a-form-item>
        </div>
        
        <!-- 提交按钮 -->
        <div class="flex justify-end">
          <a-button @click="closeDrawer" class="mr-2">取消</a-button>
          <a-button type="primary" @click="handleSubmit">保存</a-button>
        </div>
      </a-form>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconUser } from '@arco-design/web-vue/es/icon';
import orderApi from '@/api/master/order.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save']);

// 表单数据
const formData = reactive({
  store_name: '',
  invoice_title: '',
  channel_id: '',
  auto_sync: false,
  order_sync_interval: 300,
  product_sync_interval: 600,
  invoice_sync_interval: 900,
  report_sync_interval: 1200,
  account: '',
  password: '',
  login_url: '',
  cookies: ''
});

// 渠道选项
const channelOptions = reactive([]);
const channelLoading = ref(false);

// 获取渠道列表
const fetchChannelList = async () => {
  channelLoading.value = true;
  try {
    const res = await orderApi.getChannelList();
    if (res.code === 200 && res.data && res.data.items) {
      channelOptions.length = 0; // 清空原有数据
      res.data.items.forEach(item => {
        channelOptions.push({
          id: item.id,
          name: item.name,
          iconUrl: item.iconUrl || ''
        });
      });
    } else {
      Message.error('获取渠道列表失败');
    }
  } catch (error) {
    console.error('获取渠道列表出错:', error);
    Message.error('获取渠道列表出错');
  } finally {
    channelLoading.value = false;
  }
};

// 组件挂载时获取渠道列表
onMounted(() => {
  fetchChannelList();
});

// 监听record变化，更新表单数据
watch(() => props.record, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制基础字段
    formData.store_name = newVal.store_name || '';
    formData.invoice_title = newVal.invoice_title || '';
    formData.channel_id = newVal.channel_id || '';
    
    // 复制同步设置字段，如果不存在则使用默认值
    formData.auto_sync = newVal.auto_sync || false;
    formData.order_sync_interval = newVal.order_sync_interval || 300;
    formData.product_sync_interval = newVal.product_sync_interval || 600;
    formData.invoice_sync_interval = newVal.invoice_sync_interval || 900;
    formData.report_sync_interval = newVal.report_sync_interval || 1200;
    
    // 复制交互信息字段
    formData.account = newVal.account || '';
    formData.password = newVal.password || '';
    formData.login_url = newVal.login_url || '';
    formData.cookies = newVal.cookies || '';
  }
}, { immediate: true, deep: true });

// 关闭抽屉
function closeDrawer() {
  emit('update:visible', false);
}

// 提交表单
function handleSubmit() {
  // 构建要保存的数据对象，合并原记录和表单数据
  const saveData = {
    ...props.record,
    ...formData
  };
  
  // 发送保存事件
  emit('save', saveData);
  
  // 显示成功消息
  Message.success('保存成功');
  
  // 关闭抽屉
  closeDrawer();
}
</script>

<style scoped>
/* 编辑抽屉样式 */
.section {
  margin-bottom: 20px;
}
</style>

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import mallApi from '@/api/mall'

/**
 * 商城商品分类状态管理
 */
export const useMallCategoryStore = defineStore('mallCategory', () => {
  // 商品分类树形结构
  const categoryTree = ref([])

  // 商品分类标签列表结构
  const categoryTags = ref([])
  
  // 商品品牌列表
  const brands = ref([])
  
  // 商品服务列表
  const services = ref([])
  
  // 加载状态
  const loading = ref(false)
  
  // 计算属性
  // 是否有分类数据
  const hasCategories = computed(() => categoryTree.value.length > 0)
  
  /**
   * 获取商品分类树形结构
   * @returns {Promise} - 返回请求结果
   */
  const getCategoryTree = async () => {
    try {
      loading.value = true
      
      const response = await mallApi.category.getCategoryTree()
      
      if (response && response.code === 200) {
        categoryTree.value = response.data || []
        
        return {
          success: true,
          data: response.data
        }
      }
      
      return {
        success: false,
        message: response?.message || '获取商品分类失败'
      }
    } catch (error) {
      console.error('获取商品分类出错:', error)
      return {
        success: false,
        message: '获取商品分类出错'
      }
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 清空分类数据
   */
  const clearCategoryTree = () => {
    categoryTree.value = []
  }
  
  /**
   * 获取扁平化的分类列表（用于下拉选择等场景）
   * @returns {Array} - 返回扁平化的分类列表
   */
  const getFlatCategories = computed(() => {
    const result = []
    
    const flatten = (categories, level = 0) => {
      if (!categories || !categories.length) return
      
      categories.forEach(category => {
        result.push({
          id: category.id,
          name: category.name,
          level: category.level || level,
          parentId: category.goodsParentCategoryId
        })
        
        if (category.children && category.children.length) {
          flatten(category.children, level + 1)
        }
      })
    }
    
    flatten(categoryTree.value)
    return result
  })


  /**
   * 获取商品分类详情
   * @returns {Promise} - 返回请求结果
   * @param id
   */

  const getCategoryInfoById = async (id) => {
    try {
      loading.value = true

      const response = await mallApi.category.getCategoryInfoById(id)

      if (response && response.code === 200) {
        categoryTree.value = response.data || []

        return {
          success: true,
          data: response.data
        }
      }

      return {
        success: false,
        message: response?.message || '获取商品分类详情失败'
      }
    } catch (error) {
      console.error('获取商品分类详情出错:', error)
      return {
        success: false,
        message: '获取商品分类详情出错'
      }
    } finally {
      loading.value = false
    }
  }


  /**
   * 获取商品分类标签
   * @param {Object} params - 请求参数
   * @param {Number} params.page - 页码，默认1
   * @param {Number} params.pageSize - 每页数量，默认10
   * @returns {Promise} - 返回请求结果
   */

  const getCategoryTags = async (params = { page: 1, pageSize: 10 }) => {
    try {
      loading.value = true
      const response = await mallApi.category.getCategoryTags(params)
      if (response && response.code === 200) {
        categoryTags.value = response.data.items || []
        return {
          success: true,
          data: response.data.items
        }
      }
      return {
        success: false,
        message: response?.message || '获取商品分类标签失败'
      }
    } catch (error) {
      console.error('获取商品分类标签出错:', error)
      return {
        success: false,
        message: '获取商品分类标签出错'
      }
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取商品品牌列表
   * @param {Object} params - 请求参数
   * @param {Number} params.page - 页码，默认1
   * @param {Number} params.pageSize - 每页数量，默认10
   * @returns {Promise} - 返回请求结果
   */
  const getBrands = async (params = { page: 1, pageSize: 10 }) => {
    try {
      loading.value = true
      const response = await mallApi.category.getBrands(params)
      
      if (response && response.code === 200) {
        brands.value = response.data?.items || []
        
        return {
          success: true,
          data: response.data
        }
      }
      
      return {
        success: false,
        message: response?.message || '获取商品品牌列表失败'
      }
    } catch (error) {
      console.error('获取商品品牌列表出错:', error)
      return {
        success: false,
        message: '获取商品品牌列表出错'
      }
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取商品服务列表
   * @param {Object} params - 请求参数
   * @param {Number} params.page - 页码，默认1
   * @param {Number} params.pageSize - 每页数量，默认10，最大1000
   * @returns {Promise} - 返回请求结果
   */
  const getServices = async (params = { page: 1, pageSize: 10 }) => {
    try {
      loading.value = true
      const response = await mallApi.category.getServices(params)
      
      if (response && response.code === 200) {
        services.value = response.data?.items || []
        
        return {
          success: true,
          data: response.data
        }
      }
      
      return {
        success: false,
        message: response?.message || '获取商品服务列表失败'
      }
    } catch (error) {
      console.error('获取商品服务列表出错:', error)
      return {
        success: false,
        message: '获取商品服务列表出错'
      }
    } finally {
      loading.value = false
    }
  }
  return {
    // 状态
    categoryTree,
    categoryTags,
    brands,
    services,
    loading,
    
    // 计算属性
    hasCategories,
    getFlatCategories,
    
    // 方法
    getCategoryTree,
    clearCategoryTree,
    getCategoryInfoById,
    getCategoryTags,
    getBrands,
    getServices
  }
}, {
  persist: {
    storage: localStorage,
    paths: ['categoryTree'],
    key: 'mall-category-store'
  }
})

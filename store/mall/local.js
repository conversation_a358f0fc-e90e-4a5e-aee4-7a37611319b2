import { defineStore } from 'pinia';
import mallApi from '@/api/mall';
import addressApi from '@/api/mall/address';

export const useMallLocalStore = defineStore('mallLocal', {  
  state: () => ({
    // 当前定位信息
    locationInfo: {
      ip: '',
      country: '',
      area: '',
      location: '',
      provinceCode: '',
      cityCode: '',
      cityName: ''
    },
    // 定位信息更新时间戳
    locationInfoTimestamp: 0,
    // 省份树状信息
    provinceTree: [],
    // 省份树状信息更新时间戳
    provinceTreeTimestamp: 0
  }),
  
  getters: {
    // 获取当前定位信息
    currentLocation: (state) => state.locationInfo,
    
    // 获取省份树状信息
    provinces: (state) => state.provinceTree,
    
    // 获取所有省份名称和代码
    allProvinces: (state) => {
      if (!state.provinceTree || state.provinceTree.length === 0) {
        return [];
      }
      
      // 返回省份列表，只包含 name 和 code 属性
      return state.provinceTree.map(province => ({
        name: province.name,
        code: province.code
      }));
    }
  },
  
  actions: {

    // 更新当前选择定位地址
    updateLocationInfo(provinceCode, provinceName) {
      this.locationInfo.provinceCode = provinceCode;
      this.locationInfo.cityCode = provinceCode;
      this.locationInfo.cityName = provinceName;
      this.locationInfoTimestamp = Date.now();
      // 尝试保存到本地存储
      try {
        if (typeof localStorage !== 'undefined') {
          const storageData = {
            data: this.locationInfo,
            timestamp: this.locationInfoTimestamp
          };
          localStorage.setItem('mall_location_info', JSON.stringify(storageData));
          console.log('定位信息已保存到本地存储');
        }
      } catch (error) {
        console.error('保存定位信息到本地存储失败:', error);
      }
    },

    /**
     * 设置当前定位信息
     * @param {Object} locationData - 定位信息数据
     */
    setLocationInfo(locationData) {
      console.log('设置当前定位信息:', locationData);
      
      if (!locationData) {
        console.warn('定位信息数据为空');
        return;
      }
      
      // 根据 area 查找对应的省份
      let provinceCode = '';
      let provinceName = '';
      
      // 先从 provinceTree 中查找匹配的省份
      if (this.provinceTree && this.provinceTree.length > 0 && locationData.area) {
        const area = locationData.area;
        const matchedProvince = this.provinceTree.find(province => {
          // 检查省份名称是否包含在 area 中
          return province.name && area.includes(province.name);
        });
        
        if (matchedProvince) {
          provinceCode = matchedProvince.code || '';
          provinceName = matchedProvince.name || '';
          console.log('根据 area 找到匹配的省份:', provinceName, provinceCode);
        } else {
          // 如果没有匹配到，尝试从 location 或 country 中查找
          const location = locationData.location || '';
          const country = locationData.country || '';
          
          const matchByLocation = this.provinceTree.find(province => {
            return province.name && (location.includes(province.name) || country.includes(province.name));
          });
          
          if (matchByLocation) {
            provinceCode = matchByLocation.code || '';
            provinceName = matchByLocation.name || '';
            console.log('根据 location 或 country 找到匹配的省份:', provinceName, provinceCode);
          }
        }
      }
      
      // 如果没有找到匹配的省份，默认使用广东省
      if (!provinceCode || !provinceName) {
        // 尝试从 provinceTree 中查找广东省
        const guangdong = this.provinceTree.find(province => province.name === '广东省' || province.name === '广东');
        
        if (guangdong) {
          provinceCode = guangdong.code || '';
          provinceName = guangdong.name || '';
          console.log('使用默认省份（广东省）:', provinceName, provinceCode);
        } else {
          // 如果连广东省也找不到，则使用固定值
          provinceCode = '440000';
          provinceName = '广东';
          console.log('使用固定的广东省信息:', provinceName, provinceCode);
        }
      }
      
      // 更新定位信息
      this.locationInfo = {
        ip: locationData.ip || '',
        country: locationData.country || '',
        area: locationData.area || '',
        location: locationData.location || '',
        cityCode: provinceCode,
        provinceCode: provinceCode,
        cityName: provinceName
      };
      
      // 更新时间戳
      this.locationInfoTimestamp = Date.now();
      
      // 尝试保存到本地存储
      try {
        if (typeof localStorage !== 'undefined') {
          const storageData = {
            data: this.locationInfo,
            timestamp: this.locationInfoTimestamp
          };
          localStorage.setItem('mall_location_info', JSON.stringify(storageData));
          console.log('定位信息已保存到本地存储');
        }
      } catch (error) {
        console.error('保存定位信息到本地存储失败:', error);
      }
    },
    
    /**
     * 设置省份树状信息
     * @param {Array} provinceData - 省份树状数据
     */
    setProvinceTree(provinceData) {
      console.log('设置省份树状信息:', provinceData);
      
      if (!provinceData || !Array.isArray(provinceData)) {
        console.warn('省份树状数据无效');
        return;
      }
      
      // 更新省份树状信息
      this.provinceTree = provinceData;
      
      // 更新时间戳
      this.provinceTreeTimestamp = Date.now();
      
      // 尝试保存到本地存储
      try {
        if (typeof localStorage !== 'undefined') {
          const storageData = {
            data: this.provinceTree,
            timestamp: this.provinceTreeTimestamp
          };
          localStorage.setItem('mall_province_tree', JSON.stringify(storageData));
          console.log('省份树状信息已保存到本地存储');
        }
      } catch (error) {
        console.error('保存省份树状信息到本地存储失败:', error);
      }
    },
    
    /**
     * 初始化本地信息
     * 从localStorage中恢复定位信息和省份树状信息
     */
    initLocalInfo() {
      try {
        // 从本地存储中获取定位信息
        if (typeof localStorage !== 'undefined') {
          const locationInfoStr = localStorage.getItem('mall_location_info');
          if (locationInfoStr) {
            try {
              const locationData = JSON.parse(locationInfoStr);
              if (locationData && locationData.data && locationData.timestamp) {
                this.locationInfo = locationData.data;
                this.locationInfoTimestamp = locationData.timestamp;
                console.log('已从本地存储恢复定位信息');
              } else {
                // 兼容旧格式数据
                this.locationInfo = locationData;
                this.locationInfoTimestamp = Date.now();
                
                // 将旧格式数据转换为新格式并保存
                const storageData = {
                  data: this.locationInfo,
                  timestamp: this.locationInfoTimestamp
                };
                localStorage.setItem('mall_location_info', JSON.stringify(storageData));
                console.log('已从本地存储恢复定位信息（旧格式）并转换');
              }
            } catch (parseError) {
              console.error('解析定位信息失败:', parseError);
              localStorage.removeItem('mall_location_info');
            }
          }
          
          // 从本地存储中获取省份树状信息
          const provinceTreeStr = localStorage.getItem('mall_province_tree');
          if (provinceTreeStr) {
            try {
              const provinceData = JSON.parse(provinceTreeStr);
              if (provinceData && provinceData.data && provinceData.timestamp) {
                this.provinceTree = provinceData.data;
                this.provinceTreeTimestamp = provinceData.timestamp;
                console.log('已从本地存储恢复省份树状信息');
              } else {
                // 兼容旧格式数据
                this.provinceTree = provinceData;
                this.provinceTreeTimestamp = Date.now();
                
                // 将旧格式数据转换为新格式并保存
                const storageData = {
                  data: this.provinceTree,
                  timestamp: this.provinceTreeTimestamp
                };
                localStorage.setItem('mall_province_tree', JSON.stringify(storageData));
                console.log('已从本地存储恢复省份树状信息（旧格式）并转换');
              }
            } catch (parseError) {
              console.error('解析省份树状信息失败:', parseError);
              localStorage.removeItem('mall_province_tree');
            }
          }
        }
      } catch (error) {
        console.error('初始化本地信息失败:', error);
        // 清除可能损坏的数据
        if (typeof localStorage !== 'undefined') {
          localStorage.removeItem('mall_location_info');
          localStorage.removeItem('mall_province_tree');
        }
      }
    },
    
    /**
     * 获取当前定位信息
     * 从API获取当前IP的定位信息
     * @returns {Promise<Object>} - 请求结果，包含定位信息数据
     */
    async fetchLocationInfo() {
      try {
        console.log('获取当前定位信息');
        
        // 检查缓存是否过期（8小时 = 8 * 60 * 60 * 1000 = 28800000 毫秒）
        const now = Date.now();
        // 设置为长时间有效 暂定为365天
        const cacheExpiration = 365 * 24 * 60 * 60 * 1000; // 365天缓存过期时间
        
        // 从 localStorage 中获取定位信息和时间戳
        let locationData = null;
        let timestamp = 0;
        
        if (typeof localStorage !== 'undefined') {
          const locationInfoStr = localStorage.getItem('mall_location_info');
          if (locationInfoStr) {
            try {
              const storageData = JSON.parse(locationInfoStr);
              if (storageData && storageData.data && storageData.timestamp) {
                locationData = storageData.data;
                timestamp = storageData.timestamp;
                
                // 更新内存中的数据
                this.locationInfo = locationData;
                this.locationInfoTimestamp = timestamp;
              }
            } catch (error) {
              console.error('解析定位信息失败:', error);
            }
          }
        }
        
        // 如果有缓存数据且未过期，直接返回缓存数据
        if (locationData && locationData.ip && timestamp > 0 && (now - timestamp) < cacheExpiration) {
          console.log('使用缓存的定位信息，缓存时间:', new Date(timestamp).toLocaleString());
          return {
            success: true,
            message: '获取定位信息成功（缓存）',
            data: locationData
          };
        }
        
        // 缓存过期或无缓存，发送获取定位信息请求
        console.log('缓存已过期或无缓存，从接口获取定位信息');
        const response = await addressApi.location.getIpLocation();
        console.log('获取定位信息响应:', response);
        
        if (response.code === 200 && response.data) {
          // 设置定位信息
          this.setLocationInfo(response.data);
          
          return { 
            success: true, 
            message: '获取定位信息成功', 
            data: response.data 
          };
        } else {
          // 如果请求失败但有缓存数据，返回缓存数据
          if (locationData && locationData.ip) {
            console.log('请求失败，使用缓存的定位信息');
            return {
              success: true,
              message: '获取定位信息成功（使用缓存）',
              data: locationData
            };
          }
          
          return { 
            success: false, 
            message: response.message || '获取定位信息失败', 
            data: null 
          };
        }
      } catch (error) {
        console.error('获取定位信息失败:', error);
        
        // 尝试从 localStorage 中获取缓存数据
        try {
          if (typeof localStorage !== 'undefined') {
            const locationInfoStr = localStorage.getItem('mall_location_info');
            if (locationInfoStr) {
              const storageData = JSON.parse(locationInfoStr);
              if (storageData && storageData.data && storageData.data.ip) {
                console.log('请求出错，使用缓存的定位信息');
                return {
                  success: true,
                  message: '获取定位信息成功（使用缓存）',
                  data: storageData.data
                };
              }
            }
          }
        } catch (storageError) {
          console.error('从本地存储获取定位信息失败:', storageError);
        }
        
        return { 
          success: false, 
          message: '获取定位信息失败，请稍后再试', 
          data: null 
        };
      }
    },
    
    /**
     * 获取省份树状信息
     * 从API获取省市县树状数据
     * @returns {Promise<Object>} - 请求结果，包含省市县树状数据
     */
    async fetchProvinceTree() {
      try {
        console.log('获取省市县树状数据');
        
        // 检查缓存是否过期（8小时 = 8 * 60 * 60 * 1000 = 28800000 毫秒）
        const now = Date.now();
        // 设置为长时间有效 暂定为365天
        const cacheExpiration = 365 * 24 * 60 * 60 * 1000; // 365天缓存过期时间
        
        // 从 localStorage 中获取省份树状信息和时间戳
        let provinceData = [];
        let timestamp = 0;
        
        if (typeof localStorage !== 'undefined') {
          const provinceTreeStr = localStorage.getItem('mall_province_tree');
          if (provinceTreeStr) {
            try {
              const storageData = JSON.parse(provinceTreeStr);
              if (storageData && storageData.data && Array.isArray(storageData.data) && storageData.timestamp) {
                provinceData = storageData.data;
                timestamp = storageData.timestamp;
                
                // 更新内存中的数据
                this.provinceTree = provinceData;
                this.provinceTreeTimestamp = timestamp;
              }
            } catch (error) {
              console.error('解析省份树状信息失败:', error);
            }
          }
        }
        
        // 如果有缓存数据且未过期，直接返回缓存数据
        if (provinceData.length > 0 && timestamp > 0 && (now - timestamp) < cacheExpiration) {
          console.log('使用缓存的省市县树状数据，缓存时间:', new Date(timestamp).toLocaleString());
          return {
            success: true,
            message: '获取省市县树状数据成功（缓存）',
            data: provinceData
          };
        }
        
        // 缓存过期或无缓存，发送获取省市县数据请求
        console.log('缓存已过期或无缓存，从接口获取省市县树状数据');
        const response = await addressApi.address.getRegionData();
        console.log('获取省市县数据响应:', response);
        
        if (response.code === 200 && response.data) {
          // 设置省市县树状数据
          this.setProvinceTree(response.data);
          
          return { 
            success: true, 
            message: '获取省市县树状数据成功', 
            data: response.data 
          };
        } else {
          // 如果请求失败但有缓存数据，返回缓存数据
          if (provinceData.length > 0) {
            console.log('请求失败，使用缓存的省市县树状数据');
            return {
              success: true,
              message: '获取省市县树状数据成功（使用缓存）',
              data: provinceData
            };
          }
          
          return { 
            success: false, 
            message: response.message || '获取省市县树状数据失败', 
            data: null 
          };
        }
      } catch (error) {
        console.error('获取省市县树状数据失败:', error);
        
        // 尝试从 localStorage 中获取缓存数据
        try {
          if (typeof localStorage !== 'undefined') {
            const provinceTreeStr = localStorage.getItem('mall_province_tree');
            if (provinceTreeStr) {
              const storageData = JSON.parse(provinceTreeStr);
              if (storageData && storageData.data && Array.isArray(storageData.data) && storageData.data.length > 0) {
                console.log('请求出错，使用缓存的省市县树状数据');
                return {
                  success: true,
                  message: '获取省市县树状数据成功（使用缓存）',
                  data: storageData.data
                };
              }
            }
          }
        } catch (storageError) {
          console.error('从本地存储获取省份树状信息失败:', storageError);
        }
        
        return { 
          success: false, 
          message: '获取省市县树状数据失败，请稍后再试', 
          data: null 
        };
      }
    },
    
    /**
     * 清除本地信息
     * 清除定位信息和省份树状信息
     */
    clearLocalInfo() {
      console.log('清除本地信息');
      
      // 重置定位信息
      this.locationInfo = {
        ip: '',
        country: '',
        area: '',
        location: '',
        cityCode: '',
        cityName: ''
      };
      
      // 重置省份树状信息
      this.provinceTree = [];
      
      // 清除本地存储
      try {
        if (typeof localStorage !== 'undefined') {
          localStorage.removeItem('mall_location_info');
          localStorage.removeItem('mall_province_tree');
          console.log('本地信息已从本地存储中清除');
        }
      } catch (error) {
        console.error('清除本地存储信息失败:', error);
      }
    }
  }
}, {
  // 持久化配置
  persist: true
});

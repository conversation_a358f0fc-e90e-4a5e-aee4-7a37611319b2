import { defineStore } from 'pinia';
import mallApi from '~/api/mall';
import { useMallUserStore } from './user';

/**
 * 商城浏览历史记录 Store
 * 用于记录用户最近浏览的商品信息
 */
export const useMallHistoryStore = defineStore('mall-history', {
  state: () => ({
    // 浏览历史记录
    historyItems: [],
    // 最大记录数量
    maxHistoryItems: 100,
    // 已选择的商品ID
    selectedItems: [],
    // 是否使用API模式（true: 使用后端API，false: 使用localStorage）
    useApiMode: true,
    // 加载状态
    loading: false,
    // 最后一次API响应（用于保存分页信息）
    lastApiResponse: null,
    // 分页信息
    pagination: null
  }),

  getters: {
    /**
     * 获取所有浏览历史记录
     * @returns {Array} 浏览历史记录数组
     */
    getHistoryItems: (state) => state.historyItems,
    
    /**
     * 获取已选择的商品ID
     * @returns {Array} 已选择的商品ID数组
     */
    getSelectedItems: (state) => state.selectedItems
  },

  actions: {
    /**
     * 添加商品到浏览历史
     * @param {Object} product - 商品信息对象
     */
    async addToHistory(product) {
      console.log('添加商品到浏览历史:', product);
      if (!product || !product.id) return;

      // 检查是否使用API模式且用户已登录
      if (this.useApiMode && this.isUserLoggedIn()) {
        try {
          await this.addToHistoryApi(product);
          // API调用成功后，同时更新本地状态
          this.addToHistoryLocal(product);
        } catch (error) {
          console.error('API添加浏览记录失败，回退到本地存储:', error);
          // API失败时回退到本地存储
          this.addToHistoryLocal(product);
        }
      } else {
        // 未登录或不使用API模式时，使用本地存储
        this.addToHistoryLocal(product);
      }
    },

    /**
     * 通过API添加浏览记录
     * @param {Object} product - 商品信息对象
     */
    async addToHistoryApi(product) {
      try {
        const response = await mallApi.browseHistory.addBrowseHistory({
          goodsSpuId: product.id.toString(),
          goodsSkuId: product.skuId ? product.skuId.toString() : null
        });

        if (response.code === 200) {
          console.log('API添加浏览记录成功');
          return response;
        } else {
          throw new Error(response.message || 'API添加浏览记录失败');
        }
      } catch (error) {
        console.error('API添加浏览记录失败:', error);
        throw error;
      }
    },

    /**
     * 本地添加商品到浏览历史
     * @param {Object} product - 商品信息对象
     */
    addToHistoryLocal(product) {
      
      // 创建一个简化版的商品对象，只保存必要信息
      const historyItem = {
        id: product.id,
        name: product.name,
        skuId: product.skuId,
        skuName: product.skuName,
        skuCode: product.skuCode,
        stock: product.stock,
        subtitle: product.subtitle || '',
        price: product.price || '0.00',
        originalPrice: product.originalPrice || '0.00',
        image: product.images && product.images.length > 0 ? product.images[0] : '',
        categoryId: product.categories && product.categories.length > 0 ? product.categories[0].id : '',
        categoryName: product.categories && product.categories.length > 0 ? product.categories[0].name : '',
        viewTime: new Date().toISOString()
      };

      // 检查是否已存在相同商品，如果存在则移除旧记录
      const existingIndex = this.historyItems.findIndex(item => item.id === historyItem.id);
      if (existingIndex !== -1) {
        this.historyItems.splice(existingIndex, 1);
      }

      // 添加新记录到数组开头
      this.historyItems.unshift(historyItem);

      // 如果超过最大记录数，则删除最旧的记录
      if (this.historyItems.length > this.maxHistoryItems) {
        this.historyItems = this.historyItems.slice(0, this.maxHistoryItems);
      }

      // 保存到 localStorage
      this.saveToLocalStorage();
    },

    /**
     * 从浏览历史中移除商品
     * @param {String|Number} productId - 要移除的商品ID
     */
    async removeFromHistory(productId) {
      // 检查是否使用API模式且用户已登录
      if (this.useApiMode && this.isUserLoggedIn()) {
        try {
          await this.removeFromHistoryApi(productId);
          // API调用成功后，同时更新本地状态
          this.removeFromHistoryLocal(productId);
        } catch (error) {
          console.error('API删除浏览记录失败，回退到本地操作:', error);
          // API失败时回退到本地操作
          this.removeFromHistoryLocal(productId);
        }
      } else {
        // 未登录或不使用API模式时，使用本地操作
        this.removeFromHistoryLocal(productId);
      }
    },

    /**
     * 通过API删除浏览记录
     * @param {String|Number} productId - 要移除的商品ID
     */
    async removeFromHistoryApi(productId) {
      try {
        const response = await mallApi.browseHistory.removeBrowseHistory(productId.toString());

        if (response.code === 200) {
          console.log('API删除浏览记录成功');
          return response;
        } else {
          throw new Error(response.message || 'API删除浏览记录失败');
        }
      } catch (error) {
        console.error('API删除浏览记录失败:', error);
        throw error;
      }
    },

    /**
     * 本地删除浏览记录
     * @param {String|Number} productId - 要移除的商品ID
     */
    removeFromHistoryLocal(productId) {
      this.historyItems = this.historyItems.filter(item => item.id !== productId);
      this.saveToLocalStorage();
    },

    /**
     * 清空浏览历史
     */
    async clearHistory() {
      // 检查是否使用API模式且用户已登录
      if (this.useApiMode && this.isUserLoggedIn()) {
        try {
          await this.clearHistoryApi();
          // API调用成功后，同时更新本地状态
          this.clearHistoryLocal();
        } catch (error) {
          console.error('API清空浏览记录失败，回退到本地操作:', error);
          // API失败时回退到本地操作
          this.clearHistoryLocal();
        }
      } else {
        // 未登录或不使用API模式时，使用本地操作
        this.clearHistoryLocal();
      }
    },

    /**
     * 通过API清空浏览历史
     */
    async clearHistoryApi() {
      try {
        const response = await mallApi.browseHistory.clearBrowseHistory();

        if (response.code === 200) {
          console.log('API清空浏览记录成功');
          return response;
        } else {
          throw new Error(response.message || 'API清空浏览记录失败');
        }
      } catch (error) {
        console.error('API清空浏览记录失败:', error);
        throw error;
      }
    },

    /**
     * 本地清空浏览历史
     */
    clearHistoryLocal() {
      this.historyItems = [];
      this.saveToLocalStorage();
    },

    /**
     * 保存历史记录到 localStorage
     */
    saveToLocalStorage() {
      if (process.client) {
        localStorage.setItem('mall_history_items', JSON.stringify(this.historyItems));
      }
    },

    /**
     * 从 localStorage 恢复历史记录
     */
    restoreFromLocalStorage() {
      if (process.client) {
        const savedHistory = localStorage.getItem('mall_history_items');
        if (savedHistory) {
          try {
            this.historyItems = JSON.parse(savedHistory);
          } catch (e) {
            console.error('解析历史记录数据失败:', e);
            this.historyItems = [];
          }
        }
      }
    },

    /**
     * 初始化 store
     */
    async init() {
      // 检查是否使用API模式且用户已登录
      if (this.useApiMode && this.isUserLoggedIn()) {
        try {
          await this.loadHistoryFromApi();
        } catch (error) {
          console.error('从API加载浏览记录失败，回退到本地存储:', error);
          this.restoreFromLocalStorage();
        }
      } else {
        // 未登录或不使用API模式时，从本地存储恢复
        this.restoreFromLocalStorage();
      }
    },

    /**
     * 从API加载浏览记录
     * @param {Object} options - 查询选项
     */
    async loadHistoryFromApi(options = {}) {
      try {
        this.loading = true;
        const response = await mallApi.browseHistory.getBrowseHistory(options);

        if (response.code === 200) {
          // 处理新的响应格式：{ items: [], pagination: {} }
          this.historyItems = response.data?.items || response.data || [];
          this.lastApiResponse = response; // 保存完整的API响应
          console.log('从API加载浏览记录成功:', this.historyItems.length, '条记录');

          // 如果有分页信息，也保存起来
          if (response.data?.pagination) {
            this.pagination = response.data.pagination;
            console.log('分页信息:', this.pagination);
          }

          return response;
        } else {
          throw new Error(response.message || '加载浏览记录失败');
        }
      } catch (error) {
        console.error('从API加载浏览记录失败:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 批量删除浏览记录
     * @param {Array} productIds - 商品ID数组
     */
    async batchRemoveFromHistory(productIds) {
      if (!productIds || productIds.length === 0) {
        return;
      }

      // 检查是否使用API模式且用户已登录
      if (this.useApiMode && this.isUserLoggedIn()) {
        try {
          await this.batchRemoveFromHistoryApi(productIds);
          // API调用成功后，同时更新本地状态
          this.batchRemoveFromHistoryLocal(productIds);
        } catch (error) {
          console.error('API批量删除浏览记录失败，回退到本地操作:', error);
          // API失败时回退到本地操作
          this.batchRemoveFromHistoryLocal(productIds);
        }
      } else {
        // 未登录或不使用API模式时，使用本地操作
        this.batchRemoveFromHistoryLocal(productIds);
      }
    },

    /**
     * 通过API批量删除浏览记录
     * @param {Array} productIds - 商品ID数组
     */
    async batchRemoveFromHistoryApi(productIds) {
      try {
        const response = await mallApi.browseHistory.removeBrowseHistoryBatch(
          productIds.map(id => id.toString())
        );

        if (response.code === 200) {
          console.log('API批量删除浏览记录成功');
          return response;
        } else {
          throw new Error(response.message || 'API批量删除浏览记录失败');
        }
      } catch (error) {
        console.error('API批量删除浏览记录失败:', error);
        throw error;
      }
    },

    /**
     * 本地批量删除浏览记录
     * @param {Array} productIds - 商品ID数组
     */
    batchRemoveFromHistoryLocal(productIds) {
      this.historyItems = this.historyItems.filter(item => !productIds.includes(item.id));
      this.saveToLocalStorage();
    },

    /**
     * 检查用户是否已登录
     * @returns {Boolean} - 是否已登录
     */
    isUserLoggedIn() {
      const userStore = useMallUserStore();
      // 检查用户是否已登录，包括token和用户信息
      const hasToken = userStore.token && userStore.token.trim() !== '';
      const hasUser = userStore.user && userStore.user.id;
      const isLoggedIn = userStore.loggedIn;

      console.log('浏览记录store - 用户登录状态检查:', {
        hasToken,
        hasUser,
        isLoggedIn,
        token: userStore.token ? userStore.token.substring(0, 20) + '...' : null,
        userId: userStore.user?.id
      });

      return isLoggedIn && hasToken && hasUser;
    },
    
    /**
     * 更新选中的商品
     * @param {String|Number} productId - 商品ID
     * @param {Boolean} isSelected - 是否选中
     */
    updateSelectedItem(productId, isSelected) {
      if (isSelected) {
        // 如果不存在则添加
        if (!this.selectedItems.includes(productId)) {
          this.selectedItems.push(productId);
        }
      } else {
        // 如果存在则移除
        this.selectedItems = this.selectedItems.filter(id => id !== productId);
      }
    },
    
    /**
     * 批量更新选中状态
     * @param {Array} productIds - 商品ID数组
     * @param {Boolean} isSelected - 是否选中
     */
    batchUpdateSelectedItems(productIds, isSelected) {
      if (isSelected) {
        // 合并并去重
        const uniqueIds = [...new Set([...this.selectedItems, ...productIds])];
        this.selectedItems = uniqueIds;
      } else {
        // 移除指定ID
        this.selectedItems = this.selectedItems.filter(id => !productIds.includes(id));
      }
    },
    
    /**
     * 清空选中状态
     */
    clearSelectedItems() {
      this.selectedItems = [];
    },
    
  },

  // 使用 Pinia 持久化插件
  persist: {
    storage: process.client ? localStorage : null,
    paths: ['historyItems', 'selectedItems'],
    key: 'mall-history-store'
  }
});

import { defineStore } from 'pinia'

/**
 * 进项发票状态管理
 */
const useInputInvoiceStore = defineStore('inputInvoice', {
  state: () => ({
    // 当前选中的发票
    selectedInvoice: null,
    // 当前选中的状态
    currentStatus: 'all',
    // 统计数据
    statistics: {
      all: 8,
      reconciled: 4,
      unreconciled: 4
    },
    // 进项发票模拟数据
    mockData: [
      {
        id: "I001",
        merchantName: "硬件供应商有限公司",
        invoiceTitle: "北京科技有限公司",
        invoiceTime: "2025-05-15 10:20:30",
        uploadTime: "2025-05-16 09:10:25",
        invoiceCode: "20250520001",
        invoiceType: "blue", // blue: 蓝字发票, red: 红字发票, void: 作废发票
        invoiceStatus: "completed", // completed: 已开票, cancelled: 已取消
        totalAmount: 1150.44,
        taxAmount: 149.56,
        productCount: 1,
        isReconciled: true,
        reconciledAmount: 1150.44,
        reconciledTime: "2025-05-21 09:15:30",
        // 添加商品明细
        goodsItems: [
          {
            id: "G001-1",
            name: "笔记本电脑",
            model: "X15-Pro",
            unit: "台",
            quantity: 1,
            price: 1150.44,
            amount: 1150.44,
            taxRate: 0.13,
            taxAmount: 149.56
          }
        ]
      },
      {
        id: "I002",
        merchantName: "软件科技有限公司",
        invoiceTitle: "北京科技有限公司",
        invoiceTime: "2025-05-16 14:30:25",
        uploadTime: "2025-05-17 10:05:15",
        invoiceCode: "20250520002",
        invoiceType: "blue",
        invoiceStatus: "completed",
        totalAmount: 148.56,
        taxAmount: 19.31,
        productCount: 1,
        isReconciled: true,
        reconciledAmount: 148.56,
        reconciledTime: "2025-05-21 09:15:30",
        // 添加商品明细
        goodsItems: [
          {
            id: "G002-1",
            name: "软件授权",
            model: "Office",
            unit: "套",
            quantity: 1,
            price: 148.56,
            amount: 148.56,
            taxRate: 0.13,
            taxAmount: 19.31
          }
        ]
      },
      {
        id: "I003",
        merchantName: "网络设备制造商",
        invoiceTitle: "上海贸易有限公司",
        invoiceTime: "2025-05-25 09:30:15",
        uploadTime: "2025-05-26 08:45:20",
        invoiceCode: "20250527001",
        invoiceType: "blue",
        invoiceStatus: "completed",
        totalAmount: 2499.50,
        taxAmount: 324.94,
        productCount: 2,
        isReconciled: true,
        reconciledAmount: 2499.50,
        reconciledTime: "2025-05-28 10:45:12",
        // 添加商品明细
        goodsItems: [
          {
            id: "G003-1",
            name: "服务器",
            model: "S2000",
            unit: "台",
            quantity: 1,
            price: 2212.83,
            amount: 2212.83,
            taxRate: 0.13,
            taxAmount: 287.67
          },
          {
            id: "G003-2",
            name: "网络设备",
            model: "N100",
            unit: "套",
            quantity: 1,
            price: 286.67,
            amount: 286.67,
            taxRate: 0.13,
            taxAmount: 37.27
          }
        ]
      },
      {
        id: "I004",
        merchantName: "办公用品供应商",
        invoiceTitle: "广州电子科技有限公司",
        invoiceTime: "2025-05-22 11:20:30",
        uploadTime: "2025-05-23 09:30:15",
        invoiceCode: "20250522001",
        invoiceType: "blue",
        invoiceStatus: "completed",
        totalAmount: 899.00,
        taxAmount: 116.87,
        productCount: 2,
        isReconciled: false,
        reconciledAmount: 0,
        reconciledTime: "",
        // 添加商品明细
        goodsItems: [
          {
            id: "G004-1",
            name: "打印机",
            model: "P500",
            unit: "台",
            quantity: 1,
            price: 796.46,
            amount: 796.46,
            taxRate: 0.13,
            taxAmount: 103.54
          },
          {
            id: "G004-2",
            name: "墨盒",
            model: "I-255",
            unit: "个",
            quantity: 2,
            price: 51.27,
            amount: 102.54,
            taxRate: 0.13,
            taxAmount: 13.33
          }
        ]
      },
      {
        id: "I005",
        merchantName: "家具制造有限公司",
        invoiceTitle: "深圳智能科技有限公司",
        invoiceTime: "2025-05-24 15:40:10",
        uploadTime: "2025-05-25 10:15:30",
        invoiceCode: "20250524001",
        invoiceType: "blue",
        invoiceStatus: "completed",
        totalAmount: 3299.00,
        taxAmount: 428.87,
        productCount: 3,
        isReconciled: false,
        reconciledAmount: 0,
        reconciledTime: "",
        // 添加商品明细
        goodsItems: [
          {
            id: "G005-1",
            name: "办公桌",
            model: "D-200",
            unit: "张",
            quantity: 5,
            price: 459.86,
            amount: 2299.30,
            taxRate: 0.13,
            taxAmount: 298.91
          },
          {
            id: "G005-2",
            name: "办公椅",
            model: "C-100",
            unit: "把",
            quantity: 5,
            price: 199.94,
            amount: 999.70,
            taxRate: 0.13,
            taxAmount: 129.96
          }
        ]
      },
      // 红字发票示例
      {
        id: "I006",
        merchantName: "电子设备供应商",
        invoiceTitle: "北京科技有限公司",
        invoiceTime: "2025-05-26 14:25:30",
        uploadTime: "2025-05-26 16:40:15",
        invoiceCode: "20250526001",
        invoiceType: "red", // 红字发票
        invoiceStatus: "completed",
        totalAmount: -1250.00,
        taxAmount: -162.50,
        productCount: 2,
        isReconciled: false,
        reconciledAmount: 0,
        reconciledTime: "",
        // 添加商品明细
        goodsItems: [
          {
            id: "G006-1",
            name: "显示器",
            model: "M27-Pro",
            unit: "台",
            quantity: 1,
            price: -850.00,
            amount: -850.00,
            taxRate: 0.13,
            taxAmount: -110.50
          },
          {
            id: "G006-2",
            name: "键盘",
            model: "K380",
            unit: "个",
            quantity: 2,
            price: -200.00,
            amount: -400.00,
            taxRate: 0.13,
            taxAmount: -52.00
          }
        ]
      },
      // 另一个红字发票示例
      {
        id: "I007",
        merchantName: "办公家具有限公司",
        invoiceTitle: "上海贸易有限公司",
        invoiceTime: "2025-05-27 09:15:45",
        uploadTime: "2025-05-27 11:30:20",
        invoiceCode: "20250527002",
        invoiceType: "red",
        invoiceStatus: "completed",
        totalAmount: -2199.00,
        taxAmount: -285.87,
        productCount: 1,
        isReconciled: true,
        reconciledAmount: -2199.00,
        reconciledTime: "2025-05-28 14:20:30",
        // 添加商品明细
        goodsItems: [
          {
            id: "G007-1",
            name: "会议桌",
            model: "MT-200",
            unit: "套",
            quantity: 1,
            price: -2199.00,
            amount: -2199.00,
            taxRate: 0.13,
            taxAmount: -285.87
          }
        ]
      },
      // 作废发票示例
      {
        id: "I008",
        merchantName: "数码科技有限公司",
        invoiceTitle: "广州电子科技有限公司",
        invoiceTime: "2025-05-28 10:30:25",
        uploadTime: "2025-05-28 14:15:40",
        invoiceCode: "20250528001",
        invoiceType: "void", // 作废发票
        invoiceStatus: "cancelled",
        totalAmount: 3599.00,
        taxAmount: 467.87,
        productCount: 3,
        isReconciled: false,
        reconciledAmount: 0,
        reconciledTime: "",
        // 添加商品明细
        goodsItems: [
          {
            id: "G008-1",
            name: "平板电脑",
            model: "T10-Pro",
            unit: "台",
            quantity: 2,
            price: 1599.50,
            amount: 3199.00,
            taxRate: 0.13,
            taxAmount: 415.87
          },
          {
            id: "G008-2",
            name: "保护套",
            model: "TP-100",
            unit: "个",
            quantity: 2,
            price: 200.00,
            amount: 400.00,
            taxRate: 0.13,
            taxAmount: 52.00
          }
        ]
      }
    ]
  }),
  
  actions: {
    /**
     * 设置进项发票的选中发票
     * @param {Object} invoice 发票对象
     */
    setSelectedInvoice(invoice) {
      this.selectedInvoice = invoice
    },
    
    /**
     * 设置进项发票的当前状态
     * @param {String} status 状态值
     */
    setStatus(status) {
      this.currentStatus = status
    },
    
    /**
     * 更新进项发票统计数据
     * @param {Object} statistics 统计数据对象
     */
    updateStatistics(statistics) {
      this.statistics = {
        ...this.statistics,
        ...statistics
      }
    },
    
    /**
     * 获取进项发票模拟数据
     * @param {Object} params 查询参数
     * @returns {Object} 分页数据结果
     */
    getInputInvoiceData(params = {}) {
      const { page = 1, limit = 10, ...filters } = params
      
      // 过滤数据
      let filteredData = [...this.mockData]
      
      // 根据商户名称过滤
      if (filters.merchantName) {
        filteredData = filteredData.filter(item => item.merchantName.includes(filters.merchantName))
      }
      
      // 根据发票抬头过滤
      if (filters.invoiceTitle) {
        filteredData = filteredData.filter(item => item.invoiceTitle.includes(filters.invoiceTitle))
      }
      
      // 根据发票代码过滤
      if (filters.invoiceCode) {
        filteredData = filteredData.filter(item => item.invoiceCode.includes(filters.invoiceCode))
      }
      
      // 根据发票状态过滤
      if (filters.invoiceStatus) {
        filteredData = filteredData.filter(item => item.invoiceStatus === filters.invoiceStatus)
      }
      
      // 根据发票类型过滤
      if (filters.invoiceType) {
        filteredData = filteredData.filter(item => item.invoiceType === filters.invoiceType)
      }
      
      // 根据勾兑状态过滤
      if (filters.isReconciled !== undefined && filters.isReconciled !== '') {
        const isReconciled = filters.isReconciled === true || filters.isReconciled === 'true'
        filteredData = filteredData.filter(item => item.isReconciled === isReconciled)
      }
      
      // 根据开票时间范围过滤
      if (filters.startInvoiceTime && filters.endInvoiceTime) {
        filteredData = filteredData.filter(item => {
          const itemTime = new Date(item.invoiceTime).getTime()
          return itemTime >= filters.startInvoiceTime && itemTime <= filters.endInvoiceTime
        })
      }
      
      // 根据上传时间范围过滤
      if (filters.startUploadTime && filters.endUploadTime) {
        filteredData = filteredData.filter(item => {
          const itemTime = new Date(item.uploadTime).getTime()
          return itemTime >= filters.startUploadTime && itemTime <= filters.endUploadTime
        })
      }
      
      // 计算统计数据
      const all = this.mockData.length
      const reconciled = this.mockData.filter(item => item.isReconciled).length
      const unreconciled = this.mockData.filter(item => !item.isReconciled).length
      
      // 更新统计数据
      this.updateStatistics({ all, reconciled, unreconciled })
      
      // 分页处理
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedData = filteredData.slice(startIndex, endIndex)
      
      return {
        list: paginatedData,
        total: filteredData.length
      }
    },
    
    /**
     * 获取发票详情
     * @param {String} id 发票ID
     * @returns {Object|null} 发票详情对象
     */
    getInvoiceDetail(id) {
      return this.mockData.find(item => item.id === id) || null
    },
    
    /**
     * 更新发票状态
     * @param {String} id 发票ID
     * @param {String} status 新状态
     * @returns {Boolean} 是否更新成功
     */
    updateInvoiceStatus(id, status) {
      const index = this.mockData.findIndex(item => item.id === id)
      if (index === -1) return false
      
      this.mockData[index] = {
        ...this.mockData[index],
        invoiceStatus: status,
        updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
      
      return true
    },
    
    /**
     * 更新发票勾兑状态
     * @param {String} id 发票ID
     * @param {Number} reconciledAmount 勾兑金额
     * @returns {Boolean} 是否更新成功
     */
    updateReconcileStatus(id, reconciledAmount) {
      const index = this.mockData.findIndex(item => item.id === id)
      if (index === -1) return false
      
      const currentReconciledAmount = this.mockData[index].reconciledAmount || 0
      const newReconciledAmount = currentReconciledAmount + reconciledAmount
      
      this.mockData[index] = {
        ...this.mockData[index],
        isReconciled: true,
        reconciledAmount: newReconciledAmount,
        reconciledTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
      
      return true
    }
  }
})

export default useInputInvoiceStore

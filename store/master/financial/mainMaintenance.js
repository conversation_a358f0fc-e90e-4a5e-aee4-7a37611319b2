import { defineStore } from 'pinia'

/**
 * 主体维护状态管理
 */
const useMainMaintenanceStore = defineStore('mainMaintenance', {
  state: () => ({
    // 当前选中的实体
    selectedEntity: null,
    // 主体维护模拟数据
    mockData: [
      {
        id: "10001",
        name: "北京科技有限公司",
        taxId: "91110105MA00B7EX3W",
        address: "北京市朝阳区建国路88号",
        phone: "010-********",
        bank: "中国工商银行北京分行",
        bankAccount: "6222021234567890123",
        invoiceAmount: 50000.00,
        createTime: "2025-05-01 10:30:25",
        updateTime: "2025-05-01 10:30:25"
      },
      {
        id: "10002",
        name: "上海贸易有限公司",
        taxId: "91310115MA1K3FXU2P",
        address: "上海市浦东新区张江高科技园区博云路2号",
        phone: "021-********",
        bank: "中国建设银行上海分行",
        bankAccount: "6227002345678901234",
        invoiceAmount: 80000.00,
        createTime: "2025-05-10 14:22:10",
        updateTime: "2025-05-10 14:22:10"
      },
      {
        id: "10003",
        name: "广州电子科技有限公司",
        taxId: "91440101MA9XFT1R5E",
        address: "广州市天河区天河路385号",
        phone: "020-********",
        bank: "中国银行广州分行",
        bankAccount: "6216612345678901234",
        invoiceAmount: 30000.00,
        createTime: "2025-05-15 09:15:30",
        updateTime: "2025-05-15 09:15:30"
      },
      {
        id: "10004",
        name: "深圳智能科技有限公司",
        taxId: "91440300MA5ETKUW1X",
        address: "深圳市南山区科技园南区",
        phone: "0755-********",
        bank: "招商银行深圳分行",
        bankAccount: "6225882345678901234",
        invoiceAmount: 100000.00,
        createTime: "2025-05-18 16:40:15",
        updateTime: "2025-05-18 16:40:15"
      },
      {
        id: "10005",
        name: "杭州网络科技有限公司",
        taxId: "91330108MA2B25TQ1F",
        address: "杭州市滨江区网商路699号",
        phone: "0571-********",
        bank: "中国农业银行杭州分行",
        bankAccount: "6228482345678901234",
        invoiceAmount: 60000.00,
        createTime: "2025-05-20 11:05:40",
        updateTime: "2025-05-20 11:05:40"
      }
    ]
  }),
  
  actions: {
    /**
     * 设置主体维护的选中实体
     * @param {Object} entity 实体对象
     */
    setSelectedEntity(entity) {
      this.selectedEntity = entity
    },
    
    /**
     * 获取主体维护模拟数据
     * @param {Object} params 查询参数
     * @returns {Object} 分页数据结果
     */
    getMainMaintenanceData(params = {}) {
      const { page = 1, limit = 10, name, taxId, address, bank } = params
      
      // 过滤数据
      let filteredData = [...this.mockData]
      
      // 根据名称过滤
      if (name) {
        filteredData = filteredData.filter(item => item.name.includes(name))
      }
      
      // 根据纳税人识别号过滤
      if (taxId) {
        filteredData = filteredData.filter(item => item.taxId.includes(taxId))
      }
      
      // 根据地址过滤
      if (address) {
        filteredData = filteredData.filter(item => item.address.includes(address))
      }
      
      // 根据开户行过滤
      if (bank) {
        filteredData = filteredData.filter(item => item.bank.includes(bank))
      }
      
      // 分页处理
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedData = filteredData.slice(startIndex, endIndex)
      
      return {
        code: 0,
        message: "success",
        data: {
          items: paginatedData,
          total: filteredData.length,
          page: params.page || 1,
          pageSize: params.pageSize || 10
        }
      }
    },
    
    /**
     * 根据ID获取主体信息
     * @param {String} id 主体ID
     * @returns {Object|null} 主体信息对象
     */
    getEntityById(id) {
      return this.mockData.find(item => item.id === id) || null
    },
    
    /**
     * 添加主体
     * @param {Object} entity 主体数据
     * @returns {Object} 新增的主体数据
     */
    addEntity(entity) {
      // 生成新ID
      const newId = (parseInt(this.mockData[this.mockData.length - 1]?.id || '10000') + 1).toString()
      
      // 创建新主体对象
      const newEntity = {
        ...entity,
        id: newId,
        createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
      
      // 添加到模拟数据中
      this.mockData.push(newEntity)
      
      return newEntity
    },
    
    /**
     * 更新主体
     * @param {String} id 主体ID
     * @param {Object} entityData 更新的主体数据
     * @returns {Object|null} 更新后的主体对象
     */
    updateEntity(id, entityData) {
      const index = this.mockData.findIndex(item => item.id === id)
      if (index === -1) return null
      
      // 更新主体数据
      this.mockData[index] = {
        ...this.mockData[index],
        ...entityData,
        updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
      
      return this.mockData[index]
    },
    
    /**
     * 删除主体
     * @param {String|Array} ids 主体ID或ID数组
     * @returns {Boolean} 是否删除成功
     */
    deleteEntity(ids) {
      if (typeof ids === 'string') {
        // 单个ID
        const index = this.mockData.findIndex(item => item.id === ids)
        if (index === -1) return false
        
        // 从模拟数据中删除
        this.mockData.splice(index, 1)
        return true
      } else if (Array.isArray(ids)) {
        // ID数组
        ids.forEach(id => {
          const index = this.mockData.findIndex(item => item.id === id)
          if (index !== -1) {
            this.mockData.splice(index, 1)
          }
        })
        return true
      }
      
      return false
    }
  }
})

export default useMainMaintenanceStore

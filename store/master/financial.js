/**
 * 财务模块状态管理
 * 整合了四个子模块：发票对账、主体维护、进项发票和销项发票
 */
import { useInvoiceReconciliationStore } from './financial/invoiceReconciliation'
import useMainMaintenanceStore from './financial/mainMaintenance'
import useInputInvoiceStore from './financial/inputInvoice'
import useOutputInvoiceStore from './financial/outputInvoice'

/**
 * 获取销售方模板列表
 * @returns {Array} 销售方模板列表
 */
export const getSellerTemplates = () => {
  const mainMaintenanceStore = useMainMaintenanceStore();
  // 从主体维护模块获取数据
  return mainMaintenanceStore.mockData.map(item => ({
    name: item.name,
    taxNumber: item.taxId,
    addressPhone: `${item.address} ${item.phone}`,
    bankAccount: `${item.bank} ${item.bankAccount}`
  }));
};

// 导出所有模块
export {
  useInvoiceReconciliationStore,
  useMainMaintenanceStore,
  useInputInvoiceStore,
  useOutputInvoiceStore
}

/**
 * 获取发票详情
 * @param {String} id 发票ID
 * @param {String} type 发票类型：'input' 或 'output' 或 'reconciliation'
 * @returns {Object|null} 发票详情对象
 */
export const getInvoiceDetail = (id, type = 'input') => {
  if (type === 'input') {
    return useInputInvoiceStore().getInvoiceDetail(id)
  } else if (type === 'output') {
    return useOutputInvoiceStore().getInvoiceDetail(id)
  } else if (type === 'reconciliation') {
    return useInvoiceReconciliationStore().mockData.find(item => item.id === id) || null
  }
  return null
}

/**
 * 勾兑发票
 * @param {Object} reconcileData 勾兑数据
 * @returns {Boolean} 是否勾兑成功
 */
export const reconcileInvoice = (reconcileData) => {
  const { invoiceId, reconciledAmount, inputInvoices } = reconcileData
  
  // 更新对账发票数据
  const reconciliationStore = useInvoiceReconciliationStore()
  const result = reconciliationStore.reconcileInvoice(reconcileData)
  
  if (result) {
    // 更新进项发票的勾兑状态
    const inputInvoiceStore = useInputInvoiceStore()
    for (const inputInvoice of inputInvoices) {
      inputInvoiceStore.updateReconcileStatus(inputInvoice.id, inputInvoice.reconciledAmount)
    }
    
    // 更新销项发票的勾兑状态
    const reconciliationItem = reconciliationStore.mockData.find(item => item.id === invoiceId)
    if (reconciliationItem && reconciliationItem.outputInvoiceId) {
      const outputInvoiceStore = useOutputInvoiceStore()
      outputInvoiceStore.updateReconcileStatus(reconciliationItem.outputInvoiceId, reconciledAmount)
    }
  }
  
  return result
}

// 默认导出所有模块和辅助函数
export default {
  useInvoiceReconciliationStore,
  useMainMaintenanceStore,
  useInputInvoiceStore,
  useOutputInvoiceStore,
  getInvoiceDetail,
  reconcileInvoice,
  getSellerTemplates
}

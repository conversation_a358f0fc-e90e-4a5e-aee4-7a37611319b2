import { defineStore } from 'pinia'
import tool from '@/utils/tool'

// 根据当前路径动态确定默认标签的路径和名称
let defaultTagPath = '/dashboard';
let defaultTagName = 'dashboard';
let defaultTagTitle = '仪表盘';

// 检查当前URL路径
if (typeof window !== 'undefined') {
  const currentPath = window.location.pathname;
  if (currentPath.includes('/master/')) {
    defaultTagPath = '/master/dashboard';
    defaultTagName = 'master-dashboard';
    defaultTagTitle = '仪表盘';
  } else if (currentPath.includes('/merchant/')) {
    defaultTagPath = '/merchant/dashboard';
    defaultTagName = 'merchant-dashboard';
    defaultTagTitle = '仪表盘';
  } else if (currentPath.includes('/provider/')) {
    defaultTagPath = '/provider/workbench';
    defaultTagName = 'provider-workbench';
    defaultTagTitle = '工作台';
  }
}

const defaultTag = [ { name: defaultTagName, title: defaultTagTitle, path: defaultTagPath, affix: true } ]
const useTagStore = defineStore('tag', {
  state: () => ({
    tags: (! tool.local.get('tags') || tool.local.get('tags').length === 0 ) ? defaultTag : tool.local.get('tags')
  }),

  getters: {
    getState() {
      return { ...this.$state }
    },
  },

  actions: {

    addTag(tag) {
      console.log('添加标签:', tag)
      const target = this.tags.find( item => item.path === tag.path )
      if (! target && tag.path ) {
        this.tags.push(tag)
      }
      this.updateTagsToLocal()
    },

    removeTag(tag) {
      let index = 0
      this.tags.map((item, idx) => {
        if ( item.path === tag.path && ! item.affix ) {
          if (this.tags[(idx + 1)]) {
            index = idx
          } else if ( idx > 0) {
            index = idx - 1
          }
          this.tags.splice(idx, 1)
        }
      })
      this.updateTagsToLocal()
      return this.tags[index]
    },

    updateTag(tag) {
      this.tags.map(item => {
        if (item.path == tag.path) {
          item = Object.assign(item, tag)
        }
      })
      this.updateTagsToLocal()
    },

    updateTagTitle(path, title) {
      this.tags.map(item => {
        if (item.path == path) {
          item.title = title
        }
      })
      this.updateTagsToLocal()
    },

    updateTagsToLocal() {
      tool.local.set('tags', this.tags)
    },

    clearTags() {
      // 重置为默认标签
      let defaultTagPath = '/dashboard';
      let defaultTagName = 'dashboard';
      let defaultTagTitle = '仪表盘';
      
      // 检查当前URL路径
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        if (currentPath.includes('/master/')) {
          defaultTagPath = '/master/dashboard';
          defaultTagName = 'master-dashboard';
          defaultTagTitle = '仪表盘';
        } else if (currentPath.includes('/merchant/')) {
          defaultTagPath = '/merchant/dashboard';
          defaultTagName = 'merchant-dashboard';
          defaultTagTitle = '仪表盘';
        } else if (currentPath.includes('/provider/')) {
          defaultTagPath = '/provider/workbench';
          defaultTagName = 'provider-workbench';
          defaultTagTitle = '工作台';
        }
      }
      this.tags = [ { name: defaultTagName, title: defaultTagTitle, path: defaultTagPath, affix: true } ]
      this.updateTagsToLocal()
    },
  },
})

export default useTagStore
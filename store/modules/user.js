import { defineStore } from 'pinia'
import authApi from '@/api/master/auth'
import systemApi from '@/api/master/system';
import tool from '@/utils/tool'
import { useAppStore, useTagStore } from '@/store'
import masterMenuMock from '@/mock/master'
import merchantMenuMock from '@/mock/merchant'
import { Message } from '@arco-design/web-vue'
const useUserStore = defineStore('user', {

  state: () => ({
    codes: undefined,
    roles: undefined,
    routers: undefined,
    user: undefined,
    menus: undefined,
  }),

  getters: {
    getState() {
      return { ...this.$state }
    },
  },

  actions: {

    // 根据路由前缀获取对应的 token 键名
    getTokenKey(routePrefix = '') {
      // 如果没有提供路由前缀，尝试从当前路径获取
      if (!routePrefix && typeof window !== 'undefined') {
        const currentPath = window.location.pathname
        const pathParts = currentPath.split('/')
        if (pathParts.length > 1 && pathParts[1]) {
          routePrefix = pathParts[1]
        }
      }

      // 默认使用 master 前缀
      routePrefix = routePrefix || 'master'

      // 返回对应的 token 键名
      return `token_${routePrefix}`
    },

    // 设置 token，根据路由前缀存储
    setToken(token, routePrefix = '') {
      const tokenKey = this.getTokenKey(routePrefix)
      tool.local.set(tokenKey, token)
    },
    // 获取 menu 键名，根据路由前缀
    getMenuKey(routePrefix = '') {
      // 默认使用 master 前缀
      routePrefix = routePrefix || 'master'

      // 返回对应的 menu 键名
      return `menu_${routePrefix}`
    },

    // 设置 menu，根据路由前缀存储
    setMenu(menu, routePrefix = '') {
      const menuKey = this.getMenuKey(routePrefix)
      tool.local.set(menuKey, menu)
    },
    // 获取 user 键名，根据路由前缀
    getUserKey(routePrefix = '') {
      // 默认使用 master 前缀
      routePrefix = routePrefix || 'master'

      // 返回对应的 menu 键名
      return `user_${routePrefix}`
    },

    setUser(user, routePrefix = '') {
      const userKey = this.getUserKey(routePrefix)
      tool.local.set(userKey, user)
    },

    // 获取 user，根据路由前缀获取
    getUser(routePrefix = '') {
      const userKey = this.getUserKey(routePrefix)
      return tool.local.get(userKey)
    },

    // 清除特定前缀的 user
    clearUser(routePrefix = '') {
      const userKey = this.getUserKey(routePrefix)
      tool.local.remove(userKey)
    },

    // 清除所有 user
    clearAllUsers() {
      const prefixes = ['master', 'mall', 'merchant', 'supplier']
      prefixes.forEach(prefix => {
        const userKey = this.getUserKey(prefix)
        tool.local.remove(userKey)
      })
    },

    // 获取 menu，根据路由前缀获取
    getMenu(routePrefix = '') {
      const menuKey = this.getMenuKey(routePrefix)
      return tool.local.get(menuKey)
    },

    // 清除特定前缀的 menu
    clearMenu(routePrefix = '') {
      const menuKey = this.getMenuKey(routePrefix)
      tool.local.remove(menuKey)
    },

    // 获取 token，根据路由前缀获取
    getToken(routePrefix = '') {
      const tokenKey = this.getTokenKey(routePrefix)
      return tool.local.get(tokenKey)
    },

    // 清除特定前缀的 token
    clearToken(routePrefix = '') {
      const tokenKey = this.getTokenKey(routePrefix)
      tool.local.remove(tokenKey)
    },

    // 清除所有 token、menu 和 user
    clearAllTokens() {
      const prefixes = ['master', 'mall', 'merchant', 'supplier']
      prefixes.forEach(prefix => {
        const tokenKey = this.getTokenKey(prefix)
        tool.local.remove(tokenKey)
      })
      // 同步清除所有 menu 缓存，保证安全
      this.clearAllMenus();
      // 同步清除所有 user 缓存
      this.clearAllUsers();
    },

    // 清除所有 menu
    clearAllMenus() {
      const prefixes = ['master', 'mall', 'merchant', 'supplier']
      prefixes.forEach(prefix => {
        const menuKey = this.getMenuKey(prefix)
        tool.local.remove(menuKey)
      })
    },

    setInfo(data) {
      this.$patch(data)
    },

    resetUserInfo() {
      this.$reset();
    },

    /**
     * 处理缓存中的菜单和用户数据，转换为标准结构
     * @param {Object} menuData - 菜单数据
     * @param {Object} userData - 用户数据
     * @returns {Object} 标准结构的数据
     */
    processLocalData(menuData, userData) {
      // 在解析之前确保数据为对象
      menuData = typeof menuData === 'string' ? JSON.parse(menuData) : menuData || [];
      userData = typeof userData === 'string' ? JSON.parse(userData) : userData || {};
      
      // 初始化结果对象
      const result = {
        user: userData,
        routers: [],
        codes: [],
        roles: userData.role_id ? [userData.role_name || userData.role_id] : [] // 从用户数据中提取角色
      };
      
      // 递归处理菜单数据，提取按钮权限
      const processMenu = (items) => {
        const routerItems = [];
        
        if (!Array.isArray(items)) return routerItems;
        
        items.forEach(item => {
          // 如果是按钮类型，添加到按钮权限中
          if (item.type === 'B') {
            result.codes.push(item.code);
            return; // 不继续处理按钮
          }
          
          // 处理非按钮类型的菜单项
          const routerItem = {
            id: item.id,
            parent_id: item.parent_id,
            name: item.code, // 用code作为路由的name
            component: item.component || "",
            path: item.route || "",
            redirect: item.redirect || "",
            meta: {
              type: item.type,
              icon: item.icon,
              title: item.name, // 用name作为显示标题
              hidden: item.is_hidden == 0 ? false : true,
              hiddenBreadcrumb: false
            }
          };
          
          // 如果有子项，递归处理
          if (item.children && item.children.length > 0) {
            routerItem.children = processMenu(item.children);
          } else {
            routerItem.children = [];
          }
          
          routerItems.push(routerItem);
        });
        
        return routerItems;
      };
      
      // 处理所有菜单
      result.routers = processMenu(menuData);
      
      return result;
    },
    
    async requestUserInfo() {
      const router = useRouter();
      return new Promise(async (resolve, reject) => {
        try {
          // 获取当前路径及前缀
          const currentPath = window.location.pathname;
          let routePrefix = '';
          const pathParts = currentPath.split('/');
          if (pathParts.length > 1 && pathParts[1]) {
            routePrefix = pathParts[1];
          }
          routePrefix = routePrefix || 'master';

          // 尝试从本地缓存获取数据
          const menuData = this.getMenu(routePrefix);
          const userData = this.getUser(routePrefix);
          if (menuData && userData) {
            // 如果有缓存数据，处理并使用
            const processedData = this.processLocalData(menuData, userData);
            this.setInfo(processedData);
          } else {
            // 如果没有缓存，使用模拟数据
            if (currentPath.includes('/merchant/')) {
              this.setInfo(merchantMenuMock);
            } else {
              this.setInfo(masterMenuMock);
            }
          }
          
          // 创建默认的仪表盘路由
          // const homePage = {
          //   id: "1",
          //   parent_id: "0",
          //   name: currentPath.includes('/merchant/') ? "merchant-dashboard" : "master-dashboard",
          //   path: currentPath.includes('/merchant/') ? "/merchant/dashboard" : "/master/dashboard",
          //   redirect: "",
          //   meta: {
          //     title: '仪表盘',
          //     type: 'M',
          //     icon: 'IconCopy',
          //     hidden: false
          //   },
          //   children: []
          // };

          
          // 处理路由
          // if (typeof webRouter !== 'undefined' && webRouter.length > 0) {
          //   homePage.children = webRouter[0].children;
          // }
          // 获取系统配置列表的所有配置类型
          // const type = await systemApi.configuration.getTypes()
          // tool.local.set('configureTypes', type.data)

          // 移除按钮类型的菜单
          this.routers = removeButtonMenu(this.routers);

          console.log(this.routers, 'routers1')

          // 添加首页路由
          //this.routers.unshift(homePage);

          console.log(this.routers, 'routers2')

          // 设置应用状态
          this.setApp()
            .then(() => {
              resolve(true);
            })
            .catch(error => {
              reject(error);
            });
        } catch (error) {
          reject(error);
        }
      });
    },
    /**
         * 使用RSA加密密码
         * @param {string} password - 原始密码
         * @param {string} publicKey - RSA公钥
         * @returns {Promise<string>} 加密后的密码
         */
    async encryptPassword(password, publicKey) {
      try {
        // 使用TextEncoder将密码转为二进制数据（浏览器兼容的方式）
        const encoder = new TextEncoder();
        const binaryData = encoder.encode(password);

        // 将二进制数据转为字符串
        let binaryString = '';
        for (let i = 0; i < binaryData.length; i++) {
          binaryString += String.fromCharCode(binaryData[i]);
        }

        // 导入加密模块
        const encryptionUtil = await import('@/utils/encryption.js')

        // 使用二进制字符串进行加密
        return encryptionUtil.default.encrypt(binaryString, publicKey)
      } catch (error) {
        throw new Error('密码加密失败: ' + error.message)
      }
    },

    async login(form) {
      try {
        const keyResponse = await authApi.getPublicKey()
        const responseBody = keyResponse.data;
        const publicKey = responseBody.publicKey;
        // 加密密码
        const encryptedPassword = await this.encryptPassword(form.password, publicKey)
        // 准备登录参数，尝试不同的参数组合
        const loginParams = {
          username: form.username,
          password: form.password,
          captcha: form.captcha,
          captchaId: form.captchaId,
          encrypted: false
        }
        // 调用登录接口
        const loginResponse = await authApi.login(loginParams)
        if (loginResponse?.code === 200) {
          let routePrefix = ''
          if (typeof window !== 'undefined') {
            const currentPath = window.location.pathname
            const pathParts = currentPath.split('/')
            if (pathParts.length > 1 && pathParts[1]) {
              routePrefix = pathParts[1]
            }
          }
          this.setToken(loginResponse.data.token)
          this.setMenu(loginResponse.data.menuTree)
          this.setUser(loginResponse.data.user)
          return true
        } else {
          Message.error(loginResponse?.message ?? '登录失败')
          return false
        }
      } catch (error) {
        Message.error('获取公钥失败，请稍后重试')
        return false
      }
    },

    async logout() {
      // 注释掉原来的接口调用
      const tagStore = useTagStore()
      tool.local.remove('tags')
      tagStore.clearTags()

      // 获取当前路径前缀
      let routePrefix = ''
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname
        const pathParts = currentPath.split('/')
        if (pathParts.length > 1 && pathParts[1]) {
          routePrefix = pathParts[1]
        }
      }

      // 只清除当前路由前缀对应的 token、menu 和 user
      this.clearToken(routePrefix)
      this.clearMenu(routePrefix)
      this.clearUser(routePrefix)
      tool.local.remove('configureTypes')

      this.resetUserInfo()
    },

    async setApp() {
      const appStore = useAppStore()
      const setting = typeof this.user.backend_setting === 'string' ? JSON.parse(this.user.backend_setting) : this.user.backend_setting
      appStore.toggleMode(setting?.mode ?? appStore.mode)
      appStore.toggleMenu(setting?.menuCollapse ?? appStore.menuCollapse)
      appStore.toggleTag(setting?.tag ?? appStore.tag)
      appStore.changeMenuWidth(setting?.menuWidth ?? appStore.menuWidth)
      appStore.changeLayout(setting?.layout ?? appStore.layout)
      appStore.useSkin(setting?.skin ?? appStore.skin)
      appStore.changeColor(setting?.color ?? appStore.color)
    }
  }

})


// 去除按钮菜单
const removeButtonMenu = (routers) => {
  let handlerAfterRouters = []
  routers.forEach(item => {
    if (item.meta.type !== 'B') {
      let route = item
      if (item.children && item.children.length > 0) {
        route.children = removeButtonMenu(item.children)
      }
      handlerAfterRouters.push(route)
    }
  })
  return handlerAfterRouters
}
export default useUserStore

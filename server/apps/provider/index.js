/**
 * 服务商模块入口文件
 */
const express = require('express');
const path = require('path');
const BaseModule = require('../../core/module/BaseModule');

// 导入认证中间件
const authMiddleware = require('../../core/middleware/AuthMiddleware');

// 导入 Swagger 模型和路径
const userModel = require('./swagger/schemas/UserModel');
const authPaths = require('./swagger/paths/AuthPaths');

/**
 * 服务商模块类
 * 提供服务商用户的管理功能
 */
class ProviderModule extends BaseModule {
  /**
   * 初始化路由
   */
  async initRoutes() {
    try {
      console.log('ProviderModule: 开始初始化路由');
      console.log('ProviderModule: Prisma 实例状态:', !!this.prisma);

      if (!this.prisma) {
        throw new Error('Prisma 客户端未初始化');
      }

      // 加载用户路由
      const userRoutes = require('./routes/UserRoute');

      // 加载框架协议路由
      const frameworkRoutes = require('./routes/FrameworkRoute');

      // 加载服务商信息路由
      const providerInfoRoutes = require('./routes/ProviderInfoRoute');

      // 加载团队管理路由
      const teamRoutes = require('./routes/TeamRoute');

      // 加载目标设置路由
      const goalSettingRoutes = require('./routes/GoalSettingRoute');

      // 加载订单报备路由
      const orderReportRoutes = require('./routes/OrderReportRoute');

      // 加载订单管理路由
      const orderRoutes = require('./routes/OrderRoute');

      // 加载订单指派路由
      const orderAssignmentRoutes = require('./routes/OrderAssignmentRoute');

      // 注册用户路由
      this.router.use('/user', userRoutes(this.prisma));

      // 注册框架协议路由
      this.router.use('/framework', frameworkRoutes(this.prisma));

      // 注册服务商信息路由
      this.router.use('/info', providerInfoRoutes(this.prisma));

      // 注册团队管理路由
      this.router.use('/team', teamRoutes);

      // 注册目标设置路由
      this.router.use('/goal', goalSettingRoutes(this.prisma));

      // 注册订单报备路由
      this.router.use('/order', orderReportRoutes(this.prisma));

      // 注册订单管理路由
      this.router.use('/orders', orderRoutes(this.prisma));

      // 注册订单指派路由
      this.router.use('/order', orderAssignmentRoutes(this.prisma));

      console.log('服务商模块路由已加载');
    } catch (error) {
      console.error('注册服务商模块路由失败:', error);
      throw error;
    }
  }

  /**
   * 重写初始化方法以确保正确的初始化顺序
   */
  async init() {
    console.log('ProviderModule: 开始初始化');

    // 确保先初始化数据库
    await this.initDatabase();

    // 然后初始化路由
    await this.initRoutes();

    // 初始化事件监听
    await this.initEventListeners();

    // 初始化 Swagger 文档
    await super.initSwagger();

    console.log('ProviderModule: 初始化完成');
  }
}

module.exports = ProviderModule;

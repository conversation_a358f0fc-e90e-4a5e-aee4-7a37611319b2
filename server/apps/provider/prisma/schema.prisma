generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["provider", "base"]
}

/// 服务商团队信息表，存储团队基本信息
model ProviderTeam {
  // 主键
  id            BigInt    @id                           /// 团队ID，16位雪花算法，系统自动生成
  
  // 基本信息
  name          String    @db.VarChar(100)              /// 团队名称，必填
  description   String?   @db.Text                      /// 团队描述
  leader_id     BigInt                                  /// 团队负责人ID，关联系统用户表
  
  // 目标设置
  monthly_target    Decimal?  @db.Decimal(15,2)         /// 月度目标金额
  quarterly_target  Decimal?  @db.Decimal(15,2)         /// 季度目标金额
  yearly_target     Decimal?  @db.Decimal(15,2)         /// 年度目标金额
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  members       ProviderTeamMember[]                    /// 团队成员关系
  
  @@map("team")
  @@schema("provider")
}

/// 服务商团队成员关联表，存储团队与成员的多对多关系
model ProviderTeamMember {
  // 主键
  id            BigInt    @id                           /// 关联ID，16位雪花算法，系统自动生成
  
  // 关联字段
  team_id       BigInt                                  /// 团队ID，关联团队表
  user_id       BigInt                                  /// 用户ID，关联系统用户表
  
  // 成员角色
  role          String    @default("member") @db.VarChar(20) /// 成员角色：leader-负责人，member-普通成员
  
  // 个人目标设置
  monthly_target    Decimal?  @db.Decimal(15,2)         /// 个人月度目标金额
  quarterly_target  Decimal?  @db.Decimal(15,2)         /// 个人季度目标金额
  yearly_target     Decimal?  @db.Decimal(15,2)         /// 个人年度目标金额
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  join_time     BigInt                                  /// 加入团队时间戳（毫秒）
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  team          ProviderTeam @relation(fields: [team_id], references: [id])
  
  // 唯一约束
  @@unique([team_id, user_id])
  @@map("team_member")
  @@schema("provider")
}

/// 服务商平台费率表，存储服务商对各个平台渠道的费率设置
model ProviderPlatformRate {
  // 主键
  id            BigInt    @id                           /// 费率记录ID，16位雪花算法，系统自动生成

  // 关联字段
  provider_id   BigInt                                  /// 服务商ID，关联provider_user表
  channel_id    BigInt                                  /// 渠道ID，关联base.channel表

  // 费率信息
  rate          Decimal   @default(0.0000) @db.Decimal(8,4) /// 费率，小数格式，如0.0500表示5%

  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用

  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）

  // 唯一约束：同一服务商同一渠道只能有一条费率记录
  @@unique([provider_id, channel_id], name: "unique_provider_channel_rate")
  @@map("provider_platform_rate")
  @@schema("provider")
}

/// 商品分类表，映射base.goods_categories
model BaseGoodsCategory {
  // 主键
  id            BigInt    @id                           /// 分类ID
  
  // 基本信息
  name          String    @db.VarChar(100)              /// 分类名称
  code          String?   @db.VarChar(50)               /// 分类编码
  description   String?   @db.Text                      /// 分类描述
  parent_id     BigInt?                                 /// 父级分类ID，为空表示顶级分类
  level         Int       @default(1)                   /// 分类层级，1表示一级分类，2表示二级分类，以此类推
  sort_order    Int       @default(0)                   /// 排序序号，值越小越靠前
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID
  updated_by    BigInt?                                 /// 更新者ID
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  spus          BaseGoodsSpu[]                          /// 该分类下的商品
  
  @@map("goods_categories")
  @@schema("base")
}

/// 商品SPU表，映射base.goods_spus
model BaseGoodsSpu {
  // 主键
  id            BigInt    @id                           /// 商品ID
  
  // 基本信息
  name          String    @db.VarChar(200)              /// 商品名称
  code          String?   @db.VarChar(50)               /// 商品编码
  description   String?   @db.Text                      /// 商品描述
  main_image    String?   @db.VarChar(500)              /// 商品主图URL
  images        String?   @db.Text                      /// 商品图片，JSON数组格式
  price         Decimal   @default(0.00) @db.Decimal(15,2) /// 商品价格（参考价）
  category_id   BigInt                                  /// 商品分类ID，关联分类表
  has_sku       Boolean   @default(false)               /// 是否有SKU，true表示有多规格，false表示无规格
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-下架
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID
  updated_by    BigInt?                                 /// 更新者ID
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  category      BaseGoodsCategory @relation(fields: [category_id], references: [id])
  skus          BaseGoodsSku[]                          /// 商品规格列表
  
  @@map("goods_spus")
  @@schema("base")
}

/// 商品SKU表，映射base.goods_skus
model BaseGoodsSku {
  // 主键
  id            BigInt    @id                           /// 规格ID
  
  // 基本信息
  spu_id        BigInt                                  /// 商品ID，关联商品表
  code          String?   @db.VarChar(50)               /// 规格编码
  specification String    @db.VarChar(200)              /// 规格名称，如"红色,XL"
  price         Decimal   @default(0.00) @db.Decimal(15,2) /// 规格价格
  stock         Int       @default(0)                   /// 库存数量
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID
  updated_by    BigInt?                                 /// 更新者ID
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  spu           BaseGoodsSpu @relation(fields: [spu_id], references: [id])
  
  @@map("goods_skus")
  @@schema("base")
}

/**
 * 服务商用户模型 Swagger 定义
 */
module.exports = {
  ProviderUser: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        description: '用户ID，16位雪花算法，系统自动生成'
      },
      username: {
        type: 'string',
        description: '用户名，必填，唯一，用于登录'
      },
      password: {
        type: 'string',
        description: '密码，必填，加密存储'
      },
      phone: {
        type: 'string',
        description: '手机号'
      },
      status: {
        type: 'integer',
        description: '状态：1-正常，0-禁用'
      },
      last_login_time: {
        type: 'integer',
        format: 'int64',
        description: '最后登录时间戳（毫秒）'
      },
      login_count: {
        type: 'integer',
        description: '登录次数'
      },
      created_at: {
        type: 'integer',
        format: 'int64',
        description: '创建时间戳（毫秒）'
      },
      updated_at: {
        type: 'integer',
        format: 'int64',
        description: '更新时间戳（毫秒）'
      },
      created_by: {
        type: 'string',
        description: '创建人ID'
      },
      updated_by: {
        type: 'string',
        description: '更新人ID'
      },
      remark: {
        type: 'string',
        description: '备注信息'
      }
    }
  },
  ProviderUserRegistration: {
    type: 'object',
    required: ['username', 'password', 'phone', 'captcha'],
    properties: {
      username: {
        type: 'string',
        description: '用户名，必填，唯一，用于登录'
      },
      password: {
        type: 'string',
        description: '密码，必填，加密存储'
      },
      phone: {
        type: 'string',
        description: '手机号'
      },
      captcha: {
        type: 'string',
        description: '验证码'
      }
    }
  },
  CaptchaResponse: {
    type: 'object',
    properties: {
      captcha: {
        type: 'string',
        description: '验证码（仅在非生产环境下返回）'
      }
    }
  }
};

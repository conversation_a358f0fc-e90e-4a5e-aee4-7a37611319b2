/**
 * 框架协议管理路由
 */
const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入控制器
const FrameworkController = require('../controllers/FrameworkController');

/**
 * 服务商框架协议路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  console.log('FrameworkRoute initialized with prisma:', !!prisma);

  if (!prisma) {
    throw new Error('Prisma client is required');
  }

  // 创建控制器实例
  const frameworkController = new FrameworkController(prisma);

  /**
   * @swagger
   * /api/v1/provider/framework/create:
   *   post:
   *     tags: [服务商框架协议]
   *     summary: 创建框架协议
   *     description: 创建新的框架协议
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - file_name
   *               - file_url
   *               - user_id
   *             properties:
   *               file_name:
   *                 type: string
   *                 description: 上传的文件名
   *               file_url:
   *                 type: string
   *                 description: 文件存储地址
   *               express_no:
   *                 type: string
   *                 description: 快递单号
   *               express_company:
   *                 type: string
   *                 description: 快递公司
   *               user_id:
   *                 type: string
   *                 description: 关联的用户ID
   *     responses:
   *       200:
   *         description: 创建成功
   *       400:
   *         description: 请求参数错误
   */
  router.post('/create', authMiddleware, frameworkController.create.bind(frameworkController));

  /**
   * @swagger
   * /api/v1/provider/framework/list:
   *   get:
   *     tags: [服务商框架协议]
   *     summary: 获取框架协议列表
   *     description: 获取框架协议列表，支持分页和条件查询
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         description: 每页条数
   *         schema:
   *           type: integer
   *           default: 10
   *       - name: user_id
   *         in: query
   *         description: 用户ID
   *         schema:
   *           type: string
   *       - name: express_no
   *         in: query
   *         description: 快递单号
   *         schema:
   *           type: string
   *       - name: startTime
   *         in: query
   *         description: 开始时间
   *         schema:
   *           type: string
   *       - name: endTime
   *         in: query
   *         description: 结束时间
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   */
  router.get('/list', authMiddleware, frameworkController.list.bind(frameworkController));

  /**
   * @swagger
   * /api/v1/provider/framework/detail/{id}:
   *   get:
   *     tags: [服务商框架协议]
   *     summary: 获取框架协议详情
   *     description: 根据ID获取框架协议详情
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         description: 协议ID
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   */
  router.get('/detail/:id', authMiddleware, frameworkController.getDetail.bind(frameworkController));

  /**
   * @swagger
   * /api/v1/provider/framework/delete/{id}:
   *   delete:
   *     tags: [服务商框架协议]
   *     summary: 删除框架协议
   *     description: 根据ID删除框架协议
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         description: 协议ID
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 删除成功
   *       400:
   *         description: 请求参数错误
   */
  router.delete('/delete/:id', authMiddleware, frameworkController.delete.bind(frameworkController));

  return router;
};

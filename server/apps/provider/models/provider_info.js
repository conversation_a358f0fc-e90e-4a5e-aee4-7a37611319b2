const { Model } = require('@balinjs/core');

class ProviderInfo extends Model {
  static get tableName() {
    return 'provider_info';
  }

  static get schema() {
    return {
      id: { type: 'bigint', primary: true },
      type: { type: 'string', required: true },
      value: { type: 'text', required: true },
      provider_id: { type: 'bigint', required: true },
      created_by: { type: 'bigint' },
      updated_by: { type: 'bigint' },
      created_at: { type: 'integer', required: true },
      updated_at: { type: 'integer' },
      deleted_at: { type: 'integer' }
    };
  }
}

module.exports = ProviderInfo; 
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class TeamService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取团队列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 团队列表和总数
   */
  async list(params = {}) {
    let { page = 1, pageSize = 10, ...filters } = params;
    page = parseInt(page, 10) || 1;
    pageSize = parseInt(pageSize, 10) || 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where = { deleted_at: null };

    // 团队名称模糊查询
    if (filters.name && filters.name.trim() !== '') {
      where.name = { contains: filters.name.trim() };
    }

    // 团队负责人筛选
    if (filters.leader && filters.leader !== '') {
      where.leader_id = BigInt(filters.leader);
    }

    // 状态筛选
    if (filters.status !== undefined && filters.status !== '') {
      where.status = parseInt(filters.status);
    }

    // 时间范围筛选
    if (filters.timeRange && Array.isArray(filters.timeRange) && filters.timeRange.length === 2) {
      const startTime = new Date(filters.timeRange[0]).getTime();
      const endTime = new Date(filters.timeRange[1]).setHours(23, 59, 59, 999);
      
      where.created_at = {
        gte: BigInt(startTime),
        lte: BigInt(endTime)
      };
    }

    // 查询团队列表和总数
    const [total, teams] = await Promise.all([
      this.prisma.providerTeam.count({ where }),
      this.prisma.providerTeam.findMany({
        where,
        skip,
        take: pageSize,
        include: {
          members: {
            where: { deleted_at: null },
            select: {
              id: true,
              user_id: true,
              role: true,
              status: true
            }
          }
        },
        orderBy: { created_at: 'desc' }
      })
    ]);

    // 获取所有相关用户信息
    const userIds = new Set();
    teams.forEach(team => {
      userIds.add(team.leader_id.toString());
      team.members.forEach(member => {
        userIds.add(member.user_id.toString());
      });
    });

    const users = await this.prisma.baseSystemUser.findMany({
      where: {
        id: { in: Array.from(userIds).map(id => BigInt(id)) },
        deleted_at: null
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        dept_id: true
      }
    });

    // 获取部门信息
    const deptIds = users.filter(u => u.dept_id).map(u => u.dept_id);
    const depts = await this.prisma.baseSystemDept.findMany({
      where: {
        id: { in: deptIds },
        deleted_at: null
      },
      select: {
        id: true,
        name: true
      }
    });

    // 构建用户和部门映射
    const userMap = new Map(users.map(u => [u.id.toString(), u]));
    const deptMap = new Map(depts.map(d => [d.id.toString(), d.name]));

    // 格式化返回数据
    const formattedTeams = teams.map(team => {
      const leader = userMap.get(team.leader_id.toString());
      const leaderDeptName = leader?.dept_id ? deptMap.get(leader.dept_id.toString()) : null;

      return {
        id: team.id.toString(),
        name: team.name,
        description: team.description,
        leader_id: team.leader_id.toString(),
        leaderName: leader ? `${leader.nickname || leader.username}` : '未知用户',
        leaderDepartment: leaderDeptName,
        memberCount: team.members.filter(m => m.status === 1).length,
        members: team.members.map(m => m.user_id.toString()),
        monthly_target: team.monthly_target ? parseFloat(team.monthly_target) : null,
        quarterly_target: team.quarterly_target ? parseFloat(team.quarterly_target) : null,
        yearly_target: team.yearly_target ? parseFloat(team.yearly_target) : null,
        status: team.status,
        createTime: new Date(Number(team.created_at)).toLocaleString('zh-CN', { hour12: false }),
        created_at: team.created_at.toString(),
        updated_at: team.updated_at.toString()
      };
    });

    return {
      list: formattedTeams,
      total,
      page,
      pageSize
    };
  }

  /**
   * 创建团队
   * @param {Object} teamData 团队数据
   * @returns {Promise<Object>} 创建的团队信息
   */
  async create(teamData) {
    const currentTime = BigInt(Date.now());
    const teamId = generateSnowflakeId();

    // 检查团队名称是否已存在
    const existingTeam = await this.prisma.providerTeam.findFirst({
      where: {
        name: teamData.name,
        deleted_at: null
      }
    });

    if (existingTeam) {
      throw new Error('团队名称已存在');
    }

    // 验证负责人是否存在
    const leader = await this.prisma.baseSystemUser.findFirst({
      where: {
        id: BigInt(teamData.leader_id),
        deleted_at: null
      }
    });

    if (!leader) {
      throw new Error('团队负责人不存在');
    }

    // 验证成员是否都存在
    const memberIds = teamData.members.map(id => BigInt(id));
    const existingMembers = await this.prisma.baseSystemUser.findMany({
      where: {
        id: { in: memberIds },
        deleted_at: null
      }
    });

    if (existingMembers.length !== memberIds.length) {
      throw new Error('部分团队成员不存在');
    }

    // 使用事务创建团队和成员关系
    return await this.prisma.$transaction(async (tx) => {
      // 创建团队
      const team = await tx.providerTeam.create({
        data: {
          id: teamId,
          name: teamData.name,
          description: teamData.description || null,
          leader_id: BigInt(teamData.leader_id),
          monthly_target: teamData.monthly_target || null,
          quarterly_target: teamData.quarterly_target || null,
          yearly_target: teamData.yearly_target || null,
          status: 1,
          created_by: teamData.created_by ? BigInt(teamData.created_by) : null,
          updated_by: teamData.created_by ? BigInt(teamData.created_by) : null,
          created_at: currentTime,
          updated_at: currentTime
        }
      });

      // 创建团队成员关系
      const memberData = memberIds.map(userId => ({
        id: generateSnowflakeId(),
        team_id: teamId,
        user_id: userId,
        role: userId.toString() === teamData.leader_id ? 'leader' : 'member',
        status: 1,
        join_time: currentTime,
        created_by: teamData.created_by ? BigInt(teamData.created_by) : null,
        updated_by: teamData.created_by ? BigInt(teamData.created_by) : null,
        created_at: currentTime,
        updated_at: currentTime
      }));

      await tx.providerTeamMember.createMany({
        data: memberData
      });

      return {
        id: team.id.toString(),
        name: team.name,
        description: team.description,
        leader_id: team.leader_id.toString(),
        members: memberIds.map(id => id.toString()),
        monthly_target: team.monthly_target ? parseFloat(team.monthly_target) : null,
        quarterly_target: team.quarterly_target ? parseFloat(team.quarterly_target) : null,
        yearly_target: team.yearly_target ? parseFloat(team.yearly_target) : null,
        status: team.status,
        created_at: team.created_at.toString()
      };
    });
  }

  /**
   * 更新团队
   * @param {string} id 团队ID
   * @param {Object} teamData 团队数据
   * @returns {Promise<Object>} 更新的团队信息
   */
  async update(id, teamData) {
    const currentTime = BigInt(Date.now());

    // 检查团队是否存在
    const existingTeam = await this.prisma.providerTeam.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingTeam) {
      throw new Error('团队不存在');
    }

    // 检查团队名称是否已被其他团队使用
    if (teamData.name && teamData.name !== existingTeam.name) {
      const duplicateTeam = await this.prisma.providerTeam.findFirst({
        where: {
          name: teamData.name,
          id: { not: BigInt(id) },
          deleted_at: null
        }
      });

      if (duplicateTeam) {
        throw new Error('团队名称已存在');
      }
    }

    // 验证负责人是否存在
    if (teamData.leader_id) {
      const leader = await this.prisma.baseSystemUser.findFirst({
        where: {
          id: BigInt(teamData.leader_id),
          deleted_at: null
        }
      });

      if (!leader) {
        throw new Error('团队负责人不存在');
      }
    }

    // 验证成员是否都存在
    if (teamData.members && Array.isArray(teamData.members)) {
      const memberIds = teamData.members.map(id => BigInt(id));
      const existingMembers = await this.prisma.baseSystemUser.findMany({
        where: {
          id: { in: memberIds },
          deleted_at: null
        }
      });

      if (existingMembers.length !== memberIds.length) {
        throw new Error('部分团队成员不存在');
      }
    }

    // 使用事务更新团队和成员关系
    return await this.prisma.$transaction(async (tx) => {
      // 更新团队基本信息
      const updateData = {
        updated_at: currentTime,
        updated_by: teamData.updated_by ? BigInt(teamData.updated_by) : null
      };

      if (teamData.name !== undefined) updateData.name = teamData.name;
      if (teamData.description !== undefined) updateData.description = teamData.description;
      if (teamData.leader_id !== undefined) updateData.leader_id = BigInt(teamData.leader_id);
      if (teamData.monthly_target !== undefined) updateData.monthly_target = teamData.monthly_target;
      if (teamData.quarterly_target !== undefined) updateData.quarterly_target = teamData.quarterly_target;
      if (teamData.yearly_target !== undefined) updateData.yearly_target = teamData.yearly_target;

      const team = await tx.providerTeam.update({
        where: { id: BigInt(id) },
        data: updateData
      });

      // 如果更新了成员列表，重新设置成员关系
      if (teamData.members && Array.isArray(teamData.members)) {
        // 软删除现有成员关系
        await tx.providerTeamMember.updateMany({
          where: {
            team_id: BigInt(id),
            deleted_at: null
          },
          data: {
            deleted_at: currentTime,
            updated_at: currentTime,
            updated_by: teamData.updated_by ? BigInt(teamData.updated_by) : null
          }
        });

        // 创建新的成员关系
        const memberIds = teamData.members.map(id => BigInt(id));
        const memberData = memberIds.map(userId => ({
          id: generateSnowflakeId(),
          team_id: BigInt(id),
          user_id: userId,
          role: userId.toString() === (teamData.leader_id || existingTeam.leader_id.toString()) ? 'leader' : 'member',
          status: 1,
          join_time: currentTime,
          created_by: teamData.updated_by ? BigInt(teamData.updated_by) : null,
          updated_by: teamData.updated_by ? BigInt(teamData.updated_by) : null,
          created_at: currentTime,
          updated_at: currentTime
        }));

        await tx.providerTeamMember.createMany({
          data: memberData
        });
      }

      return {
        id: team.id.toString(),
        name: team.name,
        description: team.description,
        leader_id: team.leader_id.toString(),
        monthly_target: team.monthly_target ? parseFloat(team.monthly_target) : null,
        quarterly_target: team.quarterly_target ? parseFloat(team.quarterly_target) : null,
        yearly_target: team.yearly_target ? parseFloat(team.yearly_target) : null,
        status: team.status,
        updated_at: team.updated_at.toString()
      };
    });
  }

  /**
   * 删除团队
   * @param {string} id 团队ID
   * @param {string} updatedBy 操作人ID
   * @returns {Promise<void>}
   */
  async delete(id, updatedBy) {
    const currentTime = BigInt(Date.now());

    // 检查团队是否存在
    const existingTeam = await this.prisma.providerTeam.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingTeam) {
      throw new Error('团队不存在');
    }

    // 使用事务删除团队和成员关系
    await this.prisma.$transaction(async (tx) => {
      // 软删除团队
      await tx.providerTeam.update({
        where: { id: BigInt(id) },
        data: {
          deleted_at: currentTime,
          updated_at: currentTime,
          updated_by: updatedBy ? BigInt(updatedBy) : null
        }
      });

      // 软删除团队成员关系
      await tx.providerTeamMember.updateMany({
        where: {
          team_id: BigInt(id),
          deleted_at: null
        },
        data: {
          deleted_at: currentTime,
          updated_at: currentTime,
          updated_by: updatedBy ? BigInt(updatedBy) : null
        }
      });
    });
  }

  /**
   * 获取团队详情
   * @param {string} id 团队ID
   * @returns {Promise<Object>} 团队详情
   */
  async getById(id) {
    const team = await this.prisma.providerTeam.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      },
      include: {
        members: {
          where: { deleted_at: null },
          select: {
            id: true,
            user_id: true,
            role: true,
            monthly_target: true,
            quarterly_target: true,
            yearly_target: true,
            status: true,
            join_time: true
          }
        }
      }
    });

    if (!team) {
      throw new Error('团队不存在');
    }

    // 获取用户信息
    const userIds = [team.leader_id, ...team.members.map(m => m.user_id)];
    const users = await this.prisma.baseSystemUser.findMany({
      where: {
        id: { in: userIds },
        deleted_at: null
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        dept_id: true
      }
    });

    // 获取部门信息
    const deptIds = users.filter(u => u.dept_id).map(u => u.dept_id);
    const depts = await this.prisma.baseSystemDept.findMany({
      where: {
        id: { in: deptIds },
        deleted_at: null
      },
      select: {
        id: true,
        name: true
      }
    });

    const userMap = new Map(users.map(u => [u.id.toString(), u]));
    const deptMap = new Map(depts.map(d => [d.id.toString(), d.name]));

    const leader = userMap.get(team.leader_id.toString());
    const leaderDeptName = leader?.dept_id ? deptMap.get(leader.dept_id.toString()) : null;

    return {
      id: team.id.toString(),
      name: team.name,
      description: team.description,
      leader_id: team.leader_id.toString(),
      leaderName: leader ? `${leader.nickname || leader.username}` : '未知用户',
      leaderDepartment: leaderDeptName,
      members: team.members.map(member => {
        const user = userMap.get(member.user_id.toString());
        const userDeptName = user?.dept_id ? deptMap.get(user.dept_id.toString()) : null;

        return {
          id: member.id.toString(),
          user_id: member.user_id.toString(),
          name: user ? `${user.nickname || user.username}` : '未知用户',
          department: userDeptName,
          role: member.role,
          monthly_target: member.monthly_target ? parseFloat(member.monthly_target) : null,
          quarterly_target: member.quarterly_target ? parseFloat(member.quarterly_target) : null,
          yearly_target: member.yearly_target ? parseFloat(member.yearly_target) : null,
          status: member.status,
          join_time: new Date(Number(member.join_time)).toLocaleString('zh-CN', { hour12: false })
        };
      }),
      monthly_target: team.monthly_target ? parseFloat(team.monthly_target) : null,
      quarterly_target: team.quarterly_target ? parseFloat(team.quarterly_target) : null,
      yearly_target: team.yearly_target ? parseFloat(team.yearly_target) : null,
      status: team.status,
      createTime: new Date(Number(team.created_at)).toLocaleString('zh-CN', { hour12: false }),
      created_at: team.created_at.toString(),
      updated_at: team.updated_at.toString()
    };
  }
}

module.exports = TeamService;

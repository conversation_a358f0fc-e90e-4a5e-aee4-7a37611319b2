/**
 * 产品分类控制器
 * 处理产品分类相关的请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const ProductCategoryService = require('../services/ProductCategoryService');

/**
 * 产品分类控制器类
 */
class ProductCategoryController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.productCategoryService = new ProductCategoryService(prisma);
  }
  
  /**
   * 从请求中获取用户ID
   * @param {Object} req Express请求对象
   * @returns {string|null} 用户ID或null
   */
  getUserId(req) {
    // 如果请求中有用户信息，则返回用户ID，否则返回null
    return req.user ? req.user.id : null;
  }

  /**
   * 创建产品分类
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async createCategory(req, res) {
    try {
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const data = req.body;
      const result = await this.productCategoryService.createCategory(data, userId);
      
      return this.success(res, result, '创建成功');
    } catch (error) {
      return this.fail(res, `创建失败: ${error.message}`);
    }
  }

  /**
   * 获取产品分类详情
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async getCategoryById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.productCategoryService.getCategoryById(id);
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      return this.fail(res, `获取失败: ${error.message}`);
    }
  }

  /**
   * 获取产品分类列表
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async getList(req, res) {
    try {
      const params = {
        page: req.query.page,
        pageSize: req.query.pageSize,
        name: req.query.name,
        is_enabled: req.query.is_enabled !== undefined ? parseInt(req.query.is_enabled) : undefined,
        parent_id: req.query.parent_id
      };
      
      const result = await this.productCategoryService.getCategories(params);
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      return this.fail(res, `获取失败: ${error.message}`);
    }
  }

  /**
   * 更新产品分类
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async updateCategory(req, res) {
    try {
      const { id } = req.params;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const data = req.body;
      const result = await this.productCategoryService.updateCategory(id, data, userId);
      
      return this.success(res, result, '更新成功');
    } catch (error) {
      return this.fail(res, `更新失败: ${error.message}`);
    }
  }

  /**
   * 删除产品分类
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async deleteCategory(req, res) {
    try {
      const { id } = req.params;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const result = await this.productCategoryService.deleteCategory(id, userId);
      
      return this.success(res, result, '删除成功');
    } catch (error) {
      return this.fail(res, `删除失败: ${error.message}`);
    }
  }

  /**
   * 批量删除产品分类
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async batchDeleteCategories(req, res) {
    try {
      const { ids } = req.body;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const result = await this.productCategoryService.batchDeleteCategories(ids, userId);
      
      return this.success(res, result, '批量删除成功');
    } catch (error) {
      return this.fail(res, `批量删除失败: ${error.message}`);
    }
  }

  /**
   * 切换产品分类状态
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async toggleStatus(req, res) {
    try {
      const { id } = req.params;
      const { is_enabled } = req.body;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const result = await this.productCategoryService.toggleCategoryStatus(id, is_enabled, userId);
      
      return this.success(res, result, '状态更新成功');
    } catch (error) {
      return this.fail(res, `状态更新失败: ${error.message}`);
    }
  }
}

module.exports = ProductCategoryController;

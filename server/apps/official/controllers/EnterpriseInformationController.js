/**
 * 企业信息管理控制器
 * 负责处理企业信息管理相关的HTTP请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const EnterpriseInformationService = require('../services/EnterpriseInformationService');

/**
 * 企业信息管理控制器类
 */
class EnterpriseInformationController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.enterpriseInformationService = new EnterpriseInformationService(prisma);
  }

  /**
   * 获取企业信息列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async list(req, res) {
    try {
      const { page = 1, pageSize = 10 } = this.getPagination(req.query);

      const result = await this.enterpriseInformationService.list({
        page: parseInt(page), 
        pageSize: parseInt(pageSize),
        title: req.query.title,
        status: req.query.status,
        description: req.query.description,
        cate_name: req.query.cate_name
      });
      
      this.successList(res, result.items, result.total, page, pageSize);
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取企业信息详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async detail(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '企业信息ID不能为空', 40001);
      }
      
      const enterpriseInformationDetail = await this.enterpriseInformationService.getById(id);
      
      if (!enterpriseInformationDetail) {
        return this.fail(res, '企业信息不存在或已被删除', 40401);
      }
      
      this.success(res, enterpriseInformationDetail);
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 创建企业信息
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async create(req, res) {
    try {
      const { title, description, content, cate_name, cover_image, sort, status } = req.body;
      
      // 基本字段验证
      if (!title) {
        return this.fail(res, '标题不能为空', 40001);
      }
      
      if (!description) {
        return this.fail(res, '简介不能为空', 40001);
      }

      if (!content) {
        return this.fail(res, '内容不能为空', 40001);
      }

      if (!cate_name) {
        return this.fail(res, '分类名称不能为空', 40001);
      }

      if (!cover_image) {
        return this.fail(res, '首图不能为空', 40001);
      }
      
      // 创建企业信息
      const newEnterpriseInformation = await this.enterpriseInformationService.create({
        title,
        description,
        content,
        cate_name,
        cover_image,
        sort,
        status,
        created_by: req.user?.id || 0,
        updated_by: req.user?.id || 0
      });
      
      this.success(res, newEnterpriseInformation, '企业信息创建成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新企业信息
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const { title, description, content, cate_name, cover_image, status, sort } = req.body;
      
      if (!id) {
        return this.fail(res, '企业信息ID不能为空', 40001);
      }
      
      // 检查企业信息是否存在
      const existingEnterpriseInformation = await this.enterpriseInformationService.getById(id);
      
      if (!existingEnterpriseInformation) {
        return this.fail(res, '企业信息不存在或已被删除', 40401);
      }
      
      // 更新企业信息
      const updatedEnterpriseInformation = await this.enterpriseInformationService.update(id, {
        title,
        description,
        content,
        cate_name,
        cover_image,
        status,
        sort,
        updated_by: req.user?.id || 0,
        updated_at: BigInt(Date.now())
      });
      
      this.success(res, updatedEnterpriseInformation, '企业信息更新成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除企业信息
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '企业信息ID不能为空', 40001);
      }
      
      // 检查企业信息是否存在
      const existingEnterpriseInformation = await this.enterpriseInformationService.getById(id);
      
      if (!existingEnterpriseInformation) {
        return this.fail(res, '企业信息不存在或已被删除', 40401);
      }
      
      // 调用服务层软删除方法，更新deleted_at字段
      await this.enterpriseInformationService.delete(id, req.user?.id);
      
      this.success(res, null, '企业信息删除成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新企业信息状态
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async updateStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;
      
      if (!id) {
        return this.fail(res, '企业信息ID不能为空', 40001);
      }
      
      if (status === undefined || status === null) {
        return this.fail(res, '状态不能为空', 40001);
      }
      
      // 检查企业信息是否存在
      const existingEnterpriseInformation = await this.enterpriseInformationService.getById(id);
      
      if (!existingEnterpriseInformation) {
        return this.fail(res, '企业信息不存在或已被删除', 40401);
      }
      
      // 更新企业信息状态
      const updatedEnterpriseInformation = await this.enterpriseInformationService.updateStatus(id, status, req.user?.id || 0);
      
      this.success(res, updatedEnterpriseInformation, '企业信息状态更新成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = EnterpriseInformationController;

/**
 * 产品路由
 */
const express = require('express');
const mainRouter = express.Router(); // 主路由器对象

// 创建另一个路由器对象用于需要验证的路由
const authRouter = express.Router();
const RouterConfig = require('../../../core/routes/RouterConfig');

// 应用JWT保护到authRouter
RouterConfig.authRoute(authRouter);

// 将这两个路由器对象汇总为一个路由器
const router = express.Router();

/**
 * @swagger
 * /api/v1/official/products:
 *   post:
 *     tags:
 *       - 产品管理
 *     summary: 创建产品
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateProductRequest'
 *     responses:
 *       200:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
authRouter.post('/', (req, res) => req.controllers.ProductController.createProduct(req, res));

/**
 * @swagger
 * /api/v1/official/products/{id}:
 *   get:
 *     summary: 获取产品详情
 *     tags: [产品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 产品ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
mainRouter.get('/:id', (req, res) => req.controllers.ProductController.getProductById(req, res));

/**
 * @swagger
 * /api/v1/official/products:
 *   get:
 *     summary: 获取产品列表
 *     tags: [产品管理]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码，默认1
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: 每页条数，默认10
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 产品名称（模糊搜索）
 *       - in: query
 *         name: description
 *         schema:
 *           type: string
 *         description: 产品描述（模糊搜索）
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: string
 *         description: 产品分类ID
 *       - in: query
 *         name: is_enabled
 *         schema:
 *           type: integer
 *         description: 状态：1-启用，0-禁用
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
mainRouter.get('/', (req, res) => req.controllers.ProductController.getList(req, res));

/**
 * @swagger
 * /api/v1/official/products/{id}:
 *   put:
 *     summary: 更新产品
 *     tags: [产品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 产品ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProductRequest'
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
authRouter.put('/:id', (req, res) => req.controllers.ProductController.updateProduct(req, res));

/**
 * @swagger
 * /api/v1/official/products/{id}:
 *   delete:
 *     summary: 删除产品
 *     tags: [产品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 产品ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
authRouter.delete('/:id', (req, res) => req.controllers.ProductController.deleteProduct(req, res));

/**
 * @swagger
 * /api/v1/official/products/{id}/status:
 *   patch:
 *     summary: 切换产品状态
 *     tags: [产品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 产品ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ToggleProductStatusRequest'
 *     responses:
 *       200:
 *         description: 状态更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
authRouter.patch('/:id/status', (req, res) => req.controllers.ProductController.toggleStatus(req, res));
// 将两个路由器合并到主路由器
router.use('/', mainRouter);
router.use('/', authRouter);
module.exports = router;

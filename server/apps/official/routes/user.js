const express = require('express');
const router = express.Router();
const authMiddleware = require('../../../core/middleware/AuthMiddleware');
const { prisma } = require('../../../core/database/prisma');
const UserController = require('../controllers/UserController');

const userController = new UserController(prisma);

/**
 * @swagger
 * tags:
 *   - name: 官方用户
 *     description: 官方用户相关接口
 */

/**
 * @swagger
 * /api/v1/official/users/test:
 *   get:
 *     summary: 测试接口
 *     tags: [官方用户]
 *     responses:
 *       200:
 *         description: 测试接口成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     time:
 *                       type: string
 *                       example: "2025-05-27 13:46:28"
 *                     module:
 *                       type: string
 *                       example: "official"
 *                     status:
 *                       type: string
 *                       example: "running"
 */
router.get('/test', userController.test.bind(userController));

/**
 * @swagger
 * /api/v1/official/users/profile:
 *   get:
 *     summary: 获取官方用户个人信息
 *     tags: [官方用户]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取用户信息
 */
router.get('/profile', authMiddleware, (req, res) => {
  // 官方用户个人信息接口
});

/**
 * @swagger
 * /api/v1/official/users/login:
 *   post:
 *     summary: 官方用户登录
 *     tags: [官方用户]
 *     responses:
 *       200:
 *         description: 登录成功
 */
router.post('/login', (req, res) => {
  // 官方用户登录接口
});

module.exports = router;

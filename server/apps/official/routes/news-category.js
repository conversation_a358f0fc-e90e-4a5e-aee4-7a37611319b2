/**
 * 新闻分类路由
 * 定义新闻分类相关的API路由
 */
const express = require('express');
const mainRouter = express.Router(); // 主路由器对象

// 创建另一个路由器对象用于需要验证的路由
const authRouter = express.Router();
const RouterConfig = require('../../../core/routes/RouterConfig');

// 应用JWT保护到authRouter
RouterConfig.authRoute(authRouter);

// 将这两个路由器对象汇总为一个路由器
const router = express.Router();

// 将GET请求挂载到主路由器(不需要验证)
/**
 * @swagger
 * /api/v1/official/news-categories:
 *   get:
 *     tags:
 *       - 新闻分类管理
 *     summary: 获取新闻分类列表（分页）
 *     description: 分页获取新闻分类列表，支持根据名称、启用状态等条件筛选
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码，默认为1
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量，默认为10
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: name
 *         in: query
 *         description: 分类名称，支持模糊查询
 *         schema:
 *           type: string
 *       - name: isEnabled
 *         in: query
 *         description: 是否启用，1-启用，0-禁用
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 成功获取分类列表
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NewsCategoryListResponse'
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
mainRouter.get('/', (req, res) => req.controllers.NewsCategoryController.getList(req, res));
/**
 * @swagger
 * /api/v1/official/news-categories/{id}:
 *   get:
 *     tags:
 *       - 新闻分类管理
 *     summary: 获取新闻分类详情
 *     description: 根据ID获取单个新闻分类的详细信息
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 新闻分类ID
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 成功获取分类详情
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NewsCategoryDetailResponse'
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 分类不存在
 *       500:
 *         description: 服务器错误
 */
mainRouter.get('/:id', (req, res) => req.controllers.NewsCategoryController.getDetail(req, res));

// 将POST/PUT/DELETE请求挂载到验证路由器
/**
 * @swagger
 * /api/v1/official/news-categories:
 *   post:
 *     tags:
 *       - 新闻分类管理
 *     summary: 创建新闻分类
 *     security:
 *       - bearerAuth: []
 *     description: 创建一个新的新闻分类
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/NewsCategoryCreateRequest'
 *     responses:
 *       200:
 *         description: 成功创建分类
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NewsCategoryResponse'
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
authRouter.post('/', (req, res) => req.controllers.NewsCategoryController.create(req, res));
/**
 * @swagger
 * /api/v1/official/news-categories/{id}:
 *   put:
 *     tags:
 *       - 新闻分类管理
 *     summary: 更新新闻分类
 *     security:
 *       - bearerAuth: []
 *     description: 根据ID更新新闻分类信息
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 新闻分类ID
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/NewsCategoryUpdateRequest'
 *     responses:
 *       200:
 *         description: 成功更新分类
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NewsCategoryResponse'
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分类不存在
 *       500:
 *         description: 服务器错误
 */
authRouter.put('/:id', (req, res) => req.controllers.NewsCategoryController.update(req, res));
/**
 * @swagger
 * /api/v1/official/news-categories/{id}:
 *   delete:
 *     tags:
 *       - 新闻分类管理
 *     summary: 删除新闻分类
 *     security:
 *       - bearerAuth: []
 *     description: 根据ID删除新闻分类（软删除）
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 新闻分类ID
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 成功删除分类
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分类不存在
 *       500:
 *         description: 服务器错误
 */
authRouter.delete('/:id', (req, res) => req.controllers.NewsCategoryController.delete(req, res));

/**
 * @swagger
 * /api/v1/official/news-categories/{id}/status:
 *   put:
 *     tags:
 *       - 新闻分类管理
 *     summary: 切换新闻分类启用状态
 *     security:
 *       - bearerAuth: []
 *     description: 切换新闻分类的启用/禁用状态
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 新闻分类ID
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isEnabled:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: 启用状态，1-启用，0-禁用
 *             required:
 *               - isEnabled
 *     responses:
 *       200:
 *         description: 成功更新状态
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分类不存在
 *       500:
 *         description: 服务器错误
 */
authRouter.put('/:id/status', (req, res) => req.controllers.NewsCategoryController.toggleStatus(req, res));

// 将两个路由器合并到主路由器
router.use('/', mainRouter);
router.use('/', authRouter);

module.exports = router;

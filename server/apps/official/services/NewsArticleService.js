/**
 * 新闻文章服务
 * 负责新闻文章业务逻辑处理
 */
const NewsArticleModel = require('../models/NewsArticleModel');

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (!data) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && data !== null) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];
        if (typeof value === 'bigint') {
          result[key] = value.toString();
        } else if (typeof value === 'object' && value !== null) {
          result[key] = handleBigInt(value);
        } else {
          result[key] = value;
        }
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 新闻文章服务类
 */
class NewsArticleService {
  constructor(prisma) {
    this.prisma = prisma;
    this.newsArticleModel = new NewsArticleModel(prisma);
  }

  /**
   * 创建新闻文章
   * @param {Object} data 文章数据
   * @param {number} userId 创建者ID
   * @returns {Promise<Object>} 仅返回id
   */
  async createArticle(data, userId) {
    try {
      const result = await this.newsArticleModel.create(data, userId);
      // 将BigInt转换为字符串以避免JSON序列化问题
      const id = typeof result.id === 'bigint' ? result.id.toString() : result.id;
      return { id };
    } catch (error) {
      throw new Error(`创建新闻文章失败: ${error.message}`);
    }
  }

  /**
   * 获取单个新闻文章详情
   * @param {number} id 文章ID
   * @returns {Promise<Object>} 文章信息
   */
  async getArticleById(id) {
    try {
      const article = await this.newsArticleModel.findById(id);
      if (!article) {
        throw new Error('文章不存在');
      }
      return handleBigInt(article);
    } catch (error) {
      throw new Error(`获取新闻文章详情失败: ${error.message}`);
    }
  }

  /**
   * 获取所有新闻文章列表（支持分页、筛选）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 文章分页数据
   */
  async getArticles(params) {
    try {
      const result = await this.newsArticleModel.findAll(params);
      return {
        items: handleBigInt(result.list),
        pageInfo: result.pagination
      };
    } catch (error) {
      throw new Error(`获取新闻文章列表失败: ${error.message}`);
    }
  }

  /**
   * 更新新闻文章
   * @param {number} id 文章ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新人ID
   * @returns {Promise<Object>} 仅返回id
   */
  async updateArticle(id, data, userId) {
    try {
      // 检查文章是否存在
      const article = await this.newsArticleModel.findById(id);
      if (!article) {
        throw new Error('文章不存在');
      }
      
      await this.newsArticleModel.update(id, data, userId);
      // 将id转换为字符串以避免JSON序列化问题
      const idStr = typeof id === 'bigint' ? id.toString() : id;
      return { id: idStr };
    } catch (error) {
      throw new Error(`更新新闻文章失败: ${error.message}`);
    }
  }

  /**
   * 删除新闻文章
   * @param {number} id 文章ID
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 仅返回id
   */
  async deleteArticle(id, userId) {
    try {
      await this.newsArticleModel.delete(id, userId);
      // 将id转换为字符串以避免JSON序列化问题
      const idStr = typeof id === 'bigint' ? id.toString() : id;
      return { id: idStr };
    } catch (error) {
      throw new Error(`删除新闻文章失败: ${error.message}`);
    }
  }

  /**
   * 批量删除新闻文章
   * @param {Array<number>} ids 文章ID数组
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 仅返回ids
   */
  async batchDeleteArticles(ids, userId) {
    try {
      await this.newsArticleModel.batchDelete(ids, userId);
      // 将ids中的每个BigInt转换为字符串
      const idsStr = ids.map(id => typeof id === 'bigint' ? id.toString() : id);
      return { ids: idsStr };
    } catch (error) {
      throw new Error(`批量删除新闻文章失败: ${error.message}`);
    }
  }

  /**
   * 切换新闻文章启用状态
   * @param {number} id 文章ID
   * @param {number} isEnabled 启用状态：1-启用，0-禁用
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 仅返回id
   */
  async toggleArticleStatus(id, isEnabled, userId) {
    try {
      await this.newsArticleModel.toggleStatus(id, isEnabled, userId);
      // 将id转换为字符串以避免JSON序列化问题
      const idStr = typeof id === 'bigint' ? id.toString() : id;
      return { id: idStr };
    } catch (error) {
      throw new Error(`切换新闻文章状态失败: ${error.message}`);
    }
  }
}

module.exports = NewsArticleService;

/**
 * 公司基础信息服务
 * 负责公司基础信息业务逻辑处理
 */
const CompanyInfoModel = require('../models/CompanyInfoModel');

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (!data) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && data !== null) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];
        if (typeof value === 'bigint') {
          result[key] = value.toString();
        } else if (typeof value === 'object' && value !== null) {
          result[key] = handleBigInt(value);
        } else {
          result[key] = value;
        }
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 公司基础信息服务类
 */
class CompanyInfoService {
  constructor(prisma) {
    this.prisma = prisma;
    this.companyInfoModel = new CompanyInfoModel(prisma);
  }

  /**
   * 创建或更新公司基础信息
   * @param {Object} data 公司信息数据
   * @param {number} userId 创建/更新者ID
   * @returns {Promise<Object>} 处理结果
   */
  async saveCompanyInfo(data, userId) {
    try {
      const result = await this.companyInfoModel.createOrUpdate(data, userId);
      return handleBigInt(result);
    } catch (error) {
      throw new Error(`保存公司基础信息失败: ${error.message}`);
    }
  }

  /**
   * 获取公司基础信息
   * @returns {Promise<Object>} 公司信息
   */
  async getCompanyInfo() {
    try {
      const info = await this.companyInfoModel.findFirst();
      if (!info) {
        return null;
      }
      return handleBigInt(info);
    } catch (error) {
      throw new Error(`获取公司基础信息失败: ${error.message}`);
    }
  }
  
  /**
   * 获取公司信息列表（支持分页、筛选）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 公司信息列表数据
   */
  async getCompanyInfoList(params) {
    try {
      // 直接返回公司信息（由于公司信息一般只有一条记录，这里直接调用getCompanyInfo）
      const info = await this.getCompanyInfo();
      
      // 将单条记录包装为列表格式返回
      const list = info ? [info] : [];
      
      return {
        items: list,
        pageInfo: {
          current: 1,
          pageSize: 10,
          total: list.length
        }
      };
    } catch (error) {
      throw new Error(`获取公司信息列表失败: ${error.message}`);
    }
  }
  
  /**
   * 根据ID获取公司信息详情
   * @param {number} id 公司信息ID
   * @returns {Promise<Object>} 公司信息详情
   */
  async getCompanyInfoById(id) {
    try {
      // 公司信息一般只有一条记录，直接调用getCompanyInfo获取
      const info = await this.getCompanyInfo();
      
      if (!info) {
        throw new Error('公司信息不存在');
      }
      
      // 如果该条记录的ID与请求的ID不一致，则返回不存在
      if (info.id && info.id.toString() !== id.toString()) {
        throw new Error('该ID的公司信息不存在');
      }
      
      return info;
    } catch (error) {
      throw new Error(`获取公司信息详情失败: ${error.message}`);
    }
  }
  
  /**
   * 更新指定ID的公司信息
   * @param {number} id 公司信息ID
   * @param {Object} data 要更新的公司信息数据
   * @param {number} userId 操作用户ID
   * @returns {Promise<Object>} 只返回更新后的公司信息ID
   */
  async updateCompanyInfoById(id, data, userId) {
    try {
      // 首先验证该ID的公司信息是否存在
      const info = await this.getCompanyInfoById(id);
      
      // 存在则直接调用saveCompanyInfo方法进行更新
      // 注意保留原有ID，确保更新而非新建
      const updateData = {
        ...data,
        id: info.id
      };
      
      await this.saveCompanyInfo(updateData, userId);
      // 只返回id信息
      return { id: info.id.toString() };
    } catch (error) {
      throw new Error(`更新公司信息失败: ${error.message}`);
    }
  }
}

module.exports = CompanyInfoService;

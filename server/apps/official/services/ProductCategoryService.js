/**
 * 产品分类服务
 * 负责产品分类业务逻辑处理
 */
const ProductCategoryModel = require('../models/ProductCategoryModel');

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (!data) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && data !== null) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];
        if (typeof value === 'bigint') {
          result[key] = value.toString();
        } else if (typeof value === 'object' && value !== null) {
          result[key] = handleBigInt(value);
        } else {
          result[key] = value;
        }
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 产品分类服务类
 */
class ProductCategoryService {
  constructor(prisma) {
    this.prisma = prisma;
    this.productCategoryModel = new ProductCategoryModel(prisma);
  }

  /**
   * 创建产品分类
   * @param {Object} data 分类数据
   * @param {number} userId 创建者ID
   * @returns {Promise<Object>} 仅返回id
   */
  async createCategory(data, userId) {
    try {
      const result = await this.productCategoryModel.create(data, userId);
      // 将BigInt转换为字符串以避免JSON序列化问题
      const id = typeof result.id === 'bigint' ? result.id.toString() : result.id;
      return { id };
    } catch (error) {
      throw new Error(`创建产品分类失败: ${error.message}`);
    }
  }

  /**
   * 获取单个产品分类详情
   * @param {number} id 分类ID
   * @returns {Promise<Object>} 分类信息
   */
  async getCategoryById(id) {
    try {
      const category = await this.productCategoryModel.findById(id);
      if (!category) {
        throw new Error('分类不存在');
      }
      return handleBigInt(category);
    } catch (error) {
      throw new Error(`获取产品分类详情失败: ${error.message}`);
    }
  }

  /**
   * 获取产品分类列表（支持分页、筛选、树形结构）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分类分页数据
   */
  async getCategories(params) {
    try {
      const result = await this.productCategoryModel.findAll(params);
      return {
        items: handleBigInt(result.list),
        pageInfo: result.pagination
      };
    } catch (error) {
      throw new Error(`获取产品分类列表失败: ${error.message}`);
    }
  }

  /**
   * 更新产品分类
   * @param {number} id 分类ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新人ID
   * @returns {Promise<Object>} 仅返回id
   */
  async updateCategory(id, data, userId) {
    try {
      // 检查分类是否存在
      const category = await this.productCategoryModel.findById(id);
      if (!category) {
        throw new Error('分类不存在');
      }
      
      // 检查是否形成循环引用
      if (data.parent_id && data.parent_id.toString() === id.toString()) {
        throw new Error('不能将分类的父级设置为自身');
      }
      
      await this.productCategoryModel.update(id, data, userId);
      // 将id转换为字符串以避免JSON序列化问题
      const idStr = typeof id === 'bigint' ? id.toString() : id;
      return { id: idStr };
    } catch (error) {
      throw new Error(`更新产品分类失败: ${error.message}`);
    }
  }

  /**
   * 删除产品分类
   * @param {number} id 分类ID
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 仅返回id
   */
  async deleteCategory(id, userId) {
    try {
      await this.productCategoryModel.delete(id, userId);
      // 将id转换为字符串以避免JSON序列化问题
      const idStr = typeof id === 'bigint' ? id.toString() : id;
      return { id: idStr };
    } catch (error) {
      throw new Error(`删除产品分类失败: ${error.message}`);
    }
  }

  /**
   * 批量删除产品分类
   * @param {Array<number>} ids 分类ID数组
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 仅返回ids
   */
  async batchDeleteCategories(ids, userId) {
    try {
      await this.productCategoryModel.batchDelete(ids, userId);
      // 将ids中的每个BigInt转换为字符串
      const idsStr = ids.map(id => typeof id === 'bigint' ? id.toString() : id);
      return { ids: idsStr };
    } catch (error) {
      throw new Error(`批量删除产品分类失败: ${error.message}`);
    }
  }

  /**
   * 切换产品分类启用状态
   * @param {number} id 分类ID
   * @param {number} isEnabled 启用状态：1-启用，0-禁用
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 仅返回id
   */
  async toggleCategoryStatus(id, isEnabled, userId) {
    try {
      await this.productCategoryModel.toggleStatus(id, isEnabled, userId);
      // 将id转换为字符串以避免JSON序列化问题
      const idStr = typeof id === 'bigint' ? id.toString() : id;
      return { id: idStr };
    } catch (error) {
      throw new Error(`切换产品分类状态失败: ${error.message}`);
    }
  }
}

module.exports = ProductCategoryService;

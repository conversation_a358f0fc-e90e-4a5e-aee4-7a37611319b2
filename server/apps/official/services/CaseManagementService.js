/**
 * 案例管理服务
 * 负责处理案例管理相关的业务逻辑
 */
const { PrismaClient } = require('@prisma/client');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 案例管理服务类
 */
class CaseManagementService {
  /**
   * 构造函数
   * @param {PrismaClient} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取案例列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 案例列表和总数
   */
  async list(params) {
    const { page = 1, pageSize = 10, title, status, description } = params;
    
    // 构建查询条件
    const where = {
      deleted_at: null // 只查询未删除的数据
    };
    
    if (title) {
      where.title = {
        contains: title,
        mode: 'insensitive'
      };
    }
    
    if (status !== undefined && status !== null && status !== '') {
      where.status = parseInt(status);
    }

    if (description) {
      where.description = {
        contains: description,
        mode: 'insensitive'
      };
    }
    
    // 查询总数
    const total = await this.prisma.Case.count({ where });
    
    // 查询列表
    const items = await this.prisma.Case.findMany({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: {
        sort: 'asc'
      },
      select: {
        id: true,
        title: true,
        description: true,
        icon: true,
        content: true,
        cover_image: true,
        status: true,
        view_count: true,
        created_at: true,
        updated_at: true,
        sort: true
      }
    });
    
    // 处理数据
    const formattedItems = items.map(item => ({
      ...item,
      id: item.id.toString(),
      created_at: item.created_at.toString(),
      updated_at: item.updated_at ? item.updated_at.toString() : null
    }));
    
    return {
      items: formattedItems,
      total
    };
  }

  /**
   * 根据ID获取案例详情
   * @param {string|number} id 案例ID
   * @returns {Promise<Object>} 案例详情
   */
  async getById(id) {
    const caseDetail = await this.prisma.Case.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null // 只查询未删除的数据
      }
    });
    
    if (!caseDetail) {
      return null;
    }
    
    // 更新浏览次数
    await this.prisma.Case.update({
      where: {
        id: BigInt(id)
      },
      data: {
        view_count: {
          increment: 1
        }
      }
    });
    
    // 格式化数据
    return {
      ...caseDetail,
      id: caseDetail.id.toString(),
      created_at: caseDetail.created_at.toString(),
      updated_at: caseDetail.updated_at ? caseDetail.updated_at.toString() : null,
      created_by: caseDetail.created_by ? caseDetail.created_by.toString() : null,
      updated_by: caseDetail.updated_by ? caseDetail.updated_by.toString() : null
    };
  }

  /**
   * 创建案例
   * @param {Object} data 案例数据
   * @returns {Promise<Object>} 创建的案例
   */
  async create(data) {
    const id = generateSnowflakeId();
    
    const newCase = await this.prisma.Case.create({
      data: {
        id: BigInt(id),
        title: data.title,
        description: data.description || '',
        content: data.content,
        icon: data.icon || '',
        cover_image: data.cover_image || '',
        status: 1, // 默认为已发布状态
        view_count: 0,
        sort: data.sort || 0,
        created_at: BigInt(Date.now()),
        created_by: data.created_by ? BigInt(data.created_by) : null,
        updated_at: BigInt(Date.now()),
        updated_by: data.updated_by ? BigInt(data.updated_by) : null,
        deleted_at: null
      }
    });
    
    // 格式化数据
    return {
      ...newCase,
      id: newCase.id.toString(),
      created_at: newCase.created_at.toString(),
      updated_at: newCase.updated_at ? newCase.updated_at.toString() : null,
      created_by: newCase.created_by ? newCase.created_by.toString() : null,
      updated_by: newCase.updated_by ? newCase.updated_by.toString() : null
    };
  }

  /**
   * 更新案例
   * @param {string|number} id 案例ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的案例
   */
  async update(id, data) {
    const updateData = {};
    
    // 只更新有值的字段
    if (data.title !== undefined) updateData.title = data.title;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.content !== undefined) updateData.content = data.content;
    if (data.icon !== undefined) updateData.icon = data.icon;
    if (data.cover_image !== undefined) updateData.cover_image = data.cover_image;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.sort !== undefined) updateData.sort = data.sort;
    
    // 更新时间和操作人
    updateData.updated_at = BigInt(Date.now());
    if (data.updated_by) updateData.updated_by = BigInt(data.updated_by);
    
    const updatedCase = await this.prisma.Case.update({
      where: {
        id: BigInt(id)
      },
      data: updateData
    });
    
    // 格式化数据
    return {
      ...updatedCase,
      id: updatedCase.id.toString(),
      created_at: updatedCase.created_at.toString(),
      updated_at: updatedCase.updated_at ? updatedCase.updated_at.toString() : null,
      created_by: updatedCase.created_by ? updatedCase.created_by.toString() : null,
      updated_by: updatedCase.updated_by ? updatedCase.updated_by.toString() : null
    };
  }

  /**
   * 删除案例（软删除）
   * @param {string|number} id 案例ID
   * @param {string|number} userId 操作人用户ID（可选）
   * @returns {Promise<boolean>} 是否删除成功
   */
  async delete(id, userId = null) {
    // 使用软删除，更新deleted_at字段
    await this.prisma.Case.update({
      where: {
        id: BigInt(id)
      },
      data: {
        deleted_at: BigInt(Date.now()),
        updated_at: BigInt(Date.now()),
        updated_by: userId ? BigInt(userId) : null
      }
    });
    
    return true;
  }

  /**
   * 更新案例状态
   * @param {string|number} id 案例ID
   * @param {number} status 状态值
   * @param {string|number} updatedBy 更新人ID
   * @returns {Promise<Object>} 更新后的案例
   */
  async updateStatus(id, status, updatedBy) {
    const updatedCase = await this.prisma.Case.update({
      where: {
        id: BigInt(id)
      },
      data: {
        status: status,
        updated_at: BigInt(Date.now()),
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });
    
    // 格式化数据
    return {
      ...updatedCase,
      id: updatedCase.id.toString(),
      created_at: updatedCase.created_at.toString(),
      updated_at: updatedCase.updated_at ? updatedCase.updated_at.toString() : null,
      created_by: updatedCase.created_by ? updatedCase.created_by.toString() : null,
      updated_by: updatedCase.updated_by ? updatedCase.updated_by.toString() : null
    };
  }
}

module.exports = CaseManagementService;

/**
 * 招聘信息管理服务
 * 负责处理招聘信息管理相关的业务逻辑
 */
const { PrismaClient } = require('@prisma/client');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 招聘信息管理服务类
 */
class RecruitmentService {
  /**
   * 构造函数
   * @param {PrismaClient} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取招聘信息列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 招聘信息列表和总数
   */
  async list(params) {
    const { page = 1, pageSize = 10, title, status, recruitment_type, position_name, position_address } = params;
    
    // 构建查询条件
    const where = {
      deleted_at: null // 只查询未删除的数据
    };
    
    if (title) {
      where.title = {
        contains: title,
        mode: 'insensitive'
      };
    }
    
    if (position_name) {
      where.position_name = {
        contains: position_name,
        mode: 'insensitive'
      };
    }
    
    if (position_address) {
      where.position_address = {
        contains: position_address,
        mode: 'insensitive'
      };
    }
    
    if (status !== undefined && status !== null && status !== '') {
      where.status = parseInt(status);
    }

    if (recruitment_type !== undefined && recruitment_type !== null && recruitment_type !== '') {
      where.recruitment_type = parseInt(recruitment_type);
    }
    
    // 查询总数
    const total = await this.prisma.Recruitment.count({ where });
    
    // 查询列表
    const items = await this.prisma.Recruitment.findMany({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: [
        {
          sort: 'asc'
        }
      ],
      select: {
        id: true,
        title: true,
        recruitment_type: true,
        position_name: true,
        position_address: true,
        position_duty: true,
        position_requirement: true,
        position_details: true,
        publish_time: true,
        status: true,
        sort: true,
        created_at: true,
        updated_at: true
      }
    });
    
    // 处理数据
    const formattedItems = items.map(item => ({
      ...item,
      id: item.id.toString(),
      created_at: item.created_at.toString(),
      updated_at: item.updated_at ? item.updated_at.toString() : null,
      publish_time: item.publish_time ? item.publish_time.toString() : null
    }));
    
    return {
      items: formattedItems,
      total
    };
  }

  /**
   * 根据ID获取招聘信息详情
   * @param {string|number} id 招聘信息ID
   * @returns {Promise<Object>} 招聘信息详情
   */
  async getById(id) {
    const recruitmentDetail = await this.prisma.Recruitment.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null // 只查询未删除的数据
      }
    });
    
    if (!recruitmentDetail) {
      return null;
    }
    
    // 格式化数据
    return {
      ...recruitmentDetail,
      id: recruitmentDetail.id.toString(),
      created_at: recruitmentDetail.created_at.toString(),
      updated_at: recruitmentDetail.updated_at ? recruitmentDetail.updated_at.toString() : null,
      created_by: recruitmentDetail.created_by ? recruitmentDetail.created_by.toString() : null,
      updated_by: recruitmentDetail.updated_by ? recruitmentDetail.updated_by.toString() : null,
      publish_time: recruitmentDetail.publish_time ? recruitmentDetail.publish_time.toString() : null
    };
  }

  /**
   * 创建招聘信息
   * @param {Object} data 招聘信息数据
   * @returns {Promise<Object>} 创建的招聘信息
   */
  async create(data) {
    const id = generateSnowflakeId();
    
    const newRecruitment = await this.prisma.Recruitment.create({
      data: {
        id: BigInt(id),
        title: data.title,
        recruitment_type: data.recruitment_type || 1,
        position_name: data.position_name || '',
        position_address: data.position_address || '',
        position_duty: data.position_duty || '',
        position_requirement: data.position_requirement || '',
        position_details: data.position_details || '',
        publish_time: data.publish_time ? BigInt(data.publish_time) : null,
        status: data.status !== undefined ? data.status : 1, // 默认为显示状态
        sort: data.sort || 0,
        created_at: BigInt(Date.now()),
        created_by: data.created_by ? BigInt(data.created_by) : null,
        updated_at: BigInt(Date.now()),
        updated_by: data.updated_by ? BigInt(data.updated_by) : null,
        deleted_at: null
      }
    });
    
    // 格式化数据
    return {
      ...newRecruitment,
      id: newRecruitment.id.toString(),
      created_at: newRecruitment.created_at.toString(),
      updated_at: newRecruitment.updated_at ? newRecruitment.updated_at.toString() : null,
      created_by: newRecruitment.created_by ? newRecruitment.created_by.toString() : null,
      updated_by: newRecruitment.updated_by ? newRecruitment.updated_by.toString() : null,
      publish_time: newRecruitment.publish_time ? newRecruitment.publish_time.toString() : null
    };
  }

  /**
   * 更新招聘信息
   * @param {string|number} id 招聘信息ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的招聘信息
   */
  async update(id, data) {
    const updateData = {};
    
    // 只更新有值的字段
    if (data.title !== undefined) updateData.title = data.title;
    if (data.recruitment_type !== undefined) updateData.recruitment_type = data.recruitment_type;
    if (data.position_name !== undefined) updateData.position_name = data.position_name;
    if (data.position_address !== undefined) updateData.position_address = data.position_address;
    if (data.position_duty !== undefined) updateData.position_duty = data.position_duty;
    if (data.position_requirement !== undefined) updateData.position_requirement = data.position_requirement;
    if (data.position_details !== undefined) updateData.position_details = data.position_details;
    if (data.publish_time !== undefined) updateData.publish_time = data.publish_time ? BigInt(data.publish_time) : null;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.sort !== undefined) updateData.sort = data.sort;
    
    // 更新时间和操作人
    updateData.updated_at = BigInt(Date.now());
    if (data.updated_by) updateData.updated_by = BigInt(data.updated_by);
    
    const updatedRecruitment = await this.prisma.Recruitment.update({
      where: {
        id: BigInt(id)
      },
      data: updateData
    });
    
    // 格式化数据
    return {
      ...updatedRecruitment,
      id: updatedRecruitment.id.toString(),
      created_at: updatedRecruitment.created_at.toString(),
      updated_at: updatedRecruitment.updated_at ? updatedRecruitment.updated_at.toString() : null,
      created_by: updatedRecruitment.created_by ? updatedRecruitment.created_by.toString() : null,
      updated_by: updatedRecruitment.updated_by ? updatedRecruitment.updated_by.toString() : null,
      publish_time: updatedRecruitment.publish_time ? updatedRecruitment.publish_time.toString() : null
    };
  }

  /**
   * 删除招聘信息（软删除）
   * @param {string|number} id 招聘信息ID
   * @param {string|number} userId 操作人用户ID（可选）
   * @returns {Promise<boolean>} 是否删除成功
   */
  async delete(id, userId = null) {
    // 使用软删除，更新deleted_at字段
    await this.prisma.Recruitment.update({
      where: {
        id: BigInt(id)
      },
      data: {
        deleted_at: BigInt(Date.now()),
        updated_at: BigInt(Date.now()),
        updated_by: userId ? BigInt(userId) : null
      }
    });
    
    return true;
  }

  /**
   * 更新招聘信息状态
   * @param {string|number} id 招聘信息ID
   * @param {number} status 状态值
   * @param {string|number} updatedBy 更新人ID
   * @returns {Promise<Object>} 更新后的招聘信息
   */
  async updateStatus(id, status, updatedBy) {
    const updatedRecruitment = await this.prisma.Recruitment.update({
      where: {
        id: BigInt(id)
      },
      data: {
        status: status,
        updated_at: BigInt(Date.now()),
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });
    
    // 格式化数据
    return {
      ...updatedRecruitment,
      id: updatedRecruitment.id.toString(),
      created_at: updatedRecruitment.created_at.toString(),
      updated_at: updatedRecruitment.updated_at ? updatedRecruitment.updated_at.toString() : null,
      created_by: updatedRecruitment.created_by ? updatedRecruitment.created_by.toString() : null,
      updated_by: updatedRecruitment.updated_by ? updatedRecruitment.updated_by.toString() : null,
      publish_time: updatedRecruitment.publish_time ? updatedRecruitment.publish_time.toString() : null
    };
  }
}

module.exports = RecruitmentService;

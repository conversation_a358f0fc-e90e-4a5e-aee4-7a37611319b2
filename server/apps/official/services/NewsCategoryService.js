/**
 * 新闻分类服务
 * 负责新闻分类业务逻辑处理
 */
const NewsCategoryModel = require('../models/NewsCategoryModel');

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = handleBigInt(data[key]);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 新闻分类服务类
 */
class NewsCategoryService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
    this.newsCategoryModel = new NewsCategoryModel(prisma);
  }

  /**
   * 创建新闻分类
   * @param {Object} data 分类数据
   * @param {number} userId 创建者ID
   * @returns {Promise<Object>} 创建结果
   */
  async createCategory(data, userId) {
    try {
      // 检查父分类是否存在
      if (data.news_parent_category_id) {
        const parentCategory = await this.newsCategoryModel.findById(data.news_parent_category_id);
        if (!parentCategory) {
          throw new Error('父分类不存在');
        }
        
        // 设置层级
        data.level = (parentCategory.level || 0) + 1;
      } else {
        // 顶级分类
        data.level = 1;
      }

      const result = await this.newsCategoryModel.create(data, userId);
      return handleBigInt(result);
    } catch (error) {
      throw new Error(`创建新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 获取单个新闻分类详情
   * @param {number} id 分类ID
   * @returns {Promise<Object>} 分类信息
   */
  async getCategoryById(id) {
    try {
      const category = await this.newsCategoryModel.findById(id);
      if (!category) {
        throw new Error('新闻分类不存在');
      }
      return handleBigInt(category);
    } catch (error) {
      throw new Error(`获取新闻分类详情失败: ${error.message}`);
    }
  }

  /**
   * 获取所有新闻分类列表（支持分页、筛选）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分类分页数据
   */
  async getCategories(params) {
    try {
      const result = await this.newsCategoryModel.findAll(params);
      return {
        items: handleBigInt(result.list),
        pageInfo: result.pagination
      };
    } catch (error) {
      throw new Error(`获取新闻分类列表失败: ${error.message}`);
    }
  }

  /**
   * 获取新闻分类树形结构
   * @returns {Promise<Array>} 分类树形结构
   */
  async getCategoryTree() {
    try {
      const categories = await this.newsCategoryModel.getTree();
      const processedCategories = handleBigInt(categories);
      
      // 构建树形结构
      const categoryMap = {};
      const rootCategories = [];
      
      // 先将所有分类放入map
      processedCategories.forEach(category => {
        categoryMap[category.id] = {
          ...category,
          children: []
        };
      });
      
      // 构建父子关系
      processedCategories.forEach(category => {
        const processedCategory = categoryMap[category.id];
        
        if (category.news_parent_category_id) {
          // 有父分类，加入到父分类的children
          const parent = categoryMap[category.news_parent_category_id];
          if (parent) {
            parent.children.push(processedCategory);
          } else {
            // 父分类不存在，作为根节点
            rootCategories.push(processedCategory);
          }
        } else {
          // 没有父分类，作为根节点
          rootCategories.push(processedCategory);
        }
      });
      
      return rootCategories;
    } catch (error) {
      throw new Error(`获取新闻分类树失败: ${error.message}`);
    }
  }

  /**
   * 更新新闻分类
   * @param {number} id 分类ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新人ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateCategory(id, data, userId) {
    try {
      // 检查分类是否存在
      const existingCategory = await this.newsCategoryModel.findById(id);
      if (!existingCategory) {
        throw new Error('新闻分类不存在');
      }
      
      // 检查是否修改了父分类
      if (data.news_parent_category_id !== undefined) {
        // 不能将分类设为自己的子分类
        if (data.news_parent_category_id === id) {
          throw new Error('不能将分类设为自己的子分类');
        }
        
        // 检查父分类是否存在
        if (data.news_parent_category_id) {
          const parentCategory = await this.newsCategoryModel.findById(data.news_parent_category_id);
          if (!parentCategory) {
            throw new Error('父分类不存在');
          }
          
          // 设置层级
          data.level = (parentCategory.level || 0) + 1;
        } else {
          // 顶级分类
          data.level = 1;
        }
      }

      const result = await this.newsCategoryModel.update(id, data, userId);
      return handleBigInt(result);
    } catch (error) {
      throw new Error(`更新新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 删除新闻分类
   * @param {number} id 分类ID
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteCategory(id, userId) {
    try {
      // 当前模型中没有父子关系字段，如果后续需要实现父子关系检查，
      // 需要先在数据库和Prisma模型中添加parent_id字段
      
      // 检查是否有关联的新闻
      // 注意：此处假设有新闻表，实际项目中需根据实际表结构调整
      /*
      const relatedNews = await this.prisma.news.findMany({
        where: {
          news_category_id: BigInt(id),
          deleted_at: null
        }
      });
      
      if (relatedNews.length > 0) {
        throw new Error('该分类下有新闻内容，不能直接删除');
      }
      */

      const result = await this.newsCategoryModel.delete(id, userId);
      return handleBigInt(result);
    } catch (error) {
      throw new Error(`删除新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 批量删除新闻分类
   * @param {Array<number>} ids 分类ID数组
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 删除结果
   */
  async batchDeleteCategories(ids, userId) {
    try {
      // 检查是否有子分类
      const childCategories = await this.prisma.news_categories.findMany({
        where: {
          news_parent_category_id: {
            in: ids.map(id => BigInt(id))
          },
          deleted_at: null
        }
      });
      
      if (childCategories.length > 0) {
        throw new Error('选中的分类中有子分类，不能直接删除');
      }
      
      // 检查是否有关联的新闻
      // 注意：此处假设有新闻表，实际项目中需根据实际表结构调整
      /*
      const relatedNews = await this.prisma.news.findMany({
        where: {
          news_category_id: {
            in: ids.map(id => BigInt(id))
          },
          deleted_at: null
        }
      });
      
      if (relatedNews.length > 0) {
        throw new Error('选中的分类下有新闻内容，不能直接删除');
      }
      */

      const result = await this.newsCategoryModel.batchDelete(ids, userId);
      return handleBigInt(result);
    } catch (error) {
      throw new Error(`批量删除新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 切换新闻分类启用状态
   * @param {number} id 分类ID
   * @param {number} isEnabled 启用状态：1-启用，0-禁用
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 更新结果
   */
  async toggleCategoryStatus(id, isEnabled, userId) {
    try {
      // 检查分类是否存在
      const category = await this.newsCategoryModel.findById(id);
      if (!category) {
        throw new Error('新闻分类不存在');
      }
      
      const result = await this.newsCategoryModel.toggleStatus(id, isEnabled, userId);
      return handleBigInt(result);
    } catch (error) {
      throw new Error(`切换新闻分类状态失败: ${error.message}`);
    }
  }
}

module.exports = NewsCategoryService;

/**
 * 产品分类模型
 * 负责处理产品分类数据的存取
 */
const { Prisma } = require('@prisma/client');

/**
 * 构建树形结构数据
 * @param {Array} items 平铺的数据项数组
 * @param {BigInt|null} parentId 父级ID
 * @returns {Array} 树形结构数据
 */
function buildTree(items, parentId = null) {
  const result = [];
  const children = items.filter(item => {
    // 将BigInt转为字符串进行比较
    const itemParentId = item.parent_id ? item.parent_id.toString() : null;
    const compareParentId = parentId ? parentId.toString() : null;
    return itemParentId === compareParentId;
  });

  if (children.length > 0) {
    for (const child of children) {
      // 递归构建子树
      const childTree = {
        ...child,
        children: buildTree(items, child.id)
      };
      result.push(childTree);
    }
  }

  return result;
}

/**
 * 产品分类模型类
 */
class ProductCategoryModel {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 创建产品分类
   * @param {Object} data 分类数据
   * @param {number} userId 创建者ID
   * @returns {Promise<Object>} 创建结果
   */
  async create(data, userId) {
    // 准备数据字段
    const timestamp = BigInt(Date.now());
    const baseData = {
      created_at: timestamp,
      updated_at: timestamp,
      created_by: BigInt(userId),
      updated_by: BigInt(userId),
      is_enabled: data.is_enabled ?? 1
    };
    
    // 合并用户提交的数据
    const insertData = {
      ...baseData,
      name: data.name,
      parent_id: data.parent_id ? BigInt(data.parent_id) : null,
      description: data.description || '',
      sort_order: data.sort_order || 0
    };
    
    // 构建 INSERT 语句
    const result = await this.prisma.$queryRaw`
      INSERT INTO official.product_categories (
        name, parent_id, description, sort_order, is_enabled, 
        created_at, updated_at, created_by, updated_by
      )
      VALUES (
        ${insertData.name}, ${insertData.parent_id}, ${insertData.description}, ${insertData.sort_order}, ${insertData.is_enabled},
        ${insertData.created_at}, ${insertData.updated_at}, ${insertData.created_by}, ${insertData.updated_by}
      )
      RETURNING *
    `;
    
    return result[0];
  }

  /**
   * 获取产品分类列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分类列表及分页信息
   */
  async findAll(params = {}) {
    // 获取查询参数
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 1000; // 设置较大的页大小，以便获取完整树形结构
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    // 构建查询条件
    let whereConditions = Prisma.sql`
      WHERE deleted_at IS NULL
    `;

    // 根据名称筛选
    if (params.name) {
      whereConditions = Prisma.sql`
        ${whereConditions} AND name LIKE ${`%${params.name}%`}
      `;
    }

    // 根据状态筛选
    if (params.is_enabled !== undefined) {
      whereConditions = Prisma.sql`
        ${whereConditions} AND is_enabled = ${params.is_enabled}
      `;
    }

    // 根据父级ID筛选
    if (params.parent_id !== undefined) {
      whereConditions = Prisma.sql`
        ${whereConditions} AND parent_id = ${BigInt(params.parent_id)}
      `;
    }

    // 查询总数
    const countResult = await this.prisma.$queryRaw`
      SELECT COUNT(*) as total
      FROM official.product_categories
      ${whereConditions}
    `;
    const total = parseInt(countResult[0].total);

    // 查询数据列表
    const list = await this.prisma.$queryRaw`
      SELECT *
      FROM official.product_categories
      ${whereConditions}
      ORDER BY parent_id NULLS FIRST, sort_order ASC, id ASC
      LIMIT ${take} OFFSET ${skip}
    `;

    // 如果没有指定父级ID，构建树形结构
    let formattedList = list;
    if (params.parent_id === undefined) {
      formattedList = buildTree(list);
    }

    return {
      list: formattedList,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * 查找单个产品分类
   * @param {number} id 分类ID
   * @returns {Promise<Object|null>} 分类信息或null
   */
  async findById(id) {
    const result = await this.prisma.$queryRaw`
      SELECT *
      FROM official.product_categories
      WHERE id = ${BigInt(id)} AND deleted_at IS NULL
    `;
    return result.length > 0 ? result[0] : null;
  }

  /**
   * 更新产品分类
   * @param {number} id 分类ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新人ID
   * @returns {Promise<Object>} 更新结果
   */
  async update(id, data, userId) {
    // 准备更新字段
    const timestamp = BigInt(Date.now());
    
    // 构建 UPDATE 语句
    const updateFields = [];
    const params = {
      updated_at: timestamp,
      updated_by: BigInt(userId)
    };
    
    // 添加要更新的字段
    if (data.name !== undefined) {
      params.name = data.name;
      updateFields.push(Prisma.sql`name = ${params.name}`);
    }
    
    if (data.parent_id !== undefined) {
      params.parent_id = data.parent_id ? BigInt(data.parent_id) : null;
      updateFields.push(Prisma.sql`parent_id = ${params.parent_id}`);
    }
    
    if (data.description !== undefined) {
      params.description = data.description;
      updateFields.push(Prisma.sql`description = ${params.description}`);
    }
    
    if (data.sort_order !== undefined) {
      params.sort_order = data.sort_order;
      updateFields.push(Prisma.sql`sort_order = ${params.sort_order}`);
    }
    
    if (data.is_enabled !== undefined) {
      params.is_enabled = data.is_enabled;
      updateFields.push(Prisma.sql`is_enabled = ${params.is_enabled}`);
    }
    
    // 添加公共更新字段
    updateFields.push(Prisma.sql`updated_at = ${params.updated_at}`);
    updateFields.push(Prisma.sql`updated_by = ${params.updated_by}`);
    
    // 执行更新
    const result = await this.prisma.$queryRaw`
      UPDATE official.product_categories
      SET ${Prisma.join(updateFields, ', ')}
      WHERE id = ${BigInt(id)} AND deleted_at IS NULL
      RETURNING *
    `;
    
    return result.length > 0 ? result[0] : null;
  }

  /**
   * 软删除产品分类
   * @param {number} id 分类ID
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(id, userId) {
    const timestamp = BigInt(Date.now());
    
    // 检查是否有子分类
    const childrenResult = await this.prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM official.product_categories
      WHERE parent_id = ${BigInt(id)} AND deleted_at IS NULL
    `;
    
    if (parseInt(childrenResult[0].count) > 0) {
      throw new Error('该分类下存在子分类，无法删除');
    }
    
    // 执行软删除
    const result = await this.prisma.$queryRaw`
      UPDATE official.product_categories
      SET deleted_at = ${timestamp}, updated_at = ${timestamp}, updated_by = ${BigInt(userId)}
      WHERE id = ${BigInt(id)} AND deleted_at IS NULL
      RETURNING *
    `;
    
    return result.length > 0 ? result[0] : null;
  }

  /**
   * 批量软删除产品分类
   * @param {Array<number>} ids 分类ID数组
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 删除结果
   */
  async batchDelete(ids, userId) {
    const timestamp = BigInt(Date.now());
    const bigIntIds = ids.map(id => BigInt(id));
    
    // 检查是否有子分类
    const childrenResult = await this.prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM official.product_categories
      WHERE parent_id IN (${Prisma.join(bigIntIds)}) AND deleted_at IS NULL
    `;
    
    if (parseInt(childrenResult[0].count) > 0) {
      throw new Error('选中的分类中有存在子分类的项，无法删除');
    }
    
    // 执行批量软删除
    const result = await this.prisma.$queryRaw`
      UPDATE official.product_categories
      SET deleted_at = ${timestamp}, updated_at = ${timestamp}, updated_by = ${BigInt(userId)}
      WHERE id IN (${Prisma.join(bigIntIds)}) AND deleted_at IS NULL
      RETURNING *
    `;
    
    return {
      count: result.length,
      data: result
    };
  }

  /**
   * 切换产品分类启用状态
   * @param {number} id 分类ID
   * @param {number} isEnabled 启用状态：1-启用，0-禁用
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 更新结果
   */
  async toggleStatus(id, isEnabled, userId) {
    const timestamp = BigInt(Date.now());
    
    const result = await this.prisma.$queryRaw`
      UPDATE official.product_categories
      SET is_enabled = ${isEnabled}, updated_at = ${timestamp}, updated_by = ${BigInt(userId)}
      WHERE id = ${BigInt(id)} AND deleted_at IS NULL
      RETURNING *
    `;
    
    return result.length > 0 ? result[0] : null;
  }
}

module.exports = ProductCategoryModel;

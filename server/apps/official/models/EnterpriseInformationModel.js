/**
 * 企业信息模型
 */

/**
 * 企业信息创建请求验证规则
 */
const createEnterpriseInformationSchema = {
  type: 'object',
  required: ['title', 'content'],
  properties: {
    title: {
      type: 'string',
      minLength: 2,
      maxLength: 100,
      description: '标题'
    },
    description: {
      type: 'string',
      maxLength: 500,
      description: '简介'
    },
    content: {
      type: 'string',
      minLength: 10,
      description: '内容'
    },
    cate_name: {
      type: 'string',
      maxLength: 100,
      description: '分类名称'
    },
    cover_image: {
      type: 'string',
      format: 'uri',
      description: '封面图片URL'
    },
    sort: {
      type: 'integer',
      description: '排序'
    }
  }
};

/**
 * 企业信息更新请求验证规则
 */
const updateEnterpriseInformationSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      minLength: 2,
      maxLength: 100,
      description: '标题'
    },
    description: {
      type: 'string',
      maxLength: 500,
      description: '简介'
    },
    content: {
      type: 'string',
      minLength: 10,
      description: '内容'
    },
    cate_name: {
      type: 'string',
      maxLength: 100,
      description: '分类名称'
    },
    cover_image: {
      type: 'string',
      format: 'uri',
      description: '封面图片URL'
    },
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-隐藏，1-显示'
    },
    sort: {
      type: 'integer',
      description: '排序'
    }
  }
};

/**
 * 企业信息状态更新请求验证规则
 */
const updateStatusSchema = {
  type: 'object',
  required: ['status'],
  properties: {
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-草稿，1-已发布'
    }
  }
};

/**
 * 企业信息查询参数验证规则
 */
const listQuerySchema = {
  type: 'object',
  properties: {
    page: {
      type: 'integer',
      minimum: 1,
      description: '页码'
    },
    pageSize: {
      type: 'integer',
      minimum: 1,
      maximum: 100,
      description: '每页数量'
    },
    title: {
      type: 'string',
      description: '标题（模糊查询）'
    },
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-草稿，1-已发布'
    }
  }
};

/**
 * 企业信息模型定义
 * 用于Swagger文档和数据验证
 */
const enterpriseInformationModel = {
  id: {
    type: 'integer',
    description: 'ID'
  },
  title: {
    type: 'string',
    description: '标题'
  },
  description: {
    type: 'string',
    description: '简介'
  },
  content: {
    type: 'string',
    description: '内容'
  },
  cate_name: {
    type: 'string',
    description: '分类名称'
  },
  cover_image: {
    type: 'string',
    description: '封面图片URL'
  },
  status: {
    type: 'integer',
    description: '状态：0-隐藏，1-显示'
  },
  view_count: {
    type: 'integer',
    description: '浏览次数'
  },
  created_at: {
    type: 'integer',
    description: '创建时间'
  },
  created_by: {
    type: 'integer',
    description: '创建人ID'
  },
  updated_at: {
    type: 'integer',
    description: '更新时间'
  },
  updated_by: {
    type: 'integer',
    description: '更新人ID'
  },
  deleted_at: {
    type: 'integer',
    description: '删除时间'
  },
  sort: {
    type: 'integer',
    description: '排序'
  }
};

module.exports = {
  createEnterpriseInformationSchema,
  updateEnterpriseInformationSchema,
  updateStatusSchema,
  listQuerySchema,
  enterpriseInformationModel
};

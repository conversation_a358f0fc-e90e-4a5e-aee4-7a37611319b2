/**
 * 公司基础信息模型
 * 负责处理公司基础信息数据的存取
 */
const { Prisma } = require('@prisma/client');

/**
 * 公司基础信息模型类
 */
class CompanyInfoModel {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 创建或更新公司基础信息
   * @param {Object} data 公司信息数据
   * @param {number} userId 创建/更新者ID
   * @returns {Promise<Object>} 创建/更新结果
   */
  async createOrUpdate(data, userId) {
    // 准备数据字段
    const timestamp = BigInt(Date.now());
    
    // 查询现有记录
    const existingInfo = await this.findFirst();
    
    if (existingInfo) {
      // 更新现有记录
      const updateFields = [];
      const params = {
        updated_at: timestamp,
        updated_by: BigInt(userId)
      };
      
      // 添加要更新的字段
      if (data.company_name !== undefined) {
        params.company_name = data.company_name;
        updateFields.push(Prisma.sql`company_name = ${params.company_name}`);
      }
      
      if (data.company_address !== undefined) {
        params.company_address = data.company_address;
        updateFields.push(Prisma.sql`company_address = ${params.company_address}`);
      }
      
      if (data.logo_url !== undefined) {
        params.logo_url = data.logo_url;
        updateFields.push(Prisma.sql`logo_url = ${params.logo_url}`);
      }
      
      if (data.banner_url !== undefined) {
        params.banner_url = data.banner_url;
        updateFields.push(Prisma.sql`banner_url = ${params.banner_url}`);
      }
      
      if (data.vision !== undefined) {
        params.vision = data.vision;
        updateFields.push(Prisma.sql`vision = ${params.vision}`);
      }
      
      if (data.mission !== undefined) {
        params.mission = data.mission;
        updateFields.push(Prisma.sql`mission = ${params.mission}`);
      }
      
      if (data.core_values !== undefined) {
        params.core_values = data.core_values;
        updateFields.push(Prisma.sql`core_values = ${params.core_values}`);
      }
      
      if (data.icp_number !== undefined) {
        params.icp_number = data.icp_number;
        updateFields.push(Prisma.sql`icp_number = ${params.icp_number}`);
      }
      
      if (data.copyright !== undefined) {
        params.copyright = data.copyright;
        updateFields.push(Prisma.sql`copyright = ${params.copyright}`);
      }
      
      if (data.service_phone !== undefined) {
        params.service_phone = data.service_phone;
        updateFields.push(Prisma.sql`service_phone = ${params.service_phone}`);
      }
      
      if (data.wechat_qrcode !== undefined) {
        params.wechat_qrcode = data.wechat_qrcode;
        updateFields.push(Prisma.sql`wechat_qrcode = ${params.wechat_qrcode}`);
      }
      
      if (data.now_type !== undefined) {
        params.now_type = data.now_type;
        updateFields.push(Prisma.sql`now_type = ${params.now_type}`);
      }
      
      // 添加公共更新字段
      updateFields.push(Prisma.sql`updated_at = ${params.updated_at}`);
      updateFields.push(Prisma.sql`updated_by = ${params.updated_by}`);
      
      // 执行更新
      const result = await this.prisma.$queryRaw`
        UPDATE official.company_info
        SET ${Prisma.join(updateFields, ', ')}
        WHERE id = ${existingInfo.id}
        RETURNING *
      `;
      
      return result[0];
    } else {
      // 创建新记录
      const baseData = {
        created_at: timestamp,
        updated_at: timestamp,
        created_by: BigInt(userId),
        updated_by: BigInt(userId)
      };
      
      // 合并用户提交的数据
      const insertData = {
        ...baseData,
        company_name: data.company_name,
        company_address: data.company_address,
        logo_url: data.logo_url,
        banner_url: data.banner_url,
        vision: data.vision || null,
        mission: data.mission || null,
        core_values: data.core_values || null,
        icp_number: data.icp_number || null,
        copyright: data.copyright || null,
        service_phone: data.service_phone || null,
        wechat_qrcode: data.wechat_qrcode || null,
        now_type: data.now_type
      };
      
      // 构建 INSERT 语句
      const result = await this.prisma.$queryRaw`
        INSERT INTO official.company_info (
          company_name, company_address, logo_url, banner_url, 
          vision, mission, core_values, icp_number, 
          copyright, service_phone, wechat_qrcode,
          created_at, updated_at, created_by, updated_by,
          now_type
        )
        VALUES (
          ${insertData.company_name}, ${insertData.company_address}, 
          ${insertData.logo_url}, ${insertData.banner_url},
          ${insertData.vision}, ${insertData.mission}, 
          ${insertData.core_values}, ${insertData.icp_number},
          ${insertData.copyright}, ${insertData.service_phone}, 
          ${insertData.wechat_qrcode},
          ${insertData.created_at}, ${insertData.updated_at}, 
          ${insertData.created_by}, ${insertData.updated_by},
          ${insertData.now_type}
        )
        RETURNING *
      `;
      
      return result[0];
    }
  }

  /**
   * 获取公司基础信息
   * @returns {Promise<Object|null>} 公司信息或null
   */
  async findFirst() {
    const result = await this.prisma.$queryRaw`
      SELECT * FROM official.company_info
      LIMIT 1
    `;
    return result.length > 0 ? result[0] : null;
  }
}

module.exports = CompanyInfoModel;

/**
 * 留言管理模型
 * 定义留言管理相关的数据结构和验证规则
 */

/**
 * 留言创建请求验证规则
 */
const createMessageSchema = {
  type: 'object',
  required: ['message_location', 'submitter_name'],
  properties: {
    message_location: {
      type: 'string',
      minLength: 2,
      maxLength: 100,
      description: '留言位置'
    },
    submitter_name: {
      type: 'string',
      minLength: 2,
      maxLength: 100,
      description: '留言人姓名'
    },
    email: {
      type: 'string',
      format: 'email',
      maxLength: 100,
      description: '邮箱'
    },
    phone: {
      type: 'string',
      maxLength: 20,
      description: '电话'
    },
    message_details: {
      type: 'string',
      description: '留言详情'
    },
    attachment_name: {
      type: 'string',
      maxLength: 255,
      description: '附件名称'
    },
    attachment_url: {
      type: 'string',
      maxLength: 255,
      description: '附件地址'
    }
  }
};

/**
 * 留言更新请求验证规则
 */
const updateMessageSchema = {
  type: 'object',
  properties: {
    message_location: {
      type: 'string',
      minLength: 2,
      maxLength: 100,
      description: '留言位置'
    },
    submitter_name: {
      type: 'string',
      minLength: 2,
      maxLength: 100,
      description: '留言人姓名'
    },
    email: {
      type: 'string',
      format: 'email',
      maxLength: 100,
      description: '邮箱'
    },
    phone: {
      type: 'string',
      maxLength: 20,
      description: '电话'
    },
    message_details: {
      type: 'string',
      description: '留言详情'
    },
    attachment_name: {
      type: 'string',
      maxLength: 255,
      description: '附件名称'
    },
    attachment_url: {
      type: 'string',
      maxLength: 255,
      description: '附件地址'
    },
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-未处理，1-已处理'
    }
  }
};

/**
 * 留言状态更新请求验证规则
 */
const updateStatusSchema = {
  type: 'object',
  required: ['status'],
  properties: {
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-未处理，1-已处理'
    }
  }
};

/**
 * 留言查询参数验证规则
 */
const listQuerySchema = {
  type: 'object',
  properties: {
    page: {
      type: 'integer',
      minimum: 1,
      description: '页码'
    },
    pageSize: {
      type: 'integer',
      minimum: 1,
      maximum: 100,
      description: '每页数量'
    },
    submitter_name: {
      type: 'string',
      description: '留言人姓名（模糊查询）'
    },
    phone: {
      type: 'string',
      description: '电话（模糊查询）'
    },
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-未处理，1-已处理'
    },
    message_location: {
      type: 'string',
      description: '留言位置（模糊查询）'
    }
  }
};

/**
 * 留言模型定义
 * 用于Swagger文档和数据验证
 */
const messageModel = {
  id: {
    type: 'integer',
    description: '留言ID'
  },
  message_location: {
    type: 'string',
    description: '留言位置'
  },
  submitter_name: {
    type: 'string',
    description: '留言人姓名'
  },
  email: {
    type: 'string',
    description: '邮箱'
  },
  phone: {
    type: 'string',
    description: '电话'
  },
  message_details: {
    type: 'string',
    description: '留言详情'
  },
  attachment_name: {
    type: 'string',
    description: '附件名称'
  },
  attachment_url: {
    type: 'string',
    description: '附件地址'
  },
  status: {
    type: 'integer',
    description: '状态：0-未处理，1-已处理'
  },
  created_at: {
    type: 'integer',
    description: '创建时间'
  },
  updated_at: {
    type: 'integer',
    description: '更新时间'
  },
  updated_by: {
    type: 'integer',
    description: '更新人ID'
  },
  deleted_at: {
    type: 'integer',
    description: '删除时间'
  }
};

module.exports = {
  createMessageSchema,
  updateMessageSchema,
  updateStatusSchema,
  listQuerySchema,
  messageModel
};

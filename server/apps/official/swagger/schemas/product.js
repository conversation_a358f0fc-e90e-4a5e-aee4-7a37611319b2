/**
 * 产品相关的Swagger模型定义
 */
module.exports = {
  ProductBase: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: '产品名称'
      },
      image: {
        type: 'string',
        description: '产品图片URL'
      },
      description: {
        type: 'string',
        description: '产品描述',
        nullable: true
      },
      specification: {
        type: 'string',
        description: '产品规格描述',
        nullable: true
      },
      category_id: {
        type: 'string',
        description: '产品分类ID'
      },
      detail_images: {
        type: 'string',
        description: '详情图URL'
      },
      is_enabled: {
        type: 'integer',
        enum: [0, 1],
        description: '状态：1-启用，0-禁用'
      }
    }
  },
  
  ProductDetail: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        description: '产品ID'
      },
      name: {
        type: 'string',
        description: '产品名称'
      },
      image: {
        type: 'string',
        description: '产品图片URL'
      },
      description: {
        type: 'string',
        description: '产品描述',
        nullable: true
      },
      specification: {
        type: 'string',
        description: '产品规格描述',
        nullable: true
      },
      category_id: {
        type: 'string',
        description: '产品分类ID'
      },
      category_name: {
        type: 'string',
        description: '产品分类名称'
      },
      detail_images: {
        type: 'string',
        description: '详情图URL'
      },
      is_enabled: {
        type: 'integer',
        enum: [0, 1],
        description: '状态：1-启用，0-禁用'
      },
      created_at: {
        type: 'string',
        description: '创建时间'
      },
      updated_at: {
        type: 'string',
        description: '更新时间'
      },
      created_by: {
        type: 'string',
        description: '创建人ID'
      },
      updated_by: {
        type: 'string',
        description: '更新人ID'
      }
    }
  },
  
  CreateProductRequest: {
    type: 'object',
    required: ['name', 'image', 'category_id', 'detail_images'],
    properties: {
      name: {
        type: 'string',
        description: '产品名称'
      },
      image: {
        type: 'string',
        description: '产品图片URL'
      },
      description: {
        type: 'string',
        description: '产品描述'
      },
      specification: {
        type: 'string',
        description: '产品规格描述'
      },
      category_id: {
        type: 'string',
        description: '产品分类ID'
      },
      detail_images: {
        type: 'string',
        description: '详情图URL'
      },
      is_enabled: {
        type: 'integer',
        enum: [0, 1],
        description: '状态：1-启用，0-禁用',
        default: 1
      }
    }
  },
  
  UpdateProductRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: '产品名称'
      },
      image: {
        type: 'string',
        description: '产品图片URL'
      },
      description: {
        type: 'string',
        description: '产品描述'
      },
      specification: {
        type: 'string',
        description: '产品规格描述'
      },
      category_id: {
        type: 'string',
        description: '产品分类ID'
      },
      detail_images: {
        type: 'string',
        description: '详情图URL'
      },
      is_enabled: {
        type: 'integer',
        enum: [0, 1],
        description: '状态：1-启用，0-禁用'
      }
    }
  },
  
  BatchDeleteProductsRequest: {
    type: 'object',
    required: ['ids'],
    properties: {
      ids: {
        type: 'array',
        description: '要删除的产品ID列表',
        items: {
          type: 'string'
        }
      }
    }
  },
  
  ToggleProductStatusRequest: {
    type: 'object',
    required: ['is_enabled'],
    properties: {
      is_enabled: {
        type: 'integer',
        enum: [0, 1],
        description: '状态：1-启用，0-禁用'
      }
    }
  },
  
  ProductListResponse: {
    type: 'object',
    properties: {
      list: {
        type: 'array',
        description: '产品列表',
        items: {
          $ref: '#/components/schemas/ProductDetail'
        }
      },
      pagination: {
        type: 'object',
        description: '分页信息',
        properties: {
          total: {
            type: 'integer',
            description: '总记录数'
          },
          page: {
            type: 'integer',
            description: '当前页码'
          },
          pageSize: {
            type: 'integer',
            description: '每页记录数'
          },
          totalPages: {
            type: 'integer',
            description: '总页数'
          }
        }
      }
    }
  },
  
  ApiResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        description: '状态码'
      },
      message: {
        type: 'string',
        description: '响应消息'
      },
      data: {
        type: 'object',
        description: '响应数据'
      }
    }
  }
};

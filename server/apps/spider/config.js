/**
 * 爬虫管理模块配置文件
 */

module.exports = {
  // Python环境配置
  python: {
    // Python可执行文件路径，默认使用系统环境变量中的python
    pythonPath: process.env.SPIDER_PYTHON_PATH || 'python',
    // 爬虫项目路径
    spiderProjectPath: process.env.SPIDER_PROJECT_PATH || '/Users/<USER>/Documents/docker/v4/platform_spiders',
    // 爬虫运行脚本
    runnerScript: 'run_spider.py',
    // 执行超时时间（毫秒）
    defaultTimeout: 3600000, // 默认1小时
  },
  
  // RabbitMQ配置
  rabbitmq: {
    // 是否启用RabbitMQ连接，可以通过环境变量控制
    enabled: process.env.RABBITMQ_ENABLED !== 'false',
    // 使用用户提供的RabbitMQ配置
    host: process.env.RABBITMQ_HOST || '************',
    port: process.env.RABBITMQ_PORT || 5672,
    username: process.env.RABBITMQ_USER || 'admin',
    password: process.env.RABBITMQ_PASSWORD || 'password',
    vhost: process.env.RABBITMQ_VHOST || 'my_vhost',
    // 构建完整的URL
    get url() {
      return `amqp://${this.username}:${this.password}@${this.host}:${this.port}/${this.vhost}`;
    },
    // 爬虫数据队列前缀
    queuePrefix: 'spider_data_',
  },
  
  // 调度器配置
  scheduler: {
    // 时区设置
    timezone: 'Asia/Shanghai',
    // 检查间隔（毫秒）
    checkInterval: 60000, // 默认每分钟检查一次
    // 最大并发任务数
    maxConcurrentTasks: 5,
  },
  
  // 日志配置
  logging: {
    // 日志级别
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    // 日志保留天数
    retentionDays: 30,
  }
};

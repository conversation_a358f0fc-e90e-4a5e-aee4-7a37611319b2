/**
 * 爬虫执行器服务
 * 负责调用Python爬虫脚本并管理执行过程
 */
const { spawn } = require('child_process');
const path = require('path');
const config = require('../config');
const SpiderLogModel = require('../models/SpiderLogModel');
const { EXECUTION_STATUS } = require('../constants');
const RedisUtil = require('../../../core/utils/RedisUtil');

class SpiderExecutorService {
  /**
   * 执行爬虫任务
   * @param {Object} task 爬虫任务对象
   * @param {number} last_run_time 上次执行时间
   * @param {Object} [additionalParams={}] 额外参数
   * @returns {Promise<Object>} 执行结果
   */
  static async executeSpider(task, last_run_time, additionalParams = {}) {
    // 生成执行ID
    const executionId = `exec_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    
    // 创建执行日志
    const logEntry = await SpiderLogModel.create({
      task_id: task.id,
      spider_id: task.spider_id,
      store_id: task.store_id,
      spider_type: task.spider_type,
      start_time: Date.now(),
      status: EXECUTION_STATUS.RUNNING,
      execution_id: executionId,
      items_count: 0
    });
    
    try {
      // 获取爬虫名称
      const { prisma } = require('../../../core/database/prisma');
      const spider = await prisma.spider.findFirst({
        where: {
          id: BigInt(task.spider_id),
          deleted_at: null
        }
      });
      
      if (!spider) {
        throw new Error(`未找到爬虫 (ID: ${task.spider_id})`);
      }
      
      // 获取爬虫名称
      const spiderName = spider.code;
      console.log(`爬虫名称: ${spiderName}, 爬虫代码: ${spider.code}`);
      
      // 使用HTTP API调用爬虫
      const axios = require('axios');
      const FormData = require('form-data');
      const scrapydUrl = process.env.SCRAPYD_URL || 'http://localhost:6800/schedule.json';
      
      // 构建请求参数 - 参考 SpiderController.js 的实现方式
      const formData = new FormData();
      
      // 只添加基本参数，避免复杂对象
      formData.append('project', 'platform_spiders');
      formData.append('spider', spiderName);
      
      // 添加店铺ID - 确保转换为字符串
      if (task.store_id) {
        try {
          const storeIdStr = String(task.store_id).trim();
          console.log(`添加店铺ID: ${storeIdStr}`);
          formData.append('store_id', storeIdStr);
        } catch (error) {
          console.error(`添加店铺ID失败:`, error);
        }
      }
      
      // 解析任务参数
      const taskParams = task.parameters ? JSON.parse(task.parameters) : {};
      const mergedParams = { ...taskParams, ...additionalParams, last_run_time };
      
      // 添加关键参数
      if (mergedParams.model) {
        formData.append('model', String(mergedParams.model));
      }
      
      // 如果是增量模式，添加上次同步时间
      if (mergedParams.model === 'incremental' && last_run_time) {
        const lastSyncTimeStr = String(last_run_time).trim();
        console.log(`增量模式，添加上次同步时间: ${lastSyncTimeStr}`);
        formData.append('last_sync_time', lastSyncTimeStr);
      }
      console.log(`调用Scrapyd API: ${scrapydUrl}`);
      console.log('请求参数:', {
        project: 'platform_spiders',
        spider: spiderName,
        store_id: task.store_id || undefined,
        ...mergedParams
      });
      
      // 发送HTTP请求
      const startTime = Date.now();
      const response = await axios.post(scrapydUrl, formData, {
        headers: formData.getHeaders()
      });
      const endTime = Date.now();
      
      console.log(`Scrapyd响应:`, response.data);
      
      // 更新日志状态
      const status = response.data.status === 'ok' ? EXECUTION_STATUS.SUCCESS : EXECUTION_STATUS.FAILED;
      let stdoutData = JSON.stringify(response.data);
      let stderrData = '';
      
      if (response.data.status !== 'ok') {
        stderrData = `调用爬虫失败: ${JSON.stringify(response.data)}`;
      }
      // 构建Redis键名
      const key = `processed_${task.spider_type}_count:${spiderName}:${response.data.jobid}`;
      // 使用 undefined 而不是 null，因为 Prisma 不允许 items_count 为 null
      console.log(`Redis键名: ${key}`);
      // 等待 Redis 异步操作完成
      let countValue = 0;
      
      // 等待一段时间，让爬虫有时间更新Redis
      console.log('等待爬虫更新Redis数据...');
      await new Promise(resolve => setTimeout(resolve, 10000)); // 等待10秒
      try {
        // 确保 Redis 客户端存在且已连接
        if (!RedisUtil.getClient() || !RedisUtil.isConnected) {
          console.warn('Redis客户端未连接，无法获取计数值');
        } else {
          const count = await RedisUtil.getClient().get(key);
          console.log(`Redis值: ${count === null ? 'null' : count}`);
          
          // 确保将 null 或 undefined 转换为 0
          countValue = count ? parseInt(count) : 0;
          
          // 确保 countValue 是有效的整数
          if (isNaN(countValue)) {
            console.warn(`Redis 计数值无效: ${count}，使用默认值 0`);
            countValue = 0;
          } else {
            console.log(`成功获取Redis计数值: ${countValue}`);
          }
        }
      } catch (redisError) {
        console.error(`获取 Redis 计数值失败:`, redisError);
      }

      await SpiderLogModel.update(logEntry.id, {
        status,
        end_time: Date.now(),
        log_content: stdoutData,
        error_message: stderrData,
        items_count: countValue
      });
      
      if (response.data.status !== 'ok') {
        throw new Error(`爬虫执行失败: ${stderrData}`);
      }
      
      return {
        success: true,
        logId: logEntry.id,
        executionId,
        message: '爬虫任务已提交执行'
      };
    } catch (error) {
      // 更新日志状态为失败
      await SpiderLogModel.update(logEntry.id, {
        status: EXECUTION_STATUS.FAILED,
        end_time: Date.now(),
        error_message: error.message || '执行爬虫任务失败'
      });
      
      throw error;
    }
  }
}

module.exports = SpiderExecutorService;

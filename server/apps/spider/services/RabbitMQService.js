/**
 * RabbitMQ服务
 * 负责处理与RabbitMQ的连接和消息处理
 */
const amqp = require('amqplib');
const config = require('../config');

class RabbitMQService {
  constructor() {
    this.connection = null;
    this.channel = null;
    this.connected = false;
    this.retryCount = 0;
    this.maxRetries = 3; // 最大重试次数
  }
  
  /**
   * 初始化RabbitMQ连接
   * @returns {Promise<void>}
   */
  async initialize() {
    // 检查是否启用RabbitMQ
    if (!config.rabbitmq.enabled) {
      console.log('[spider] RabbitMQ服务已禁用，跳过连接初始化');
      return;
    }
    
    if (this.connected) {
      return;
    }
    
    // 如果超过最大重试次数，不再尝试
    if (this.retryCount >= this.maxRetries) {
      console.warn(`RabbitMQ连接失败超过${this.maxRetries}次，停止重试。请检查RabbitMQ配置和服务状态。`);
      return;
    }
    
    try {
      console.log('正在连接RabbitMQ服务器...');
      this.connection = await amqp.connect(config.rabbitmq.url);
      
      this.connection.on('error', (err) => {
        console.error('RabbitMQ连接错误:', err);
        this.connected = false;
      });
      
      this.connection.on('close', () => {
        console.log('RabbitMQ连接已关闭');
        this.connected = false;
        
        // 重置重试计数并尝试重新连接
        if (this.retryCount < this.maxRetries) {
          setTimeout(() => this.initialize(), 5000);
        }
      });
      
      // 创建通道
      this.channel = await this.connection.createChannel();
      this.connected = true;
      this.retryCount = 0; // 连接成功后重置重试计数
      
      console.log('[spider] RabbitMQ连接成功');
    } catch (error) {
      console.error('初始化RabbitMQ连接失败:', error);
      this.connected = false;
      this.retryCount++;
      
      // 如果未超过最大重试次数，则尝试重新连接
      if (this.retryCount < this.maxRetries) {
        console.log(`RabbitMQ连接失败，将在 5 秒后进行第 ${this.retryCount} 次重试...`);
        setTimeout(() => this.initialize(), 5000);
      } else {
        console.warn(`RabbitMQ连接失败超过${this.maxRetries}次，停止重试。请检查RabbitMQ配置和服务状态。`);
      }
    }
  }
  
  /**
   * 确保队列存在
   * @param {string} queueName 队列名称
   * @returns {Promise<Object>} 队列信息
   */
  async assertQueue(queueName) {
    // 检查是否启用RabbitMQ
    if (!config.rabbitmq.enabled) {
      console.log(`[spider] RabbitMQ服务已禁用，跳过队列创建: ${queueName}`);
      return { queue: queueName, messageCount: 0, consumerCount: 0 };
    }
    
    if (!this.connected) {
      await this.initialize();
    }
    
    if (!this.connected || !this.channel) {
      console.warn(`[spider] RabbitMQ未连接，无法创建队列: ${queueName}`);
      return { queue: queueName, messageCount: 0, consumerCount: 0 };
    }
    
    return this.channel.assertQueue(queueName, {
      durable: true
    });
  }
  
  /**
   * 发送消息到队列
   * @param {string} queueName 队列名称
   * @param {Object} message 消息内容
   * @returns {Promise<boolean>} 是否发送成功
   */
  async sendToQueue(queueName, message) {
    // 检查是否启用RabbitMQ
    if (!config.rabbitmq.enabled) {
      console.log(`[spider] RabbitMQ服务已禁用，跳过消息发送: ${queueName}`);
      return true; // 返回true以避免上层业务逻辑出错
    }
    
    if (!this.connected) {
      await this.initialize();
    }
    
    if (!this.connected || !this.channel) {
      console.warn(`[spider] RabbitMQ未连接，无法发送消息到队列: ${queueName}`);
      return false;
    }
    
    try {
      await this.assertQueue(queueName);
      
      const messageBuffer = Buffer.from(JSON.stringify(message));
      return this.channel.sendToQueue(queueName, messageBuffer, {
        persistent: true
      });
    } catch (error) {
      console.error(`发送消息到队列 ${queueName} 失败:`, error);
      return false;
    }
  }
  
  /**
   * 消费队列消息
   * @param {string} queueName 队列名称
   * @param {Function} callback 回调函数
   * @returns {Promise<Object>} 消费者标签
   */
  async consumeQueue(queueName, callback) {
    // 检查是否启用RabbitMQ
    if (!config.rabbitmq.enabled) {
      console.log(`[spider] RabbitMQ服务已禁用，跳过队列消费: ${queueName}`);
      return { consumerTag: `disabled-consumer-${Date.now()}` };
    }
    
    if (!this.connected) {
      await this.initialize();
    }
    
    if (!this.connected || !this.channel) {
      console.warn(`[spider] RabbitMQ未连接，无法消费队列: ${queueName}`);
      return { consumerTag: `failed-consumer-${Date.now()}` };
    }
    
    try {
      await this.assertQueue(queueName);
      
      return this.channel.consume(queueName, (msg) => {
        if (msg) {
          try {
            const content = JSON.parse(msg.content.toString());
            callback(content, msg);
            this.channel.ack(msg);
          } catch (error) {
            console.error(`处理队列 ${queueName} 消息失败:`, error);
            this.channel.nack(msg);
          }
        }
      });
    } catch (error) {
      console.error(`消费队列 ${queueName} 失败:`, error);
      throw error;
    }
  }
  
  /**
   * 关闭连接
   * @returns {Promise<void>}
   */
  async close() {
    if (this.channel) {
      await this.channel.close();
    }
    
    if (this.connection) {
      await this.connection.close();
    }
    
    this.connected = false;
    console.log('RabbitMQ连接已关闭');
  }
}

// 创建单例
const rabbitMQService = new RabbitMQService();

module.exports = rabbitMQService;

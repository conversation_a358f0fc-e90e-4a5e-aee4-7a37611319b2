/**
 * 爬虫任务Swagger文档定义
 */

const definitions = {
  SpiderTask: {
    type: 'object',
    properties: {
      id: {
        type: 'integer',
        format: 'int64',
        description: '爬虫任务ID'
      },
      task_name: {
        type: 'string',
        description: '任务名称'
      },
      platform: {
        type: 'string',
        description: '平台名称'
      },
      spider_type: {
        type: 'string',
        description: '爬虫类型'
      },
      cron_expression: {
        type: 'string',
        description: 'Cron表达式'
      },
      parameters: {
        type: 'string',
        description: '爬虫参数，JSON格式'
      },
      status: {
        type: 'integer',
        description: '任务状态：0-禁用，1-启用'
      },
      last_run_time: {
        type: 'integer',
        format: 'int64',
        description: '上次运行时间戳（毫秒）'
      },
      next_run_time: {
        type: 'integer',
        format: 'int64',
        description: '下次计划运行时间戳（毫秒）'
      },
      timeout: {
        type: 'integer',
        description: '超时时间（秒）'
      },
      retry_count: {
        type: 'integer',
        description: '失败重试次数'
      },
      priority: {
        type: 'integer',
        description: '优先级：1-10，值越大优先级越高'
      },
      created_by: {
        type: 'integer',
        format: 'int64',
        description: '创建者ID'
      },
      updated_by: {
        type: 'integer',
        format: 'int64',
        description: '更新者ID'
      },
      created_at: {
        type: 'integer',
        format: 'int64',
        description: '创建时间戳（毫秒）'
      },
      updated_at: {
        type: 'integer',
        format: 'int64',
        description: '更新时间戳（毫秒）'
      },
      deleted_at: {
        type: 'integer',
        format: 'int64',
        description: '删除时间戳（毫秒），空表示未删除'
      },
      remark: {
        type: 'string',
        description: '备注信息'
      },
      shops: {
        type: 'array',
        description: '关联店铺列表',
        items: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              format: 'int64',
              description: '关联ID'
            },
            task_id: {
              type: 'integer',
              format: 'int64',
              description: '任务ID'
            },
            shop_id: {
              type: 'integer',
              format: 'int64',
              description: '店铺ID'
            },
            platform_id: {
              type: 'integer',
              format: 'int64',
              description: '平台ID'
            },
            status: {
              type: 'integer',
              description: '状态（0-禁用，1-启用）'
            },
            priority: {
              type: 'integer',
              description: '优先级'
            },
            config: {
              type: 'string',
              description: '店铺特定配置（JSON字符串）'
            },
            remark: {
              type: 'string',
              description: '备注'
            },
            created_at: {
              type: 'integer',
              format: 'int64',
              description: '创建时间（时间戳）'
            },
            updated_at: {
              type: 'integer',
              format: 'int64',
              description: '更新时间（时间戳）'
            }
          }
        }
      }
    }
  },
  
  SpiderTaskCreateRequest: {
    type: 'object',
    required: ['task_name', 'platform', 'spider_type'],
    properties: {
      task_name: {
        type: 'string',
        description: '任务名称'
      },
      platform: {
        type: 'string',
        description: '平台名称'
      },
      spider_type: {
        type: 'string',
        description: '爬虫类型'
      },
      cron_expression: {
        type: 'string',
        description: 'Cron表达式'
      },
      parameters: {
        type: 'object',
        description: '爬虫参数'
      },
      status: {
        type: 'integer',
        description: '任务状态：0-禁用，1-启用',
        default: 0
      },
      timeout: {
        type: 'integer',
        description: '超时时间（秒）',
        default: 3600
      },
      retry_count: {
        type: 'integer',
        description: '失败重试次数',
        default: 3
      },
      priority: {
        type: 'integer',
        description: '优先级：1-10，值越大优先级越高',
        default: 5
      },
      remark: {
        type: 'string',
        description: '备注信息'
      },
      shops: {
        type: 'array',
        description: '关联店铺列表',
        items: {
          type: 'object',
          properties: {
            shop_id: {
              type: 'integer',
              format: 'int64',
              description: '店铺ID'
            },
            platform_id: {
              type: 'integer',
              format: 'int64',
              description: '平台ID'
            },
            status: {
              type: 'integer',
              description: '状态（0-禁用，1-启用）'
            },
            priority: {
              type: 'integer',
              description: '优先级'
            },
            config: {
              type: 'object',
              description: '店铺特定配置'
            },
            remark: {
              type: 'string',
              description: '备注'
            }
          },
          required: ['shop_id']
        }
      }
    }
  },
  
  SpiderTaskUpdateRequest: {
    type: 'object',
    properties: {
      task_name: {
        type: 'string',
        description: '任务名称'
      },
      platform: {
        type: 'string',
        description: '平台名称'
      },
      spider_type: {
        type: 'string',
        description: '爬虫类型'
      },
      cron_expression: {
        type: 'string',
        description: 'Cron表达式'
      },
      parameters: {
        type: 'object',
        description: '爬虫参数'
      },
      status: {
        type: 'integer',
        description: '任务状态：0-禁用，1-启用'
      },
      timeout: {
        type: 'integer',
        description: '超时时间（秒）'
      },
      retry_count: {
        type: 'integer',
        description: '失败重试次数'
      },
      priority: {
        type: 'integer',
        description: '优先级：1-10，值越大优先级越高'
      },
      remark: {
        type: 'string',
        description: '备注信息'
      },
      shops: {
        type: 'array',
        description: '关联店铺列表',
        items: {
          type: 'object',
          properties: {
            shop_id: {
              type: 'integer',
              format: 'int64',
              description: '店铺ID'
            },
            platform_id: {
              type: 'integer',
              format: 'int64',
              description: '平台ID'
            },
            status: {
              type: 'integer',
              description: '状态（0-禁用，1-启用）'
            },
            priority: {
              type: 'integer',
              description: '优先级'
            },
            config: {
              type: 'object',
              description: '店铺特定配置'
            },
            remark: {
              type: 'string',
              description: '备注'
            }
          },
          required: ['shop_id']
        }
      }
    }
  },
  
  SpiderTaskRunRequest: {
    type: 'object',
    properties: {
      params: {
        type: 'object',
        description: '执行参数'
      },
      shop_id: {
        type: 'integer',
        format: 'int64',
        description: '店铺ID，指定执行特定店铺的爬虫任务'
      }
    }
  },
  
  SpiderTaskResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        description: '状态码'
      },
      data: {
        $ref: '#/definitions/SpiderTask',
        description: '爬虫任务数据'
      },
      message: {
        type: 'string',
        description: '响应消息'
      }
    }
  },
  
  SpiderTaskListResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        description: '状态码'
      },
      data: {
        type: 'array',
        items: {
          $ref: '#/definitions/SpiderTask'
        },
        description: '爬虫任务列表'
      },
      pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            description: '当前页码'
          },
          pageSize: {
            type: 'integer',
            description: '每页数量'
          },
          total: {
            type: 'integer',
            description: '总记录数'
          },
          totalPages: {
            type: 'integer',
            description: '总页数'
          }
        },
        description: '分页信息'
      },
      message: {
        type: 'string',
        description: '响应消息'
      }
 * 爬虫任务API文档
 * @swagger
 * /api/spider/tasks:
 *   get:
 *     tags:
 *       - 爬虫任务管理
 *     summary: 获取爬虫任务列表
 *     description: 分页获取爬虫任务列表，支持按平台、类型和状态筛选
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码
 *         required: false
 *         type: integer
 *         default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量
 *         required: false
 *         type: integer
 *         default: 10
 *       - name: platform
 *         in: query
 *         description: 平台名称
 *         required: false
 *         type: string
 *       - name: spiderType
 *         in: query
 *         description: 爬虫类型
 *         required: false
 *         type: string
 *       - name: status
 *         in: query
 *         description: 任务状态：0-禁用，1-启用
 *         required: false
 *         type: integer
 *       - name: include_shops
 *         in: query
 *         description: 是否包含关联店铺信息（1-包含，0-不包含）
 *         required: false
 *         schema:
 *           type: string
 *           enum: ['0', '1', 'true', 'false']
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/SpiderTaskListResponse'
 *   post:
 *     tags:
 *       - 爬虫任务管理
 *     summary: 创建爬虫任务
 *     description: 创建新的爬虫任务
 *     parameters:
 *       - name: body
 *         in: body
 *         description: 爬虫任务数据
 *         required: true
 *         schema:
 *           $ref: '#/definitions/SpiderTaskCreateRequest'
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/SpiderTaskResponse'
 * 
 * /api/spider/tasks/{id}:
 *   get:
 *     tags:
 *       - 爬虫任务管理
 *     summary: 获取爬虫任务详情
 *     description: 根据ID获取爬虫任务详情
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫任务ID
 *         required: true
 *         type: integer
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/SpiderTaskResponse'
 *   put:
 *     tags:
 *       - 爬虫任务管理
 *     summary: 更新爬虫任务
 *     description: 根据ID更新爬虫任务
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫任务ID
 *         required: true
 *         type: integer
 *       - name: body
 *         in: body
 *         description: 爬虫任务更新数据
 *         required: true
 *         schema:
 *           $ref: '#/definitions/SpiderTaskUpdateRequest'
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/SpiderTaskResponse'
 *   delete:
 *     tags:
 *       - 爬虫任务管理
 *     summary: 删除爬虫任务
 *     description: 根据ID删除爬虫任务
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫任务ID
 *         required: true
 *         type: integer
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 响应消息
 * 
 * /api/spider/tasks/{id}/run:
 *   post:
 *     tags:
 *       - 爬虫任务管理
 *     summary: 运行爬虫任务
 *     description: 手动运行指定的爬虫任务
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫任务ID
 *         required: true
 *         type: integer
 *       - name: body
 *         in: body
 *         description: 执行参数
 *         required: false
 *         schema:
 *           $ref: '#/definitions/SpiderTaskRunRequest'
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             data:
 *               type: object
 *               properties:
 *                 logId:
 *                   type: integer
 *                   description: 日志ID
 *                 executionId:
 *                   type: string
 *                   description: 执行ID
 *             message:
 *               type: string
 *               description: 响应消息
 * 
 * /api/spider/tasks/{id}/stop:
 *   post:
 *     tags:
 *       - 爬虫任务管理
 *     summary: 停止爬虫任务
 *     description: 停止正在运行的爬虫任务
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫任务ID
 *         required: true
 *         type: integer
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 响应消息
 */

module.exports = {
  definitions
};

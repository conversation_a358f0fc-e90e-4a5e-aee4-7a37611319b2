/**
 * 爬虫信息Swagger文档定义
 */

const definitions = {
  Platform: {
    type: 'object',
    properties: {
      code: {
        type: 'string',
        description: '平台代码'
      },
      name: {
        type: 'string',
        description: '平台名称'
      }
    }
  },
  
  SpiderType: {
    type: 'object',
    properties: {
      code: {
        type: 'string',
        description: '爬虫类型代码'
      },
      name: {
        type: 'string',
        description: '爬虫类型名称'
      }
    }
  },
  
  PlatformListResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        description: '状态码'
      },
      data: {
        type: 'array',
        items: {
          $ref: '#/definitions/Platform'
        },
        description: '平台列表'
      },
      message: {
        type: 'string',
        description: '响应消息'
      }
    }
  },
  
  SpiderTypeListResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        description: '状态码'
      },
      data: {
        type: 'array',
        items: {
          $ref: '#/definitions/SpiderType'
        },
        description: '爬虫类型列表'
      },
      message: {
        type: 'string',
        description: '响应消息'
      }
    }
  }
};

/**
 * 爬虫信息API文档
 * @swagger
 * /api/spider/info/platforms:
 *   get:
 *     tags:
 *       - 爬虫信息
 *     summary: 获取可用的爬虫平台列表
 *     description: 获取系统中可用的爬虫平台列表
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/PlatformListResponse'
 * 
 * /api/spider/info/spider-types:
 *   get:
 *     tags:
 *       - 爬虫信息
 *     summary: 获取可用的爬虫类型列表
 *     description: 获取指定平台下可用的爬虫类型列表
 *     parameters:
 *       - name: platform
 *         in: query
 *         description: 平台代码
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/SpiderTypeListResponse'
 */

module.exports = {
  definitions
};

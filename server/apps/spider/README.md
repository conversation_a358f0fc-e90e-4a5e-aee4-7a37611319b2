# 爬虫管理模块

## 模块说明

爬虫管理模块用于集成Python Scrapy爬虫项目，实现爬虫任务管理、定时调度和数据同步功能。

## 环境配置

爬虫模块需要以下环境变量配置：

```bash
# Python环境配置
SPIDER_PYTHON_PATH=/path/to/python  # Python可执行文件路径，如不设置则使用系统默认的python命令
SPIDER_PROJECT_PATH=/path/to/platform_spiders  # 爬虫项目路径

# RabbitMQ配置
# 可以使用以下两种方式之一进行配置

# 方式一：使用单一URL（兼容旧版配置）
RABBITMQ_URL=amqp://username:password@localhost:5672/vhost  # RabbitMQ连接URL

# 方式二：使用分离的配置项（推荐）
RABBITMQ_ENABLED=true  # 是否启用RabbitMQ连接，设置为false可在开发环境中禁用RabbitMQ
RABBITMQ_HOST=localhost  # RabbitMQ主机地址
RABBITMQ_PORT=5672  # RabbitMQ端口
RABBITMQ_USER=admin  # RabbitMQ用户名
RABBITMQ_PASSWORD=password  # RabbitMQ密码
RABBITMQ_VHOST=my_vhost  # RabbitMQ虚拟主机
```

## 数据库模型

爬虫模块使用以下数据库表：

1. `spider.spider_task` - 爬虫任务表
2. `spider.spider_log` - 爬虫执行日志表

## 模块结构

```
spider/
├── constants/       # 常量定义
├── controllers/     # 控制器
├── dto/             # 数据传输对象
├── models/          # 数据模型
├── prisma/          # Prisma模型
│   └── models/      # Prisma模式文件
├── routes/          # 路由定义
├── services/        # 业务服务
├── swagger/         # Swagger文档
├── config.js        # 模块配置
├── index.js         # 模块入口
└── module.json      # 模块元数据
```

## 功能特性

1. 爬虫任务管理
2. 定时任务调度
3. 爬虫执行控制
4. 执行日志查询
5. 数据同步处理

## 使用方法

1. 配置环境变量
2. 创建爬虫任务
3. 设置定时调度或手动触发
4. 查看执行日志和数据同步结果

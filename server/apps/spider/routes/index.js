/**
 * 爬虫管理模块路由配置
 */
const express = require('express');
const router = express.Router();
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 任务管理路由
const taskController = require('../controllers/SpiderTaskController');

// 使用身份验证中间件保护路由
router.get('/tasks', authMiddleware, (req, res) => taskController.list(req, res));
router.get('/tasks/:id', authMiddleware, (req, res) => taskController.detail(req, res));
router.post('/tasks', authMiddleware, (req, res) => taskController.create(req, res));
router.put('/tasks/:id', authMiddleware, (req, res) => taskController.update(req, res));
router.delete('/tasks/:id', authMiddleware, (req, res) => taskController.delete(req, res));
router.post('/tasks/:id/run', authMiddleware, (req, res) => taskController.runTask(req, res));
router.post('/tasks/:id/stop', authMiddleware, (req, res) => taskController.stopTask(req, res));



// 日志查询路由
const SpiderLogController = require('../controllers/SpiderLogController');
// 使用身份验证中间件保护路由
router.get('/logs', authMiddleware, (req, res) => SpiderLogController.list(req, res));
router.get('/logs/:id', authMiddleware, (req, res) => SpiderLogController.detail(req, res));
router.get('/tasks/:taskId/logs', authMiddleware, (req, res) => SpiderLogController.listByTask(req, res));

// 爬虫管理路由
const SpiderController = require('../controllers/SpiderController');
// 使用身份验证中间件保护路由
router.get('/spiders', authMiddleware, (req, res) => SpiderController.getList(req, res));
router.get('/spiders/:id', authMiddleware, (req, res) => SpiderController.getDetail(req, res));
router.post('/spiders', authMiddleware, (req, res) => SpiderController.create(req, res));
router.put('/spiders/:id', authMiddleware, (req, res) => SpiderController.update(req, res));
router.delete('/spiders/:id', authMiddleware, (req, res) => SpiderController.delete(req, res));
router.post('/spiders/call', authMiddleware, (req, res) => SpiderController.callSpider(req, res));
// router.get('/spiders/:id/platforms', authMiddleware, (req, res) => SpiderController.listPlatforms(req, res)); // 暂时注释掉，因为这个方法尚未实现


module.exports = router;

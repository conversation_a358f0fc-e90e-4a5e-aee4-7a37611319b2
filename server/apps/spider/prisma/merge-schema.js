/**
 * 合并Prisma模式文件
 */
const fs = require('fs');
const path = require('path');

// 模型目录
const modelsDir = path.join(__dirname, 'models');
// 输出文件
const outputFile = path.join(__dirname, 'schema.prisma');

/**
 * 合并Prisma模式文件
 */
function mergeSchemas() {
  try {
    console.log('开始合并Prisma模式文件...');
    
    // 读取基础模式文件
    const baseSchema = fs.readFileSync(path.join(modelsDir, 'base.prisma'), 'utf8');
    
    // 读取所有其他模式文件
    const modelFiles = fs.readdirSync(modelsDir)
      .filter(file => file.endsWith('.prisma') && file !== 'base.prisma');
    
    // 合并模式文件内容
    let mergedSchema = baseSchema;
    
    for (const file of modelFiles) {
      const modelSchema = fs.readFileSync(path.join(modelsDir, file), 'utf8');
      mergedSchema += '\n\n' + modelSchema;
    }
    
    // 递归读取子目录中的模式文件
    function readDirRecursive(dir) {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          readDirRecursive(itemPath);
        } else if (item.endsWith('.prisma')) {
          const modelSchema = fs.readFileSync(itemPath, 'utf8');
          mergedSchema += '\n\n' + modelSchema;
        }
      }
    }
    
    // 读取子目录
    const subdirs = fs.readdirSync(modelsDir)
      .filter(item => {
        const itemPath = path.join(modelsDir, item);
        return fs.statSync(itemPath).isDirectory();
      });
    
    for (const subdir of subdirs) {
      readDirRecursive(path.join(modelsDir, subdir));
    }
    
    // 写入合并后的模式文件
    fs.writeFileSync(outputFile, mergedSchema);
    
    console.log('Prisma模式文件合并完成，输出到:', outputFile);
  } catch (error) {
    console.error('合并Prisma模式文件失败:', error);
    process.exit(1);
  }
}

// 执行合并
mergeSchemas();

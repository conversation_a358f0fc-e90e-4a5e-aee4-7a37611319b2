generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["master", "spider"]
}

/// 平台爬虫关联表，存储平台与爬虫的多对多关系
model PlatformSpider {
  /// 平台ID
  platform_id BigInt
  /// 爬虫ID
  spider_id   BigInt
  /// 平台特定的爬虫配置，JSON格式
  config      String?
  /// 创建者ID
  created_by  BigInt?
  /// 创建时间戳（毫秒）
  created_at  BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  spider      Spider  @relation(fields: [spider_id], references: [id])

  @@id([platform_id, spider_id])
  @@map("platform_spider")
  @@schema("spider")
}

/// 爬虫表，存储爬虫基本信息
model Spider {
  /// 爬虫ID，雪花ID
  id               BigInt           @id
  /// 爬虫名称
  name             String           @db.VarChar(100)
  /// 爬虫代码，唯一标识
  code             String           @db.VarChar(50)
  /// 爬虫版本
  version          String           @db.VarChar(20)
  /// 状态：1-启用，0-禁用
  status           Int              @default(1) @db.SmallInt
  /// 创建者ID
  created_by       BigInt?
  /// 更新者ID
  updated_by       BigInt?
  /// 创建时间戳（毫秒）
  created_at       BigInt           @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// 更新时间戳（毫秒）
  updated_at       BigInt           @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// 删除时间戳（毫秒），空表示未删除
  deleted_at       BigInt?
  /// 爬虫配置，JSON格式
  config           String?
  /// 爬虫描述
  description      String?
  platform_spiders PlatformSpider[]

  @@index([code])
  @@index([status])
  @@map("spider")
  @@schema("spider")
}

/// 爬虫日志表，记录爬虫执行日志信息
model SpiderLog {
  /// 爬虫日志ID，雪花ID
  id            BigInt     @id
  /// 关联的爬虫任务ID
  task_id       BigInt
  /// 开始执行时间戳（毫秒）
  start_time    BigInt
  /// 结束执行时间戳（毫秒）
  end_time      BigInt?
  /// 执行状态：0-进行中，1-成功，2-失败
  status        Int        @default(0) @db.SmallInt
  /// 爬取的数据条数
  items_count   Int        @default(0)
  /// 错误信息
  error_message String?
  /// 执行日志内容
  log_content   String?
  /// 执行ID，用于关联具体执行实例
  execution_id  String     @db.VarChar(50)
  /// 执行参数，JSON格式
  parameters    String?
  /// 触发类型：0-定时触发，1-手动触发
  trigger_type  Int        @default(0) @db.SmallInt
  /// 创建者ID
  created_by    BigInt?
  /// 更新者ID
  updated_by    BigInt?
  /// 创建时间戳（毫秒）
  created_at    BigInt     @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// 更新时间戳（毫秒）
  updated_at    BigInt     @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// 删除时间戳（毫秒），空表示未删除
  deleted_at    BigInt?
  task          SpiderTask @relation(fields: [task_id], references: [id])

  @@index([task_id])
  @@index([status])
  @@index([start_time])
  @@map("spider_log")
  @@schema("spider")
}

/// 爬虫任务表，存储爬虫任务配置信息
model SpiderTask {
  /// 爬虫任务ID，雪花ID
  id              BigInt      @id
  /// 爬虫ID
  spider_id       BigInt?
  /// 平台ID
  platform_id     BigInt
  /// 店铺ID
  store_id        BigInt?
  /// 爬虫类型，如order、product等
  spider_type     String      @db.VarChar(50)
  /// Cron表达式，定义任务执行计划
  cron_expression String?     @db.VarChar(100)
  /// 运行间隔（秒）
  run_interval    Int?
  /// 上次运行时间戳（毫秒）
  last_run_time   BigInt?
  /// 下次计划运行时间戳（毫秒）
  next_run_time   BigInt?
  /// 创建者ID
  created_by      BigInt?
  /// 更新者ID
  updated_by      BigInt?
  /// 创建时间戳（毫秒）
  created_at      BigInt      @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// 更新时间戳（毫秒）
  updated_at      BigInt      @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// 删除时间戳（毫秒），空表示未删除
  deleted_at      BigInt?
  logs            SpiderLog[]

  @@index([spider_id])
  @@index([platform_id])
  @@index([store_id])
  @@map("spider_task")
  @@schema("spider")
}

const Joi = require('joi');

/**
 * 商品属性模板DTO
 */
class GoodsAttrTemplateDto {
  /**
   * 验证创建商品属性模板的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果
   */
  static validateCreate(data) {
    const schema = Joi.object({
      name: Joi.string().required().max(50).messages({
        'string.base': '模板名称必须是字符串',
        'string.empty': '模板名称不能为空',
        'string.max': '模板名称不能超过50个字符',
        'any.required': '模板名称是必填项'
      }),
      sort: Joi.number().integer().min(0).default(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      categoryId: Joi.alternatives().try(
        Joi.number().integer().min(1),
        Joi.string().pattern(/^\d+$/).custom((value, helpers) => {
          // 允许字符串形式的大整数
          if (!/^\d+$/.test(value)) {
            return helpers.error('string.pattern.base');
          }
          return value;
        })
      ).allow(null).messages({
        'number.base': '分类ID必须是数字',
        'number.integer': '分类ID必须是整数',
        'number.min': '分类ID不能小于1',
        'string.pattern.base': '分类ID必须是数字字符串'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证更新商品属性模板的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      // 允许请求体中包含id字段，但不会对其进行验证
      id: Joi.any().optional(),
      name: Joi.string().max(50).messages({
        'string.base': '模板名称必须是字符串',
        'string.empty': '模板名称不能为空',
        'string.max': '模板名称不能超过50个字符'
      }),
      sort: Joi.number().integer().min(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      categoryId: Joi.alternatives().try(
        Joi.number().integer().min(1),
        Joi.string().pattern(/^\d+$/).custom((value, helpers) => {
          // 允许字符串形式的大整数
          if (!/^\d+$/.test(value)) {
            return helpers.error('string.pattern.base');
          }
          return value;
        })
      ).allow(null).messages({
        'number.base': '分类ID必须是数字',
        'number.integer': '分类ID必须是整数',
        'number.min': '分类ID不能小于1',
        'string.pattern.base': '分类ID必须是数字字符串'
      })
    }).min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证查询商品属性模板的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果
   */
  static validateQuery(data) {
    const schema = Joi.object({
      name: Joi.string().allow('').max(50).messages({
        'string.base': '模板名称必须是字符串',
        'string.max': '模板名称不能超过50个字符'
      }),
      categoryId: Joi.alternatives().try(
        Joi.number().integer().min(1),
        Joi.string().pattern(/^\d+$/).custom((value, helpers) => {
          // 允许字符串形式的大整数
          if (!/^\d+$/.test(value)) {
            return helpers.error('string.pattern.base');
          }
          return value;
        })
      ).messages({
        'number.base': '分类ID必须是数字',
        'number.integer': '分类ID必须是整数',
        'number.min': '分类ID不能小于1',
        'string.pattern.base': '分类ID必须是数字字符串'
      }),
      startTime: Joi.alternatives().try(
        Joi.number().min(0),
        Joi.string().custom((value, helpers) => {
          const num = Number(value);
          if (isNaN(num) || num < 0) {
            return helpers.error('any.invalid');
          }
          return num;
        })
      ).messages({
        'number.base': '开始时间必须是数字',
        'number.min': '开始时间不能小于0',
        'any.invalid': '开始时间格式不正确或小于0'
      }),
      endTime: Joi.alternatives().try(
        Joi.number().min(0),
        Joi.string().custom((value, helpers) => {
          const num = Number(value);
          if (isNaN(num) || num < 0) {
            return helpers.error('any.invalid');
          }
          return num;
        })
      ).messages({
        'number.base': '结束时间必须是数字',
        'number.min': '结束时间不能小于0',
        'any.invalid': '结束时间格式不正确或小于0'
      }),
      // 添加分页参数的验证规则
      page: Joi.alternatives().try(
        Joi.number().integer().min(1),
        Joi.string().pattern(/^\d+$/).custom((value, helpers) => {
          const num = Number(value);
          if (isNaN(num) || num < 1) {
            return helpers.error('any.invalid');
          }
          return num;
        })
      ).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码不能小于1',
        'string.pattern.base': '页码必须是数字字符串',
        'any.invalid': '页码格式不正确或小于1'
      }),
      pageSize: Joi.alternatives().try(
        Joi.number().integer().min(1).max(100),
        Joi.string().pattern(/^\d+$/).custom((value, helpers) => {
          const num = Number(value);
          if (isNaN(num) || num < 1 || num > 100) {
            return helpers.error('any.invalid');
          }
          return num;
        })
      ).messages({
        'number.base': '每页数量必须是数字',
        'number.integer': '每页数量必须是整数',
        'number.min': '每页数量不能小于1',
        'number.max': '每页数量不能大于100',
        'string.pattern.base': '每页数量必须是数字字符串',
        'any.invalid': '每页数量格式不正确或超出范围(1-100)'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }
  /**
   * 验证创建属性模板参数的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果
   */
  static validateCreateParam(data) {
    const schema = Joi.object({
      name: Joi.string().required().max(50).messages({
        'string.base': '参数名称必须是字符串',
        'string.empty': '参数名称不能为空',
        'string.max': '参数名称不能超过50个字符',
        'any.required': '参数名称是必填项'
      }),
      type: Joi.string().required().valid('text', 'number', 'radio', 'checkbox', 'select').messages({
        'string.base': '参数类型必须是字符串',
        'string.empty': '参数类型不能为空',
        'any.required': '参数类型是必填项',
        'any.only': '参数类型必须是文本、数字、单选、多选或下拉选择'
      }),
      value: Joi.string().when('type', {
        is: Joi.string().valid('radio', 'checkbox', 'select'),
        then: Joi.string().required().messages({
          'string.base': '可选值必须是字符串',
          'string.empty': '可选值不能为空',
          'any.required': '当类型为单选、多选或下拉选择时，可选值是必填项'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),

      isRequired: Joi.number().integer().valid(0, 1).default(0).messages({
        'number.base': '是否必填必须是整数',
        'number.integer': '是否必填必须是整数',
        'any.only': '是否必填必须是0或1（0表示否，1表示是）'
      }),
      isFilterable: Joi.number().integer().valid(0, 1).default(0).messages({
        'number.base': '是否可筛选必须是整数',
        'number.integer': '是否可筛选必须是整数',
        'any.only': '是否可筛选必须是0或1（0表示否，1表示是）'
      }),
      sort: Joi.number().integer().min(0).default(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      isEnabled: Joi.number().integer().valid(0, 1).default(1).messages({
        'number.base': '状态必须是整数',
        'number.integer': '状态必须是整数',
        'any.only': '状态必须是0或1（0表示禁用，1表示启用）'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证更新属性模板参数的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果
   */
  static validateUpdateParam(data) {
    const schema = Joi.object({
      name: Joi.string().max(50).messages({
        'string.base': '参数名称必须是字符串',
        'string.empty': '参数名称不能为空',
        'string.max': '参数名称不能超过50个字符'
      }),
      type: Joi.string().valid('text', 'number', 'radio', 'checkbox', 'select').messages({
        'string.base': '参数类型必须是字符串',
        'string.empty': '参数类型不能为空',
        'any.only': '参数类型必须是文本、数字、单选、多选或下拉选择'
      }),
      value: Joi.string().when('type', {
        is: Joi.string().valid('radio', 'checkbox', 'select'),
        then: Joi.string().required().messages({
          'string.base': '可选值必须是字符串',
          'string.empty': '可选值不能为空',
          'any.required': '当类型为单选、多选或下拉选择时，可选值是必填项'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),
      isRequired: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否必填必须是整数',
        'number.integer': '是否必填必须是整数',
        'any.only': '是否必填必须是0或1（0表示否，1表示是）'
      }),
      isFilterable: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否可筛选必须是整数',
        'number.integer': '是否可筛选必须是整数',
        'any.only': '是否可筛选必须是0或1（0表示否，1表示是）'
      }),
      sort: Joi.number().integer().min(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      isEnabled: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是整数',
        'number.integer': '状态必须是整数',
        'any.only': '状态必须是0或1（0表示禁用，1表示启用）'
      })
    }).min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    });

    return schema.validate(data, { abortEarly: false });
  }
}

module.exports = GoodsAttrTemplateDto;

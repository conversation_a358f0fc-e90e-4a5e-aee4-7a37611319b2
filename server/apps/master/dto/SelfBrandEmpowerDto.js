const Joi = require('joi');

// 自主品牌授权DTO验证类
class SelfBrandEmpowerDto {
  /**
   * 查询自主品牌授权列表的验证规则
   * @returns {Object} 验证规则
   */
  static getListValidate() {
    return Joi.object({
      page: Joi.number().integer().min(1).default(1).description('页码'),
      limit: Joi.number().integer().min(1).max(100).default(20).description('每页数量'),
      empower_number: Joi.string().allow('').description('授权编号'),
      empower_brand: Joi.string().allow('').description('授权品牌'),
      start_time: Joi.number().integer().allow(null).description('创建开始时间戳（毫秒）'),
      end_time: Joi.number().integer().allow(null).description('创建结束时间戳（毫秒）'),
      status: Joi.number().valid(0, 1).description('状态：0-禁用，1-启用')
    });
  }

  /**
   * 获取自主品牌授权详情的验证规则
   * @returns {Object} 验证规则
   */
  static getDetailValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('自主品牌授权ID')
    });
  }

  /**
   * 创建自主品牌授权的验证规则
   * @returns {Object} 验证规则
   */
  static createValidate() {
    return Joi.object({
      empower_number: Joi.string().required().max(100).description('授权编号'),
      empower_unit: Joi.string().required().max(255).description('授权单位'),
      empower_this_unit: Joi.string().required().max(255).description('兹授权单位'),
      empower_brand: Joi.string().required().max(100).description('授权品牌'),
      empower_image: Joi.string().required().max(255).description('授权品牌图片'),
      empower_er_image: Joi.string().required().max(255).description('授权品牌二维码图片'),
      type: Joi.number().integer().default(0).description('商标类型'),
      empower_start_date: Joi.date().allow(null).description('授权开始日期'),
      empower_end_date: Joi.date().allow(null).description('授权结束日期'),
      status: Joi.number().valid(0, 1).default(1).description('状态：0-禁用，1-启用')
    });
  }

  /**
   * 更新自主品牌授权的验证规则
   * @returns {Object} 验证规则
   */
  static updateValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('自主品牌授权ID'),
      empower_number: Joi.string().max(100).description('授权编号'),
      empower_unit: Joi.string().max(255).description('授权单位'),
      empower_this_unit: Joi.string().max(255).description('兹授权单位'),
      empower_brand: Joi.string().max(100).description('授权品牌'),
      empower_image: Joi.string().max(255).description('授权品牌图片'),
      empower_er_image: Joi.string().max(255).description('授权品牌二维码图片'),
      type: Joi.number().integer().description('商标类型'),
      empower_start_date: Joi.date().allow(null).description('授权开始日期'),
      empower_end_date: Joi.date().allow(null).description('授权结束日期'),
      status: Joi.number().valid(0, 1).description('状态：0-禁用，1-启用')
    });
  }

  /**
   * 删除自主品牌授权的验证规则
   * @returns {Object} 验证规则
   */
  static deleteValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('自主品牌授权ID')
    });
  }
}

module.exports = SelfBrandEmpowerDto;

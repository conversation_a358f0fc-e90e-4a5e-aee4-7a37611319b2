/**
 * 订单跟单员数据传输对象
 * 负责订单绑定跟单员相关数据的验证
 */
const Jo<PERSON> = require('joi');

/**
 * 订单跟单员DTO
 */
class OrderFollowerDto {
  /**
   * 验证添加跟单员的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateAddFollower(data) {
    const schema = Joi.object({
      // 跟单员ID，必填，来自system_user中控台用户
      followerId: Joi.string().required().messages({
        'string.empty': '跟单员ID不能为空',
        'any.required': '跟单员ID是必填项'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证添加多个跟单员的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateAddFollowers(data) {
    const schema = Joi.object({
      // 跟单员ID数组，必填，来自system_user中控台用户
      followerIds: Joi.array().items(Joi.string()).min(1).required().messages({
        'array.min': '至少需要添加一个跟单员',
        'array.base': '跟单员ID列表必须是数组',
        'any.required': '跟单员ID列表是必填项'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证删除跟单员的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateRemoveFollower(data) {
    const schema = Joi.object({
      // 跟单员ID，必填，来自system_user中控台用户
      followerId: Joi.string().required().messages({
        'string.empty': '跟单员ID不能为空',
        'any.required': '跟单员ID是必填项'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }
}

module.exports = OrderFollowerDto;

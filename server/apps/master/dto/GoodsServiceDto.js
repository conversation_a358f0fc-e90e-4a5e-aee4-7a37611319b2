const { z } = require('zod');

/**
 * 商品服务 DTO
 */
class GoodsServiceDto {
  /**
   * 创建商品服务的验证规则
   */
  static createSchema = z.object({
    name: z.string({
      required_error: '服务名称不能为空'
    }).min(1, '服务名称不能为空'),
    imageUrl: z.string().url('图片 URL 格式不正确').optional().nullable(),
    description: z.string().optional().nullable(),
    sortOrder: z.number().int().optional().nullable()
  });

  /**
   * 更新商品服务的验证规则
   */
  static updateSchema = z.object({
    name: z.string({
      required_error: '服务名称不能为空'
    }).min(1, '服务名称不能为空'),
    imageUrl: z.string().url('图片 URL 格式不正确').optional().nullable(),
    description: z.string().optional().nullable(),
    sortOrder: z.number().int().optional().nullable()
  });

  /**
   * 验证创建商品服务的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证后的数据
   */
  static validateCreate(data) {
    return this.createSchema.parse(data);
  }

  /**
   * 验证更新商品服务的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证后的数据
   */
  static validateUpdate(data) {
    return this.updateSchema.parse(data);
  }
}

module.exports = GoodsServiceDto;

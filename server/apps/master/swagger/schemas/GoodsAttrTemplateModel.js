/**
 * @swagger
 * components:
 *   schemas:
 *     GoodsAttrTemplate:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 模板ID
 *           example: 1
 *         name:
 *           type: string
 *           description: 模板名称
 *           example: "服装属性模板"
 *         sortOrder:
 *           type: integer
 *           description: 排序值
 *           example: 0
 *         sort:
 *           type: integer
 *           description: 排序值（兼容字段）
 *           example: 0
 *         createdAt:
 *           type: integer
 *           description: 创建时间戳（毫秒）
 *           example: 1681881600000
 *         updatedAt:
 *           type: integer
 *           description: 更新时间戳（毫秒）
 *           example: 1681881600000
 *         deletedAt:
 *           type: integer
 *           nullable: true
 *           description: 删除时间戳（毫秒），null表示未删除
 *           example: null
 *         createdBy:
 *           type: integer
 *           nullable: true
 *           description: 创建者ID
 *           example: 1
 *         updatedBy:
 *           type: integer
 *           nullable: true
 *           description: 更新者ID
 *           example: null
 *     GoodsAttrTemplateParam:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 参数ID
 *           example: 1
 *         goodsAttributeSetId:
 *           type: integer
 *           description: 所属模板ID
 *           example: 2
 *         name:
 *           type: string
 *           description: 参数名称
 *           example: "颜色"
 *         type:
 *           type: string
 *           enum: [text, number, radio, checkbox, select]
 *           description: 参数类型
 *           example: "radio"
 *         value:
 *           type: string
 *           description: 可选值，用换行符分隔
 *           example: "红色\n蓝色\n绿色"
 *         isRequired:
 *           type: boolean
 *           description: 是否必填
 *           example: false
 *         isFilterable:
 *           type: boolean
 *           description: 是否可筛选
 *           example: true
 *         sort:
 *           type: integer
 *           description: 排序值（兼容字段）
 *           example: 0
 *         sortOrder:
 *           type: integer
 *           description: 排序值
 *           example: 0
 *         isEnabled:
 *           type: boolean
 *           description: 是否启用
 *           example: true
 *         createdAt:
 *           type: integer
 *           description: 创建时间戳（毫秒）
 *           example: 1681881600000
 *         updatedAt:
 *           type: integer
 *           description: 更新时间戳（毫秒）
 *           example: 1681881600000
 *         deletedAt:
 *           type: integer
 *           nullable: true
 *           description: 删除时间戳（毫秒），null表示未删除
 *           example: null
 */

/**
 * 商城前端用户模型定义
 */
module.exports = {
  MallUser: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        description: '用户ID'
      },
      username: {
        type: 'string',
        description: '用户名'
      },
      nickname: {
        type: 'string',
        description: '用户昵称'
      },
      avatar: {
        type: 'string',
        description: '头像地址'
      },
      phone: {
        type: 'string',
        description: '手机号'
      },
      email: {
        type: 'string',
        description: '邮箱'
      },
      status: {
        type: 'integer',
        enum: [0, 1],
        description: '状态：1-正常，0-禁用'
      },
      last_login_ip: {
        type: 'string',
        description: '最后登录IP'
      },
      last_login_time: {
        type: 'integer',
        description: '最后登录时间戳（毫秒）'
      },
      login_count: {
        type: 'integer',
        description: '登录次数'
      },
      created_at: {
        type: 'integer',
        description: '创建时间戳（毫秒）'
      },
      updated_at: {
        type: 'integer',
        description: '更新时间戳（毫秒）'
      },
      remark: {
        type: 'string',
        description: '备注信息'
      }
    }
  },
  MallUserRegisterRequest: {
    type: 'object',
    required: ['username', 'password'],
    properties: {
      username: {
        type: 'string',
        description: '用户名'
      },
      password: {
        type: 'string',
        description: '密码'
      },
      nickname: {
        type: 'string',
        description: '用户昵称'
      },
      phone: {
        type: 'string',
        description: '手机号'
      },
      email: {
        type: 'string',
        description: '邮箱'
      }
    }
  },
  MallUserLoginRequest: {
    type: 'object',
    required: ['username', 'password'],
    properties: {
      username: {
        type: 'string',
        description: '用户名'
      },
      password: {
        type: 'string',
        description: '密码'
      }
    }
  },
  MallUserStatusUpdateRequest: {
    type: 'object',
    required: ['status'],
    properties: {
      status: {
        type: 'integer',
        enum: [0, 1],
        description: '状态：1-正常，0-禁用'
      }
    }
  }
};

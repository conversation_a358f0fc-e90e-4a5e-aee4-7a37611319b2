/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 用户ID
 *           example: 1
 *         username:
 *           type: string
 *           description: 用户名
 *           example: "admin"
 *         nickname:
 *           type: string
 *           description: 昵称
 *           example: "管理员"
 *         email:
 *           type: string
 *           description: 邮箱
 *           example: "<EMAIL>"
 *         mobile:
 *           type: string
 *           description: 手机号
 *           example: "13800138000"
 *         avatar:
 *           type: string
 *           description: 头像URL
 *           example: "https://example.com/avatar.jpg"
 *         status:
 *           type: integer
 *           description: 状态（0：禁用，1：启用）
 *           example: 1
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2025-04-12T03:34:08.000Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2025-04-12T03:34:08.000Z"
 * 
 *     UserLoginRequest:
 *       type: object
 *       required:
 *         - username
 *         - password
 *         - captcha
 *         - captchaId
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名
 *           example: "admin"
 *         password:
 *           type: string
 *           description: 密码
 *           example: "123456"
 *         captcha:
 *           type: string
 *           description: 验证码
 *           example: "a1b2"
 *         captchaId:
 *           type: string
 *           description: 验证码ID
 *           example: "7f8d9a2b-3c4e-5f6g-7h8i-9j0k1l2m3n4o"
 * 
 *     UserLoginResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/Success'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   description: 访问令牌
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 user:
 *                   $ref: '#/components/schemas/User'
 * 
 *     UserCreateRequest:
 *       type: object
 *       required:
 *         - username
 *         - password
 *         - nickname
 *         - email
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名
 *           example: "newuser"
 *         password:
 *           type: string
 *           description: 密码
 *           example: "123456"
 *         nickname:
 *           type: string
 *           description: 昵称
 *           example: "新用户"
 *         email:
 *           type: string
 *           description: 邮箱
 *           example: "<EMAIL>"
 *         mobile:
 *           type: string
 *           description: 手机号
 *           example: "13800138000"
 * 
 *     UserUpdateRequest:
 *       type: object
 *       properties:
 *         nickname:
 *           type: string
 *           description: 昵称
 *           example: "新昵称"
 *         email:
 *           type: string
 *           description: 邮箱
 *           example: "<EMAIL>"
 *         mobile:
 *           type: string
 *           description: 手机号
 *           example: "13800138000"
 *         avatar:
 *           type: string
 *           description: 头像URL
 *           example: "https://example.com/new-avatar.jpg"
 *         status:
 *           type: integer
 *           description: 状态（0：禁用，1：启用）
 *           example: 1
 * 
 *     UserListResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessWithPagination'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 list:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 */

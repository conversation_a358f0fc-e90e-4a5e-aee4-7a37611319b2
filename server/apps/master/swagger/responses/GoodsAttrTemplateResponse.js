/**
 * @swagger
 * /api/master/goods-attr-template:
 *   get:
 *     tags: [商品属性模板]
 *     summary: 获取商品属性模板列表
 *     description: 获取所有商品属性模板，支持按名称模糊搜索、分类ID筛选和创建时间范围筛选
 *     parameters:
 *       - name: name
 *         in: query
 *         description: 模板名称（模糊搜索）
 *         required: false
 *         schema:
 *           type: string
 *       - name: categoryId
 *         in: query
 *         description: 关联的商品分类ID
 *         required: false
 *         schema:
 *           type: integer
 *       - name: startTime
 *         in: query
 *         description: 创建时间范围的开始时间戳（毫秒）
 *         required: false
 *         schema:
 *           type: integer
 *       - name: endTime
 *         in: query
 *         description: 创建时间范围的结束时间戳（毫秒）
 *         required: false
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         $ref: '#/components/responses/GoodsAttrTemplateList'
 *       500:
 *         $ref: '#/components/responses/Error500'
 * 
 * components:
 *   responses:
 *     GoodsAttrTemplateList:
 *       description: 成功获取商品属性模板列表
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 200
 *               message:
 *                 type: string
 *                 example: 获取商品属性模板列表成功
 *               data:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/GoodsAttrTemplate'
 *     GoodsAttrTemplateDetail:
 *       description: 成功获取商品属性模板详情
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 200
 *               message:
 *                 type: string
 *                 example: 获取商品属性模板成功
 *               data:
 *                 $ref: '#/components/schemas/GoodsAttrTemplate'
 *     GoodsAttrTemplateCreated:
 *       description: 成功创建商品属性模板
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 201
 *               message:
 *                 type: string
 *                 example: 添加商品属性模板成功
 *               data:
 *                 $ref: '#/components/schemas/GoodsAttrTemplate'
 *     GoodsAttrTemplateUpdated:
 *       description: 成功更新商品属性模板
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 200
 *               message:
 *                 type: string
 *                 example: 更新商品属性模板成功
 *               data:
 *                 $ref: '#/components/schemas/GoodsAttrTemplate'
 *     GoodsAttrTemplateDeleted:
 *       description: 成功删除商品属性模板
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 200
 *               message:
 *                 type: string
 *                 example: 删除商品属性模板成功
 *     GoodsAttrTemplateParamList:
 *       description: 成功获取属性模板参数列表
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 200
 *               message:
 *                 type: string
 *                 example: 获取属性模板参数列表成功
 *               data:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/GoodsAttrTemplateParam'
 *     GoodsAttrTemplateParamCreated:
 *       description: 成功创建属性模板参数
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 201
 *               message:
 *                 type: string
 *                 example: 添加属性模板参数成功
 *               data:
 *                 $ref: '#/components/schemas/GoodsAttrTemplateParam'
 *     GoodsAttrTemplateParamUpdated:
 *       description: 成功更新属性模板参数
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 200
 *               message:
 *                 type: string
 *                 example: 更新属性模板参数成功
 *               data:
 *                 $ref: '#/components/schemas/GoodsAttrTemplateParam'
 *     GoodsAttrTemplateParamDeleted:
 *       description: 成功删除属性模板参数
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: integer
 *                 example: 200
 *               message:
 *                 type: string
 *                 example: 删除属性模板参数成功
 */

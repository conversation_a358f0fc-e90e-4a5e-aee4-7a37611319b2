/**
 * 防火墙规则服务
 * 处理防火墙规则的业务逻辑
 */
const { generateSnowflakeId } = require('../../../../../../shared/utils/snowflake');
const { clearFirewallCache } = require('../../../../../../core/middleware/FirewallMiddleware');

class FirewallService {
  constructor(prisma) {
    this.prisma = prisma;
    this.model = prisma.baseFirewallRule;
  }

  /**
   * 获取防火墙规则列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params = {}) {
    try {
      const {
        page = 1,
        pageSize = 20,
        rule_name,
        ip_address,
        rule_type,
        status,
        sortField = 'priority',
        sortOrder = 'desc'
      } = params;

      const skip = (page - 1) * pageSize;
      const take = parseInt(pageSize);

      // 构建查询条件
      const where = {
        deleted_at: null
      };

      if (rule_name) {
        where.rule_name = {
          contains: rule_name
        };
      }

      if (ip_address) {
        where.ip_address = {
          contains: ip_address
        };
      }

      if (rule_type !== undefined && rule_type !== '') {
        where.rule_type = parseInt(rule_type);
      }

      if (status !== undefined && status !== '') {
        where.status = parseInt(status);
      }

      // 构建排序
      const orderBy = {};
      orderBy[sortField] = sortOrder;

      // 查询数据
      const [items, total] = await Promise.all([
        this.model.findMany({
          where,
          skip,
          take,
          orderBy,
          select: {
            id: true,
            rule_name: true,
            ip_address: true,
            ip_mask: true,
            rule_type: true,
            match_type: true,
            status: true,
            priority: true,
            hit_count: true,
            last_hit_at: true,
            created_at: true,
            updated_at: true,
            remark: true
          }
        }),
        this.model.count({ where })
      ]);

      // 处理BigInt字段
      const processedItems = items.map(item => ({
        ...item,
        id: item.id.toString(),
        hit_count: item.hit_count.toString(),
        last_hit_at: item.last_hit_at ? item.last_hit_at.toString() : null,
        created_at: item.created_at.toString(),
        updated_at: item.updated_at.toString()
      }));

      return {
        items: processedItems,
        total,
        page: parseInt(page),
        pageSize: take,
        totalPages: Math.ceil(total / take)
      };
    } catch (error) {
      console.error('获取防火墙规则列表失败:', error);
      throw new Error('获取防火墙规则列表失败');
    }
  }

  /**
   * 根据ID获取防火墙规则
   * @param {string} id 规则ID
   * @returns {Promise<Object>} 规则信息
   */
  async getById(id) {
    try {
      const rule = await this.model.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        select: {
          id: true,
          rule_name: true,
          ip_address: true,
          ip_mask: true,
          rule_type: true,
          match_type: true,
          status: true,
          priority: true,
          hit_count: true,
          last_hit_at: true,
          created_at: true,
          updated_at: true,
          remark: true
        }
      });

      if (!rule) {
        throw new Error('防火墙规则不存在');
      }

      // 处理BigInt字段
      return {
        ...rule,
        id: rule.id.toString(),
        hit_count: rule.hit_count.toString(),
        last_hit_at: rule.last_hit_at ? rule.last_hit_at.toString() : null,
        created_at: rule.created_at.toString(),
        updated_at: rule.updated_at.toString()
      };
    } catch (error) {
      console.error('获取防火墙规则失败:', error);
      throw error;
    }
  }

  /**
   * 创建防火墙规则
   * @param {Object} data 规则数据
   * @param {string} userId 创建者ID
   * @returns {Promise<Object>} 创建的规则
   */
  async create(data, userId) {
    try {
      const {
        rule_name,
        ip_address,
        ip_mask,
        rule_type = 1,
        match_type = 1,
        status = 1,
        priority = 0,
        remark
      } = data;

      // 验证必填字段
      if (!rule_name || !ip_address) {
        throw new Error('规则名称和IP地址不能为空');
      }

      // 检查规则名称是否重复
      const existingRule = await this.model.findFirst({
        where: {
          rule_name,
          deleted_at: null
        }
      });

      if (existingRule) {
        throw new Error('规则名称已存在');
      }

      const now = BigInt(Date.now());
      const ruleId = generateSnowflakeId();

      const rule = await this.model.create({
        data: {
          id: ruleId,
          rule_name,
          ip_address,
          ip_mask,
          rule_type: parseInt(rule_type),
          match_type: parseInt(match_type),
          status: parseInt(status),
          priority: parseInt(priority),
          hit_count: BigInt(0),
          remark,
          created_at: now,
          updated_at: now,
          created_by: userId ? BigInt(userId) : null
        }
      });

      // 清除缓存
      clearFirewallCache();

      // 处理BigInt字段
      return {
        ...rule,
        id: rule.id.toString(),
        hit_count: rule.hit_count.toString(),
        created_at: rule.created_at.toString(),
        updated_at: rule.updated_at.toString(),
        created_by: rule.created_by ? rule.created_by.toString() : null
      };
    } catch (error) {
      console.error('创建防火墙规则失败:', error);
      throw error;
    }
  }

  /**
   * 更新防火墙规则
   * @param {string} id 规则ID
   * @param {Object} data 更新数据
   * @param {string} userId 更新者ID
   * @returns {Promise<Object>} 更新后的规则
   */
  async update(id, data, userId) {
    try {
      // 检查规则是否存在
      const existingRule = await this.model.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!existingRule) {
        throw new Error('防火墙规则不存在');
      }

      const {
        rule_name,
        ip_address,
        ip_mask,
        rule_type,
        match_type,
        status,
        priority,
        remark
      } = data;

      // 如果修改了规则名称，检查是否重复
      if (rule_name && rule_name !== existingRule.rule_name) {
        const duplicateRule = await this.model.findFirst({
          where: {
            rule_name,
            id: { not: BigInt(id) },
            deleted_at: null
          }
        });

        if (duplicateRule) {
          throw new Error('规则名称已存在');
        }
      }

      const now = BigInt(Date.now());
      const updateData = {
        updated_at: now,
        updated_by: userId ? BigInt(userId) : null
      };

      // 只更新提供的字段
      if (rule_name !== undefined) updateData.rule_name = rule_name;
      if (ip_address !== undefined) updateData.ip_address = ip_address;
      if (ip_mask !== undefined) updateData.ip_mask = ip_mask;
      if (rule_type !== undefined) updateData.rule_type = parseInt(rule_type);
      if (match_type !== undefined) updateData.match_type = parseInt(match_type);
      if (status !== undefined) updateData.status = parseInt(status);
      if (priority !== undefined) updateData.priority = parseInt(priority);
      if (remark !== undefined) updateData.remark = remark;

      const rule = await this.model.update({
        where: { id: BigInt(id) },
        data: updateData
      });

      // 清除缓存
      clearFirewallCache();

      // 处理BigInt字段
      return {
        ...rule,
        id: rule.id.toString(),
        hit_count: rule.hit_count.toString(),
        last_hit_at: rule.last_hit_at ? rule.last_hit_at.toString() : null,
        created_at: rule.created_at.toString(),
        updated_at: rule.updated_at.toString(),
        created_by: rule.created_by ? rule.created_by.toString() : null,
        updated_by: rule.updated_by ? rule.updated_by.toString() : null
      };
    } catch (error) {
      console.error('更新防火墙规则失败:', error);
      throw error;
    }
  }

  /**
   * 删除防火墙规则
   * @param {string} id 规则ID
   * @param {string} userId 删除者ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async delete(id, userId) {
    try {
      // 检查规则是否存在
      const existingRule = await this.model.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!existingRule) {
        throw new Error('防火墙规则不存在');
      }

      const now = BigInt(Date.now());

      await this.model.update({
        where: { id: BigInt(id) },
        data: {
          deleted_at: now,
          updated_at: now,
          updated_by: userId ? BigInt(userId) : null
        }
      });

      // 清除缓存
      clearFirewallCache();

      return true;
    } catch (error) {
      console.error('删除防火墙规则失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除防火墙规则
   * @param {Array} ids 规则ID数组
   * @param {string} userId 删除者ID
   * @returns {Promise<number>} 删除的数量
   */
  async batchDelete(ids, userId) {
    try {
      const now = BigInt(Date.now());
      const bigIntIds = ids.map(id => BigInt(id));

      const result = await this.model.updateMany({
        where: {
          id: { in: bigIntIds },
          deleted_at: null
        },
        data: {
          deleted_at: now,
          updated_at: now,
          updated_by: userId ? BigInt(userId) : null
        }
      });

      // 清除缓存
      clearFirewallCache();

      return result.count;
    } catch (error) {
      console.error('批量删除防火墙规则失败:', error);
      throw error;
    }
  }

  /**
   * 更新规则状态
   * @param {string} id 规则ID
   * @param {number} status 状态
   * @param {string} userId 更新者ID
   * @returns {Promise<Object>} 更新后的规则
   */
  async updateStatus(id, status, userId) {
    try {
      const rule = await this.update(id, { status }, userId);
      return rule;
    } catch (error) {
      console.error('更新防火墙规则状态失败:', error);
      throw error;
    }
  }
}

module.exports = FirewallService;

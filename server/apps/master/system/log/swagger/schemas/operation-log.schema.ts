/**
 * 操作日志模型定义
 */

export const OperationLogSchema = {
  type: 'object',
  properties: {
    id: {
      type: 'string',
      description: '日志ID'
    },
    user_id: {
      type: 'string',
      description: '操作用户ID'
    },
    username: {
      type: 'string',
      description: '操作用户名'
    },
    module: {
      type: 'string',
      description: '操作模块'
    },
    operation: {
      type: 'string',
      description: '操作类型'
    },
    method: {
      type: 'string',
      description: '请求方法'
    },
    path: {
      type: 'string',
      description: '请求路径'
    },
    ip: {
      type: 'string',
      description: '操作IP地址'
    },
    user_agent: {
      type: 'string',
      description: '用户代理信息'
    },
    request_data: {
      type: 'object',
      description: '请求参数'
    },
    response_data: {
      type: 'object',
      description: '响应数据'
    },
    status: {
      type: 'integer',
      description: '状态：1-成功，0-失败'
    },
    error_message: {
      type: 'string',
      description: '错误信息'
    },
    execution_time: {
      type: 'integer',
      description: '执行时间(毫秒)'
    },
    created_at: {
      type: 'string',
      description: '创建时间戳'
    }
  }
};

export const OperationLogQuerySchema = {
  type: 'object',
  properties: {
    page: {
      type: 'integer',
      description: '页码',
      default: 1
    },
    pageSize: {
      type: 'integer',
      description: '每页数量',
      default: 10
    },
    module: {
      type: 'string',
      description: '模块名称'
    },
    operation: {
      type: 'string',
      description: '操作类型'
    },
    username: {
      type: 'string',
      description: '用户名'
    },
    startTime: {
      type: 'string',
      description: '开始时间戳'
    },
    endTime: {
      type: 'string',
      description: '结束时间戳'
    },
    status: {
      type: 'integer',
      description: '状态：1-成功，0-失败'
    }
  }
};

export const OperationLogClearSchema = {
  type: 'object',
  properties: {
    beforeTime: {
      type: 'string',
      description: '清空指定时间前的日志'
    }
  }
};

/**
 * 系统配置API路由定义
 */
const ConfigManagementController = require('../controllers/ConfigManagementController');
const IntegrationConfigController = require('../controllers/IntegrationConfigController');
const ConfigRoute = require('./ConfigRoute');
const securityCenterRoutes = require('../securityCenter/routes');
const ocrTestRoutes = require('../../config/ocrConfig/routes/ocrTestRoutes');

/**
 * 注册系统配置相关路由
 * @param {Object} app Express应用实例
 * @param {Object} prisma Prisma客户端实例
 */
module.exports = (app, prisma) => {
  // 创建控制器实例
  const configController = new ConfigManagementController(prisma);
  const integrationConfigController = new IntegrationConfigController(prisma);

  // 定义API路由前缀
  const apiPrefix = '/api/v1/master/system';

  // 系统配置管理路由
  app.post(`${apiPrefix}/config`, configController.create.bind(configController));
  app.put(`${apiPrefix}/config/:id`, configController.update.bind(configController));
  app.delete(`${apiPrefix}/config/:id`, configController.delete.bind(configController));
  app.get(`${apiPrefix}/config/:id`, configController.detail.bind(configController));
  app.get(`${apiPrefix}/config`, configController.list.bind(configController));
  app.get(`${apiPrefix}/config/type/:configType`, configController.getByType.bind(configController));

  // 集成服务配置路由
  app.use(`${apiPrefix}/configure`, ConfigRoute(prisma));

  // 安全中心路由
  app.use(`${apiPrefix}/config/securityCenter`, securityCenterRoutes);

  // OCR配置测试路由
  console.log('[DEBUG] 注册OCR配置测试路由:', `${apiPrefix}/configure/ocrConfig`);
  app.use(`${apiPrefix}/configure/ocrConfig`, ocrTestRoutes);
}

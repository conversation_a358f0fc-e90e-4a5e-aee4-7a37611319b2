/**
 * @swagger
 * components:
 *   schemas:
 *     SystemDepartment:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           format: int64
 *           description: 部门ID
 *           example: 1
 *         name:
 *           type: string
 *           description: 部门名称
 *           example: "技术部"
 *         leader:
 *           type: string
 *           description: 部门负责人
 *           example: "张三"
 *         phone:
 *           type: string
 *           description: 联系电话，11位手机号
 *           example: "13800138000"
 *         sort:
 *           type: integer
 *           description: 排序，值越小越靠前
 *           example: 0
 *         status:
 *           type: integer
 *           description: 状态：1-正常，2-停用
 *           example: 1
 *         remark:
 *           type: string
 *           description: 备注信息
 *           example: "公司技术研发部门"
 *         created_at:
 *           type: integer
 *           format: int64
 *           description: 创建时间戳（毫秒）
 *           example: 1617265271000
 *         updated_at:
 *           type: integer
 *           format: int64
 *           description: 更新时间戳（毫秒）
 *           example: 1617265271000
 * 
 *     SystemDepartmentCreateRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: 部门名称
 *           example: "技术部"
 *         leader:
 *           type: string
 *           description: 部门负责人
 *           example: "张三"
 *         phone:
 *           type: string
 *           description: 联系电话，11位手机号
 *           example: "13800138000"
 *         sort:
 *           type: integer
 *           description: 排序，值越小越靠前
 *           example: 0
 *         status:
 *           type: integer
 *           description: 状态：1-正常，2-停用
 *           example: 1
 *         remark:
 *           type: string
 *           description: 备注信息
 *           example: "公司技术研发部门"
 *
 *     SystemDepartmentUpdateRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: 部门名称
 *           example: "技术部"
 *         leader:
 *           type: string
 *           description: 部门负责人
 *           example: "张三"
 *         phone:
 *           type: string
 *           description: 联系电话，11位手机号
 *           example: "13800138000"
 *         sort:
 *           type: integer
 *           description: 排序，值越小越靠前
 *           example: 0
 *         status:
 *           type: integer
 *           description: 状态：1-正常，2-停用
 *           example: 1
 *         remark:
 *           type: string
 *           description: 备注信息
 *           example: "公司技术研发部门"
 *
 *     SystemDepartmentResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SystemDepartment'
 *         - type: object
 *
 *     SystemDepartmentListResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ListResponse'
 *         - type: object
 *           properties:
 *             list:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/SystemDepartment'
 */

// 导出模型定义
module.exports = {
  schemas: {
    SystemDepartment: {
      type: 'object',
      properties: {
        id: {
          type: 'integer',
          format: 'int64',
          description: '部门ID',
          example: 1
        },
        name: {
          type: 'string',
          description: '部门名称',
          example: '技术部'
        },
        leader: {
          type: 'string',
          description: '部门负责人',
          example: '张三'
        },
        phone: {
          type: 'string',
          description: '联系电话，11位手机号',
          example: '13800138000'
        },
        sort: {
          type: 'integer',
          description: '排序，值越小越靠前',
          example: 0
        },
        status: {
          type: 'integer',
          description: '状态：1-正常，2-停用',
          example: 1
        },
        remark: {
          type: 'string',
          description: '备注信息',
          example: '公司技术研发部门'
        },
        created_at: {
          type: 'integer',
          format: 'int64',
          description: '创建时间戳（毫秒）',
          example: 1617265271000
        },
        updated_at: {
          type: 'integer',
          format: 'int64',
          description: '更新时间戳（毫秒）',
          example: 1617265271000
        }
      }
    },
    SystemDepartmentCreateRequest: {
      type: 'object',
      required: ['name'],
      properties: {
        name: {
          type: 'string',
          description: '部门名称',
          example: '技术部'
        },
        leader: {
          type: 'string',
          description: '部门负责人',
          example: '张三'
        },
        phone: {
          type: 'string',
          description: '联系电话，11位手机号',
          example: '13800138000'
        },
        sort: {
          type: 'integer',
          description: '排序，值越小越靠前',
          example: 0
        },
        status: {
          type: 'integer',
          description: '状态：1-正常，2-停用',
          example: 1
        },
        remark: {
          type: 'string',
          description: '备注信息',
          example: '公司技术研发部门'
        }
      }
    },
    SystemDepartmentUpdateRequest: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '部门名称',
          example: '技术部'
        },
        leader: {
          type: 'string',
          description: '部门负责人',
          example: '张三'
        },
        phone: {
          type: 'string',
          description: '联系电话，11位手机号',
          example: '13800138000'
        },
        sort: {
          type: 'integer',
          description: '排序，值越小越靠前',
          example: 0
        },
        status: {
          type: 'integer',
          description: '状态：1-正常，2-停用',
          example: 1
        },
        remark: {
          type: 'string',
          description: '备注信息',
          example: '公司技术研发部门'
        }
      }
    },
    SystemDepartmentResponse: {
      allOf: [
        { $ref: '#/components/schemas/SystemDepartment' },
        { type: 'object' }
      ]
    },
    SystemDepartmentListResponse: {
      allOf: [
        { $ref: '#/components/schemas/ListResponse' },
        {
          type: 'object',
          properties: {
            list: {
              type: 'array',
              items: { $ref: '#/components/schemas/SystemDepartment' }
            }
          }
        }
      ]
    }
  }
};

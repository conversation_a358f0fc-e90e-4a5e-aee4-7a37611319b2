{"openapi": "3.0.0", "info": {"title": "系统管理/用户中心/角色管理 API", "version": "1.0.0", "description": "角色管理相关接口文档，仅包含获取角色已分配菜单（回显用）接口"}, "tags": [{"name": "系统管理/用户中心/角色管理"}], "paths": {"/api/v1/system/role/{id}/menus": {"get": {"tags": ["系统管理/用户中心/角色管理"], "summary": "获取角色已分配菜单（回显用）", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int64"}, "description": "角色ID"}], "responses": {"200": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}, "404": {"description": "角色不存在"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "responses": {"Error": {"description": "请求失败", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}}}}}}}}}
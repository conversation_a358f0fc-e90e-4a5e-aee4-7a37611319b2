const Joi = require('joi');
const BaseDto = require('../../../../../core/dto/BaseDto');

/**
 * 角色数据传输对象
 * 用于验证角色接口的请求参数
 */
class SystemRoleDto extends BaseDto {
  /**
   * 验证创建角色的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateCreate(data) {
    const schema = Joi.object({
      name: Joi.string().min(2).max(30).required().messages({
        'string.base': '角色名称必须是字符串',
        'string.min': '角色名称长度不能少于2个字符',
        'string.max': '角色名称长度不能超过30个字符',
        'any.required': '角色名称不能为空'
      }),
      code: Joi.string().max(100).messages({
        'string.base': '角色编码必须是字符串',
        'string.max': '角色编码长度不能超过100个字符'
      }),
      dataScope: Joi.number().integer().valid(1, 2, 3, 4, 5).default(1).messages({
        'number.base': '数据范围必须是数字',
        'number.integer': '数据范围必须是整数',
        'any.only': '数据范围只能是1-5的整数'
      }),
      deptIds: Joi.array().items(
        Joi.alternatives().try(
          Joi.number().integer().min(1),
          Joi.string().pattern(/^[0-9]+$/)
        )
      ).default([]).messages({
        'array.base': '部门ID必须是数组',
        'string.pattern.base': '部门ID必须是数字或数字字符串',
        'number.base': '部门ID必须是数字或数字字符串'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(正常)或1(停用)'
      }),
      sort: Joi.number().integer().min(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      remark: Joi.string().allow('').max(255).messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过255个字符'
      }),
      menuIds: Joi.array().items(Joi.number().integer().min(1)).default([]).messages({
        'array.base': '菜单ID必须是数组'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证更新角色的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).required().messages({
        'any.required': 'ID不能为空',
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      name: Joi.string().min(2).max(30).messages({
        'string.base': '角色名称必须是字符串',
        'string.min': '角色名称长度不能少于2个字符',
        'string.max': '角色名称长度不能超过30个字符'
      }),
      code: Joi.string().max(100).messages({
        'string.base': '角色编码必须是字符串',
        'string.max': '角色编码长度不能超过100个字符'
      }),
      dataScope: Joi.number().integer().valid(1, 2, 3, 4, 5).messages({
        'number.base': '数据范围必须是数字',
        'number.integer': '数据范围必须是整数',
        'any.only': '数据范围只能是1-5的整数'
      }),
      deptIds: Joi.array().items(
        Joi.alternatives().try(
          Joi.number().integer().min(1),
          Joi.string().pattern(/^[0-9]+$/)
        )
      ).messages({
        'array.base': '部门ID必须是数组',
        'string.pattern.base': '部门ID必须是数字或数字字符串',
        'number.base': '部门ID必须是数字或数字字符串'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(正常)或1(停用)'
      }),
      sort: Joi.number().integer().min(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      remark: Joi.string().allow('').max(255).messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过255个字符'
      }),
      menuIds: Joi.array().items(Joi.number().integer().min(1)).messages({
        'array.base': '菜单ID必须是数组'
      })
    }).min(1).messages({
      'object.min': '至少需要一个更新字段'
    });

    return this.validate(data, schema);
  }

  /**
   * 验证查询角色的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateQuery(data) {
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码不能小于1'
      }),
      pageSize: Joi.number().integer().min(1).max(1000).default(10).messages({
        'number.base': '每页条数必须是数字',
        'number.integer': '每页条数必须是整数',
        'number.min': '每页条数不能小于1',
        'number.max': '每页条数不能超过1000'
      }),
      name: Joi.string().max(30).allow('').messages({
        'string.base': '角色名称必须是字符串',
        'string.max': '角色名称长度不能超过30个字符'
      }),
      status: Joi.number().integer().valid(0, 1).allow('').messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(正常)或1(停用)'
      })
    });

    return this.validate(data, schema);
  }
}

module.exports = SystemRoleDto;

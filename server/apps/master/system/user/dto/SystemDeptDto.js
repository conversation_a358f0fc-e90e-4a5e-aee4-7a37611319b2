const Joi = require('joi');
const BaseDto = require('../../../../../core/dto/BaseDto');

/**
 * 部门数据传输对象
 * 用于验证部门管理接口的请求参数
 */
class SystemDeptDto extends BaseDto {
  /**
   * 验证创建部门的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateCreate(data) {
    const schema = Joi.object({
      name: Joi.string().required().max(50).messages({
        'string.base': '部门名称必须是字符串',
        'string.empty': '部门名称不能为空',
        'string.max': '部门名称长度不能超过50个字符',
        'any.required': '部门名称是必填项'
      }),
      sort: Joi.number().integer().min(0).default(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      leader: Joi.string().max(50).allow(null, '').messages({
        'string.base': '负责人必须是字符串',
        'string.max': '负责人长度不能超过50个字符'
      }),
      phone: Joi.string().pattern(/^1[3-9]\d{9}$/).allow(null, '').messages({
        'string.base': '联系电话必须是字符串',
        'string.pattern.base': '联系电话格式不正确，必须是11位手机号'
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.base': '邮箱必须是字符串',
        'string.email': '邮箱格式不正确'
      }),
      status: Joi.number().integer().valid(0, 1).default(1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(正常)或1(禁用)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证更新部门的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).required().messages({
        'any.required': 'ID不能为空',
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      name: Joi.string().required().max(50).messages({
        'string.base': '部门名称必须是字符串',
        'string.empty': '部门名称不能为空',
        'string.max': '部门名称长度不能超过50个字符',
        'any.required': '部门名称是必填项'
      }),
      sort: Joi.number().integer().min(0).default(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      leader: Joi.string().max(50).allow(null, '').messages({
        'string.base': '负责人必须是字符串',
        'string.max': '负责人长度不能超过50个字符'
      }),
      phone: Joi.string().pattern(/^1[3-9]\d{9}$/).allow(null, '').messages({
        'string.base': '联系电话必须是字符串',
        'string.pattern.base': '联系电话格式不正确，必须是11位手机号'
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.base': '邮箱必须是字符串',
        'string.email': '邮箱格式不正确'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(正常)或1(禁用)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      })
    }).min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    });

    return this.validate(data, schema);
  }

  /**
   * 验证查询部门的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateQuery(data) {
    const schema = Joi.object({
      name: Joi.string().max(50).allow('').messages({
        'string.base': '部门名称必须是字符串',
        'string.max': '部门名称长度不能超过50个字符'
      }),
      leader: Joi.string().max(50).allow('').messages({
        'string.base': '负责人必须是字符串',
        'string.max': '负责人长度不能超过50个字符'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是1(正常)或0(禁用)'
      }),
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '分页页码必须是数字',
        'number.integer': '分页页码必须是整数',
        'number.min': '分页页码不能小于1'
      }),
      pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
        'number.base': '每页数量必须是数字',
        'number.integer': '每页数量必须是整数',
        'number.min': '每页数量不能小于1',
        'number.max': '每页数量不能超过100'
      })
    });

    return this.validate(data, schema);
  }
}

module.exports = SystemDeptDto;

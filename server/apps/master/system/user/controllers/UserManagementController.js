const BaseController = require('../../../../../core/controllers/BaseController');
const UserService = require('../services/UserManagementService');
const SystemUserDto = require('../dto/SystemUserDto');

class UserManagementController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.userService = new UserService(prisma);
  }

  /**
   * 创建用户
   */
  async create(req, res) {
    try {
      // 验证请求数据
      const { error, value } = SystemUserDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message);
      }
      
      console.log('创建用户请求数据:', req.body);
      console.log('验证后的数据:', value);
      
      // 添加创建者信息
      value.created_by = req.user?.id;
      
      // 确保roleId和deptId被正确传递
      if (!value.roleId && req.body.roleId) {
        value.roleId = req.body.roleId;
      }
      
      if (!value.deptId && req.body.deptId) {
        value.deptId = req.body.deptId;
      }
      
      console.log('传递给service的数据:', value);
      
      const user = await this.userService.create(value);
      this.success(res, user, '创建用户成功', 200);
    } catch (err) {
      console.error('创建用户失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新用户
   */
  async update(req, res) {
    try {
      const data = { ...req.body, id: req.params.id };
      const { error, value } = SystemUserDto.validateUpdate(data);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      value.updated_by = req.user?.id;
      const result = await this.userService.update(value.id, value);
      this.success(res, result, '更新用户成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除用户
   */
  async delete(req, res) {
    try {
      // 传入当前操作用户ID作为更新人
      await this.userService.delete(req.params.id, req.user?.id);
      this.success(res, null, '删除用户成功');
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取用户详情
   */
  async getById(req, res) {
    try {
      const user = await this.userService.getById(req.params.id);
      this.success(res, user);
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取用户列表
   */
  async list(req, res) {
    try {
      // 参数校验（包含分页参数）
      const { error, value } = SystemUserDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      // 提取分页参数
      const { page, pageSize, ...filters } = value;
      // 组装分页参数
      const pagination = this.getPagination({ page, pageSize });
      // 调用服务获取用户列表
      const result = await this.userService.list({ ...filters, ...pagination });
      // 使用标准列表响应格式
      this.successList(
        res,
        result.list,
        result.total,
        page,
        pageSize,
        '获取用户列表成功'
      );
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 强制用户下线
   * 管理员功能：使指定用户的所有会话失效
   */
  async forceLogout(req, res) {
    try {
      const userId = req.params.id;
      
      // 验证用户是否存在
      const user = await this.userService.getById(userId);
      if (!user) {
        return this.fail(res, '用户不存在', 404);
      }
      
      // 验证当前用户是否有权限（这里可以添加角色检查逻辑）
      // 例如：if (req.user.role !== 'admin') return this.fail(res, '没有权限执行此操作', 403);
      
      // 引入Redis工具类
      const redisUtil = require('../../../../../core/utils/RedisUtil');
      
      // 执行强制下线
      const result = await redisUtil.forceUserLogout(userId);
      
      if (result) {
        console.log(`管理员${req.user.username}(${req.user.id})强制用户${user.username}(${userId})下线`);
        this.success(res, null, `用户 ${user.username} 已被强制下线`);
      } else {
        this.fail(res, '操作失败，请稍后重试', 500);
      }
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }
}

module.exports = UserManagementController;

const DeptManagementController = require('../../../controllers/DeptManagementController');
const DeptManagementService = require('../../../services/DeptManagementService');

// Mock Prisma
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn()
}));

describe('DeptManagementController', () => {
  let controller;
  let mockPrisma;
  let mockReq;
  let mockRes;

  beforeEach(() => {
    // 初始化 mock
    mockPrisma = {
      systemDept: {
        create: jest.fn(),
        update: jest.fn(),
        findFirst: jest.fn(),
        findMany: jest.fn(),
        count: jest.fn()
      }
    };

    // Mock 请求对象
    mockReq = {
      body: {},
      query: {},
      params: {},
      user: { id: 1 }
    };

    // Mock 响应对象
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // 创建控制器实例
    controller = new DeptManagementController(mockPrisma);
  });

  describe('create', () => {
    it('应该成功创建部门', async () => {
      // 准备测试数据
      const deptData = {
        name: '测试部门',
        parent_id: 0,
        level: '0',
        created_by: 1
      };
      mockReq.body = {
        name: '测试部门',
        parent_id: 0,
        level: '0'
      };
      mockReq.user = { id: 1 };

      // Mock Service 返回值
      mockPrisma.systemDept.create.mockResolvedValue({
        id: 1,
        ...deptData
      });

      // 执行测试
      await controller.create(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 200,
        message: '创建部门成功',
        data: {
          id: 1,
          ...deptData
        }
      });
    });

    it('缺少必填字段时应该返回错误', async () => {
      // 准备测试数据（缺少必填字段）
      mockReq.body = { name: '测试部门' };

      // 执行测试
      await controller.create(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 200,
        message: '缺少必填字段: parent_id, level',
        data: null
      });
    });
  });

  describe('update', () => {
    it('应该成功更新部门', async () => {
      // 准备测试数据
      const deptData = {
        name: '更新后的部门',
        status: 2,
        updated_by: 1
      };
      mockReq.body = {
        name: '更新后的部门',
        status: 2
      };
      mockReq.params = { id: '1' };

      // Mock Service 返回值
      mockPrisma.systemDept.update.mockResolvedValue({
        id: 1,
        ...deptData
      });

      // 执行测试
      await controller.update(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 200,
        message: '更新部门成功',
        data: {
          id: 1,
          ...deptData
        }
      });
    });
  });

  describe('delete', () => {
    it('应该成功删除部门', async () => {
      // 准备测试数据
      mockReq.params = { id: '1' };

      // Mock Service 返回值
      mockPrisma.systemDept.update.mockResolvedValue({
        id: 1,
        deleted_at: Date.now()
      });

      // 执行测试
      await controller.delete(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 200,
        message: '删除部门成功',
        data: expect.objectContaining({
          id: 1
        })
      });
    });
  });

  describe('list', () => {
    it('应该返回分页的部门列表', async () => {
      // 准备测试数据
      mockReq.query = { page: 1, pageSize: 10 };

      // Mock Service 返回值
      const mockItems = [
        { id: 1, name: '部门1' },
        { id: 2, name: '部门2' }
      ];
      mockPrisma.systemDept.findMany.mockResolvedValue(mockItems);
      mockPrisma.systemDept.count.mockResolvedValue(2);

      // 执行测试
      await controller.list(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 200,
        message: '操作成功',
        data: {
          list: mockItems,
          pagination: {
            total: 2,
            page: 1,
            pageSize: 10,
            totalPages: 1
          }
        }
      });
    });
  });

  describe('detail', () => {
    it('应该返回部门详情', async () => {
      // 准备测试数据
      mockReq.params = { id: '1' };

      // Mock Service 返回值
      const mockDept = {
        id: 1,
        name: '测试部门'
      };
      mockPrisma.systemDept.findFirst.mockResolvedValue(mockDept);

      // 执行测试
      await controller.detail(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 200,
        message: '获取部门详情成功',
        data: mockDept
      });
    });

    it('部门不存在时应该返回404错误', async () => {
      // 准备测试数据
      mockReq.params = { id: '999' };

      // Mock Service 返回值
      mockPrisma.systemDept.findFirst.mockResolvedValue(null);

      // 执行测试
      await controller.detail(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 404,
        message: '部门不存在',
        data: null
      });
    });
  });

  describe('tree', () => {
    it('应该返回部门树结构', async () => {
      // Mock Service 返回值
      const mockDepts = [
        { id: 1, name: '部门1', parent_id: BigInt(0) },
        { id: 2, name: '部门2', parent_id: BigInt(1) }
      ];
      mockPrisma.systemDept.findMany.mockResolvedValue(mockDepts);

      // 执行测试
      await controller.tree(mockReq, mockRes);

      // 验证结果
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 200,
        message: '获取部门树结构成功',
        data: [
          {
            id: 1,
            name: '部门1',
            parent_id: BigInt(0),
            children: [
              {
                id: 2,
                name: '部门2',
                parent_id: BigInt(1),
                children: []
              }
            ]
          }
        ]
      });
    });
  });
});

/**
 * AuthController 加密密码处理测试
 */
const AuthController = require('../../../controllers/AuthController');
const encryptionUtil = require('../../../../../../../core/utils/EncryptionUtil');

// 模拟依赖
jest.mock('../../../services/UserManagementService');
jest.mock('../../../../../../../core/services/CaptchaService');
jest.mock('../../../../../../../core/utils/EncryptionUtil');

const UserService = require('../../../services/UserManagementService');
const CaptchaService = require('../../../../../../../core/services/CaptchaService');

describe('AuthController - 密码加密', () => {
  let controller;
  let mockService;
  let mockCaptchaService;
  let mockReq;
  let mockRes;
  
  beforeEach(() => {
    // 清理所有 mock
    jest.clearAllMocks();
    
    // 初始化 mock services
    mockService = {
      login: jest.fn()
    };
    mockCaptchaService = {
      verifyCaptcha: jest.fn(),
      isIpLocked: jest.fn(),
      incrementIpAttempts: jest.fn()
    };
    
    UserService.mockImplementation(() => mockService);
    CaptchaService.mockImplementation(() => mockCaptchaService);
    
    // 创建被测试的控制器实例
    controller = new AuthController({});
    
    // 模拟请求和响应对象
    mockReq = {
      body: {},
      headers: {
        'x-forwarded-for': '127.0.0.1'
      }
    };
    
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    // 模拟控制器基类方法
    controller.success = jest.fn();
    controller.fail = jest.fn();
    controller.handleDbError = jest.fn().mockReturnValue({
      message: '测试错误',
      code: 200
    });
    
    // 模拟验证码验证通过
    mockCaptchaService.verifyCaptcha.mockResolvedValue(true);
    mockCaptchaService.isIpLocked.mockResolvedValue(false);
  });
  
  describe('login() - 处理加密密码', () => {
    it('应该正确处理加密密码', async () => {
      // 准备
      const mockCredentials = {
        username: 'admin',
        password: 'encrypted-password-123', // 加密的密码
        encrypted: true, // 标记密码已加密
        captcha: '1234',
        captchaId: 'test_captcha_id'
      };
      
      const mockDecryptedPassword = 'password123'; // 解密后的密码
      
      const mockLoginResult = {
        token: 'mock_token_123',
        user: {
          id: 1,
          username: 'admin',
          nickname: '管理员'
        }
      };
      
      mockReq.body = mockCredentials;
      
      // 模拟解密操作
      encryptionUtil.decrypt.mockReturnValue(mockDecryptedPassword);
      
      // 模拟登录成功
      mockService.login.mockResolvedValue(mockLoginResult);
      
      // 执行
      await controller.login(mockReq, mockRes);
      
      // 验证
      expect(encryptionUtil.decrypt).toHaveBeenCalledWith(mockCredentials.password);
      expect(mockService.login).toHaveBeenCalledWith(
        mockCredentials.username,
        mockDecryptedPassword // 应该用解密后的密码进行登录验证
      );
      expect(controller.success).toHaveBeenCalledWith(
        mockRes,
        mockLoginResult,
        '登录成功'
      );
    });
    
    it('应该处理未加密的密码（向下兼容）', async () => {
      // 准备
      const mockCredentials = {
        username: 'admin',
        password: 'plain-password-123', // 明文密码
        // encrypted 标志不存在，表示未加密
        captcha: '1234',
        captchaId: 'test_captcha_id'
      };
      
      const mockLoginResult = {
        token: 'mock_token_123',
        user: {
          id: 1,
          username: 'admin',
          nickname: '管理员'
        }
      };
      
      mockReq.body = mockCredentials;
      
      // 模拟登录成功
      mockService.login.mockResolvedValue(mockLoginResult);
      
      // 执行
      await controller.login(mockReq, mockRes);
      
      // 验证
      expect(encryptionUtil.decrypt).not.toHaveBeenCalled(); // 不应调用解密
      expect(mockService.login).toHaveBeenCalledWith(
        mockCredentials.username,
        mockCredentials.password // 应该直接使用明文密码
      );
      expect(controller.success).toHaveBeenCalledWith(
        mockRes,
        mockLoginResult,
        '登录成功'
      );
    });
    
    it('应该处理密码解密失败的情况', async () => {
      // 准备
      const mockCredentials = {
        username: 'admin',
        password: 'invalid-encrypted-password', // 无效的加密密码
        encrypted: true, // 标记密码已加密
        captcha: '1234',
        captchaId: 'test_captcha_id'
      };
      
      mockReq.body = mockCredentials;
      
      // 模拟解密失败
      encryptionUtil.decrypt.mockImplementation(() => {
        throw new Error('解密失败');
      });
      
      // 执行
      await controller.login(mockReq, mockRes);
      
      // 验证
      expect(encryptionUtil.decrypt).toHaveBeenCalledWith(mockCredentials.password);
      expect(mockService.login).not.toHaveBeenCalled(); // 不应尝试登录
      expect(controller.fail).toHaveBeenCalledWith(
        mockRes,
        '密码格式错误',
        200
      );
    });
  });
});

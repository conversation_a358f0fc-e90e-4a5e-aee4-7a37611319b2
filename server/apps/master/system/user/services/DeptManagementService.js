const { PrismaClient } = require('@prisma/client');
const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

class DeptManagementService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 创建部门
   * @param {Object} data 部门数据
   * @returns {Promise<Object>} 创建的部门信息
   */
  async create(data) {
    // 获取当前时间戳
    const currentTime = BigInt(Date.now());
    
    // 将驼峰命名转换为下划线命名
    const formattedData = {
      id: generateSnowflakeId(),
      name: data.name,
      leader: data.leader,
      phone: data.phone,
      status: data.status !== undefined ? data.status : 1,
      sort: data.sort !== undefined ? data.sort : 0,
      remark: data.remark,
      created_at: currentTime,
      updated_at: currentTime,
      // 添加创建人ID
      created_by: data.created_by ? BigInt(data.created_by) : null
    };

    return await this.prisma.baseSystemDept.create({
      data: formattedData
    });
  }

  /**
   * 更新部门
   * @param {number} id 部门ID
   * @param {Object} data 部门数据
   * @returns {Promise<Object>} 更新后的部门信息
   */
  async update(id, data) {
    // 检查部门是否存在
    const existingDept = await this.prisma.baseSystemDept.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingDept) {
      throw new Error('部门不存在');
    }

    // 获取当前时间戳
    const currentTime = BigInt(Date.now());
    
    // 构造更新数据
    const updateData = {
      updated_at: currentTime
    };
    
    // 处理可选字段的更新
    if (data.name !== undefined) updateData.name = data.name;
    if (data.leader !== undefined) updateData.leader = data.leader;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.sort !== undefined) updateData.sort = data.sort;
    if (data.remark !== undefined) updateData.remark = data.remark;
    
    // 添加更新人ID
    if (data.updated_by) {
      updateData.updated_by = BigInt(data.updated_by);
    }

    return await this.prisma.baseSystemDept.update({
      where: { id: BigInt(id) },
      data: updateData
    });
  }

  /**
   * 删除部门（软删除）
   * @param {number} id 部门ID
   * @param {number|string} updatedBy 更新人ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(id, updatedBy) {
    // 检查部门是否存在
    const existingDept = await this.prisma.baseSystemDept.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingDept) {
      throw new Error('部门不存在');
    }

    // 获取当前时间戳
    const currentTime = BigInt(Date.now());

    // 软删除：更新deleted_at字段
    return await this.prisma.baseSystemDept.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: currentTime,
        updated_at: currentTime,
        // 添加更新人ID（执行删除操作的用户）
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });
  }

  /**
   * 获取部门列表
   * @param {Object} query 查询参数
   * @returns {Promise<Object>} 部门列表和总数
   */
  async list(query = {}) {
    const { name, leader, status, page = 1, pageSize = 10 } = query;
    
    const where = {
      deleted_at: null
    };
    
    // 按名称模糊查询
    if (name !== undefined && name !== '') {
      where.name = { contains: name };
    }
    
    // 按负责人模糊查询
    if (leader !== undefined && leader !== '') {
      where.leader = { contains: leader };
    }
    
    // 按状态筛选
    if (status !== undefined && status !== '') {
      where.status = parseInt(status);
    }

    const [total, items] = await Promise.all([
      this.prisma.baseSystemDept.count({ where }),
      this.prisma.baseSystemDept.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: [
          { sort: 'asc' },
          { id: 'desc' }
        ]
      })
    ]);

    return { 
      list: items, 
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  }

  /**
   * 获取部门详情
   * @param {number} id 部门ID
   * @returns {Promise<Object>} 部门详情
   */
  async detail(id) {
    const dept = await this.prisma.baseSystemDept.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!dept) {
      throw new Error('部门不存在');
    }

    return dept;
  }
}

module.exports = DeptManagementService;

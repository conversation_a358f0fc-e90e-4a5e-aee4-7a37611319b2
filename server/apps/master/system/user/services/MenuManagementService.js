/**
 * 菜单管理服务
 * 负责菜单的CRUD操作和树形结构管理
 */
class MenuManagementService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 创建菜单
   * @param {Object} data 菜单数据
   * @returns {Promise<Object>} 创建的菜单信息
   */
  async create(data) {
    const currentTime = BigInt(Date.now());
    
    // 检查同级菜单中是否已存在相同名称
    const existingMenu = await this.prisma.baseSystemMenu.findFirst({
      where: {
        name: data.name,
        parent_id: BigInt(data.parent_id || 0),
        deleted_at: null
      }
    });

    if (existingMenu) {
      throw new Error('同级菜单下已存在相同名称的菜单');
    }

    // 检查菜单代码是否已存在
    const existingCode = await this.prisma.baseSystemMenu.findFirst({
      where: {
        code: data.code,
        deleted_at: null
      }
    });

    if (existingCode) {
      throw new Error('菜单标识代码已存在');
    }

    // 验证字段长度
    if (data.name && data.name.length > 50) {
      throw new Error('菜单名称长度不能超过50个字符');
    }

    if (data.code && data.code.length > 100) {
      throw new Error('菜单标识代码长度不能超过100个字符');
    }

    if (data.icon && data.icon.length > 50) {
      throw new Error('菜单图标长度不能超过50个字符');
    }

    if (data.route && data.route.length > 200) {
      throw new Error('路由地址长度不能超过200个字符');
    }

    if (data.component && data.component.length > 255) {
      throw new Error('组件路径长度不能超过255个字符');
    }

    if (data.redirect && data.redirect.length > 255) {
      throw new Error('跳转地址长度不能超过255个字符');
    }

    if (data.type && data.type.length > 1) {
      throw new Error('菜单类型只能是单个字符(M菜单 B按钮 L链接 I iframe)');
    }

    if (data.remark && data.remark.length > 255) {
      throw new Error('备注长度不能超过255个字符');
    }

    // 将字段转换为Prisma模型匹配的格式
    const formattedData = {
      parent_id: BigInt(data.parent_id || 0),
      level: data.level,
      name: data.name,
      code: data.code,
      icon: data.icon,
      route: data.route,
      component: data.component,
      redirect: data.redirect,
      is_hidden: data.is_hidden !== undefined ? data.is_hidden : 2, // 默认显示(2)
      type: data.type,
      status: data.status !== undefined ? data.status : 1, // 默认启用(1)
      sort: data.sort !== undefined ? data.sort : 0,
      remark: data.remark,
      created_by: data.created_by ? BigInt(data.created_by) : null,
      updated_by: null,
      created_at: currentTime,
      updated_at: currentTime
    };

    return await this.prisma.baseSystemMenu.create({
      data: formattedData
    });
  }

  /**
   * 更新菜单
   * @param {number} id 菜单ID
   * @param {Object} data 菜单数据
   * @returns {Promise<Object>} 更新后的菜单信息
   */
  async update(id, data) {
    // 检查菜单是否存在
    const existingMenu = await this.prisma.baseSystemMenu.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingMenu) {
      throw new Error('菜单不存在');
    }

    // 如果更改菜单名称，检查同级是否有重名
    if (data.name && data.name !== existingMenu.name) {
      const parentId = data.parent_id !== undefined ? BigInt(data.parent_id) : existingMenu.parent_id;
      const sameLevelMenu = await this.prisma.baseSystemMenu.findFirst({
        where: {
          name: data.name,
          parent_id: parentId,
          id: { not: BigInt(id) },
          deleted_at: null
        }
      });

      if (sameLevelMenu) {
        throw new Error('同级菜单下已存在相同名称的菜单');
      }
    }

    // 如果更改菜单代码，检查是否有重复
    if (data.code && data.code !== existingMenu.code) {
      const existingCode = await this.prisma.baseSystemMenu.findFirst({
        where: {
          code: data.code,
          id: { not: BigInt(id) },
          deleted_at: null
        }
      });

      if (existingCode) {
        throw new Error('菜单标识代码已存在');
      }
    }

    // 验证字段长度
    if (data.name && data.name.length > 50) {
      throw new Error('菜单名称长度不能超过50个字符');
    }

    if (data.code && data.code.length > 100) {
      throw new Error('菜单标识代码长度不能超过100个字符');
    }

    if (data.icon && data.icon.length > 50) {
      throw new Error('菜单图标长度不能超过50个字符');
    }

    if (data.route && data.route.length > 200) {
      throw new Error('路由地址长度不能超过200个字符');
    }

    if (data.component && data.component.length > 255) {
      throw new Error('组件路径长度不能超过255个字符');
    }

    if (data.redirect && data.redirect.length > 255) {
      throw new Error('跳转地址长度不能超过255个字符');
    }

    if (data.type && data.type.length > 1) {
      throw new Error('菜单类型只能是单个字符(M菜单 B按钮 L链接 I iframe)');
    }

    if (data.remark && data.remark.length > 255) {
      throw new Error('备注长度不能超过255个字符');
    }

    // 构造更新数据
    const updateData = {
      updated_at: BigInt(Date.now())
    };

    // 处理可选字段
    if (data.parent_id !== undefined) updateData.parent_id = BigInt(data.parent_id);
    if (data.level !== undefined) updateData.level = data.level;
    if (data.name !== undefined) updateData.name = data.name;
    if (data.code !== undefined) updateData.code = data.code;
    if (data.icon !== undefined) updateData.icon = data.icon;
    if (data.route !== undefined) updateData.route = data.route;
    if (data.component !== undefined) updateData.component = data.component;
    if (data.redirect !== undefined) updateData.redirect = data.redirect;
    if (data.is_hidden !== undefined) updateData.is_hidden = data.is_hidden;
    if (data.type !== undefined) updateData.type = data.type;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.sort !== undefined) updateData.sort = data.sort;
    if (data.remark !== undefined) updateData.remark = data.remark;
    // 确保将 updated_by 转换为 BigInt 类型
    if (data.updated_by !== undefined) updateData.updated_by = data.updated_by ? BigInt(data.updated_by) : null;

    return await this.prisma.baseSystemMenu.update({
      where: { id: BigInt(id) },
      data: updateData
    });
  }

  /**
   * 删除菜单（软删除）
   * @param {number} id 菜单ID
   * @param {number|string} updatedBy 更新人ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(id, updatedBy) {
    // 检查菜单是否存在
    const existingMenu = await this.prisma.baseSystemMenu.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingMenu) {
      throw new Error('菜单不存在');
    }

    // 检查是否有子菜单
    const childMenus = await this.prisma.baseSystemMenu.findFirst({
      where: {
        parent_id: BigInt(id),
        deleted_at: null
      }
    });

    if (childMenus) {
      throw new Error('该菜单下存在子菜单，请先删除子菜单');
    }

    // 获取当前时间戳
    const currentTime = BigInt(Date.now());

    // 软删除：更新deleted_at字段
    return await this.prisma.baseSystemMenu.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: currentTime,
        updated_at: currentTime,
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });
  }

  /**
   * 获取菜单列表
   * @param {Object} query 查询参数
   * @returns {Promise<Object>} 菜单列表和总数
   */
  async list(query = {}) {
    try {
      console.log('开始获取菜单列表，查询参数:', query);
      const { keyword, status, type, page = 1, pageSize = 10 } = query;
      
      const where = {
        deleted_at: null,
        ...(keyword && {
          OR: [
            { name: { contains: keyword } },
            { code: { contains: keyword } },
            { route: { contains: keyword } }
          ]
        }),
        // 只有当status是有效数字时才添加到查询条件中
        ...(status !== undefined && status !== '' && !isNaN(parseInt(status)) && { 
          status: parseInt(status) 
        }),
        // 只有当type是非空字符串时才添加到查询条件中
        ...(type !== undefined && type !== '' && { type })
      };
      
      // 不直接使用JSON.stringify，因为BigInt不能被序列化
      console.log('查询条件:', Object.entries(where).reduce((acc, [k, v]) => {
        acc[k] = v === null ? null : String(v);
        return acc;
      }, {}));

      const [total, items] = await Promise.all([
        this.prisma.baseSystemMenu.count({ where }),
        this.prisma.baseSystemMenu.findMany({
          where,
          skip: (page - 1) * pageSize,
          take: pageSize,
          orderBy: [
            { sort: 'asc' },
            { id: 'desc' }
          ]
        })
      ]);
      
      console.log(`查询结果: 总数=${total}, 条目数=${items.length}`);
      if (total === 0) {
        // 尝试直接查询所有菜单，不带任何条件
        const allMenus = await this.prisma.baseSystemMenu.findMany({
          take: 5 // 只取前5条作为示例
        });
        
        // 特殊处理BigInt类型数据，将其转换为字符串
        const serializableMenus = allMenus.map(menu => {
          return Object.fromEntries(
            Object.entries(menu).map(([key, value]) => {
              // 如果是BigInt类型，转为字符串
              if (typeof value === 'bigint') {
                return [key, value.toString()];
              }
              return [key, value];
            })
          );
        });
        
        console.log('数据库中所有菜单数据示例:', JSON.stringify(serializableMenus));
      }

      return { 
        list: items, 
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };
    } catch (error) {
      console.error('获取菜单列表出错:', error);
      throw error;
    }
  }

  /**
   * 获取菜单详情
   * @param {number} id 菜单ID
   * @returns {Promise<Object>} 菜单详情
   */
  async detail(id) {
    const menu = await this.prisma.baseSystemMenu.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!menu) {
      throw new Error('菜单不存在');
    }

    return menu;
  }

  /**
   * 获取菜单树形结构
   * @returns {Promise<Array>} 菜单树形结构
   */
  async tree() {
    const allMenus = await this.prisma.baseSystemMenu.findMany({
      where: { 
        deleted_at: null
      },
      orderBy: [
        { sort: 'asc' },
        { id: 'asc' }
      ]
    });

    // 构建树形结构
    const buildTree = (items, parentId = 0) => {
      const result = [];
      
      for (const item of items) {
        if (item.parent_id.toString() === parentId.toString()) {
          const children = buildTree(items, item.id);
          if (children.length > 0) {
            item.children = children;
          }
          result.push(item);
        }
      }
      
      return result;
    };

    return buildTree(allMenus);
  }

  /**
   * 获取用户菜单权限
   * @param {number} userId 用户ID
   * @returns {Promise<Array>} 用户有权限的菜单列表
   */
  async getUserMenus(userId) {
    // 根据实际业务需求实现
    // 这里只是一个示例，实际可能需要查询用户角色和角色菜单关联
    return await this.tree();
  }
}

module.exports = MenuManagementService;

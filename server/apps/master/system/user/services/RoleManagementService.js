const { PrismaClient } = require('@prisma/client');
const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

/**
 * 角色管理服务
 * 处理角色的增删改查等业务逻辑
 */
class RoleManagementService {
  constructor(prisma) {
    this.prisma = prisma;
    this.roleModel = this.prisma.baseSystemRole;
  }

  /**
   * 获取角色列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页条数
   * @param {string} params.name 角色名称（模糊查询）
   * @param {number} params.status 状态筛选
   * @returns {Promise<Object>} 角色列表分页数据
   */
  async list({ page = 1, pageSize = 10, name, status }) {
    console.log('服务层接收到的查询参数:', { page, pageSize, name, status });
    
    // 构造查询条件
    const whereCondition = {
      deleted_at: null
    };

    // 添加名称模糊查询条件
    if (name) {
      whereCondition.name = { contains: name };
    }

    // 添加状态筛选条件
    if (status !== undefined && status !== '') {
      whereCondition.status = Number(status);
    }
    
    console.log('构造的查询条件:', JSON.stringify(whereCondition));

    // 计算分页偏移量
    const skip = (page - 1) * pageSize;

    // 并行查询总数和列表数据
    const [total, items] = await Promise.all([
      this.roleModel.count({ where: whereCondition }),
      this.roleModel.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy: [
          { sort: 'asc' },
          { created_at: 'desc' }
        ]
      })
    ]);
    
    console.log(`查询到${items.length}条角色记录，总数:${total}`);

    // 返回分页数据
    return {
      items,
      pagination: {
        total,
        page,
        pageSize
      }
    };
  }

  /**
   * 获取角色详情
   * @param {string|number} id 角色ID
   * @returns {Promise<Object>} 角色详情或错误信息
   */
  async getById(id) {
    const roleId = BigInt(id);

    const role = await this.roleModel.findFirst({
      where: {
        id: roleId,
        deleted_at: null
      }
    });

    if (!role) {
      return {
        success: false,
        code: 404,
        message: '角色不存在'
      };
    }

    return {
      success: true,
      data: role
    };
  }

  /**
   * 创建角色
   * @param {Object} data 角色数据
   * @returns {Promise<Object>} 创建的角色信息或错误信息
   */
  async create(data) {
    // 检查角色名称唯一性
    const existingRoleByName = await this.roleModel.findFirst({
      where: {
        name: data.name,
        deleted_at: null
      }
    });

    if (existingRoleByName) {
      return {
        success: false,
        code: 200,
        message: `角色名称 ${data.name} 已存在`
      };
    }

    // 获取当前时间戳
    const now = BigInt(Date.now());

    // 创建角色
    const role = await this.roleModel.create({
      data: {
        ...data,
        // 使用雪花算法生成ID
        id: BigInt(generateSnowflakeId()),
        // 确保 created_by 为 BigInt 类型
        created_by: data.created_by ? BigInt(data.created_by) : null,
        created_at: now,
        updated_at: now
      }
    });

    return {
      success: true,
      data: role
    };
  }

  /**
   * 更新角色
   * @param {string|number} id 角色ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的角色信息或错误信息
   */
  async update(id, data) {
    const roleId = BigInt(id);

    // 查询角色是否存在
    const role = await this.roleModel.findFirst({
      where: {
        id: roleId,
        deleted_at: null
      }
    });

    if (!role) {
      return {
        success: false,
        code: 404,
        message: '角色不存在'
      };
    }
    
    // 检查是否为超级管理员角色，禁止编辑
    if (role.name === '超级管理员') {
      return {
        success: false,
        code: 200,
        message: '超级管理员角色不允许编辑'
      };
    }

    // 如果要更新角色名称，需要检查名称唯一性
    if (data.name && data.name !== role.name) {
      const existingRoleByName = await this.roleModel.findFirst({
        where: {
          name: data.name,
          deleted_at: null,
          id: { not: roleId }
        }
      });

      if (existingRoleByName) {
        return {
          success: false,
          code: 200,
          message: `角色名称 ${data.name} 已存在`
        };
      }
    }

    // 更新角色
    const updatedRole = await this.roleModel.update({
      where: { id: roleId },
      data: {
        ...data,
        updated_by: data.updated_by ? BigInt(data.updated_by) : null,
        updated_at: BigInt(Date.now())
      }
    });

    return {
      success: true,
      data: updatedRole
    };
  }

  /**
   * 删除角色(软删除)
   * @param {string|number} id 角色ID
   * @returns {Promise<Object>} 删除结果或错误信息
   */
  async delete(id) {
    const roleId = BigInt(id);

    // 检查角色是否存在
    const role = await this.roleModel.findFirst({
      where: {
        id: roleId,
        deleted_at: null
      }
    });

    if (!role) {
      return {
        success: false,
        code: 404,
        message: '角色不存在'
      };
    }
    
    // 检查是否为超级管理员角色，禁止删除
    if (role.name === '超级管理员') {
      return {
        success: false,
        code: 200,
        message: '超级管理员角色不允许删除'
      };
    }

    // 检查是否有关联的用户（未软删除）
    // 注意：role_id 字段为 BigInt，deleted_at 需为 null
    const userCount = await this.prisma.baseSystemUser.count({
      where: {
        role_id: roleId,
        deleted_at: null
      }
    });
    console.log('[角色删除校验] 角色ID:', roleId.toString(), '未删除用户数:', userCount);
    if (userCount > 0) {
      return {
        success: false,
        code: 200,
        message: '该角色下存在未删除的用户，无法删除！'
      };
    }

    // 软删除角色
    await this.roleModel.update({
      where: { id: roleId },
      data: {
        deleted_at: BigInt(Date.now())
      }
    });

    return {
      success: true,
      message: '角色删除成功'
    };
  }

  /**
   * 获取所有可用角色（用于下拉选择）
   * @returns {Promise<Array>} 角色列表
   */
  async getOptions() {
    // 查询所有可用的角色
    const roles = await this.roleModel.findMany({
      where: {
        status: 1, // 正常状态
        deleted_at: null
      },
      select: {
        id: true,
        name: true
      },
      orderBy: [
        { sort: 'asc' },
        { created_at: 'desc' }
      ]
    });

    return roles;
  }

  /**
   * 创建角色及其菜单权限（事务）
   * @param {Object} roleData 角色基本信息
   * @param {string[]} menu_ids 菜单ID数组
   * @param {string[]} dept_ids 部门ID数组 (用于数据权限)
   */
  async createRoleWithMenus(roleData, menu_ids, dept_ids = []) {
    const RoleMenuService = require('./RoleMenuService');
    const RoleDeptService = require('./RoleDeptService');
    const roleMenuService = new RoleMenuService();
    const roleDeptService = new RoleDeptService();
    
    return await this.prisma.$transaction(async (tx) => {
      // 1. 生成角色ID
      const roleId = BigInt(generateSnowflakeId());
      const now = BigInt(Date.now());
      
      // 2. 创建角色基本信息
      // 只保留 baseSystemRole 表允许的字段（去除 dept_ids、menu_ids 等无关字段）
      const {
        dept_ids: _dept_ids, // eslint-disable-line
        menu_ids: _menu_ids, // eslint-disable-line
        id, // eslint-disable-line
        ...roleFields
      } = roleData;
      await tx.baseSystemRole.create({
        data: {
          ...roleFields,
          id: roleId,
          created_by: roleFields.created_by ? BigInt(roleFields.created_by) : null,
          created_at: now,
          updated_at: now,
          updated_by: null,
          deleted_at: null
        }
      });
      // 3. 批量插入菜单关联
      if (Array.isArray(menu_ids) && menu_ids.length > 0) {
        await roleMenuService.resetMenusOfRoleTx(tx, roleId, menu_ids, now);
      }
      // 4. 批量插入部门关联（用于数据权限）
      if (Array.isArray(dept_ids) && dept_ids.length > 0) {
        await roleDeptService.resetDeptsOfRoleTx(tx, roleId, dept_ids, now);
      }
      return roleId.toString();
    });
  }

  /**
   * 编辑角色及其菜单权限（事务）
   * @param {string} roleId 角色ID
   * @param {Object} roleData 角色基本信息
   * @param {string[]} menu_ids 菜单ID数组
   * @param {string[]} dept_ids 部门ID数组 (用于数据权限)
   */
  async updateRoleWithMenus(roleId, roleData, menu_ids, dept_ids = []) {
    // 检查是否为超级管理员角色
    const role = await this.roleModel.findUnique({
      where: { id: BigInt(roleId) }
    });
    
    if (role && role.name === '超级管理员') {
      return {
        success: false,
        code: 200,
        message: '超级管理员角色不允许修改'
      };
    }
    
    const RoleMenuService = require('./RoleMenuService');
    const RoleDeptService = require('./RoleDeptService');
    const roleMenuService = new RoleMenuService();
    const roleDeptService = new RoleDeptService();
    
    return await this.prisma.$transaction(async (tx) => {
      const now = BigInt(Date.now());
      // 1. 更新角色
      // 只保留 baseSystemRole 表允许的字段（去除 dept_ids、menu_ids 等无关字段）
      const {
        dept_ids: _dept_ids, // eslint-disable-line
        menu_ids: _menu_ids, // eslint-disable-line
        id, // eslint-disable-line
        ...roleFields
      } = roleData;
      await tx.baseSystemRole.update({
        where: { id: BigInt(roleId) },
        data: {
          ...roleFields,
          updated_at: now,
          updated_by: roleFields.updated_by ? BigInt(roleFields.updated_by) : null
        }
      });
      // 2. 删除旧关联并批量插入新关联
      await roleMenuService.resetMenusOfRoleTx(tx, roleId, menu_ids || [], now);
      
      // 3. 处理部门数据权限关联
      // 仅当数据范围为"自定义数据权限"(2)时才处理部门关联
      if (roleFields.data_scope === 2 && Array.isArray(dept_ids)) {
        await roleDeptService.resetDeptsOfRoleTx(tx, roleId, dept_ids, now);
      } else if (roleFields.data_scope !== 2) {
        // 如果数据范围不是自定义权限，则删除所有部门关联
        await roleDeptService.resetDeptsOfRoleTx(tx, roleId, [], now);
      }
      
      return true;
    });
  }
}

module.exports = RoleManagementService;

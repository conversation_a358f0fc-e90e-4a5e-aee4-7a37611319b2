/**
 * 阿里云邮件服务类
 * 使用阿里云DirectMail服务发送邮件
 */
const Core = require('@alicloud/pop-core');
const BaseEmailService = require('./BaseEmailService');

class AliyunEmailService extends BaseEmailService {
  /**
   * 构造函数
   * @param {Object} config 配置对象
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(config, prisma) {
    super(config, prisma);
    this.client = null;
  }

  /**
   * 初始化阿里云客户端
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      const config = await this.getConfig();
      
      // 创建阿里云客户端
      this.client = new Core({
        accessKeyId: config.accessKeyId,
        accessKeySecret: config.accessKeySecret,
        endpoint: 'https://dm.aliyuncs.com',
        apiVersion: '2015-11-23'
      });
      
      console.log('阿里云邮件客户端初始化成功');
    } catch (error) {
      console.error('阿里云邮件客户端初始化失败:', error);
      throw new Error(`阿里云邮件客户端初始化失败: ${error.message}`);
    }
  }

  /**
   * 获取阿里云客户端
   * @returns {Promise<Object>} 阿里云客户端
   */
  async getClient() {
    if (!this.client) {
      await this.initialize();
    }
    return this.client;
  }

  /**
   * 获取默认发件人
   * @returns {Promise<Object>} 默认发件人
   */
  async getDefaultFrom() {
    const config = await this.getConfig();
    return {
      accountName: config.accountName,
      fromAlias: config.fromAlias || ''
    };
  }

  /**
   * 发送简单文本邮件
   * @param {Object} options 邮件选项
   * @param {String|Array} options.to 收件人邮箱，可以是字符串或数组
   * @param {String} options.subject 邮件主题
   * @param {String} options.text 邮件文本内容
   * @param {Object} options.from 发件人信息，可选，默认使用配置中的发件人
   * @returns {Promise<Object>} 发送结果
   */
  async sendText(options) {
    try {
      const client = await this.getClient();
      const defaultFrom = await this.getDefaultFrom();
      
      // 处理收件人，如果是数组则转为逗号分隔的字符串
      const toAddress = Array.isArray(options.to) ? options.to.join(',') : options.to;
      
      const params = {
        AccountName: options.from?.accountName || defaultFrom.accountName,
        FromAlias: options.from?.fromAlias || defaultFrom.fromAlias,
        AddressType: 1, // 0: 随机账号，1: 发信地址
        TagName: options.tagName || '',
        ReplyToAddress: true,
        ToAddress: toAddress,
        Subject: options.subject,
        TextBody: options.text
      };
      
      const result = await client.request('SingleSendMail', params, {
        method: 'POST'
      });
      
      return {
        success: true,
        requestId: result.RequestId,
        envId: result.EnvId
      };
    } catch (error) {
      console.error('发送文本邮件失败:', error);
      throw new Error(`发送文本邮件失败: ${error.message}`);
    }
  }

  /**
   * 发送HTML邮件
   * @param {Object} options 邮件选项
   * @param {String|Array} options.to 收件人邮箱，可以是字符串或数组
   * @param {String} options.subject 邮件主题
   * @param {String} options.html HTML内容
   * @param {Object} options.from 发件人信息，可选，默认使用配置中的发件人
   * @returns {Promise<Object>} 发送结果
   */
  async sendHtml(options) {
    try {
      const client = await this.getClient();
      const defaultFrom = await this.getDefaultFrom();
      
      // 处理收件人，如果是数组则转为逗号分隔的字符串
      const toAddress = Array.isArray(options.to) ? options.to.join(',') : options.to;
      
      const params = {
        AccountName: options.from?.accountName || defaultFrom.accountName,
        FromAlias: options.from?.fromAlias || defaultFrom.fromAlias,
        AddressType: 1, // 0: 随机账号，1: 发信地址
        TagName: options.tagName || '',
        ReplyToAddress: true,
        ToAddress: toAddress,
        Subject: options.subject,
        HtmlBody: options.html
      };
      
      const result = await client.request('SingleSendMail', params, {
        method: 'POST'
      });
      
      return {
        success: true,
        requestId: result.RequestId,
        envId: result.EnvId
      };
    } catch (error) {
      console.error('发送HTML邮件失败:', error);
      throw new Error(`发送HTML邮件失败: ${error.message}`);
    }
  }

  /**
   * 发送带附件的邮件
   * @param {Object} options 邮件选项
   * @param {String|Array} options.to 收件人邮箱，可以是字符串或数组
   * @param {String} options.subject 邮件主题
   * @param {String} options.text 邮件文本内容，可选
   * @param {String} options.html HTML内容，可选
   * @param {Array} options.attachments 附件列表
   * @param {Object} options.from 发件人信息，可选，默认使用配置中的发件人
   * @returns {Promise<Object>} 发送结果
   */
  async sendWithAttachments(options) {
    try {
      // 阿里云DirectMail API不直接支持附件发送
      // 需要使用批量发信API，并且需要先上传附件
      // 这里简化处理，提示不支持
      throw new Error('阿里云DirectMail API不直接支持附件发送，请使用SMTP服务发送带附件的邮件');
    } catch (error) {
      console.error('发送带附件的邮件失败:', error);
      throw new Error(`发送带附件的邮件失败: ${error.message}`);
    }
  }

  /**
   * 发送模板邮件
   * @param {Object} options 邮件选项
   * @param {String|Array} options.to 收件人邮箱，可以是字符串或数组
   * @param {String} options.subject 邮件主题
   * @param {String} options.templateId 模板ID
   * @param {Object} options.templateData 模板数据
   * @param {Object} options.from 发件人信息，可选，默认使用配置中的发件人
   * @returns {Promise<Object>} 发送结果
   */
  async sendTemplate(options) {
    try {
      const client = await this.getClient();
      const defaultFrom = await this.getDefaultFrom();
      
      // 处理收件人，如果是数组则转为逗号分隔的字符串
      const toAddress = Array.isArray(options.to) ? options.to.join(',') : options.to;
      
      const params = {
        AccountName: options.from?.accountName || defaultFrom.accountName,
        FromAlias: options.from?.fromAlias || defaultFrom.fromAlias,
        AddressType: 1, // 0: 随机账号，1: 发信地址
        TagName: options.tagName || '',
        ReplyToAddress: true,
        ToAddress: toAddress,
        Subject: options.subject,
        TemplateName: options.templateId,
        TemplateParam: JSON.stringify(options.templateData || {})
      };
      
      const result = await client.request('BatchSendMail', params, {
        method: 'POST'
      });
      
      return {
        success: true,
        requestId: result.RequestId,
        envId: result.EnvId
      };
    } catch (error) {
      console.error('发送模板邮件失败:', error);
      throw new Error(`发送模板邮件失败: ${error.message}`);
    }
  }

  /**
   * 验证配置
   * @param {Object} config 配置对象
   * @returns {boolean} 是否有效
   */
  validateConfig(config) {
    // 验证必要的配置项
    if (!config.accessKeyId) {
      throw new Error('阿里云AccessKeyId不能为空');
    }
    
    if (!config.accessKeySecret) {
      throw new Error('阿里云AccessKeySecret不能为空');
    }
    
    if (!config.accountName) {
      throw new Error('阿里云发信地址不能为空');
    }
    
    return true;
  }
}

module.exports = AliyunEmailService;

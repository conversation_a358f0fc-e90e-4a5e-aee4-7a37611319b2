/**
 * 微信平台DTO
 * 用于验证微信平台相关的请求参数
 */
const Joi = require('joi');
const BaseDto = require('../../../../../core/dto/BaseDto');

class WechatDto extends BaseDto {
  /**
   * 验证服务器验证请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateVerifyServer(data) {
    const schema = Joi.object({
      signature: Joi.string().required(),
      timestamp: Joi.string().required(),
      nonce: Joi.string().required(),
      echostr: Joi.string().required()
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证获取授权URL请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateGetAuthUrl(data) {
    const schema = Joi.object({
      redirect_uri: Joi.string().required(),
      scope: Joi.string().valid('snsapi_base', 'snsapi_userinfo').default('snsapi_base'),
      state: Joi.string().default('')
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证获取用户信息请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateGetUserInfo(data) {
    const schema = Joi.object({
      code: Joi.string().required()
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证获取JS SDK配置请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateGetJssdkConfig(data) {
    const schema = Joi.object({
      url: Joi.string().required(),
      jsApiList: Joi.array().items(Joi.string()).default([
        'updateAppMessageShareData',
        'updateTimelineShareData',
        'onMenuShareTimeline',
        'onMenuShareAppMessage',
        'chooseWXPay'
      ])
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证创建支付订单请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateCreatePayOrder(data) {
    const schema = Joi.object({
      outTradeNo: Joi.string().required(),
      totalFee: Joi.number().integer().min(1).required(),
      body: Joi.string().required(),
      notifyUrl: Joi.string().required(),
      openid: Joi.string().when('tradeType', {
        is: 'JSAPI',
        then: Joi.string().required(),
        otherwise: Joi.string().allow('')
      }),
      tradeType: Joi.string().valid('JSAPI', 'NATIVE', 'APP').default('JSAPI')
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证查询订单请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateQueryOrder(data) {
    const schema = Joi.object({
      outTradeNo: Joi.string().required()
    });
    
    return this.validate(schema, data);
  }
}

module.exports = WechatDto;

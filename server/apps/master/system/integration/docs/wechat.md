# 微信平台集成文档

## 1. 概述

本文档描述了系统与微信平台的集成方案，包括微信公众号、微信支付等功能的实现。

## 2. 功能列表

- 微信公众号服务器验证
- 接收微信消息
- 微信网页授权
- 获取用户信息
- JS-SDK配置
- 微信支付
- 支付结果通知

## 3. 配置项

系统需要在配置表中设置以下微信相关配置：

| 配置键 | 配置名称 | 说明 |
| ----- | ------- | ---- |
| appid | 公众号AppID | 微信公众号的AppID |
| appsecret | 公众号AppSecret | 微信公众号的AppSecret |
| token | 公众号Token | 用于验证服务器的Token |
| encodingAESKey | 消息加密密钥 | 用于加密消息的密钥 |
| mch_id | 商户号 | 微信支付商户号 |
| pay_key | 支付密钥 | 微信支付API密钥 |
| cert_path | 证书路径 | 微信支付证书路径 |
| key_path | 证书密钥路径 | 微信支付证书密钥路径 |

## 4. API接口

### 4.1 验证服务器

- 接口：`GET /master/system/integration/wechat/verify`
- 参数：
  - signature: 微信加密签名
  - timestamp: 时间戳
  - nonce: 随机数
  - echostr: 随机字符串
- 返回：
  - 成功：返回echostr
  - 失败：返回错误信息

### 4.2 接收微信消息

- 接口：`POST /master/system/integration/wechat/message`
- 参数：XML格式的消息数据
- 返回：XML格式的响应消息

### 4.3 获取授权URL

- 接口：`POST /master/system/integration/wechat/oauth/url`
- 参数：
  - redirect_uri: 授权后重定向的回调链接地址
  - scope: 应用授权作用域（snsapi_base或snsapi_userinfo）
  - state: 重定向后会带上state参数
- 返回：
  - url: 授权URL

### 4.4 获取用户信息

- 接口：`POST /master/system/integration/wechat/oauth/userinfo`
- 参数：
  - code: 授权code
- 返回：用户信息

### 4.5 获取JS SDK配置

- 接口：`POST /master/system/integration/wechat/jssdk/config`
- 参数：
  - url: 当前网页的URL
  - jsApiList: 需要使用的JS接口列表
- 返回：JS SDK配置信息

### 4.6 创建支付订单

- 接口：`POST /master/system/integration/wechat/pay/order`
- 参数：
  - outTradeNo: 商户订单号
  - totalFee: 总金额（分）
  - body: 商品描述
  - notifyUrl: 回调通知URL
  - openid: 用户openid（JSAPI支付必填）
  - tradeType: 交易类型（JSAPI, NATIVE, APP）
- 返回：支付参数

### 4.7 查询支付订单

- 接口：`POST /master/system/integration/wechat/pay/query`
- 参数：
  - outTradeNo: 商户订单号
- 返回：订单信息

### 4.8 接收支付通知

- 接口：`POST /master/system/integration/wechat/pay/notify`
- 参数：XML格式的支付结果通知
- 返回：XML格式的处理结果

## 5. 数据库设计

### 5.1 微信用户表（wechat_user）

| 字段名 | 类型 | 说明 |
| ----- | --- | ---- |
| id | BigInt | 主键 |
| openid | String | 微信用户唯一标识 |
| nickname | String | 用户昵称 |
| sex | Int | 性别，1男，2女，0未知 |
| headimgurl | String | 头像URL |
| country | String | 国家 |
| province | String | 省份 |
| city | String | 城市 |
| subscribe | Int | 是否关注，0未关注，1已关注 |
| subscribe_time | BigInt | 关注时间 |
| unionid | String | 微信开放平台唯一ID |
| remark | String | 备注 |
| groupid | Int | 分组ID |
| tagid_list | String | 标签ID列表，JSON格式 |
| created_at | BigInt | 创建时间 |
| updated_at | BigInt | 更新时间 |
| deleted_at | BigInt | 删除时间 |
| created_by | BigInt | 创建人 |
| updated_by | BigInt | 更新人 |

### 5.2 微信消息表（wechat_message）

| 字段名 | 类型 | 说明 |
| ----- | --- | ---- |
| id | BigInt | 主键 |
| msg_id | String | 微信消息ID |
| from_user | String | 发送方帐号（OpenID） |
| to_user | String | 接收方帐号（OpenID） |
| msg_type | String | 消息类型 |
| content | String | 文本消息内容 |
| event | String | 事件类型 |
| event_key | String | 事件KEY值 |
| create_time | BigInt | 消息创建时间 |
| media_id | String | 媒体ID |
| pic_url | String | 图片链接 |
| format | String | 语音格式 |
| recognition | String | 语音识别结果 |
| thumb_media_id | String | 视频消息缩略图的媒体ID |
| location_x | String | 地理位置纬度 |
| location_y | String | 地理位置经度 |
| scale | String | 地图缩放大小 |
| label | String | 地理位置信息 |
| title | String | 消息标题 |
| description | String | 消息描述 |
| url | String | 消息链接 |
| created_at | BigInt | 创建时间 |
| updated_at | BigInt | 更新时间 |
| deleted_at | BigInt | 删除时间 |
| created_by | BigInt | 创建人 |
| updated_by | BigInt | 更新人 |

## 6. 使用示例

### 6.1 网页授权获取用户信息

```javascript
// 1. 获取授权URL
const authUrlResponse = await axios.post('/master/system/integration/wechat/oauth/url', {
  redirect_uri: 'https://example.com/callback',
  scope: 'snsapi_userinfo',
  state: 'STATE'
});

// 2. 跳转到授权URL
window.location.href = authUrlResponse.data.data.url;

// 3. 在回调页面获取用户信息
const code = new URLSearchParams(window.location.search).get('code');
const userInfoResponse = await axios.post('/master/system/integration/wechat/oauth/userinfo', {
  code
});
const userInfo = userInfoResponse.data.data;
```

### 6.2 JSAPI支付

```javascript
// 1. 获取JS SDK配置
const jssdkConfigResponse = await axios.post('/master/system/integration/wechat/jssdk/config', {
  url: window.location.href,
  jsApiList: ['chooseWXPay']
});
const jssdkConfig = jssdkConfigResponse.data.data;

// 2. 配置JS SDK
wx.config({
  debug: false,
  appId: jssdkConfig.appId,
  timestamp: jssdkConfig.timestamp,
  nonceStr: jssdkConfig.nonceStr,
  signature: jssdkConfig.signature,
  jsApiList: jssdkConfig.jsApiList
});

// 3. 创建支付订单
const orderResponse = await axios.post('/master/system/integration/wechat/pay/order', {
  outTradeNo: 'ORDER_123456',
  totalFee: 1,
  body: '测试商品',
  notifyUrl: 'https://example.com/notify',
  openid: 'USER_OPENID',
  tradeType: 'JSAPI'
});
const payParams = orderResponse.data.data.params;

// 4. 调起支付
wx.chooseWXPay({
  timestamp: payParams.timeStamp,
  nonceStr: payParams.nonceStr,
  package: payParams.package,
  signType: payParams.signType,
  paySign: payParams.paySign,
  success: function(res) {
    console.log('支付成功');
  }
});
```

/**
 * 上传服务配置模板
 * 提供各平台的配置模板，用于系统初始化或用户参考
 */

// 阿里云OSS配置模板
const aliyunOSSTemplate = {
  accessKeyId: '',
  accessKeySecret: '',
  bucket: '',
  region: 'oss-cn-hangzhou',
  endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
  domain: '',
  secure: true,
  cname: false
};

// 腾讯云COS配置模板
const tencentCOSTemplate = {
  secretId: '',
  secretKey: '',
  bucket: '',
  region: 'ap-guangzhou',
  appId: '',
  domain: '',
  secure: true
};

// 七牛云配置模板
const qiniuTemplate = {
  accessKey: '',
  secretKey: '',
  bucket: '',
  region: 'z0',
  domain: '',
  isPrivate: false
};

// 本地存储配置模板
const localTemplate = {
  basePath: '/data/uploads',
  baseUrl: 'http://localhost:3000/uploads',
  maxSize: 10 // MB
};

/**
 * 获取配置模板
 * @param {string} platform 平台类型
 * @returns {Object} 配置模板
 */
function getConfigTemplate(platform) {
  switch (platform.toLowerCase()) {
    case 'aliyun':
      return aliyunOSSTemplate;
    case 'tencent':
      return tencentCOSTemplate;
    case 'qiniu':
      return qiniuTemplate;
    case 'local':
      return localTemplate;
    default:
      throw new Error(`不支持的平台类型: ${platform}`);
  }
}

/**
 * 获取所有平台的配置模板
 * @returns {Object} 所有平台的配置模板
 */
function getAllConfigTemplates() {
  return {
    aliyun: aliyunOSSTemplate,
    tencent: tencentCOSTemplate,
    qiniu: qiniuTemplate,
    local: localTemplate
  };
}

/**
 * 生成系统配置数据
 * @param {string} platform 平台类型
 * @returns {Object} 系统配置数据
 */
function generateSystemConfig(platform) {
  const template = getConfigTemplate(platform);
  const configValue = JSON.stringify(template);
  
  return {
    config_type: 'Upload',
    config_key: platform.toLowerCase(),
    config_value: configValue,
    name: `${platform}上传配置`,
    sort: 0,
    is_system: platform.toLowerCase() === 'aliyun' ? 1 : 0, // 默认阿里云为系统默认
    remark: `${platform}上传服务配置`
  };
}

module.exports = {
  getConfigTemplate,
  getAllConfigTemplates,
  generateSystemConfig
};

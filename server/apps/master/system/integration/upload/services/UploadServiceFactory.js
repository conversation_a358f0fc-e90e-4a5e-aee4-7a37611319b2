/**
 * 上传服务工厂类
 * 根据配置动态选择上传服务
 */
const { prisma } = require('../../../../../../core/database/prisma');
const LocalStorageService = require('./LocalStorageService');

class UploadServiceFactory {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prismaInstance) {
    this.prisma = prismaInstance || prisma;
    this.serviceInstances = {};
    this.configCache = null;
    this.configCacheTime = 0;
  }

  /**
   * 获取上传配置
   * @param {Boolean} forceRefresh 是否强制刷新缓存
   * @returns {Promise<Array>} 上传配置列表
   */
  async getUploadConfigs(forceRefresh = false) {
    try {
      // 缓存有效期为5分钟
      const cacheExpireTime = 5 * 60 * 1000;
      const now = Date.now();
      
      // 如果缓存存在且未过期，且不强制刷新，则使用缓存
      if (this.configCache && (now - this.configCacheTime) < cacheExpireTime && !forceRefresh) {
        console.log('使用缓存的上传配置');
        return this.configCache;
      }
      
      // 从配置表中查询上传相关的所有配置
      const configs = await this.prisma.baseSystemConfig.findMany({
        where: {
          config_type: 'Upload',
          deleted_at: null
        },
        select: {
          id: true,
          config_key: true,
          config_value: true,
          is_default: true,
          status: true,
          name: true,
          created_at: true,
          updated_at: true
        }
      });
      
      // 过滤出启用的配置
      const enabledConfigs = configs.filter(config => config.status === 1);
      
      if (!enabledConfigs || enabledConfigs.length === 0) {
        console.log('未找到启用的上传配置，使用默认本地存储配置');
        // 如果没有配置，返回默认本地存储配置
        const defaultConfig = {
          id: 'default',
          config_key: 'local',
          config_value: JSON.stringify({
            basePath: './public/uploads',
            baseUrl: '/uploads',
            domain: ''
          }),
          is_default: 1,
          status: 1,
          name: '默认本地存储配置',
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        };
        
        // 更新缓存
        this.configCache = [defaultConfig];
        this.configCacheTime = now;
        
        return this.configCache;
      }
      
      // 检查是否有默认配置
      const hasDefault = enabledConfigs.some(config => config.is_default === 1);
      
      // 如果没有默认配置，则将第一个配置设为默认
      if (!hasDefault && enabledConfigs.length > 0) {
        console.log('未找到默认上传配置，将第一个配置设为默认');
        enabledConfigs[0].is_default = 1;
      }
      
      // 更新缓存
      this.configCache = enabledConfigs;
      this.configCacheTime = now;
      
      return enabledConfigs;
    } catch (error) {
      console.error('获取上传配置失败:', error);
      // 返回默认本地存储配置
      return [{
        id: 'default',
        config_key: 'local',
        config_value: JSON.stringify({
          basePath: './public/uploads',
          baseUrl: '/uploads'
        }),
        is_default: 1,
        status: 1,
        name: '默认本地存储配置',
        created_at: BigInt(Date.now()),
        updated_at: BigInt(Date.now())
      }];
    }
  }

  /**
   * 获取默认上传服务
   * @returns {Promise<Object>} 上传服务实例
   */
  async getDefaultService() {
    try {
      const configs = await this.getUploadConfigs();
      
      // 查找默认配置
      const defaultConfig = configs.find(config => config.is_default === 1);
      
      if (!defaultConfig) {
        console.log('没有找到默认上传服务配置，使用本地存储');
        return this.getLocalStorageService();
      }
      
      // 获取默认服务类型
      const type = defaultConfig.config_key;
      
      try {
        // 尝试获取指定类型的服务
        return await this.getServiceByType(type);
      } catch (error) {
        console.log(`默认上传服务 ${type} 不可用或未启用，使用本地存储:`, error.message);
        return this.getLocalStorageService();
      }
    } catch (error) {
      console.error('获取默认上传服务失败，使用本地存储:', error);
      return this.getLocalStorageService();
    }
  }

  /**
   * 获取本地存储服务
   * @returns {Object} 本地存储服务实例
   */
  getLocalStorageService() {
    // 如果已经缓存了本地存储服务，则直接返回
    if (this.serviceInstances['local']) {
      return this.serviceInstances['local'];
    }
    
    // 创建默认本地存储服务
    const defaultConfig = {
      config_key: 'local',
      config_value: JSON.stringify({
        basePath: './public/uploads',
        baseUrl: '/uploads'
      }),
      is_default: 1,
      status: 1,
      name: '默认本地存储配置',
      created_at: BigInt(Date.now()),
      updated_at: BigInt(Date.now())
    };
    
    const service = new LocalStorageService(defaultConfig, this.prisma);
    this.serviceInstances['local'] = service;
    
    return service;
  }

  /**
   * 根据类型获取上传服务
   * @param {String} type 服务类型
   * @returns {Promise<Object>} 上传服务实例
   */
  async getServiceByType(type) {
    console.log(`获取上传服务 ${type}`);
    
    // 如果缓存中已有该类型的服务实例，直接返回
    if (this.serviceInstances[type]) {
      return this.serviceInstances[type];
    }
    
    // 获取配置
    const configs = await this.getUploadConfigs();
    const config = configs.find(c => c.config_key === type && c.status === 1);
    
    if (!config) {
      throw new Error(`获取上传服务 ${type} 失败: 未找到配置或配置已禁用`);
    }
    
    let service;
    
    // 根据类型创建对应的服务实例
    switch (type) {
      case 'local':
        // 本地存储
        service = new LocalStorageService(config, this.prisma);
        break;
      case 'aliyun':
        // 阿里云OSS
        const AliyunOSSUploadService = require('./AliyunOSSUploadService');
        service = new AliyunOSSUploadService(config, this.prisma);
        break;
      case 'tencent':
        // 腾讯云COS
        throw new Error('腾讯云COS服务暂未实现');
      case 'qiniu':
        // 七牛云存储
        throw new Error('七牛云存储服务暂未实现');
      default:
        throw new Error(`不支持的上传服务类型: ${type}`);
    }
    
    // 缓存服务实例
    this.serviceInstances[type] = service;
    
    return service;
  }

  /**
   * 设置默认上传服务
   * @param {string} type 上传服务类型
   * @returns {Promise<Boolean>} 是否设置成功
   */
  async setDefaultService(type) {
    try {
      // 获取所有配置
      const configs = await this.getUploadConfigs(true);
      
      // 查找指定类型的配置
      const config = configs.find(config => config.config_key === type);
      
      if (!config) {
        throw new Error(`上传服务 ${type} 不存在`);
      }
      
      if (config.status !== 1) {
        throw new Error(`上传服务 ${type} 未启用`);
      }
      
      // 将所有配置的默认标志设为0
      await this.prisma.baseSystemConfig.updateMany({
        where: {
          config_type: 'Upload',
          deleted_at: null
        },
        data: {
          is_default: 0
        }
      });
      
      // 将指定配置的默认标志设为1
      await this.prisma.baseSystemConfig.update({
        where: {
          id: config.id
        },
        data: {
          is_default: 1
        }
      });
      
      // 清空缓存
      this.configCache = null;
      this.serviceInstances = {};
      
      return true;
    } catch (error) {
      console.error(`设置默认上传服务 ${type} 失败:`, error);
      throw new Error(`设置默认上传服务 ${type} 失败: ${error.message}`);
    }
  }
  
  /**
   * 更新上传服务配置
   * @param {string} type 上传服务类型
   * @param {Object} configValue 配置值
   * @param {Boolean} isDefault 是否设为默认
   * @param {String} name 配置名称
   * @returns {Promise<Boolean>} 是否更新成功
   */
  async updateServiceConfig(type, configValue, isDefault = false, name = '') {
    try {
      // 验证平台类型
      const validTypes = ['local', 'aliyun', 'tencent', 'qiniu'];
      if (!validTypes.includes(type)) {
        throw new Error(`不支持的上传服务类型: ${type}`);
      }
      
      // 获取所有配置
      const configs = await this.getUploadConfigs(true);
      
      const config = configs.find(config => config.config_key === type);
      
      if (config) {
        // 更新现有配置
        await this.prisma.baseSystemConfig.update({
          where: {
            id: config.id
          },
          data: {
            config_value: typeof configValue === 'string' ? configValue : JSON.stringify(configValue),
            name: name || config.name, // 使用提供的名称或保留原名称
            status: 1,
            updated_at: BigInt(Date.now()) // 添加更新时间戳
          }
        });
      } else {
        // 创建新配置
        await this.prisma.baseSystemConfig.create({
          data: {
            config_type: 'Upload',
            config_key: type,
            config_value: typeof configValue === 'string' ? configValue : JSON.stringify(configValue),
            name: name || `${type}上传配置`, // 使用提供的名称或生成默认名称
            is_default: 0,
            status: 1,
            created_at: BigInt(Date.now()), // 添加创建时间戳
            updated_at: BigInt(Date.now())  // 添加更新时间戳
          }
        });
      }
      
      // 如果需要设为默认，则调用设置默认服务方法
      if (isDefault) {
        await this.setDefaultService(type);
      }
      
      // 清空缓存
      this.configCache = null;
      this.serviceInstances = {};
      
      return true;
    } catch (error) {
      console.error(`更新上传服务配置 ${type} 失败:`, error);
      throw new Error(`更新上传服务配置 ${type} 失败: ${error.message}`);
    }
  }
}

module.exports = UploadServiceFactory;

/**
 * 阿里云对象存储上传服务
 * 实现IUploadService接口，处理阿里云OSS的上传、下载等功能
 */
const OSS = require('ali-oss');
const crypto = require('crypto');
const { PrismaClient } = require('@prisma/client');
const IUploadService = require('./IUploadService');
const ConfigurationService = require('../../../configure/services/ConfigurationService');

// 常量定义
const CONFIG_TYPE = 'Upload';
const PLATFORM_TYPE = 'aliyun';

class AliyunOSSUploadService extends IUploadService {
  constructor(prisma) {
    super();
    this.prisma = prisma || new PrismaClient();
    this.configService = new ConfigurationService(prisma);
    this.ossClient = null;
  }

  /**
   * 获取阿里云OSS配置
   * @returns {Promise<Object>} 阿里云OSS配置对象
   */
  async getConfig() {
    try {
      console.log('获取阿里云OSS配置');
      
      // 使用ConfigurationService获取配置
      const config = await this.configService.getConfigObject(CONFIG_TYPE, PLATFORM_TYPE);
      
      if (!config) {
        throw new Error('未找到阿里云OSS配置');
      }
      
      return config;
    } catch (error) {
      console.error('获取阿里云OSS配置失败:', error);
      throw new Error(`获取阿里云OSS配置失败: ${error.message}`);
    }
  }

  /**
   * 获取OSS客户端实例
   * @returns {Promise<OSS>} OSS客户端实例
   */
  async getOSSClient() {
    if (this.ossClient) {
      return this.ossClient;
    }

    try {
      console.log('初始化OSS客户端');
      
      // 获取阿里云配置
      const config = await this.getConfig();
      
      if (!config.accessKeyId || !config.accessKeySecret || !config.bucket) {
        throw new Error('阿里云OSS配置中缺少必要参数');
      }
      
      // 创建OSS客户端实例
      this.ossClient = new OSS({
        accessKeyId: config.accessKeyId,
        accessKeySecret: config.accessKeySecret,
        bucket: config.bucket,
        region: config.region,
        endpoint: config.endpoint
      });
      
      return this.ossClient;
    } catch (error) {
      console.error('初始化OSS客户端失败:', error);
      throw new Error(`初始化OSS客户端失败: ${error.message}`);
    }
  }

  /**
   * 获取上传凭证
   * @param {string} dir 上传目录
   * @param {number} maxSize 最大文件大小（MB）
   * @returns {Promise<Object>} 上传凭证
   */
  async getUploadCredentials(dir = 'uploads', maxSize = 10) {
    try {
      console.log('获取OSS上传凭证，目录:', dir, '最大大小:', maxSize, 'MB');
      
      // 获取阿里云配置
      const config = await this.getConfig();
      
      if (!config.accessKeyId || !config.accessKeySecret || !config.bucket) {
        throw new Error('阿里云OSS配置中缺少必要参数');
      }
      
      const endpoint = config.endpoint || `https://${config.region}.aliyuncs.com`;
      
      // 生成上传策略
      const expiration = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30分钟有效期
      const conditions = [
        ['content-length-range', 0, maxSize * 1024 * 1024], // 限制文件大小
        ['starts-with', '$key', dir] // 限制上传目录
      ];
      
      const policy = {
        expiration,
        conditions
      };
      
      const policyString = JSON.stringify(policy);
      const policyBase64 = Buffer.from(policyString).toString('base64');
      const signature = crypto.createHmac('sha1', config.accessKeySecret).update(policyBase64).digest('base64');
      
      return {
        accessKeyId: config.accessKeyId,
        policy: policyBase64,
        signature,
        dir,
        host: `https://${config.bucket}.${endpoint.replace(/^https?:\/\//, '')}`,
        expire: expiration
      };
    } catch (error) {
      console.error('获取OSS上传凭证失败:', error);
      throw new Error(`获取OSS上传凭证失败: ${error.message}`);
    }
  }

  /**
   * 上传文件
   * @param {Buffer} fileBuffer 文件数据
   * @param {string} fileName 文件名
   * @param {string} dir 上传目录
   * @param {Object} options 其他选项
   * @returns {Promise<Object>} 上传结果
   */
  async uploadBuffer(fileBuffer, fileName, dir = 'uploads', options = {}) {
    try {
      console.log('上传文件到OSS，文件名:', fileName, '目录:', dir);
      
      // 获取OSS客户端
      const client = await this.getOSSClient();
      
      // 构建文件路径
      const objectName = dir ? `${dir}/${fileName}` : fileName;
      
      // 设置上传选项
      const uploadOptions = {
        mime: options.mime || options.contentType || undefined,
        meta: options.meta || undefined
      };
      
      // 上传文件
      const result = await client.put(objectName, fileBuffer, uploadOptions);
      
      // 获取配置
      const config = await this.getConfig();
      
      // 构建返回结果
      return {
        success: true,
        fileName: fileName,
        filePath: objectName,
        fileUrl: result.url || `https://${config.bucket}.${config.endpoint.replace(/^https?:\/\//, '')}/${objectName}`,
        fileSize: fileBuffer.length,
        mimeType: uploadOptions.mime,
        platform: 'aliyun',
        meta: result.meta
      };
    } catch (error) {
      console.error('上传文件到OSS失败:', error);
      throw new Error(`上传文件到OSS失败: ${error.message}`);
    }
  }

  /**
   * 获取文件列表
   * @param {string} dir 目录
   * @param {string} prefix 前缀
   * @param {string} marker 起始标记
   * @param {number} maxKeys 最大返回数量
   * @returns {Promise<Object>} 文件列表
   */
  async listFiles(dir = '', prefix = '', marker = '', maxKeys = 100) {
    try {
      console.log('获取OSS文件列表，目录:', dir, '前缀:', prefix);
      
      // 获取OSS客户端
      const client = await this.getOSSClient();
      
      // 构建查询参数
      const options = {
        prefix: dir ? `${dir}/${prefix}` : prefix,
        marker,
        'max-keys': maxKeys
      };
      
      // 查询文件列表
      const result = await client.list(options);
      
      // 转换为前端友好的格式
      return {
        files: result.objects ? result.objects.map(obj => ({
          name: obj.name,
          url: obj.url,
          size: obj.size,
          lastModified: obj.lastModified,
          type: obj.type
        })) : [],
        prefixes: result.prefixes || [],
        isTruncated: result.isTruncated,
        nextMarker: result.nextMarker
      };
    } catch (error) {
      console.error('获取OSS文件列表失败:', error);
      throw new Error(`获取OSS文件列表失败: ${error.message}`);
    }
  }

  /**
   * 删除文件
   * @param {string} objectName 文件名
   * @returns {Promise<Object>} 删除结果
   */
  async deleteFile(objectName) {
    try {
      console.log('删除OSS文件，文件名:', objectName);
      
      // 获取OSS客户端
      const client = await this.getOSSClient();
      
      // 删除文件
      const result = await client.delete(objectName);
      
      return {
        success: true,
        result
      };
    } catch (error) {
      console.error('删除OSS文件失败:', error);
      throw new Error(`删除OSS文件失败: ${error.message}`);
    }
  }

  /**
   * 获取文件访问URL
   * @param {string} objectName 文件名
   * @param {number} expires 过期时间（秒）
   * @returns {Promise<string>} 文件访问URL
   */
  async getFileUrl(objectName, expires = 3600) {
    try {
      console.log('获取OSS文件访问URL，文件名:', objectName);
      
      // 获取OSS客户端
      const client = await this.getOSSClient();
      
      // 生成签名URL
      const url = client.signatureUrl(objectName, { expires });
      
      return url;
    } catch (error) {
      console.error('获取OSS文件访问URL失败:', error);
      throw new Error(`获取OSS文件访问URL失败: ${error.message}`);
    }
  }
}

module.exports = AliyunOSSUploadService;

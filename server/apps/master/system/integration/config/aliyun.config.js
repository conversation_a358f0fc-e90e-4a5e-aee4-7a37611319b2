/**
 * 阿里云平台配置
 * 包含阿里云平台相关的配置信息
 */
module.exports = {
  // OSS对象存储配置
  oss: {
    // 默认上传目录
    defaultDir: 'uploads',
    
    // 默认最大文件大小（MB）
    defaultMaxSize: 10,
    
    // 临时凭证有效期（秒）
    stsExpireSeconds: 3600,
    
    // 是否启用CDN加速
    enableCdn: false,
    
    // CDN域名
    cdnDomain: '',
    
    // 是否自动生成唯一文件名
    generateUniqueFilename: true,
    
    // 允许的文件类型
    allowedFileTypes: [
      'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
      'mp4', 'mov', 'avi', 'wmv',
      'mp3', 'wav', 'ogg',
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
      'zip', 'rar', 'tar', 'gz'
    ]
  },
  
  // 短信服务配置
  sms: {
    // API版本
    apiVersion: '2017-05-25',
    
    // 接入点
    endpoint: 'dysmsapi.aliyuncs.com',
    
    // 默认短信签名
    defaultSignName: '系统通知',
    
    // 验证码模板
    verifyCodeTemplate: 'SMS_123456789',
    
    // 验证码长度
    verifyCodeLength: 6,
    
    // 验证码有效期（分钟）
    verifyCodeExpireMinutes: 5,
    
    // 短信发送频率限制（秒）
    sendFrequencyLimit: 60,
    
    // 是否记录短信发送日志
    recordLog: true
  },
  
  // 接口调用配置
  api: {
    // 重试次数
    retryTimes: 3,
    
    // 超时时间（毫秒）
    timeout: 5000,
    
    // 是否记录API调用日志
    recordLog: true
  }
};

/**
 * 集成服务基类
 * 所有集成服务的基础类
 */
const { PrismaClient } = require('@prisma/client');
const ConfigurationService = require('../../configure/services/ConfigurationService');

class BaseIntegrationService {
  /**
   * 构造函数
   * @param {Object} config 配置对象
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(config, prisma) {
    this.config = config;
    this.prisma = prisma || new PrismaClient();
    this.configService = new ConfigurationService(prisma);
  }

  /**
   * 获取配置对象
   * @returns {Promise<Object>} 配置对象
   */
  async getConfig() {
    if (!this.config || !this.config.config_value) {
      throw new Error('配置不存在');
    }

    try {
      return typeof this.config.config_value === 'string' 
        ? JSON.parse(this.config.config_value) 
        : this.config.config_value;
    } catch (e) {
      throw new Error('配置格式错误');
    }
  }

  /**
   * 获取服务类型
   * @returns {string} 服务类型
   */
  getServiceType() {
    return this.config && this.config.config_key 
      ? this.config.config_key 
      : 'unknown';
  }

  /**
   * 获取集成类型
   * @returns {string} 集成类型
   */
  getIntegrationType() {
    // 子类必须实现此方法
    throw new Error('必须由子类实现');
  }

  /**
   * 测试连接
   * @param {Object} testData 测试数据
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection(testData = {}) {
    // 子类必须实现此方法
    throw new Error('必须由子类实现');
  }

  /**
   * 初始化服务
   * @returns {Promise<void>}
   */
  async initialize() {
    // 子类可以重写此方法进行初始化
    return;
  }

  /**
   * 验证配置
   * @param {Object} config 配置对象
   * @returns {boolean} 是否有效
   */
  validateConfig(config) {
    // 子类应该重写此方法以验证特定的配置
    return true;
  }

  /**
   * 获取默认配置
   * @returns {Promise<Object>} 默认配置
   */
  async getDefaultConfig() {
    const integrationType = this.getIntegrationType();
    return await this.configService.getDefaultConfig(integrationType);
  }

  /**
   * 获取所有配置
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Array>} 配置列表
   */
  async getAllConfigs(forceRefresh = false) {
    const integrationType = this.getIntegrationType();
    return await this.configService.getConfigsByType(integrationType, forceRefresh);
  }
}

module.exports = BaseIntegrationService;

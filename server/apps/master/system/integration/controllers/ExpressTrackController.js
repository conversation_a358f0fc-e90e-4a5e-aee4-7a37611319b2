/**
 * 快递轨迹控制器
 */
const BaseController = require('../../../../../core/controllers/BaseController');
const ExpressTrackService = require('../services/express/ExpressTrackService');
const ExpressSubscriptionService = require('../services/express/ExpressSubscriptionService');
const ExpressScheduleService = require('../services/express/ExpressScheduleService');

class ExpressTrackController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.trackService = new ExpressTrackService(prisma);
    this.subscriptionService = new ExpressSubscriptionService(prisma);
    this.scheduleService = new ExpressScheduleService(prisma);
  }

  /**
   * 获取包裹轨迹信息
   */
  async getTrackByPackageId(req, res) {
    try {
      const { packageId } = req.params;
      
      if (!packageId) {
        return this.fail(res, '包裹ID不能为空');
      }

      const result = await this.trackService.getTrackByPackageId(BigInt(packageId));
      
      if (result.success) {
        return this.success(res, result.data, '获取轨迹信息成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('获取包裹轨迹失败:', error);
      return this.fail(res, '获取轨迹信息失败');
    }
  }

  /**
   * 根据快递单号获取轨迹信息
   */
  async getTrackByExpressNo(req, res) {
    try {
      const { expressNo } = req.params;
      
      if (!expressNo) {
        return this.fail(res, '快递单号不能为空');
      }

      const result = await this.trackService.getTrackByExpressNo(expressNo);
      
      if (result.success) {
        return this.success(res, result.data, '获取轨迹信息成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('获取快递轨迹失败:', error);
      return this.fail(res, '获取轨迹信息失败');
    }
  }

  /**
   * 获取订单的所有包裹轨迹
   */
  async getTracksByOrderId(req, res) {
    try {
      const { orderId } = req.params;
      
      if (!orderId) {
        return this.fail(res, '订单ID不能为空');
      }

      const result = await this.trackService.getTracksByOrderId(BigInt(orderId));
      
      if (result.success) {
        return this.success(res, result.data, '获取订单轨迹信息成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('获取订单轨迹失败:', error);
      return this.fail(res, '获取订单轨迹信息失败');
    }
  }

  /**
   * 订阅快递轨迹
   */
  async subscribeExpress(req, res) {
    try {
      const { packageId } = req.body;
      
      if (!packageId) {
        return this.fail(res, '包裹ID不能为空');
      }

      // 获取包裹信息
      const packageInfo = await this.prisma.order_packages.findFirst({
        where: {
          id: BigInt(packageId)
        }
      });

      if (!packageInfo) {
        return this.fail(res, '包裹不存在');
      }

      if (!packageInfo.tracking_number || !packageInfo.shipping_company_code) {
        return this.fail(res, '包裹缺少快递信息');
      }

      const result = await this.subscriptionService.subscribeExpress(packageInfo);
      
      if (result.success) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('订阅快递轨迹失败:', error);
      return this.fail(res, '订阅快递轨迹失败');
    }
  }

  /**
   * 批量订阅快递轨迹
   */
  async batchSubscribeExpress(req, res) {
    try {
      const { packageIds } = req.body;
      
      if (!packageIds || !Array.isArray(packageIds) || packageIds.length === 0) {
        return this.fail(res, '包裹ID列表不能为空');
      }

      // 获取包裹信息
      const packages = await this.prisma.order_packages.findMany({
        where: {
          id: {
            in: packageIds.map(id => BigInt(id))
          }
        }
      });

      if (packages.length === 0) {
        return this.fail(res, '未找到有效的包裹');
      }

      const result = await this.subscriptionService.batchSubscribeExpress(packages);
      
      return this.success(res, result.data, result.message);
    } catch (error) {
      console.error('批量订阅快递轨迹失败:', error);
      return this.fail(res, '批量订阅快递轨迹失败');
    }
  }

  /**
   * 获取未订阅的包裹列表
   */
  async getUnsubscribedPackages(req, res) {
    try {
      const { limit = 100 } = req.query;
      
      const result = await this.subscriptionService.getUnsubscribedPackages(parseInt(limit));
      
      if (result.success) {
        return this.success(res, result.data, '获取未订阅包裹成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('获取未订阅包裹失败:', error);
      return this.fail(res, '获取未订阅包裹失败');
    }
  }

  /**
   * 处理快递100回调
   */
  async handleCallback(req, res) {
    try {
      const callbackData = req.body;
      
      console.log('收到快递100回调:', JSON.stringify(callbackData));
      
      const result = await this.subscriptionService.handleCallback(callbackData);
      
      if (result.success) {
        return res.json({ result: true, message: '回调处理成功' });
      } else {
        return res.json({ result: false, message: result.message });
      }
    } catch (error) {
      console.error('处理快递100回调失败:', error);
      return res.json({ result: false, message: '回调处理失败' });
    }
  }

  /**
   * 手动执行订阅任务
   */
  async runSubscribeTask(req, res) {
    try {
      const result = await this.scheduleService.runOnce();
      
      if (result.success) {
        return this.success(res, null, result.message);
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('执行订阅任务失败:', error);
      return this.fail(res, '执行订阅任务失败');
    }
  }

  /**
   * 获取订阅统计信息
   */
  async getSubscriptionStats(req, res) {
    try {
      const result = await this.scheduleService.getStats();
      
      if (result.success) {
        return this.success(res, result.data, '获取统计信息成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('获取订阅统计失败:', error);
      return this.fail(res, '获取统计信息失败');
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(req, res) {
    try {
      const status = this.scheduleService.getStatus();
      return this.success(res, status, '获取任务状态成功');
    } catch (error) {
      console.error('获取任务状态失败:', error);
      return this.fail(res, '获取任务状态失败');
    }
  }
}

module.exports = ExpressTrackController;

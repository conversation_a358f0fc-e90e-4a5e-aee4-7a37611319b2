/**
 * 阿里云平台控制器
 * 处理阿里云相关的API请求
 */
const BaseController = require('../../../../../core/controllers/BaseController');
const AliyunOSSService = require('../services/aliyun/AliyunOSSService');
const AliyunSMSService = require('../services/aliyun/AliyunSMSService');
const AliyunOCRService = require('../services/aliyun/AliyunOCRService');
const VerificationCodeService = require('../services/VerificationCodeService');

class AliyunController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.aliyunOSSService = new AliyunOSSService(prisma);
    this.aliyunSMSService = new AliyunSMSService(prisma);
    this.aliyunOCRService = new AliyunOCRService(prisma);
    this.verificationCodeService = new VerificationCodeService(prisma);
  }

  /**
   * 获取OSS上传凭证
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getOSSCredentials(req, res) {
    try {
      const { dir = 'uploads', maxSize = 10 } = req.body;
      
      console.log('获取OSS上传凭证，目录:', dir);
      const credentials = await this.aliyunOSSService.getUploadCredentials(dir, maxSize);
      return this.success(res, credentials, '获取OSS上传凭证成功');
    } catch (error) {
      console.error('获取OSS上传凭证失败:', error);
      return this.fail(res, '获取OSS上传凭证失败: ' + error.message);
    }
  }

  /**
   * 发送短信
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async sendSMS(req, res) {
    try {
      // 支持两种格式的请求
      // 格式1: {phoneNumber, templateCode, templateParam}
      // 格式2: {phone, content, template, data}
      let phoneNumber, templateCode, templateParam;
      
      if (req.body.phone) {
        // 格式2
        phoneNumber = req.body.phone;
        templateCode = req.body.template;
        
        // 如果data是对象，直接使用
        if (req.body.data && typeof req.body.data === 'object') {
          templateParam = req.body.data;
        } 
        // 如果data是字符串，尝试解析为JSON
        else if (req.body.data && typeof req.body.data === 'string') {
          try {
            templateParam = JSON.parse(req.body.data);
          } catch (e) {
            // 如果解析失败，将其作为code参数
            templateParam = { code: req.body.data };
          }
        }
        // 如果没有data但有content，将content作为code
        else if (req.body.content) {
          templateParam = { code: req.body.content };
        }
        // 都没有，使用空对象
        else {
          templateParam = {};
        }
      } else {
        // 格式1
        phoneNumber = req.body.phoneNumber;
        templateCode = req.body.templateCode;
        templateParam = req.body.templateParam;
      }
      
      if (!phoneNumber || !templateCode) {
        return this.fail(res, '手机号码和模板编码不能为空');
      }

      console.log('发送短信，手机号码:', phoneNumber, '模板编码:', templateCode, '模板参数:', templateParam);
      const result = await this.aliyunSMSService.sendSMS(phoneNumber, templateCode, templateParam);
      return this.success(res, result, '发送短信成功');
    } catch (error) {
      console.error('发送短信失败:', error);
      return this.fail(res, '发送短信失败: ' + error.message);
    }
  }

  /**
   * 获取验证码
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getVerificationCode(req, res) {
    try {
      const { phoneNumber, type = 'default', templateCode } = req.body;
      
      if (!phoneNumber) {
        return this.fail(res, '手机号码不能为空');
      }

      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
        return this.fail(res, '手机号格式不正确');
      }

      // 获取验证码模板编码
      let smsTemplateCode = templateCode;
      if (!smsTemplateCode) {
        // 如果没有提供模板编码，从配置中获取
        const configs = await this.aliyunSMSService.getConfig();
        smsTemplateCode = configs.find(item => item.config_key === 'sms_template')?.config_value;
        
        if (!smsTemplateCode) {
          return this.fail(res, '短信模板编码未配置');
        }
      }

      // 生成验证码
      const codeResult = await this.verificationCodeService.getCode(phoneNumber, type);
      
      // 发送验证码短信
      const smsResult = await this.aliyunSMSService.sendSMS(
        phoneNumber, 
        smsTemplateCode, 
        { code: codeResult.code }
      );

      if (!smsResult.success) {
        return this.fail(res, '验证码短信发送失败: ' + smsResult.message);
      }

      return this.success(res, {
        success: true,
        message: '验证码已发送',
        expireTime: codeResult.expireTime
      }, '获取验证码成功');
    } catch (error) {
      console.error('获取验证码失败:', error);
      return this.fail(res, '获取验证码失败: ' + error.message);
    }
  }

  /**
   * 获取OSS文件列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async listOSSFiles(req, res) {
    try {
      const { dir = '', prefix = '', marker = '', maxKeys = 100 } = req.query;
      
      console.log('获取OSS文件列表，目录:', dir, '前缀:', prefix);
      const fileList = await this.aliyunOSSService.listFiles(dir, prefix, marker, maxKeys);
      return this.success(res, fileList, '获取OSS文件列表成功');
    } catch (error) {
      console.error('获取OSS文件列表失败:', error);
      return this.fail(res, '获取OSS文件列表失败: ' + error.message);
    }
  }

  /**
   * 删除OSS文件
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async deleteOSSFile(req, res) {
    try {
      const { filePath } = req.body;
      
      if (!filePath) {
        return this.fail(res, '文件路径不能为空');
      }
      
      await this.aliyunOSSService.deleteFile(filePath);
      return this.success(res, null, '删除文件成功');
    } catch (error) {
      console.error('删除OSS文件失败:', error);
      return this.fail(res, '删除OSS文件失败: ' + error.message);
    }
  }

  /**
   * 上传图片到OSS
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async uploadImage(req, res) {
    try {
      // 检查是否有文件上传
      if (!req.file && !req.files) {
        return this.fail(res, '没有上传文件');
      }

      // 获取上传的文件
      const file = req.file || req.files[0];
      
      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.mimetype)) {
        return this.fail(res, '只允许上传图片文件(JPEG, PNG, GIF, WEBP)');
      }
      
      // 检查文件大小（限制为5MB）
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        return this.fail(res, '文件大小不能超过5MB');
      }
      
      // 生成文件路径
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      
      // 获取用户ID（如果有）
      const userId = req.user?.id || 'anonymous';
      
      // 生成文件名（使用时间戳和随机字符串）
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substring(2, 10);
      const ext = file.originalname.split('.').pop();
      const fileName = `${timestamp}_${randomStr}.${ext}`;
      
      // 构建OSS路径
      const ossPath = `uploads/images/${year}${month}${day}/${userId}/${fileName}`;
      
      // 上传文件到OSS
      const result = await this.aliyunOSSService.uploadBuffer(
        file.buffer,
        ossPath,
        { contentType: file.mimetype }
      );
      
      // 生成雪花ID
      const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');
      const fileId = generateSnowflakeId();
      
      // 保存文件信息到数据库
      const systemFile = await this.prisma.systemFile.create({
        data: {
          id: fileId,
          file_name: fileName,
          original_name: file.originalname,
          file_path: ossPath,
          file_url: result.url,
          file_size: BigInt(file.size),
          file_type: file.mimetype,
          extension: ext,
          storage_type: 'aliyun_oss',
          bucket: result.bucket || '',
          md5: result.etag ? result.etag.replace(/"/g, '') : null,
          module: req.body.module || 'system',
          biz_type: req.body.bizType || 'image',
          biz_id: req.body.bizId ? BigInt(req.body.bizId) : null,
          tags: req.body.tags || null,
          remark: req.body.remark || null,
          created_by: req.user ? BigInt(req.user.id) : BigInt(0),
          created_at: BigInt(Date.now())
        }
      });
      
      // 返回上传结果
      return this.success(res, {
        id: systemFile.id.toString(),
        url: result.url,
        path: ossPath,
        name: fileName,
        size: file.size,
        type: file.mimetype
      }, '图片上传成功');
    } catch (error) {
      console.error('上传图片失败:', error);
      return this.fail(res, '上传图片失败: ' + error.message);
    }
  }
  /**
   * 通用文字识别
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async recognizeGeneral(req, res) {
    try {
      // 检查是否有文件上传或URL
      const { imageUrl } = req.body;
      let imageData;
      let isUrl = false;
      
      if (imageUrl) {
        // 使用图片URL
        imageData = imageUrl;
        isUrl = true;
      } else if (req.file || req.files) {
        // 使用上传的文件
        const file = req.file || req.files[0];
        imageData = file.buffer;
      } else {
        return this.fail(res, '请提供图片文件或图片URL');
      }
      
      // 调用OCR服务进行识别
      const result = await this.aliyunOCRService.recognizeGeneral(imageData, isUrl);
      
      if (result.success) {
        return this.success(res, result.data, '文字识别成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('文字识别失败:', error);
      return this.fail(res, '文字识别失败: ' + error.message);
    }
  }

  /**
   * 身份证识别
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async recognizeIDCard(req, res) {
    try {
      // 检查是否有文件上传或URL
      const imageUrl = req.body.imageUrl ?? null;
      const side = req.body.side ?? 'face';
      let imageData;
      let isUrl = true;
      
      if (imageUrl) {
        // 使用图片URL
        imageData = imageUrl;
        isUrl = true;
      } else if (req.file || req.files) {
        // 使用上传的文件
        const file = req.file || req.files[0];
        imageData = file.buffer;
      } else {
        return this.fail(res, '请提供图片文件或图片URL');
      }
      
      // 调用OCR服务进行识别
      const result = await this.aliyunOCRService.recognizeIDCard(imageData, isUrl, side);
      
      if (result.success) {
        return this.success(res, result.data, '身份证识别成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('身份证识别失败:', error);
      return this.fail(res, '身份证识别失败: ' + error.message);
    }
  }

  /**
   * 驾驶证识别
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async recognizeDrivingLicense(req, res) {
    try {
      // 检查是否有文件上传或URL
      const { imageUrl, side } = req.body;
      let imageData;
      let isUrl = false;
      
      if (imageUrl) {
        // 使用图片URL
        imageData = imageUrl;
        isUrl = true;
      } else if (req.file || req.files) {
        // 使用上传的文件
        const file = req.file || req.files[0];
        imageData = file.buffer;
      } else {
        return this.fail(res, '请提供图片文件或图片URL');
      }
      
      // 调用OCR服务进行识别
      const result = await this.aliyunOCRService.recognizeDrivingLicense(imageData, isUrl, side || 'face');
      
      if (result.success) {
        return this.success(res, result.data, '驾驶证识别成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('驾驶证识别失败:', error);
      return this.fail(res, '驾驶证识别失败: ' + error.message);
    }
  }

  /**
   * 营业执照识别
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async recognizeBusinessLicense(req, res) {
    try {
      // 检查是否有文件上传或URL
      const imageUrl = req.body.imageUrl ?? null;
      let imageData;
      let isUrl = false;
      
      if (imageUrl) {
        // 使用图片URL
        imageData = imageUrl;
        isUrl = true;
      } else if (req.file || req.files) {
        // 使用上传的文件
        const file = req.file || req.files[0];
        imageData = file.buffer;
      } else {
        return this.fail(res, '请提供图片文件或图片URL');
      }
      
      // 调用OCR服务进行识别
      const result = await this.aliyunOCRService.recognizeBusinessLicense(imageData, isUrl);
      
      if (result.success) {
        return this.success(res, result.data, '营业执照识别成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('营业执照识别失败:', error);
      return this.fail(res, '营业执照识别失败: ' + error.message);
    }
  }

  /**
   * 银行卡识别
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async recognizeBankCard(req, res) {
    try {
      // 检查是否有文件上传或URL
      const { imageUrl } = req.body;
      let imageData;
      let isUrl = false;
      
      if (imageUrl) {
        // 使用图片URL
        imageData = imageUrl;
        isUrl = true;
      } else if (req.file || req.files) {
        // 使用上传的文件
        const file = req.file || req.files[0];
        imageData = file.buffer;
      } else {
        return this.fail(res, '请提供图片文件或图片URL');
      }
      
      // 调用OCR服务进行识别
      const result = await this.aliyunOCRService.recognizeBankCard(imageData, isUrl);
      
      if (result.success) {
        return this.success(res, result.data, '银行卡识别成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('银行卡识别失败:', error);
      return this.fail(res, '银行卡识别失败: ' + error.message);
    }
  }

  /**
   * 表格识别
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async recognizeTable(req, res) {
    try {
      // 检查是否有文件上传或URL
      const { imageUrl } = req.body;
      let imageData;
      let isUrl = false;
      
      if (imageUrl) {
        // 使用图片URL
        imageData = imageUrl;
        isUrl = true;
      } else if (req.file || req.files) {
        // 使用上传的文件
        const file = req.file || req.files[0];
        imageData = file.buffer;
      } else {
        return this.fail(res, '请提供图片文件或图片URL');
      }
      
      // 调用OCR服务进行识别
      const result = await this.aliyunOCRService.recognizeTable(imageData, isUrl);
      
      if (result.success) {
        return this.success(res, result.data, '表格识别成功');
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('表格识别失败:', error);
      return this.fail(res, '表格识别失败: ' + error.message);
    }
  }

  /**
   * 测试OCR连接
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async testOCRConnection(req, res) {
    try {
      const result = await this.aliyunOCRService.testConnection();
      
      if (result.success) {
        return this.success(res, null, result.message);
      } else {
        return this.fail(res, result.message);
      }
    } catch (error) {
      console.error('测试OCR连接失败:', error);
      return this.fail(res, '测试OCR连接失败: ' + error.message);
    }
  }
}

module.exports = AliyunController;

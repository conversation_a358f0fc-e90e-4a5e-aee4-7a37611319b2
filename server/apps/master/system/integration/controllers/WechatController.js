/**
 * 微信平台控制器
 * 处理微信相关的API请求
 */
const BaseController = require('../../../../../core/controllers/BaseController');
const WechatOAuthService = require('../services/wechat/WechatOAuthService');
const crypto = require('crypto');

class WechatController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.wechatOAuthService = new WechatOAuthService(prisma);
  }

  /**
   * 处理微信服务器验证请求
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async verify(req, res) {
    try {
      const { signature, timestamp, nonce, echostr } = req.query;
      
      if (!signature || !timestamp || !nonce) {
        return this.fail(res, '参数不完整');
      }

      console.log('微信服务器验证请求:', { signature, timestamp, nonce, echostr });
      
      // 获取微信配置中的token
      const config = await this.wechatOAuthService.getConfig();
      const token = config.find(item => item.config_key === 'token')?.config_value;
      
      if (!token) {
        return this.fail(res, '微信配置中缺少token');
      }

      // 按照微信的规则进行签名验证
      const arr = [token, timestamp, nonce].sort();
      const str = arr.join('');
      const sha1Str = crypto.createHash('sha1').update(str).digest('hex');
      
      if (sha1Str === signature) {
        // 验证成功，返回echostr
        console.log('微信服务器验证成功');
        return res.send(echostr);
      } else {
        console.error('微信服务器验证失败');
        return this.fail(res, '签名验证失败');
      }
    } catch (error) {
      console.error('处理微信服务器验证请求失败:', error);
      return this.fail(res, '处理微信服务器验证请求失败: ' + error.message);
    }
  }

  /**
   * 获取微信JS SDK配置
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getJsConfig(req, res) {
    try {
      const { url } = req.body;
      
      if (!url) {
        return this.fail(res, 'URL不能为空');
      }

      console.log('获取微信JS SDK配置，URL:', url);
      const jsConfig = await this.wechatOAuthService.getJsConfig(url);
      return this.success(res, jsConfig, '获取微信JS SDK配置成功');
    } catch (error) {
      console.error('获取微信JS SDK配置失败:', error);
      return this.fail(res, '获取微信JS SDK配置失败: ' + error.message);
    }
  }

  /**
   * 获取微信授权URL
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getAuthUrl(req, res) {
    try {
      const { redirectUrl, scope = 'snsapi_base' } = req.body;
      
      if (!redirectUrl) {
        return this.fail(res, '重定向URL不能为空');
      }

      console.log('获取微信授权URL，重定向URL:', redirectUrl);
      const authUrl = await this.wechatOAuthService.getAuthUrl(redirectUrl, scope);
      return this.success(res, { authUrl }, '获取微信授权URL成功');
    } catch (error) {
      console.error('获取微信授权URL失败:', error);
      return this.fail(res, '获取微信授权URL失败: ' + error.message);
    }
  }

  /**
   * 通过code获取用户信息
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getUserInfo(req, res) {
    try {
      const { code } = req.body;
      
      if (!code) {
        return this.fail(res, '授权码不能为空');
      }

      console.log('通过code获取用户信息，code:', code);
      
      // 先获取访问令牌
      const tokenResult = await this.wechatOAuthService.getAccessToken(code);
      
      if (!tokenResult.access_token || !tokenResult.openid) {
        return this.fail(res, '获取访问令牌失败');
      }
      
      // 获取用户信息
      const userInfo = await this.wechatOAuthService.getUserInfo(tokenResult.access_token, tokenResult.openid);
      return this.success(res, userInfo, '获取用户信息成功');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return this.fail(res, '获取用户信息失败: ' + error.message);
    }
  }
}

module.exports = WechatController;

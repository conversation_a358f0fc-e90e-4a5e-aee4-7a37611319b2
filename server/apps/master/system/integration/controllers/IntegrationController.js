/**
 * 第三方平台集成管理控制器
 * 负责管理所有第三方平台的配置和状态
 * 使用 ConfigurationService 来存储和读取配置数据
 */
const BaseController = require('../../../../../core/controllers/BaseController');
const IntegrationService = require('../services/IntegrationService');
const { PrismaClient } = require('@prisma/client');

class IntegrationController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma || new PrismaClient();
    this.integrationService = new IntegrationService(this.prisma);
  }

  /**
   * 获取支持的集成平台列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getSupportedPlatforms(req, res) {
    try {
      const platforms = await this.integrationService.getSupportedPlatforms();
      return this.success(res, platforms, '获取支持的平台列表成功');
    } catch (error) {
      console.error('获取支持的平台列表失败:', error);
      return this.fail(res, '获取支持的平台列表失败: ' + error.message);
    }
  }

  /**
   * 测试平台集成连接
   * 注意：该方法测试与第三方服务的连接是否正常
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async testConnection(req, res) {
    try {
      const { platform } = req.params;
      const configData = req.body;
      
      if (!platform) {
        return this.fail(res, '平台标识不能为空');
      }
      
      if (!configData) {
        return this.fail(res, '配置数据不能为空');
      }
      
      console.log(`测试平台[${platform}]集成连接`);
      const result = await this.integrationService.testConnection(platform, configData);
      return this.success(res, result, `测试${platform}平台集成连接成功`);
    } catch (error) {
      console.error('测试平台集成连接失败:', error);
      return this.fail(res, '测试平台集成连接失败: ' + error.message);
    }
  }
}

module.exports = IntegrationController;

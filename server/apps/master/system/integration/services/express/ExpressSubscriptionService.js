/**
 * 快递订阅服务
 */
const Express100Service = require('../Express100Service');

class ExpressSubscriptionService {
  constructor(prisma) {
    this.prisma = prisma;
    this.express100Service = new Express100Service(prisma);
  }

  /**
   * 获取未订阅的包裹列表
   * @param {Number} limit 限制数量，默认100
   * @returns {Promise<Object>} 未订阅包裹列表
   */
  async getUnsubscribedPackages(limit = 100) {
    try {
      // 查询已发货但未订阅的包裹
      const packages = await this.prisma.order_packages.findMany({
        where: {
          shipping_status: 1, // 已发货
          tracking_number: {
            not: null
          },
          shipping_company_code: {
            not: null
          }
        },
        take: limit,
        orderBy: {
          shipped_at: 'asc'
        }
      });

      // 过滤掉已经订阅的包裹
      const filteredPackages = [];
      for (const pkg of packages) {
        const existingSubscription = await this.prisma.expressSubscription.findFirst({
          where: {
            package_id: pkg.id,
            express_no: pkg.tracking_number,
            subscription_status: 'subscribed',
            deleted_at: null
          }
        });

        if (!existingSubscription) {
          filteredPackages.push(pkg);
        }
      }

      return {
        success: true,
        data: filteredPackages
      };
    } catch (error) {
      console.error('获取未订阅包裹失败:', error);
      return {
        success: false,
        message: `获取未订阅包裹失败: ${error.message}`
      };
    }
  }

  /**
   * 订阅快递轨迹
   * @param {Object} packageData 包裹数据
   * @returns {Promise<Object>} 订阅结果
   */
  async subscribeExpress(packageData) {
    try {
      const { id: package_id, order_id, tracking_number: express_no, shipping_company_code: express_code } = packageData;
      const now = BigInt(Date.now());

      // 检查是否已经订阅
      const existingSubscription = await this.prisma.expressSubscription.findFirst({
        where: {
          package_id: BigInt(package_id),
          express_no: express_no,
          subscription_status: 'subscribed',
          deleted_at: null
        }
      });

      if (existingSubscription) {
        return {
          success: true,
          message: '该包裹已经订阅过了',
          data: existingSubscription
        };
      }

      // 创建或更新订阅记录
      let subscription = await this.prisma.expressSubscription.findFirst({
        where: {
          package_id: BigInt(package_id),
          express_no: express_no,
          deleted_at: null
        }
      });

      if (!subscription) {
        subscription = await this.prisma.expressSubscription.create({
          data: {
            package_id: BigInt(package_id),
            order_id: BigInt(order_id),
            express_no: express_no,
            express_code: express_code,
            subscription_status: 'pending',
            created_at: now,
            updated_at: now
          }
        });
      }

      // 调用快递100订阅接口
      const subscribeResult = await this.express100Service.subscribeExpress({
        expressNo: express_no,
        expressCode: express_code
      });

      // 更新订阅状态
      const updateData = {
        updated_at: now
      };

      if (subscribeResult.success) {
        updateData.subscription_status = 'subscribed';
        updateData.subscription_time = now;
        updateData.error_message = null;
      } else {
        updateData.subscription_status = 'failed';
        updateData.error_message = subscribeResult.message;
      }

      const updatedSubscription = await this.prisma.expressSubscription.update({
        where: {
          id: subscription.id
        },
        data: updateData
      });

      return {
        success: subscribeResult.success,
        message: subscribeResult.success ? '订阅成功' : `订阅失败: ${subscribeResult.message}`,
        data: updatedSubscription
      };
    } catch (error) {
      console.error('订阅快递轨迹失败:', error);
      return {
        success: false,
        message: `订阅快递轨迹失败: ${error.message}`
      };
    }
  }

  /**
   * 批量订阅快递轨迹
   * @param {Array} packages 包裹列表
   * @returns {Promise<Object>} 批量订阅结果
   */
  async batchSubscribeExpress(packages) {
    const results = {
      success: 0,
      failed: 0,
      total: packages.length,
      details: []
    };

    for (const packageData of packages) {
      try {
        const result = await this.subscribeExpress(packageData);
        
        if (result.success) {
          results.success++;
        } else {
          results.failed++;
        }

        results.details.push({
          package_id: packageData.id,
          express_no: packageData.tracking_number,
          success: result.success,
          message: result.message
        });

        // 添加延迟避免频率限制
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        results.failed++;
        results.details.push({
          package_id: packageData.id,
          express_no: packageData.tracking_number,
          success: false,
          message: error.message
        });
      }
    }

    return {
      success: true,
      data: results,
      message: `批量订阅完成，成功: ${results.success}，失败: ${results.failed}`
    };
  }

  /**
   * 处理快递100回调
   * @param {Object} callbackData 回调数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleCallback(callbackData) {
    try {
      console.log('ExpressSubscriptionService 处理回调数据:', callbackData);

      // 解析快递100回调数据结构
      let parsedData;
      if (callbackData.param) {
        // 快递100的回调数据在param字段中，且是JSON字符串
        try {
          parsedData = JSON.parse(callbackData.param);
          console.log('解析后的回调数据:', parsedData);
        } catch (parseError) {
          console.error('解析回调数据失败:', parseError);
          return {
            success: false,
            message: '回调数据格式错误'
          };
        }
      } else {
        parsedData = callbackData;
      }

      // 从lastResult中获取实际的物流数据
      const lastResult = parsedData.lastResult || parsedData;
      const { nu: express_no, com: express_code, data: trackData } = lastResult;

      console.log('提取的快递信息:', { express_no, express_code, trackDataLength: trackData?.length });

      if (!express_no || !express_code) {
        return {
          success: false,
          message: '回调数据不完整'
        };
      }

      // 查找对应的订阅记录
      const subscription = await this.prisma.expressSubscription.findFirst({
        where: {
          express_no: express_no,
          express_code: express_code,
          subscription_status: 'subscribed',
          deleted_at: null
        }
      });

      if (!subscription) {
        console.log(`未找到快递单号 ${express_no} 的订阅记录`);
        return {
          success: false,
          message: '未找到对应的订阅记录'
        };
      }

      // 更新回调统计
      const now = BigInt(Date.now());
      await this.prisma.expressSubscription.update({
        where: {
          id: subscription.id
        },
        data: {
          callback_count: subscription.callback_count + 1,
          last_callback_time: now,
          updated_at: now
        }
      });

      // 更新轨迹信息
      const ExpressTrackService = require('./ExpressTrackService');
      const trackService = new ExpressTrackService(this.prisma);

      console.log('准备更新轨迹信息:', {
        package_id: subscription.package_id,
        order_id: subscription.order_id,
        express_no: express_no,
        express_code: express_code,
        status: lastResult.state,
        condition: lastResult.condition,
        ischeck: lastResult.ischeck
      });

      const trackResult = await trackService.createOrUpdateTrack({
        package_id: subscription.package_id,
        order_id: subscription.order_id,
        express_no: express_no,
        express_code: express_code,
        express_name: '', // 快递公司名称可以从express_code映射获取
        status: lastResult.state || '0',
        condition_code: lastResult.condition || '',
        is_check: lastResult.ischeck || '0',
        track_data: lastResult,
        last_update_time: now
      });

      console.log('轨迹更新结果:', trackResult);

      return {
        success: true,
        message: '回调处理成功',
        data: trackResult.data
      };
    } catch (error) {
      console.error('处理快递100回调失败:', error);
      return {
        success: false,
        message: `处理回调失败: ${error.message}`
      };
    }
  }

  /**
   * 获取订阅统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getSubscriptionStats() {
    try {
      const stats = await this.prisma.expressSubscription.groupBy({
        by: ['subscription_status'],
        where: {
          deleted_at: null
        },
        _count: {
          id: true
        }
      });

      const result = {
        total: 0,
        pending: 0,
        subscribed: 0,
        failed: 0
      };

      stats.forEach(stat => {
        result.total += stat._count.id;
        result[stat.subscription_status] = stat._count.id;
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取订阅统计失败:', error);
      return {
        success: false,
        message: `获取订阅统计失败: ${error.message}`
      };
    }
  }
}

module.exports = ExpressSubscriptionService;

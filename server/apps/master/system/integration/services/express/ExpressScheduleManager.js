/**
 * 快递订阅定时任务管理器
 * 在应用启动时自动启动定时任务
 */
const ExpressScheduleService = require('./ExpressScheduleService');

class ExpressScheduleManager {
  constructor(prisma) {
    this.prisma = prisma;
    this.scheduleService = null;
  }

  /**
   * 初始化并启动定时任务
   */
  async initialize() {
    try {
      console.log('初始化快递订阅定时任务...');
      
      this.scheduleService = new ExpressScheduleService(this.prisma);
      
      // 启动定时任务，每5分钟执行一次
      this.scheduleService.start('*/5 * * * *');
      
      console.log('快递订阅定时任务初始化完成');
      
      // 应用启动时执行一次订阅任务
      setTimeout(async () => {
        console.log('应用启动，执行一次快递订阅任务...');
        await this.scheduleService.runOnce();
      }, 10000); // 延迟10秒执行，确保应用完全启动
      
    } catch (error) {
      console.error('初始化快递订阅定时任务失败:', error);
    }
  }

  /**
   * 停止定时任务
   */
  stop() {
    if (this.scheduleService) {
      this.scheduleService.stop();
      console.log('快递订阅定时任务已停止');
    }
  }

  /**
   * 获取定时任务服务实例
   */
  getScheduleService() {
    return this.scheduleService;
  }
}

// 创建全局实例
let scheduleManager = null;

/**
 * 获取定时任务管理器实例
 */
function getScheduleManager(prisma) {
  if (!scheduleManager) {
    scheduleManager = new ExpressScheduleManager(prisma);
  }
  return scheduleManager;
}

module.exports = {
  ExpressScheduleManager,
  getScheduleManager
};

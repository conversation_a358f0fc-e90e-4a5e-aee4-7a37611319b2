/**
 * 快递轨迹服务
 */
class ExpressTrackService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取包裹的轨迹信息
   * @param {BigInt} packageId 包裹ID
   * @returns {Promise<Object>} 轨迹信息
   */
  async getTrackByPackageId(packageId) {
    try {
      const track = await this.prisma.expressTrack.findFirst({
        where: {
          package_id: packageId,
          deleted_at: null
        },
        orderBy: {
          updated_at: 'desc'
        }
      });

      return {
        success: true,
        data: track
      };
    } catch (error) {
      console.error('获取轨迹信息失败:', error);
      return {
        success: false,
        message: `获取轨迹信息失败: ${error.message}`
      };
    }
  }

  /**
   * 根据快递单号获取轨迹信息
   * @param {String} expressNo 快递单号
   * @returns {Promise<Object>} 轨迹信息
   */
  async getTrackByExpressNo(expressNo) {
    try {
      const track = await this.prisma.expressTrack.findFirst({
        where: {
          express_no: expressNo,
          deleted_at: null
        },
        orderBy: {
          updated_at: 'desc'
        }
      });

      return {
        success: true,
        data: track
      };
    } catch (error) {
      console.error('获取轨迹信息失败:', error);
      return {
        success: false,
        message: `获取轨迹信息失败: ${error.message}`
      };
    }
  }

  /**
   * 创建或更新轨迹信息
   * @param {Object} trackData 轨迹数据
   * @returns {Promise<Object>} 操作结果
   */
  async createOrUpdateTrack(trackData) {
    try {
      const {
        package_id,
        order_id,
        express_no,
        express_code,
        express_name,
        status,
        condition_code,
        is_check,
        track_data,
        last_update_time
      } = trackData;

      const now = BigInt(Date.now());

      // 尝试更新现有记录
      const existingTrack = await this.prisma.expressTrack.findFirst({
        where: {
          package_id: BigInt(package_id),
          express_no: express_no,
          deleted_at: null
        }
      });

      let result;
      if (existingTrack) {
        // 更新现有记录
        result = await this.prisma.expressTrack.update({
          where: {
            id: existingTrack.id
          },
          data: {
            express_name,
            status,
            condition_code,
            is_check,
            track_data,
            last_update_time: last_update_time ? BigInt(last_update_time) : now,
            updated_at: now
          }
        });
      } else {
        // 创建新记录
        result = await this.prisma.expressTrack.create({
          data: {
            package_id: BigInt(package_id),
            order_id: BigInt(order_id),
            express_no,
            express_code,
            express_name,
            status,
            condition_code,
            is_check,
            track_data,
            last_update_time: last_update_time ? BigInt(last_update_time) : now,
            created_at: now,
            updated_at: now
          }
        });
      }

      return {
        success: true,
        data: result,
        message: existingTrack ? '轨迹信息更新成功' : '轨迹信息创建成功'
      };
    } catch (error) {
      console.error('创建或更新轨迹信息失败:', error);
      return {
        success: false,
        message: `创建或更新轨迹信息失败: ${error.message}`
      };
    }
  }

  /**
   * 获取订单的所有包裹轨迹
   * @param {BigInt} orderId 订单ID
   * @returns {Promise<Object>} 轨迹信息列表
   */
  async getTracksByOrderId(orderId) {
    try {
      const tracks = await this.prisma.expressTrack.findMany({
        where: {
          order_id: orderId,
          deleted_at: null
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      return {
        success: true,
        data: tracks
      };
    } catch (error) {
      console.error('获取订单轨迹信息失败:', error);
      return {
        success: false,
        message: `获取订单轨迹信息失败: ${error.message}`
      };
    }
  }

  /**
   * 删除轨迹信息（软删除）
   * @param {BigInt} trackId 轨迹ID
   * @returns {Promise<Object>} 操作结果
   */
  async deleteTrack(trackId) {
    try {
      const now = BigInt(Date.now());
      
      await this.prisma.expressTrack.update({
        where: {
          id: BigInt(trackId)
        },
        data: {
          deleted_at: now,
          updated_at: now
        }
      });

      return {
        success: true,
        message: '轨迹信息删除成功'
      };
    } catch (error) {
      console.error('删除轨迹信息失败:', error);
      return {
        success: false,
        message: `删除轨迹信息失败: ${error.message}`
      };
    }
  }
}

module.exports = ExpressTrackService;

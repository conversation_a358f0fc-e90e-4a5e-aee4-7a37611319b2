/**
 * 验证码服务
 * 处理验证码的生成、存储和验证
 */
const { PrismaClient } = require('@prisma/client');
const redisUtil = require('../../../../../core/utils/RedisUtil');

// 常量定义
const CODE_EXPIRE_TIME = 300; // 验证码有效期，单位：秒（5分钟）
const CODE_INTERVAL_TIME = 60; // 验证码获取间隔，单位：秒（1分钟）
const CODE_LENGTH = 6; // 验证码长度
const CODE_PREFIX = 'verification_code:'; // Redis键前缀

class VerificationCodeService {
  constructor(prisma) {
    this.prisma = prisma || new PrismaClient();
  }

  /**
   * 生成随机验证码
   * @param {number} length 验证码长度
   * @returns {string} 生成的验证码
   */
  generateCode(length = CODE_LENGTH) {
    let code = '';
    for (let i = 0; i < length; i++) {
      code += Math.floor(Math.random() * 10).toString();
    }
    return code;
  }

  /**
   * 获取验证码
   * @param {string} phoneNumber 手机号码
   * @param {string} type 验证码类型（例如：login, register, resetPassword）
   * @returns {Promise<Object>} 验证码信息
   */
  async getCode(phoneNumber, type = 'default') {
    try {
      console.log(`开始获取验证码: 手机号=${phoneNumber}, 类型=${type}`);
      
      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
        console.log('手机号格式不正确');
        throw new Error('手机号格式不正确');
      }

      // 构建Redis键
      const codeKey = `${CODE_PREFIX}${type}:${phoneNumber}`;
      const intervalKey = `${CODE_PREFIX}${type}:interval:${phoneNumber}`;
      console.log(`验证码Redis键: ${codeKey}`);
      console.log(`间隔Redis键: ${intervalKey}`);

      // 检查是否在获取间隔内
      const intervalExists = await redisUtil.getClient().get(intervalKey);
      console.log(`间隔检查结果: ${intervalExists ? '存在间隔限制' : '无间隔限制'}`);
      
      if (intervalExists) {
        // 获取剩余时间
        const ttl = await redisUtil.getClient().ttl(intervalKey);
        console.log(`剩余间隔时间: ${ttl}秒`);
        throw new Error(`获取验证码过于频繁，请${Math.ceil(ttl / 60)}分钟后再试`);
      }

      // 生成验证码
      const code = this.generateCode();
      console.log(`生成的验证码: ${code}`);

      // 存储验证码到Redis
      await redisUtil.getClient().set(codeKey, code, { EX: CODE_EXPIRE_TIME });
      console.log(`验证码已存储到Redis，过期时间: ${CODE_EXPIRE_TIME}秒`);
      
      // 设置获取间隔
      await redisUtil.getClient().set(intervalKey, '1', { EX: CODE_INTERVAL_TIME });
      console.log(`已设置获取间隔: ${CODE_INTERVAL_TIME}秒`);

      // 再次检查验证码是否成功存储
      const savedCode = await redisUtil.getClient().get(codeKey);
      console.log(`检查存储结果: ${savedCode === code ? '存储成功' : '存储失败'}`);

      return {
        success: true,
        code: code, // 注意：实际生产环境中不应该返回验证码，这里为了方便测试
        expireTime: CODE_EXPIRE_TIME,
        message: '验证码已发送'
      };
    } catch (error) {
      console.error('获取验证码失败:', error);
      throw error;
    }
  }

  /**
   * 验证验证码
   * @param {string} phoneNumber 手机号码
   * @param {string} code 验证码
   * @param {string} type 验证码类型
   * @returns {Promise<boolean>} 验证结果
   */
  async verifyCode(phoneNumber, code, type = 'default') {
    try {
      console.log(`开始验证验证码: 手机号=${phoneNumber}, 验证码=${code}, 类型=${type}`);
      console.log(`验证码类型: ${typeof code}, 长度: ${code.length}`);
      
      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
        console.log('手机号格式不正确');
        throw new Error('手机号格式不正确');
      }

      // 构建Redis键
      const codeKey = `${CODE_PREFIX}${type}:${phoneNumber}`;
      console.log(`查询Redis键: ${codeKey}`);

      // 从Redis获取验证码
      const savedCode = await redisUtil.getClient().get(codeKey);
      console.log(`Redis中存储的验证码: ${savedCode || '无'}`);
      console.log(`Redis中验证码类型: ${typeof savedCode}, 长度: ${savedCode ? savedCode.length : 0}`);
      
      if (!savedCode) {
        console.log('验证码已过期或不存在');
        throw new Error('验证码已过期或不存在');
      }

      // 确保类型一致（转换为字符串）
      const normalizedCode = String(code).trim();
      const normalizedSavedCode = String(savedCode).trim();
      
      console.log(`规范化后的输入验证码: ${normalizedCode}`);
      console.log(`规范化后的存储验证码: ${normalizedSavedCode}`);
      
      // 验证码比对
      const isMatch = normalizedCode === normalizedSavedCode;
      console.log(`验证码比对结果: ${isMatch ? '匹配' : '不匹配'}`);
      
      if (!isMatch) {
        console.log('验证码不正确');
        throw new Error('验证码不正确');
      }

      // 验证成功，但不删除验证码，允许重复验证
      console.log(`验证成功，保留Redis键: ${codeKey}，允许重复验证`);

      return true;
    } catch (error) {
      console.error('验证码验证失败:', error);
      throw error;
    }
  }
}

module.exports = VerificationCodeService;

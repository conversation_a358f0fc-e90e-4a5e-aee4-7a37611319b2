/**
 * 阿里云OCR服务
 * 处理阿里云OCR识别相关功能
 */
const { PrismaClient } = require('@prisma/client');
const ocr_api20210707 = require('@alicloud/ocr-api20210707');
const OpenApi = require('@alicloud/openapi-client');
const Util = require('@alicloud/tea-util');
const Credential = require('@alicloud/credentials');

// 常量定义
const CONFIG_TYPE = 'OCR';

class AliyunOCRService {
  constructor(prisma) {
    this.prisma = prisma || new PrismaClient();
    this.client = null;
  }

  /**
   * 获取阿里云配置
   * @returns {Promise<Array>} 阿里云配置列表
   */
  async getConfig() {
    try {
      console.log('获取阿里云配置');
      
      // 从配置表中查询阿里云平台的所有配置
      const configs = await this.prisma.baseSystemConfig.findMany({
        where: {
          config_type: CONFIG_TYPE,
          deleted_at: null
        },
        select: {
          config_key: true,
          config_value: true
        }
      });
      
      return configs;
    } catch (error) {
      console.error('获取阿里云配置失败:', error);
      throw new Error(`获取阿里云配置失败: ${error.message}`);
    }
  }

  /**
   * 获取阿里云OCR客户端
   * @returns {Promise<Object>} 阿里云OCR客户端
   */
  async getOCRClient() {
    if (this.client) {
      return this.client;
    }

    try {
      console.log('初始化阿里云OCR客户端');

      // 从数据库获取阿里云OCR配置
      const configItems = await this.getConfig();

      // 将配置数组转换为对象
      const configMap = {};
      configItems.forEach(item => {
        configMap[item.config_key] = item.config_value;
      });

      // 检查是否启用OCR服务
      if (configMap.enabled !== 'true') {
        throw new Error('阿里云OCR服务未启用');
      }

      // 获取accessKeyId和accessKeySecret
      const accessKeyId = configMap.accessKeyId;
      const accessKeySecret = configMap.accessKeySecret;
      const region = configMap.region || 'cn-hangzhou';

      // 检查是否有效
      if (!accessKeyId || !accessKeySecret) {
        throw new Error('阿里云OCR配置不完整，缺少accessKeyId或accessKeySecret');
      }

      // 初始化客户端配置
      const config = new OpenApi.Config({
        accessKeyId,
        accessKeySecret,
      });

      // 根据区域设置端点
      config.endpoint = `ocr-api.${region}.aliyuncs.com`;

      // 创建客户端
      this.client = new ocr_api20210707.default(config);

      return this.client;
    } catch (error) {
      console.error('初始化阿里云OCR客户端失败:', error);
      throw new Error(`初始化阿里云OCR客户端失败: ${error.message}`);
    }
  }

  /**
   * 通用文字识别
   * @param {Buffer|string} imageData 图片数据Buffer或图片URL
   * @param {boolean} isUrl 是否是URL
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeGeneral(imageData, isUrl = false) {
    try {
      console.log('进行通用文字识别');
      
      // 获取OCR客户端
      const client = await this.getOCRClient();
      
      // 创建请求对象
      const recognizeRequest = new ocr_api20210707.RecognizeGeneralRequest({});
      
      // 设置图片数据
      if (isUrl) {
        recognizeRequest.url = imageData;
      } else {
        recognizeRequest.body = imageData.toString('base64');
      }
      
      // 创建运行时选项
      const runtime = new Util.RuntimeOptions({});
      
      // 调用识别接口
      const result = await client.recognizeGeneralWithOptions(recognizeRequest, runtime);
      
      return { success: true, data: result.body };
    } catch (error) {
      console.error('通用文字识别失败:', error);
      return {
        success: false,
        message: `通用文字识别失败: ${error.message}`
      };
    }
  }

  /**
   * 识别身份证
   * @param {Buffer|String} imageData 图片数据，可以是Buffer或URL字符串
   * @param {Boolean} isUrl 是否是URL
   * @param {String} side 身份证正反面，'face'或'back'
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeIDCard(imageData, isUrl = false, side = 'face') {
    try {
      console.log(`识别身份证${side === 'back' ? '背面' : '正面'}`);
      
      // 获取OCR客户端
      const client = await this.getOCRClient();
      
      // 创建请求对象
      const recognizeRequest = new ocr_api20210707.RecognizeIdcardRequest({
        side: side === 'back' ? 'back' : 'face'
      });
      
      // 设置图片数据
      if (isUrl) {
        recognizeRequest.url = imageData;
      } else {
        recognizeRequest.body = imageData.toString('base64');
      }
      
      // 创建运行时选项
      const runtime = new Util.RuntimeOptions({});
      
      // 调用识别接口
      const result = await client.recognizeIdcardWithOptions(recognizeRequest, runtime);
      
      return { success: true, data: result.body };
    } catch (error) {
      console.error('身份证识别失败:', error);
      return { success: false, message: `身份证识别失败: ${error.message}` };
    }
  }

  /**
   * 驾驶证识别
   * @param {Buffer|String} imageData 图片数据，可以是Buffer或URL字符串
   * @param {Boolean} isUrl 是否是URL
   * @param {String} side 驾驶证正反面，'face'或'back'
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeDrivingLicense(imageData, isUrl = false, side = 'face') {
    try {
      console.log(`识别驾驶证${side === 'back' ? '副页' : '正面'}`);
      
      // 获取OCR客户端
      const client = await this.getOCRClient();
      
      // 创建请求对象
      const recognizeRequest = new ocr_api20210707.RecognizeDrivingLicenseRequest({
        side: side === 'back' ? 'back' : 'face'
      });
      
      // 设置图片数据
      if (isUrl) {
        recognizeRequest.url = imageData;
      } else {
        recognizeRequest.body = imageData.toString('base64');
      }
      
      // 创建运行时选项
      const runtime = new Util.RuntimeOptions({});
      
      // 调用识别接口
      const result = await client.recognizeDrivingLicenseWithOptions(recognizeRequest, runtime);
      
      return { success: true, data: result.body };
    } catch (error) {
      console.error('驾驶证识别失败:', error);
      return { success: false, message: `驾驶证识别失败: ${error.message}` };
    }
  }

  /**
   * 营业执照识别
   * @param {Buffer|String} imageData 图片数据，可以是Buffer或URL字符串
   * @param {Boolean} isUrl 是否是URL
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeBusinessLicense(imageData, isUrl = false) {
    try {
      console.log('识别营业执照');
      
      // 获取OCR客户端
      const client = await this.getOCRClient();
      
      // 创建请求对象
      const recognizeRequest = new ocr_api20210707.RecognizeBusinessLicenseRequest({});
      
      // 设置图片数据
      if (isUrl) {
        recognizeRequest.url = imageData;
      } else {
        recognizeRequest.body = imageData.toString('base64');
      }
      
      // 创建运行时选项
      const runtime = new Util.RuntimeOptions({});
      
      // 调用识别接口
      const result = await client.recognizeBusinessLicenseWithOptions(recognizeRequest, runtime);
      
      return { success: true, data: result.body };
    } catch (error) {
      console.error('营业执照识别失败:', error);
      return { success: false, message: `营业执照识别失败: ${error.message}` };
    }
  }

  /**
   * 银行卡识别
   * @param {Buffer|String} imageData 图片数据，可以是Buffer或URL字符串
   * @param {Boolean} isUrl 是否是URL
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeBankCard(imageData, isUrl = false) {
    try {
      console.log('识别银行卡');
      
      // 获取OCR客户端
      const client = await this.getOCRClient();
      
      // 创建请求对象
      const recognizeRequest = new ocr_api20210707.RecognizeBankCardRequest({});
      
      // 设置图片数据
      if (isUrl) {
        recognizeRequest.url = imageData;
      } else {
        recognizeRequest.body = imageData.toString('base64');
      }
      
      // 创建运行时选项
      const runtime = new Util.RuntimeOptions({});
      
      // 调用识别接口
      const result = await client.recognizeBankCardWithOptions(recognizeRequest, runtime);
      
      return { success: true, data: result.body };
    } catch (error) {
      console.error('银行卡识别失败:', error);
      return { success: false, message: `银行卡识别失败: ${error.message}` };
    }
  }

  /**
   * 表格识别
   * @param {Buffer|String} imageData 图片数据，可以是Buffer或URL字符串
   * @param {Boolean} isUrl 是否是URL
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeTable(imageData, isUrl = false) {
    try {
      console.log('识别表格');
      
      // 获取OCR客户端
      const client = await this.getOCRClient();
      
      // 创建请求对象
      const recognizeRequest = new ocr_api20210707.RecognizeTableOcrRequest({});
      
      // 设置图片数据
      if (isUrl) {
        recognizeRequest.url = imageData;
      } else {
        recognizeRequest.body = imageData.toString('base64');
      }
      
      // 创建运行时选项
      const runtime = new Util.RuntimeOptions({});
      
      // 调用识别接口
      const result = await client.recognizeTableOcrWithOptions(recognizeRequest, runtime);
      
      return { success: true, data: result.body };
    } catch (error) {
      console.error('表格识别失败:', error);
      return { success: false, message: `表格识别失败: ${error.message}` };
    }
  }

  /**
   * 测试OCR连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      console.log('测试阿里云OCR连接');
      
      // 获取OCR客户端
      const client = await this.getOCRClient();
      
      // 如果能成功获取客户端，则连接成功
      // 我们不需要实际发送请求，因为客户端初始化已经验证了配置是否正确
      console.log('阿里云OCR客户端初始化成功');
      
      // 返回成功结果
      
      return {
        success: true,
        message: '阿里云OCR连接测试成功'
      };
    } catch (error) {
      console.error('测试阿里云OCR连接失败:', error);
      return {
        success: false,
        message: `测试阿里云OCR连接失败: ${error.message}`
      };
    }
  }
}

module.exports = AliyunOCRService;

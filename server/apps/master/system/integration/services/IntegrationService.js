/**
 * 第三方平台集成管理服务
 * 负责管理所有第三方平台的状态和测试连接功能
 * 注意：配置管理相关功能已移至 ConfigurationService
 */
const { PrismaClient } = require('@prisma/client');
const WechatOAuthService = require('./wechat/WechatOAuthService');
const AliyunOSSService = require('./aliyun/AliyunOSSService');

class IntegrationService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma || new PrismaClient();
    this.wechatService = new WechatOAuthService(this.prisma);
    this.aliyunService = new AliyunOSSService(this.prisma);
  }

  /**
   * 获取所有支持的集成平台列表
   * @returns {Promise<Array>} 平台列表
   */
  async getSupportedPlatforms() {
    console.log('获取所有支持的集成平台列表');
    
    // 定义支持的平台列表
    const platforms = [
      {
        id: 'wechat',
        name: '微信平台',
        description: '微信公众号、小程序、支付等服务',
        icon: 'wechat',
        status: await this.getPlatformStatus('wechat')
      },
      {
        id: 'aliyun',
        name: '阿里云',
        description: '阿里云OSS、短信、邮件等服务',
        icon: 'aliyun',
        status: await this.getPlatformStatus('aliyun')
      }
    ];
    
    return platforms;
  }

  /**
   * 获取平台状态
   * @param {string} platform 平台标识
   * @returns {Promise<number>} 状态码：0-未配置，1-已配置但未启用，2-已启用
   */
  async getPlatformStatus(platform) {
    try {
      console.log(`获取平台[${platform}]状态`);
      
      // 从配置表中查询该平台的配置数量
      const configs = await this.prisma.baseSystemConfig.findMany({
        where: {
          config_type: platform,
          deleted_at: null
        }
      });
      const configCount = configs.length;
      
      // 查询该平台的启用状态
      const statusConfig = await this.prisma.baseSystemConfig.findFirst({
        where: {
          config_type: platform,
          config_key: 'enabled',
          deleted_at: null
        }
      });
      
      if (configCount === 0) {
        return 0; // 未配置
      } else if (!statusConfig || statusConfig.config_value === '0') {
        return 1; // 已配置但未启用
      } else {
        return 2; // 已启用
      }
    } catch (error) {
      console.error(`获取平台[${platform}]状态失败:`, error);
      return 0; // 出错时默认为未配置
    }
  }

  /**
   * 测试平台连接
   * @param {string} platform 平台标识
   * @param {Object} configData 配置数据
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection(platform, configData) {
    try {
      console.log(`测试平台[${platform}]连接`);
      
      // 根据平台类型调用不同的测试方法
      let result = { success: false, message: '不支持的平台类型' };
      
      switch (platform) {
        case 'wechat':
          result = await this.wechatService.testConnection(configData);
          break;
        case 'aliyun':
          result = await this.aliyunService.testConnection(configData);
          break;
        default:
          throw new Error(`不支持的平台类型: ${platform}`);
      }
      
      return result;
    } catch (error) {
      console.error(`测试平台[${platform}]连接失败:`, error);
      return { success: false, message: error.message };
    }
  }
}

module.exports = IntegrationService;

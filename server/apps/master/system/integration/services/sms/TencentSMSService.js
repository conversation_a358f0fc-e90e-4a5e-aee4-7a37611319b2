/**
 * 腾讯云短信服务
 * 实现腾讯云短信发送等功能
 */
const tencentcloud = require('tencentcloud-sdk-nodejs');
const BaseSMSService = require('./BaseSMSService');

class TencentSMSService extends BaseSMSService {
  /**
   * 构造函数
   * @param {Object} config 腾讯云短信配置
   */
  constructor(config) {
    super(config);
    this.client = null;
  }

  /**
   * 初始化腾讯云短信客户端
   * @returns {Object} 腾讯云短信客户端
   */
  createClient() {
    if (this.client) {
      return this.client;
    }

    try {
      console.log('初始化腾讯云短信客户端');
      console.log('当前配置:', JSON.stringify(this.config, null, 2));
      
      const { secretId, secretKey, region = 'ap-guangzhou', sdkAppId } = this.config;
      
      console.log('关键参数检查:');
      console.log('- secretId:', secretId ? '已提供' : '未提供');
      console.log('- secretKey:', secretKey ? '已提供' : '未提供');
      console.log('- sdkAppId:', sdkAppId ? '已提供' : '未提供');
      
      if (!secretId || !secretKey || !sdkAppId) {
        throw new Error('腾讯云配置中缺少必要参数');
      }
      
      // 导入腾讯云短信SDK
      const smsClient = tencentcloud.sms.v20210111;
      
      // 创建SMS客户端实例
      const clientConfig = {
        credential: {
          secretId,
          secretKey,
        },
        region,
        profile: {
          httpProfile: {
            endpoint: "sms.tencentcloudapi.com",
          },
        },
      };
      
      this.client = new smsClient.Client(clientConfig);
      
      return this.client;
    } catch (error) {
      console.error('初始化腾讯云短信客户端失败:', error);
      throw new Error(`初始化腾讯云短信客户端失败: ${error.message}`);
    }
  }

  /**
   * 发送短信
   * @param {string|Array<string>} phoneNumber 手机号码
   * @param {string} templateCode 模板编码
   * @param {Object} templateParams 模板参数
   * @returns {Promise<Object>} 发送结果
   */
  async sendSMS(phoneNumber, templateCode, templateParams) {
    try {
      console.log('发送短信，手机号码:', phoneNumber, '模板编码:', templateCode);
      console.log('当前配置:', JSON.stringify(this.config, null, 2));
      
      const { sdkAppId, signName } = this.config;
      
      console.log('关键参数检查:');
      console.log('- sdkAppId:', sdkAppId ? '已提供' : '未提供');
      console.log('- signName:', signName ? '已提供' : '未提供');
      
      if (!sdkAppId) {
        throw new Error('腾讯云配置中缺少必要参数');
      }
      
      // 获取SMS客户端
      const client = this.createClient();
      
      // 处理模板参数
      let templateParamSet = [];
      
      // 如果是字符串，尝试解析为JSON
      if (typeof templateParams === 'string') {
        try {
          const parsed = JSON.parse(templateParams);
          // 如果解析结果是数组，则直接使用
          if (Array.isArray(parsed)) {
            templateParamSet = parsed;
          }
          // 如果解析结果是对象，则提取值作为参数数组
          else if (typeof parsed === 'object') {
            templateParamSet = Object.values(parsed);
          }
          // 如果解析结果是其他类型，则将其作为单个参数
          else {
            templateParamSet = [String(parsed)];
          }
        } catch (e) {
          // 如果不是有效的JSON，则将其作为单个参数
          templateParamSet = [templateParams];
        }
      } 
      // 如果是对象，提取值作为参数数组
      else if (templateParams && typeof templateParams === 'object') {
        templateParamSet = Object.values(templateParams);
      }
      
      // 处理手机号码格式
      let phoneNumberSet = [];
      if (Array.isArray(phoneNumber)) {
        phoneNumberSet = phoneNumber.map(num => `+86${num}`);
      } else {
        phoneNumberSet = [`+86${phoneNumber}`];
      }
      
      // 构建请求参数
      const params = {
        PhoneNumberSet: phoneNumberSet,
        SmsSdkAppId: sdkAppId,
        SignName: signName,
        TemplateId: templateCode,
        TemplateParamSet: templateParamSet
      };
      
      console.log('构建的请求参数:', params);
      
      // 发送短信
      const result = await client.SendSms(params);
      
      console.log('短信发送结果:', result);
      
      // 检查发送状态
      const isSuccess = result.SendStatusSet && 
                      result.SendStatusSet.length > 0 && 
                      result.SendStatusSet[0].Code === 'Ok';
      
      const message = result.SendStatusSet && 
                     result.SendStatusSet.length > 0 ? 
                     result.SendStatusSet[0].Message : '发送失败';
      
      return {
        success: isSuccess,
        message: message,
        data: {
          requestId: result.RequestId,
          statusSet: result.SendStatusSet
        }
      };
    } catch (error) {
      console.error('发送短信失败:', error);
      throw new Error(`发送短信失败: ${error.message}`);
    }
  }

  /**
   * 测试连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      // 创建客户端来测试连接
      const client = this.createClient();
      
      // 使用简单的参数验证方式测试连接
      // 不调用具体的API，只验证客户端是否成功创建
      if (!this.client) {
        throw new Error('短信客户端创建失败');
      }
      
      return {
        success: true,
        message: '连接成功',
        data: {
          sdkAppId: this.config.sdkAppId,
          region: this.config.region || 'ap-guangzhou'
        }
      };
    } catch (error) {
      console.error('测试腾讯云短信连接失败:', error);
      return {
        success: false,
        message: `连接失败: ${error.message}`,
        error: error.message
      };
    }
  }
}

module.exports = TencentSMSService;

/**
 * 阿里云平台路由
 * 处理阿里云平台相关的路由
 */
const express = require('express');
const { PrismaClient } = require('@prisma/client');
const AliyunController = require('../controllers/AliyunController');
const authMiddleware = require('../../../../../core/middleware/AuthMiddleware');
const multer = require('multer');

// 配置multer存储
const storage = multer.memoryStorage();
const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 限制5MB
  }
});

const router = express.Router();
const prisma = new PrismaClient();
const aliyunController = new AliyunController(prisma);

/**
 * @swagger
 * /oss/credentials:
 *   post:
 *     summary: 获取OSS上传凭证
 *     description: 获取阿里云OSS上传凭证
 *     tags:
 *       - 阿里云平台
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               dir:
 *                 type: string
 *                 description: 上传目录
 *               maxSize:
 *                 type: integer
 *                 description: 最大文件大小（MB）
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.post('/oss/credentials', authMiddleware, (req, res) => {
  aliyunController.getOSSCredentials(req, res);
});

/**
 * @swagger
 * /sms/send:
 *   post:
 *     summary: 发送短信
 *     description: 发送阿里云短信
 *     tags:
 *       - 阿里云平台
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 properties:
 *                   phoneNumber:
 *                     type: string
 *                     description: 手机号码
 *                   templateCode:
 *                     type: string
 *                     description: 模板编码
 *                   templateParam:
 *                     type: object
 *                     description: 模板参数
 *                 required:
 *                   - phoneNumber
 *                   - templateCode
 *               - type: object
 *                 properties:
 *                   phone:
 *                     type: string
 *                     description: 手机号码
 *                   content:
 *                     type: string
 *                     description: 短信内容（可选）
 *                   template:
 *                     type: string
 *                     description: 模板编码
 *                   data:
 *                     type: object
 *                     description: 模板参数
 *                     properties:
 *                       code:
 *                         type: string
 *                         description: 验证码
 *                 required:
 *                   - phone
 *                   - template
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.post('/sms/send', (req, res) => {
  aliyunController.sendSMS(req, res);
});

/**
 * @swagger
 * /sms/verification-code:
 *   post:
 *     summary: 获取验证码
 *     description: 获取短信验证码，每个手机号每5分钟只能获取一次，验证码有效期5分钟
 *     tags:
 *       - 阿里云平台
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 description: 手机号码
 *               type:
 *                 type: string
 *                 description: 验证码类型（例如：login, register, resetPassword）
 *                 default: default
 *               templateCode:
 *                 type: string
 *                 description: 短信模板编码（可选，如不提供则使用系统配置）
 *             required:
 *               - phoneNumber
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.post('/sms/verification-code', (req, res) => {
  aliyunController.getVerificationCode(req, res);
});

/**
 * @swagger
 * /oss/files:
 *   get:
 *     summary: 获取OSS文件列表
 *     description: 获取阿里云OSS文件列表
 *     tags:
 *       - 阿里云平台
 *     parameters:
 *       - name: dir
 *         in: query
 *         description: 目录
 *         schema:
 *           type: string
 *       - name: prefix
 *         in: query
 *         description: 前缀
 *         schema:
 *           type: string
 *       - name: marker
 *         in: query
 *         description: 起始位置
 *         schema:
 *           type: string
 *       - name: maxKeys
 *         in: query
 *         description: 最大返回数量
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.get('/oss/files', authMiddleware, (req, res) => {
  aliyunController.listOSSFiles(req, res);
});

/**
 * @swagger
 * /oss/file:
 *   delete:
 *     summary: 删除OSS文件
 *     description: 删除阿里云OSS文件
 *     tags:
 *       - 阿里云平台
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               objectName:
 *                 type: string
 *                 description: 文件名
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.delete('/oss/file', authMiddleware, (req, res) => {
  aliyunController.deleteOSSFile(req, res);
});

/**
 * @swagger
 * /oss/upload-image:
 *   post:
 *     summary: 上传图片
 *     description: 上传图片到阿里云OSS
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: file
 *         type: file
 *         required: true
 *         description: 要上传的图片文件
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 图片上传成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                       description: 图片URL
 *                     path:
 *                       type: string
 *                       description: OSS路径
 *                     name:
 *                       type: string
 *                       description: 文件名
 *                     size:
 *                       type: integer
 *                       description: 文件大小（字节）
 *                     type:
 *                       type: string
 *                       description: 文件MIME类型
 *       400:
 *         description: 请求错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/oss/upload-image', authMiddleware, upload.single('file'), (req, res) => {
  aliyunController.uploadImage(req, res);
});

/**
 * @swagger
 * /ocr/general:
 *   post:
 *     summary: 通用文字识别
 *     description: 使用阿里云OCR进行通用文字识别
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 要识别的图片文件
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 description: 图片URL地址
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器错误
 */
router.post('/ocr/general', authMiddleware, upload.single('file'), (req, res) => {
  aliyunController.recognizeGeneral(req, res);
});

/**
 * @swagger
 * /ocr/idcard:
 *   post:
 *     summary: 身份证识别
 *     description: 使用阿里云OCR进行身份证识别
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 要识别的身份证图片
 *               side:
 *                 type: string
 *                 description: 身份证正反面，face(人像面)或back(国徽面)
 *                 default: face
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 description: 图片URL地址
 *               side:
 *                 type: string
 *                 description: 身份证正反面，face(人像面)或back(国徽面)
 *                 default: face
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器错误
 */
router.post('/ocr/idcard', authMiddleware, upload.single('file'), (req, res) => {
  aliyunController.recognizeIDCard(req, res);
});

/**
 * @swagger
 * /ocr/driving-license:
 *   post:
 *     summary: 驾驶证识别
 *     description: 使用阿里云OCR进行驾驶证识别
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 要识别的驾驶证图片
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 description: 图片URL地址
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器错误
 */
router.post('/ocr/driving-license', authMiddleware, upload.single('file'), (req, res) => {
  aliyunController.recognizeDrivingLicense(req, res);
});

/**
 * @swagger
 * /ocr/business-license:
 *   post:
 *     summary: 营业执照识别
 *     description: 使用阿里云OCR进行营业执照识别
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 要识别的营业执照图片
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 description: 图片URL地址
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器错误
 */
router.post('/ocr/business-license', authMiddleware, upload.single('file'), (req, res) => {
  aliyunController.recognizeBusinessLicense(req, res);
});

/**
 * @swagger
 * /ocr/bank-card:
 *   post:
 *     summary: 银行卡识别
 *     description: 使用阿里云OCR进行银行卡识别
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 要识别的银行卡图片
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 description: 图片URL地址
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器错误
 */
router.post('/ocr/bank-card', authMiddleware, upload.single('file'), (req, res) => {
  aliyunController.recognizeBankCard(req, res);
});

/**
 * @swagger
 * /ocr/table:
 *   post:
 *     summary: 表格识别
 *     description: 使用阿里云OCR进行表格识别
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 要识别的表格图片
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 description: 图片URL地址
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器错误
 */
router.post('/ocr/table', authMiddleware, upload.single('file'), (req, res) => {
  aliyunController.recognizeTable(req, res);
});

/**
 * @swagger
 * /ocr/test-connection:
 *   post:
 *     summary: 测试OCR连接
 *     description: 测试阿里云OCR服务连接
 *     tags:
 *       - 阿里云平台
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.post('/ocr/test-connection', authMiddleware, (req, res) => {
  aliyunController.testOCRConnection(req, res);
});

module.exports = router;

/**
 * 快递轨迹路由
 */
const express = require('express');
const router = express.Router();
const authMiddleware = require('../../../../../core/middleware/AuthMiddleware');
const ExpressTrackController = require('../controllers/ExpressTrackController');
const { prisma } = require('../../../../../core/database/prisma');

// 创建控制器实例
const controller = new ExpressTrackController(prisma);

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/package/{packageId}:
 *   get:
 *     summary: 获取包裹轨迹信息
 *     description: 根据包裹ID获取快递轨迹信息
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: packageId
 *         required: true
 *         schema:
 *           type: string
 *         description: 包裹ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/package/:packageId', authMiddleware, (req, res) => {
  controller.getTrackByPackageId(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/express/{expressNo}:
 *   get:
 *     summary: 根据快递单号获取轨迹信息
 *     description: 根据快递单号获取快递轨迹信息
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expressNo
 *         required: true
 *         schema:
 *           type: string
 *         description: 快递单号
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/express/:expressNo', authMiddleware, (req, res) => {
  controller.getTrackByExpressNo(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/order/{orderId}:
 *   get:
 *     summary: 获取订单的所有包裹轨迹
 *     description: 根据订单ID获取该订单下所有包裹的快递轨迹信息
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/order/:orderId', authMiddleware, (req, res) => {
  controller.getTracksByOrderId(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/subscribe:
 *   post:
 *     summary: 订阅快递轨迹
 *     description: 为指定包裹订阅快递轨迹推送
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - packageId
 *             properties:
 *               packageId:
 *                 type: string
 *                 description: 包裹ID
 *     responses:
 *       200:
 *         description: 订阅成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/subscribe', authMiddleware, (req, res) => {
  controller.subscribeExpress(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/batch-subscribe:
 *   post:
 *     summary: 批量订阅快递轨迹
 *     description: 为多个包裹批量订阅快递轨迹推送
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - packageIds
 *             properties:
 *               packageIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 包裹ID列表
 *     responses:
 *       200:
 *         description: 批量订阅成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/batch-subscribe', authMiddleware, (req, res) => {
  controller.batchSubscribeExpress(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/unsubscribed:
 *   get:
 *     summary: 获取未订阅的包裹列表
 *     description: 获取已发货但未订阅快递轨迹的包裹列表
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: 限制返回数量
 *     responses:
 *       200:
 *         description: 获取成功
 *       500:
 *         description: 服务器错误
 */
router.get('/unsubscribed', authMiddleware, (req, res) => {
  controller.getUnsubscribedPackages(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/callback:
 *   post:
 *     summary: 快递100回调接口
 *     description: 接收快递100的轨迹推送回调
 *     tags:
 *       - 快递轨迹管理
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: 快递100回调数据
 *     responses:
 *       200:
 *         description: 回调处理成功
 *       400:
 *         description: 回调处理失败
 */
router.post('/callback', (req, res) => {
  controller.handleCallback(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/task/run:
 *   post:
 *     summary: 手动执行订阅任务
 *     description: 手动触发一次快递轨迹订阅任务
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 任务执行成功
 *       500:
 *         description: 任务执行失败
 */
router.post('/task/run', authMiddleware, (req, res) => {
  controller.runSubscribeTask(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/stats:
 *   get:
 *     summary: 获取订阅统计信息
 *     description: 获取快递轨迹订阅的统计信息
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       500:
 *         description: 获取失败
 */
router.get('/stats', authMiddleware, (req, res) => {
  controller.getSubscriptionStats(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/express-track/task/status:
 *   get:
 *     summary: 获取任务状态
 *     description: 获取快递轨迹订阅任务的运行状态
 *     tags:
 *       - 快递轨迹管理
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       500:
 *         description: 获取失败
 */
router.get('/task/status', authMiddleware, (req, res) => {
  controller.getTaskStatus(req, res);
});

module.exports = router;

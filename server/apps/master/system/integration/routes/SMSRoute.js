/**
 * 短信服务路由
 */
const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const SMSController = require('../controllers/SMSController');
const authMiddleware = require('../../../../../core/middleware/AuthMiddleware');

const controller = new SMSController(prisma);

/**
 * @swagger
 * /api/v1/master/system/integration/sms/send:
 *   post:
 *     summary: 发送短信
 *     description: 发送短信到指定手机号码
 *     tags:
 *       - 系统管理/集成服务/短信服务
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 description: 手机号码
 *                 example: "13800138000"
 *               templateCode:
 *                 type: string
 *                 description: 模板编码
 *                 example: "SMS_123456789"
 *               templateParams:
 *                 type: object
 *                 description: 模板参数
 *                 example: {"code":"123456"}
 *               platform:
 *                 type: string
 *                 description: 短信平台类型（可选，不指定则使用默认平台）
 *                 example: "aliyun"
 *             required:
 *               - phoneNumber
 *               - templateCode
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 短信发送成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: 发送成功
 *                     requestId:
 *                       type: string
 *                       example: "F655A8D5-B967-440B-8683-C474D1B1A8"
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/send', authMiddleware, (req, res) => {
  controller.sendSMS(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/sms/platform-test-connection:
 *   post:
 *     summary: 测试短信平台连接
 *     description: 测试指定短信平台的连接是否正常
 *     tags:
 *       - 系统管理/集成服务/短信服务
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               platform:
 *                 type: string
 *                 description: 短信平台类型
 *                 example: "aliyun"
 *             required:
 *               - platform
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 连接测试成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: 连接成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/platform-test-connection', authMiddleware, (req, res) => {
  controller.testConnection(req, res);
});

module.exports = router;

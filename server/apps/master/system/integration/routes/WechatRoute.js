/**
 * 微信平台路由
 * 处理微信平台相关的路由
 */
const express = require('express');
const { prisma } = require('../../../../../core/database/prisma');
const WechatController = require('../controllers/WechatController');
const authMiddleware = require('../../../../../core/middleware/AuthMiddleware');

const router = express.Router();
const wechatController = new WechatController(prisma);

/**
 * @swagger
 * /master/system/integration/wechat/verify:
 *   get:
 *     summary: 验证服务器
 *     description: 用于微信公众平台验证服务器
 *     tags:
 *       - 微信平台
 *     parameters:
 *       - name: signature
 *         in: query
 *         description: 微信加密签名
 *         required: true
 *         schema:
 *           type: string
 *       - name: timestamp
 *         in: query
 *         description: 时间戳
 *         required: true
 *         schema:
 *           type: string
 *       - name: nonce
 *         in: query
 *         description: 随机数
 *         required: true
 *         schema:
 *           type: string
 *       - name: echostr
 *         in: query
 *         description: 随机字符串
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.get('/verify', (req, res) => {
  wechatController.verifyServer(req, res);
});

/**
 * @swagger
 * /master/system/integration/wechat/jssdk/config:
 *   post:
 *     summary: 获取JS SDK配置
 *     description: 获取微信JS SDK配置信息
 *     tags:
 *       - 微信平台
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 description: 当前网页的URL
 *               jsApiList:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 需要使用的JS接口列表
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.post('/jssdk/config', authMiddleware, (req, res) => {
  wechatController.getJssdkConfig(req, res);
});

/**
 * @swagger
 * /master/system/integration/wechat/oauth/url:
 *   post:
 *     summary: 获取授权URL
 *     description: 获取微信授权URL
 *     tags:
 *       - 微信平台
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               redirectUrl:
 *                 type: string
 *                 description: 授权后重定向的URL
 *               scope:
 *                 type: string
 *                 description: 授权范围
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.post('/oauth/url', authMiddleware, (req, res) => {
  wechatController.getAuthUrl(req, res);
});

/**
 * @swagger
 * /master/system/integration/wechat/oauth/userinfo:
 *   post:
 *     summary: 获取用户信息
 *     description: 获取微信用户信息
 *     tags:
 *       - 微信平台
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: 授权码
 *     responses:
 *       200:
 *         description: 成功
 *       500:
 *         description: 服务器错误
 */
router.post('/oauth/userinfo', authMiddleware, (req, res) => {
  wechatController.getUserInfo(req, res);
});

module.exports = router;

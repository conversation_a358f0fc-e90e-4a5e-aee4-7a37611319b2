/**
 * 快递100路由
 * 处理快递100相关的路由
 */
const express = require('express');
const { PrismaClient } = require('@prisma/client');
const Express100Controller = require('../controllers/Express100Controller');
const authMiddleware = require('../../../../../core/middleware/AuthMiddleware');

const router = express.Router();
const prisma = new PrismaClient();
const express100Controller = new Express100Controller(prisma);

/**
 * @swagger
 * /system/integration/express100/query:
 *   post:
 *     summary: 查询快递物流信息
 *     description: 查询快递物流信息
 *     tags:
 *       - 快递100
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               expressCode:
 *                 type: string
 *                 description: 快递公司编码
 *               expressNo:
 *                 type: string
 *                 description: 快递单号
 *               phone:
 *                 type: string
 *                 description: 手机号后四位（可选）
 *             required:
 *               - expressCode
 *               - expressNo
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 查询物流信息成功
 *                 data:
 *                   type: object
 */
router.post('/', authMiddleware, (req, res) => {
  express100Controller.queryExpress(req, res);
});

/**
 * @swagger
 * /system/integration/express100/subscribe:
 *   post:
 *     summary: 订阅快递物流状态
 *     description: 订阅快递物流状态变化通知
 *     tags:
 *       - 快递100
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               expressCode:
 *                 type: string
 *                 description: 快递公司编码
 *               expressNo:
 *                 type: string
 *                 description: 快递单号
 *               parameters:
 *                 type: object
 *                 description: 订阅参数
 *             required:
 *               - expressCode
 *               - expressNo
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 订阅物流状态成功
 *                 data:
 *                   type: object
 */
router.post('/subscribe', authMiddleware, (req, res) => {
  express100Controller.subscribeExpress(req, res);
});

/**
 * @swagger
 * /system/integration/express100/callback:
 *   post:
 *     summary: 快递100回调通知
 *     description: 处理快递100回调通知
 *     tags:
 *       - 快递100
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 回调处理成功
 *                 data:
 *                   type: object
 */
router.post('/callback', (req, res) => {
  express100Controller.handleCallback(req, res);
});

/**
 * @swagger
 * /system/integration/express100/track:
 *   get:
 *     summary: 获取物流记录
 *     description: 根据快递单号获取物流记录
 *     tags:
 *       - 快递100
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: expressNo
 *         schema:
 *           type: string
 *         required: true
 *         description: 快递单号
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 获取物流记录成功
 *                 data:
 *                   type: object
 */
router.get('/track', authMiddleware, (req, res) => {
  express100Controller.getTrackRecord(req, res);
});

/**
 * @swagger
 * /system/integration/express100/delete:
 *   post:
 *     summary: 删除物流记录
 *     description: 根据快递单号删除物流记录（软删除）
 *     tags:
 *       - 快递100
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               expressNo:
 *                 type: string
 *                 description: 快递单号
 *             required:
 *               - expressNo
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 删除物流记录成功
 *                 data:
 *                   type: null
 */
router.post('/delete', authMiddleware, (req, res) => {
  express100Controller.deleteTrackRecord(req, res);
});

/**
 * @swagger
 * /system/integration/express100/test:
 *   post:
 *     summary: 测试快递物流查询
 *     description: 使用指定配置测试快递物流查询功能
 *     tags:
 *       - 快递100
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               expressCode:
 *                 type: string
 *                 description: 快递公司编码
 *               expressNo:
 *                 type: string
 *                 description: 快递单号
 *               phone:
 *                 type: string
 *                 description: 手机号后四位（可选）
 *               configValue:
 *                 type: object
 *                 description: 配置信息
 *                 properties:
 *                   key:
 *                     type: string
 *                     description: 授权Key
 *                   customer:
 *                     type: string
 *                     description: Customer
 *                   callbackUrl:
 *                     type: string
 *                     description: 回调地址
 *             required:
 *               - expressCode
 *               - expressNo
 *               - configValue
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 测试查询成功
 *                 data:
 *                   type: object
 */
router.post('/test', authMiddleware, (req, res) => {
  express100Controller.testLogisticsQuery(req, res);
});

module.exports = router;

/**
 * @swagger
 * components:
 *   schemas:
 *     MessageBoard:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 留言ID
 *           example: '1'
 *         current_url:
 *           type: string
 *           description: 当前链接
 *           example: 'https://example.com/page'
 *         submitter:
 *           type: string
 *           description: 提交人
 *           example: '张三'
 *         content:
 *           type: string
 *           description: 内容（富文本）
 *           example: '<p>这是一条留言内容</p>'
 *         created_at:
 *           type: integer
 *           description: 创建时间戳（毫秒）
 *           example: 1620000000000
 *         updated_at:
 *           type: integer
 *           description: 更新时间戳（毫秒）
 *           example: 1620000000000
 *         deleted_at:
 *           type: integer
 *           description: 删除时间戳（毫秒，软删除）
 *           example: null
 *           nullable: true
 *         created_by:
 *           type: string
 *           description: 创建人ID
 *           example: null
 *           nullable: true
 *         updated_by:
 *           type: string
 *           description: 更新人ID
 *           example: null
 *           nullable: true
 *         remark:
 *           type: string
 *           description: 备注
 *           example: '这是备注信息'
 *           nullable: true
 *       required:
 *         - id
 *         - current_url
 *         - submitter
 *         - content
 *         - created_at
 *         - updated_at
 * 
 *     CreateMessageBoard:
 *       type: object
 *       properties:
 *         current_url:
 *           type: string
 *           description: 当前链接
 *           example: 'https://example.com/page'
 *         submitter:
 *           type: string
 *           description: 提交人
 *           example: '张三'
 *         content:
 *           type: string
 *           description: 内容（富文本）
 *           example: '<p>这是一条留言内容</p>'
 *         remark:
 *           type: string
 *           description: 备注
 *           example: '这是备注信息'
 *           nullable: true
 *       required:
 *         - current_url
 *         - submitter
 *         - content
 * 
 *     UpdateMessageBoard:
 *       type: object
 *       properties:
 *         current_url:
 *           type: string
 *           description: 当前链接
 *           example: 'https://example.com/page'
 *         submitter:
 *           type: string
 *           description: 提交人
 *           example: '张三'
 *         content:
 *           type: string
 *           description: 内容（富文本）
 *           example: '<p>这是一条更新后的留言内容</p>'
 *         remark:
 *           type: string
 *           description: 备注
 *           example: '这是更新后的备注信息'
 *           nullable: true
 * 
 * tags:
 *   - name: 留言板
 *     description: 留言板相关接口
 * 
 * /message-board:
 *   post:
 *     tags:
 *       - 留言板
 *     summary: 创建留言
 *     description: 创建一条新的留言记录
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateMessageBoard'
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 留言创建成功
 *                 data:
 *                   $ref: '#/components/schemas/MessageBoard'
 *       400:
 *         description: 参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: 当前链接不能为空
 *                 data:
 *                   type: null
 *                   example: null
 * 
 *   get:
 *     tags:
 *       - 留言板
 *     summary: 获取留言列表
 *     description: 分页获取留言列表，支持多种筛选条件
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: current_url
 *         schema:
 *           type: string
 *         description: 当前链接（模糊查询）
 *       - in: query
 *         name: submitter
 *         schema:
 *           type: string
 *         description: 提交人（模糊查询）
 *       - in: query
 *         name: start_time
 *         schema:
 *           type: integer
 *         description: 开始时间戳
 *       - in: query
 *         name: end_time
 *         schema:
 *           type: integer
 *         description: 结束时间戳
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页条数
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取留言列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     records:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/MessageBoard'
 *                     total:
 *                       type: integer
 *                       example: 100
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 * 
 * /message-board/{id}:
 *   get:
 *     tags:
 *       - 留言板
 *     summary: 获取留言详情
 *     description: 根据ID获取留言详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 留言ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取留言详情成功
 *                 data:
 *                   $ref: '#/components/schemas/MessageBoard'
 *       404:
 *         description: 留言不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 message:
 *                   type: string
 *                   example: 留言不存在
 *                 data:
 *                   type: null
 *                   example: null
 * 
 *   put:
 *     tags:
 *       - 留言板
 *     summary: 更新留言
 *     description: 根据ID更新留言信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 留言ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateMessageBoard'
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新留言成功
 *                 data:
 *                   $ref: '#/components/schemas/MessageBoard'
 *       404:
 *         description: 留言不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 message:
 *                   type: string
 *                   example: 留言不存在
 *                 data:
 *                   type: null
 *                   example: null
 * 
 *   delete:
 *     tags:
 *       - 留言板
 *     summary: 删除留言
 *     description: 根据ID软删除留言
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 留言ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除留言成功
 *                 data:
 *                   type: null
 *                   example: null
 *       404:
 *         description: 留言不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 message:
 *                   type: string
 *                   example: 留言不存在
 *                 data:
 *                   type: null
 *                   example: null
 * 
 * /message-board/batch-remove:
 *   post:
 *     tags:
 *       - 留言板
 *     summary: 批量删除留言
 *     description: 批量软删除多条留言
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3]
 *             required:
 *               - ids
 *     responses:
 *       200:
 *         description: 批量删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 批量删除留言成功
 *                 data:
 *                   type: null
 *                   example: null
 *       400:
 *         description: 参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: 请选择要删除的留言
 *                 data:
 *                   type: null
 *                   example: null
 */

-- 创建商城前端用户表
CREATE TABLE IF NOT EXISTS "base"."mall_user" (
  "id" BIGINT NOT NULL,
  "username" TEXT NOT NULL,
  "password" TEXT NOT NULL,
  "nickname" TEXT,
  "avatar" TEXT,
  "phone" VARCHAR(11),
  "email" TEXT,
  "status" INTEGER NOT NULL DEFAULT 1,
  "last_login_ip" VARCHAR(45),
  "last_login_time" BIGINT,
  "login_count" INTEGER NOT NULL DEFAULT 0,
  "created_at" BIGINT NOT NULL,
  "updated_at" BIGINT NOT NULL,
  "created_by" BIGINT,
  "updated_by" BIGINT,
  "deleted_at" BIGINT,
  "remark" TEXT,
  "wechat_openid" TEXT,
  
  CONSTRAINT "mall_user_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "mall_user_username_key" UNIQUE ("username"),
  CONSTRAINT "mall_user_email_key" UNIQUE ("email")
);

-- 添加表注释
COMMENT ON TABLE "base"."mall_user" IS '商城前端用户表，存储商城用户数据，与系统用户无关联';

-- 添加字段注释
COMMENT ON COLUMN "base"."mall_user"."id" IS '用户ID，16位雪花算法，系统自动生成';
COMMENT ON COLUMN "base"."mall_user"."username" IS '用户名，必填，唯一，用于登录';
COMMENT ON COLUMN "base"."mall_user"."password" IS '密码，必填，加密存储';
COMMENT ON COLUMN "base"."mall_user"."nickname" IS '用户昵称';
COMMENT ON COLUMN "base"."mall_user"."avatar" IS '头像地址';
COMMENT ON COLUMN "base"."mall_user"."phone" IS '手机号';
COMMENT ON COLUMN "base"."mall_user"."email" IS '邮箱，唯一，可用于找回密码';
COMMENT ON COLUMN "base"."mall_user"."status" IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN "base"."mall_user"."last_login_ip" IS '最后登录IP';
COMMENT ON COLUMN "base"."mall_user"."last_login_time" IS '最后登录时间戳（毫秒）';
COMMENT ON COLUMN "base"."mall_user"."login_count" IS '登录次数';
COMMENT ON COLUMN "base"."mall_user"."created_at" IS '创建时间戳（毫秒）';
COMMENT ON COLUMN "base"."mall_user"."updated_at" IS '更新时间戳（毫秒）';
COMMENT ON COLUMN "base"."mall_user"."created_by" IS '创建人ID';
COMMENT ON COLUMN "base"."mall_user"."updated_by" IS '更新人ID';
COMMENT ON COLUMN "base"."mall_user"."deleted_at" IS '删除时间戳（毫秒），空表示未删除';
COMMENT ON COLUMN "base"."mall_user"."remark" IS '备注信息';
COMMENT ON COLUMN "base"."mall_user"."wechat_openid" IS '微信用户唯一标识，用于微信扫码登录';

-- 创建索引
CREATE INDEX IF NOT EXISTS "idx_mall_user_status" ON "base"."mall_user" ("status");
CREATE INDEX IF NOT EXISTS "idx_mall_user_deleted_at" ON "base"."mall_user" ("deleted_at");
CREATE INDEX IF NOT EXISTS "idx_mall_user_wechat_openid" ON "base"."mall_user" ("wechat_openid");

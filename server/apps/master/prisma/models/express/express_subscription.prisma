// 快递订阅记录表
model expressSubscription {
  id                  BigInt  @id @default(autoincrement())
  package_id          BigInt  // 包裹ID，关联order_packages表
  order_id            BigInt  // 订单ID
  express_no          String  @db.VarChar(100) // 快递单号
  express_code        String  @db.VarChar(50)  // 快递公司编码
  subscription_status String  @default("pending") @db.VarChar(20) // 订阅状态：pending待订阅，subscribed已订阅，failed订阅失败
  subscription_time   BigInt? // 订阅时间戳
  callback_count      Int     @default(0) // 回调次数
  last_callback_time  BigInt? // 最后回调时间
  error_message       String? @db.Text // 错误信息
  created_at          BigInt  @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  updated_at          BigInt  @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  deleted_at          BigInt?

  // 关联关系
  package             order_packages @relation(fields: [package_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([package_id, express_no])
  @@index([package_id])
  @@index([subscription_status])
  @@index([express_no])
  @@map("express_subscription")
  @@schema("base")
}

// 自主品牌溯源码模型
model SelfSourceCode {
  id               BigInt    @id @default(autoincrement())                 // 主键ID
  brand_id         BigInt                                                  // 关联的自主品牌ID
  source_code      String    @unique @db.VarChar(50)                       // 溯源码
  status           Int       @default(0)                                   // 状态：0-未启用，1-已启用
  activation_time  BigInt?                                                 // 激活时间戳（毫秒）
  first_query_time BigInt?                                                 // 首次查询时间戳（毫秒）
  query_count      Int       @default(0)                                   // 查询次数
  created_at       BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at       BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at       BigInt?                                                 // 删除时间戳（毫秒，软删除）
  created_by       BigInt?                                                 // 创建人ID
  updated_by       BigInt?                                                 // 更新人ID

  // 关联
  brand            SelfOwnedBrand  @relation(fields: [brand_id], references: [id])
  queryLogs        SelfSourceCodeQueryLog[]                               // 查询日志

  @@index([brand_id], map: "idx_self_source_code_brand_id")
  @@index([source_code], map: "idx_self_source_code_source_code") 
  @@index([status], map: "idx_self_source_code_status")
  @@index([deleted_at], map: "idx_self_source_code_deleted_at")
  @@index([created_by], map: "idx_self_source_codes_created_by")
  @@index([updated_by], map: "idx_self_source_codes_updated_by")
  @@map("self_source_codes")
  @@schema("base")
}

// 商品服务模型
model GoodsService {
  id          BigInt    @id @default(autoincrement())
  name        String    @db.VarChar(100)               // 服务名称
  image_url   String?   @db.VarChar(255)               // 服务图标URL
  description String?                                   // 服务描述
  sort_order  Int       @default(0)                    // 排序顺序，数值越小越靠前
  created_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at  BigInt?                                   // 删除时间戳（毫秒，软删除）
  created_by  BigInt?                                   // 创建人ID
  updated_by  BigInt?                                   // 最后更新人ID

  // 关联
  goods_service_associations GoodsServiceAssociation[]

  @@index([deleted_at], map: "idx_goods_services_deleted_at")
  @@index([created_by], map: "idx_goods_services_created_by")
  @@index([updated_by], map: "idx_goods_services_updated_by")
  @@map("goods_services")
  @@schema("base")
}

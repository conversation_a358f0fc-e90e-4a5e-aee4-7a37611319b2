// 商品服务关联模型（多对多关联表）
model GoodsServiceAssociation {
  goods_spu_id      BigInt        // 商品SPU ID，关联goods_spus表
  goods_service_id  BigInt        // 服务ID，关联goods_services表

  // 关联（多对多关系的中间表）
  goods_spu         GoodsSpu      @relation(fields: [goods_spu_id], references: [id])      // 关联到商品SPU
  goods_service     GoodsService  @relation(fields: [goods_service_id], references: [id])  // 关联到商品服务

  @@id([goods_spu_id, goods_service_id])  // 联合主键
  @@index([goods_service_id], map: "idx_goods_service_associations_service_id")  // 服务ID索引
  @@map("goods_service_associations")  // 映射到数据库表名
  @@schema("base")  // 所属schema
}

// 商品品牌模型
model GoodsBrand {
  id          BigInt    @id @default(autoincrement())
  name        String    @db.VarChar(100)               // 品牌名称
  logo_url    String?   @db.VarChar(255)               // 品牌logo URL
  description String?                                   // 品牌描述
  created_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at  BigInt?                                   // 删除时间戳（毫秒，软删除）
  created_by  BigInt?                                   // 创建人ID
  updated_by  BigInt?                                   // 最后更新人ID

  // 关联商品
  goods_spus  GoodsSpu[]

  @@index([deleted_at], map: "idx_goods_brands_deleted_at")
  @@index([created_by], map: "idx_goods_brands_created_by")
  @@index([updated_by], map: "idx_goods_brands_updated_by")
  @@map("goods_brands")
  @@schema("base")
}

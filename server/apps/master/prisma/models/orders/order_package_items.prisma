model order_package_items {
  id              BigInt @id @default(autoincrement()) // 包裹项ID
  package_id      BigInt // 关联包裹ID
  order_item_id   BigInt // 关联订单项ID
  quantity        Int    // 该包裹中商品的数量（支持拆分）
  created_at      BigInt @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  updated_at      BigInt @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  order_package   order_packages @relation(fields: [package_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_package_items_package_id")
  order_item      order_items @relation(fields: [order_item_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_package_items_order_item_id")

  @@index([package_id], map: "idx_order_package_items_package_id")
  @@index([order_item_id], map: "idx_order_package_items_order_item_id")
  @@schema("base")
}

model order_shipping_info {
  id                    BigInt  @id @default(autoincrement()) // 配送信息ID，主键，自增长
  order_id              BigInt  @unique // 关联的订单ID，外键，一对一关系
  recipient_name        String  @db.VarChar(100) // 收件人姓名
  recipient_phone       String  @db.VarChar(20) // 收件人电话
  region_province_id    Int?    // 省份ID
  region_city_id        Int?    // 城ID
  region_district_id    Int?    // 区/县 ID
  region_path_name      String? // 地区完整路径名称，如“广东省/广州市/天河区”
  street_address        String  // 详细街道地址
  postal_code           String? @db.VarChar(20) // 邮政编码
  created_at            BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戟，毫秒）
  updated_at            BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戟，毫秒）
  orders                orders  @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_shipping_info_order_id") // 关联到订单表

  @@index([recipient_phone], map: "idx_order_shipping_info_recipient_phone")
  @@schema("base")
}

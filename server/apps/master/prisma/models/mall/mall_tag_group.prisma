// 商城会员标签组模型
/// 商城会员标签组表，用于对标签进行分类管理
model MallTagGroup {
  /// 标签组ID，16位雪花算法，系统自动生成
  id                BigInt   @id 
  /// 标签组名称，必填，同一子站点下唯一
  tag_group_name    String   
  /// 标签组排序，数字越小越靠前
  tag_group_sort    Int      @default(0) 
  /// 是否系统内置：1-是，0-否
  tag_group_buildin Int      @default(0) @db.SmallInt
  /// 是否启用：1-启用，0-禁用
  tag_group_enable  Int      @default(1) @db.SmallInt
  /// 子站点ID，为空表示适用于所有子站点
  subsite_id        BigInt?  
  /// 状态：1-正常，0-禁用
  status            Int      @default(1) 
  /// 创建时间戳（毫秒）
  created_at        BigInt   
  /// 更新时间戳（毫秒）
  updated_at        BigInt   
  /// 创建人ID
  created_by        BigInt?  
  /// 更新人ID
  updated_by        BigInt?  
  /// 删除时间戳（毫秒），空表示未删除
  deleted_at        BigInt?  
  /// 备注信息
  remark            String?  @db.Text

  // 关联标签
  tags              MallTag[]

  // 设置唯一约束
  @@unique([tag_group_name, subsite_id])
  @@map("mall_tag_group")
  @@schema("base")
}

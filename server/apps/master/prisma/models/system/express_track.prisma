/// 快递物流记录表
model ExpressTrackSystem {
  // 主键
  id                  BigInt    @id(map: "express_track_system_pkey") @default(autoincrement()) /// @db.Comment('记录ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  shipping_company_code  String    @db.VarChar(50) @map("express_code") /// @db.Comment('快递公司编码')
  express_name        String    @db.VarChar(100)              /// @db.Comment('快递公司名称')
  tracking_number     String    @db.VarChar(100) @unique @map("express_no") /// @db.Comment('快递单号')
  
  // 物流状态
  status              Int       @default(0)                   /// @db.Comment('物流状态：0-在途中，1-揽收，2-疑难，3-已签收，4-退签，5-派件，6-退回，8-清关')
  track_data          Json?                                   /// @db.Comment('物流轨迹数据')
  last_track          String?   @db.Text                      /// @db.Comment('最新物流信息')
  is_subscribed       Boolean   @default(false)               /// @db.Comment('是否已订阅')
  sub_status          Int?                                    /// @db.Comment('订阅状态')
  sub_biz_id          String?   @db.VarChar(100)              /// @db.Comment('订阅业务ID')
  
  // 审计字段
  created_by          BigInt                                  /// @db.Comment('创建人ID')
  created_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000")) /// @db.Comment('创建时间戳（毫秒）')
  updated_by          BigInt?                                 /// @db.Comment('更新人ID')
  updated_at          BigInt?                                 /// @db.Comment('更新时间戳（毫秒）')
  deleted_at          BigInt?                                 /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  
  @@index([tracking_number], map: "idx_express_track_system_tracking_number")
  @@map("express_track_system")
  @@schema("base")
}

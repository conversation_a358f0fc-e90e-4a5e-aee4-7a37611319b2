const fs = require('fs');
const path = require('path');

// 递归读取目录下的所有 .prisma 文件
function readPrismaFiles(dir) {
  let files = [];
  const items = fs.readdirSync(dir);

  items.forEach(item => {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      files = files.concat(readPrismaFiles(fullPath));
    } else if (item.endsWith('.prisma') && item !== 'base.prisma') {
      files.push(fullPath);
    }
  });

  return files;
}

// 读取模型文件
function readModelFiles() {
  const modelsDir = path.join(__dirname, 'models');
  let schema = '';

  // 首先读取基础配置
  const baseConfig = path.join(modelsDir, 'base.prisma');
  if (!fs.existsSync(baseConfig)) {
    console.error('基础配置文件不存在：', baseConfig);
    process.exit(1);
  }
  schema = fs.readFileSync(baseConfig, 'utf8') + '\n';

  // 读取所有其他 .prisma 文件
  const modelFiles = readPrismaFiles(modelsDir);
  modelFiles.sort(); // 确保文件顺序一致

  modelFiles.forEach(filePath => {
    console.log('正在合并文件：', path.relative(__dirname, filePath));
    schema += fs.readFileSync(filePath, 'utf8') + '\n';
  });

  return schema;
}

// 合并并写入主 schema 文件
const outputFile = path.join(__dirname, 'schema.prisma');
if (fs.existsSync(outputFile)) {
  console.log('清空主schema文件:', outputFile);
  // 只保留基础配置（如果需要）
  const baseConfig = path.join(__dirname, 'models', 'base.prisma');
  if (fs.existsSync(baseConfig)) {
    fs.writeFileSync(outputFile, fs.readFileSync(baseConfig, 'utf8'));
  } else {
    fs.writeFileSync(outputFile, '');
  }
}

const mergedSchema = readModelFiles();
fs.writeFileSync(outputFile, mergedSchema);

console.log('Schema文件合并成功！');

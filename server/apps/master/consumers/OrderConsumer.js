/**
 * 订单消息消费者
 * 用于处理订单相关的异步任务
 */
const BaseConsumer = require('../../../core/mq/BaseConsumer');
const logger = require('../../../core/utils/logger').createLogger('订单消费者');
const OrderController = require('../controllers/OrderController');

/**
 * 订单消息队列配置
 */
const ORDER_EXCHANGE = 'order_exchange';  // 订单交换机名称
const ORDER_QUEUE = 'order_queue';        // 订单队列名称
const ORDER_ROUTING_KEY = 'order';        // 订单路由键

class OrderConsumer extends BaseConsumer {
  /**
   * 构造函数
   */
  constructor() {
    super(ORDER_QUEUE, { 
      exchange: ORDER_EXCHANGE,
      routingKey: ORDER_ROUTING_KEY,
      prefetch: 5 
    });
    
    // 初始化订单控制器
    this.orderController = new OrderController();
  }

  /**
   * 处理订单消息
   * @param {Object} message 消息内容
   * @param {Object} originalMessage 原始消息对象
   * @returns {Promise<void>}
   */
  async processMessage(message, originalMessage) {
    try {
      logger.info(`收到订单消息: ${JSON.stringify(message)}`);

      // 检查消息格式
      if (message.thirdPartyOrderSn) {
        // 这是一个新订单创建消息
        await this.handleOrderCreated(message);
        logger.info(`订单消息处理完成: 订单创建, 订单号: ${message.thirdPartyOrderSn}`);
      } else if (message.order_dto) {
        // 处理包含 order_dto 字段的新消息格式
        await this.handleOrderCreated(message.order_dto);
        logger.info(`订单消息处理完成: 订单创建, 订单号: ${message.order_dto.thirdPartyOrderSn}`);
      } else {
        logger.warn(`未知的订单消息格式: ${JSON.stringify(message)}`);
      }
    } catch (error) {
      logger.error('处理订单消息失败:', error);
      // 设置错误是否可重试
      error.retryable = this.isRetryableError(error);
      throw error;
    }
  }

  /**
   * 处理订单创建消息
   * @param {Object} data 订单数据
   * @returns {Promise<void>}
   */
  async handleOrderCreated(data) {
    logger.info('处理订单创建消息:', data);
    
    try {
      // 创建模拟请求和响应对象
      const req = {
        body: data,
        user: {
          id: data.createdBy || data.created_by || BigInt(1), // 使用消息中的创建者ID或默认系统用户
          username: 'system',
          roles: ['admin']
        }
      };
      
      // 创建模拟响应对象
      const res = {
        status: function(code) {
          this.statusCode = code;
          return this;
        },
        json: function(data) {
          this.responseData = data;
          return this;
        },
        send: function(data) {
          this.responseData = data;
          return this;
        }
      };
      
      // 调用OrderController的createThirdPartyOrder方法
      await this.orderController.createThirdPartyOrder(req, res);
      
      // 检查响应结果
      if (res.statusCode >= 400) {
        throw new Error(`订单创建失败: ${res.responseData?.message || '未知错误'}`);
      }
      
      logger.info('订单创建成功:', res.responseData);
    } catch (error) {
      logger.error('调用订单控制器创建订单失败:', error);
      throw error; // 将错误向上传递，让消息队列处理重试逻辑
    }
  }



  /**
   * 判断错误是否可重试
   * @param {Error} error 错误对象
   * @returns {boolean} 是否可重试
   */
  isRetryableError(error) {
    // 根据错误类型判断是否可重试
    // 例如：网络错误、临时服务不可用等可以重试
    // 而参数错误、业务逻辑错误等不应重试
    if (error.name === 'NetworkError' || error.message.includes('timeout')) {
      return true;
    }
    return false;
  }

  /**
   * 模拟异步处理过程
   * @param {number} ms 处理时间（毫秒）
   * @returns {Promise<void>}
   */
  simulateProcessing(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = OrderConsumer;

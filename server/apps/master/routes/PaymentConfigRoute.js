/**
 * 支付配置路由
 * 处理支付配置相关的API路由
 */
const express = require('express');
const router = express.Router();
const PaymentConfigController = require('../controllers/PaymentConfigController');
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 创建控制器实例
const paymentConfigController = new PaymentConfigController();

/**
 * @swagger
 * tags:
 *   name: 支付配置管理
 *   description: 支付配置相关接口
 */

/**
 * @swagger
 * /api/v1/master/payment-config:
 *   get:
 *     summary: 获取所有支付配置
 *     description: 获取系统中所有的支付配置列表
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: payment_type
 *         schema:
 *           type: string
 *           enum: [wechat, alipay, unionpay]
 *         description: 支付类型过滤
 *       - in: query
 *         name: enabled
 *         schema:
 *           type: boolean
 *         description: 启用状态过滤
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/', authMiddleware, paymentConfigController.getAllConfigs.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/type/{type}:
 *   get:
 *     summary: 根据支付类型获取配置
 *     description: 获取指定支付类型的所有配置
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [wechat, alipay, unionpay]
 *         description: 支付类型
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/type/:type', authMiddleware, paymentConfigController.getConfigsByType.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/default/{type}:
 *   get:
 *     summary: 获取默认配置
 *     description: 获取指定支付类型的默认配置
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [wechat, alipay, unionpay]
 *         description: 支付类型
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 未找到默认配置
 *       500:
 *         description: 服务器错误
 */
router.get('/default/:type', authMiddleware, paymentConfigController.getDefaultConfig.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/{id}:
 *   get:
 *     summary: 根据ID获取配置详情
 *     description: 获取指定ID的支付配置详情
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 配置不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', authMiddleware, paymentConfigController.getConfigById.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/wechat:
 *   post:
 *     summary: 保存微信支付配置
 *     description: 创建或更新微信支付配置
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - enabled
 *             properties:
 *               enabled:
 *                 type: boolean
 *                 description: 是否启用
 *               mch_id:
 *                 type: string
 *                 description: 商户号
 *               api_v3_key:
 *                 type: string
 *                 description: APIv3密钥
 *               mch_private_key:
 *                 type: string
 *                 description: 商户私钥
 *               mch_public_key:
 *                 type: string
 *                 description: 商户公钥
 *               wechat_pay_cert:
 *                 type: string
 *                 description: 微信支付公钥
 *               wechat_pay_public_key_id:
 *                 type: string
 *                 description: 微信支付公钥ID
 *               native_callback_url:
 *                 type: string
 *                 description: 回调链接
 *               config_name:
 *                 type: string
 *                 description: 配置名称
 *               environment:
 *                 type: string
 *                 enum: [sandbox, production]
 *                 description: 环境
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 保存成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/wechat', authMiddleware, paymentConfigController.saveWechatConfig.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/alipay:
 *   post:
 *     summary: 保存支付宝配置
 *     description: 创建或更新支付宝配置
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - enabled
 *             properties:
 *               enabled:
 *                 type: boolean
 *                 description: 是否启用
 *               app_id:
 *                 type: string
 *                 description: 支付宝应用ID
 *               private_key:
 *                 type: string
 *                 description: 应用私钥
 *               public_key:
 *                 type: string
 *                 description: 应用公钥
 *               alipay_public_key:
 *                 type: string
 *                 description: 支付宝公钥
 *               alipay_cert:
 *                 type: string
 *                 description: 支付宝根证书
 *               config_name:
 *                 type: string
 *                 description: 配置名称
 *               environment:
 *                 type: string
 *                 enum: [sandbox, production]
 *                 description: 环境
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 保存成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/alipay', authMiddleware, paymentConfigController.saveAlipayConfig.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/{id}:
 *   put:
 *     summary: 更新配置
 *     description: 更新指定ID的支付配置
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               config_name:
 *                 type: string
 *                 description: 配置名称
 *               enabled:
 *                 type: boolean
 *                 description: 是否启用
 *               environment:
 *                 type: string
 *                 enum: [sandbox, production]
 *                 description: 环境
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 配置不存在
 *       500:
 *         description: 服务器错误
 */
router.put('/:id', authMiddleware, paymentConfigController.updateConfig.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/{id}/default:
 *   put:
 *     summary: 设置默认配置
 *     description: 将指定ID的配置设置为默认配置
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 设置成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 配置不存在
 *       500:
 *         description: 服务器错误
 */
router.put('/:id/default', authMiddleware, paymentConfigController.setDefaultConfig.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/{id}:
 *   delete:
 *     summary: 删除配置
 *     description: 删除指定ID的支付配置
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 配置不存在
 *       500:
 *         description: 服务器错误
 */
router.delete('/:id', authMiddleware, paymentConfigController.deleteConfig.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/stats:
 *   get:
 *     summary: 获取支付类型统计
 *     description: 获取各支付类型的配置统计信息
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       500:
 *         description: 服务器错误
 */
router.get('/stats', authMiddleware, paymentConfigController.getTypeStats.bind(paymentConfigController));

/**
 * @swagger
 * /api/v1/master/payment-config/{id}/test:
 *   post:
 *     summary: 测试配置连接
 *     description: 测试指定配置的连通性
 *     tags: [支付配置管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 测试成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 配置不存在
 *       500:
 *         description: 服务器错误
 */
router.post('/:id/test', authMiddleware, paymentConfigController.testConfig.bind(paymentConfigController));

module.exports = router;

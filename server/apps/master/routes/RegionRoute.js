/**
 * 区域路由
 * 处理区域相关的路由请求
 * @typedef {import('../controllers/RegionController')} RegionController
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const RegionController = require('../controllers/RegionController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 使用已实例化的控制器
const controller = RegionController;

// 获取区域树结构（不受JWT保护）
router.get('/tree', controller.getRegionTree.bind(controller));

// 创建受JWT保护的路由（注意：必须在定义完所有不受保护的路由后才创建受保护路由）
const protectedRouter = RouterConfig.authRoute(router);

// 这里可以添加其他受JWT保护的路由
// protectedRouter.get('/protected-route', controller.someMethod.bind(controller));

module.exports = router;

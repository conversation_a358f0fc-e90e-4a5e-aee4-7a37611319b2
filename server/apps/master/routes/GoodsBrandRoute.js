const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const GoodsBrandController = require('../controllers/GoodsBrandController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new GoodsBrandController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/goods-brand:
 *   get:
 *     summary: 获取所有商品品牌
 *     tags: [商品品牌]
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 品牌名称（模糊搜索）
 *     responses:
 *       200:
 *         description: 成功获取品牌列表
 */
protectedRouter.get('/', controller.getAllBrands.bind(controller));

/**
 * @swagger
 * /api/master/goods-brand/{id}:
 *   get:
 *     summary: 根据ID获取商品品牌
 *     tags: [商品品牌]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 品牌ID
 *     responses:
 *       200:
 *         description: 成功获取品牌信息
 *       404:
 *         description: 品牌不存在
 */
protectedRouter.get('/:id', controller.getBrandById.bind(controller));

/**
 * @swagger
 * /api/master/goods-brand:
 *   post:
 *     summary: 创建商品品牌
 *     tags: [商品品牌]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: 品牌名称
 *               logo_url:
 *                 type: string
 *                 description: 品牌Logo URL
 *               description:
 *                 type: string
 *                 description: 品牌描述
 *     responses:
 *       201:
 *         description: 品牌创建成功
 *       400:
 *         description: 数据验证失败
 */
protectedRouter.post('/', controller.createBrand.bind(controller));

/**
 * @swagger
 * /api/master/goods-brand/{id}:
 *   put:
 *     summary: 更新商品品牌
 *     tags: [商品品牌]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 品牌ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: 品牌名称
 *               logo_url:
 *                 type: string
 *                 description: 品牌Logo URL
 *               description:
 *                 type: string
 *                 description: 品牌描述
 *     responses:
 *       200:
 *         description: 品牌更新成功
 *       400:
 *         description: 数据验证失败
 *       404:
 *         description: 品牌不存在
 */
protectedRouter.put('/:id', controller.updateBrand.bind(controller));

/**
 * @swagger
 * /api/master/goods-brand/{id}:
 *   delete:
 *     summary: 删除商品品牌
 *     tags: [商品品牌]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 品牌ID
 *     responses:
 *       200:
 *         description: 品牌删除成功
 *       400:
 *         description: 品牌下有关联商品，无法删除
 *       404:
 *         description: 品牌不存在
 */
protectedRouter.delete('/:id', controller.deleteBrand.bind(controller));

module.exports = router;

/**
 * 产品品牌路由
 */
const express = require('express');
const router = express.Router();
const SelfProductBrandController = require('../controllers/SelfProductBrandController');

// 创建控制器实例
const selfProductBrandController = new SelfProductBrandController();

/**
 * @swagger
 * /api/master/self-product-brand:
 *   get:
 *     tags:
 *       - 产品品牌
 *     summary: 获取产品品牌列表
 *     description: 获取产品品牌列表
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码
 *         required: false
 *         type: integer
 *       - name: limit
 *         in: query
 *         description: 每页数量
 *         required: false
 *         type: integer
 *       - name: brand_code
 *         in: query
 *         description: 品牌代码
 *         required: false
 *         type: string
 *       - name: brand_name
 *         in: query
 *         description: 品牌名称
 *         required: false
 *         type: string
 *       - name: is_self_brand
 *         in: query
 *         description: 是否自主品牌：1-否，2-是
 *         required: false
 *         type: integer
 *       - name: start_time
 *         in: query
 *         description: 创建开始时间戳（毫秒）
 *         required: false
 *         type: integer
 *       - name: end_time
 *         in: query
 *         description: 创建结束时间戳（毫秒）
 *         required: false
 *         type: integer
 *     responses:
 *       200:
 *         description: 成功
 */
router.get('/', selfProductBrandController.getSelfProductBrandList.bind(selfProductBrandController));

/**
 * @swagger
 * /api/master/self-product-brand/{id}:
 *   get:
 *     tags:
 *       - 产品品牌
 *     summary: 获取产品品牌详情
 *     description: 获取产品品牌详情
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 产品品牌ID
 *         required: true
 *         type: integer
 *     responses:
 *       200:
 *         description: 成功
 */
router.get('/:id', selfProductBrandController.getSelfProductBrandDetail.bind(selfProductBrandController));

/**
 * @swagger
 * /api/master/self-product-brand:
 *   post:
 *     tags:
 *       - 产品品牌
 *     summary: 创建产品品牌
 *     description: 创建产品品牌
 *     parameters:
 *       - name: body
 *         in: body
 *         description: 产品品牌数据
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             brand_code:
 *               type: string
 *               description: 品牌代码
 *             brand_name:
 *               type: string
 *               description: 品牌名称
 *             is_self_brand:
 *               type: integer
 *               description: 是否自主品牌：1-否，2-是
 *     responses:
 *       201:
 *         description: 创建成功
 */
router.post('/', selfProductBrandController.createSelfProductBrand.bind(selfProductBrandController));

/**
 * @swagger
 * /api/master/self-product-brand/{id}:
 *   put:
 *     tags:
 *       - 产品品牌
 *     summary: 更新产品品牌
 *     description: 更新产品品牌
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 产品品牌ID
 *         required: true
 *         type: integer
 *       - name: body
 *         in: body
 *         description: 产品品牌数据
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             brand_code:
 *               type: string
 *               description: 品牌代码
 *             brand_name:
 *               type: string
 *               description: 品牌名称
 *             is_self_brand:
 *               type: integer
 *               description: 是否自主品牌：1-否，2-是
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.put('/:id', selfProductBrandController.updateSelfProductBrand.bind(selfProductBrandController));

/**
 * @swagger
 * /api/master/self-product-brand/{id}:
 *   delete:
 *     tags:
 *       - 产品品牌
 *     summary: 删除产品品牌
 *     description: 删除产品品牌
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 产品品牌ID
 *         required: true
 *         type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 */
router.delete('/:id', selfProductBrandController.deleteSelfProductBrand.bind(selfProductBrandController));

module.exports = router;

const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 引入控制器
const MessageBoardController = require('../system/message-board/controllers/MessageBoardController');
const controller = new MessageBoardController(prisma);

// 创建留言 - 不需要身份验证，允许匿名提交
router.post('/', (req, res) => {
  const { current_url, submitter, content, remark } = req.body;
  
  // 手动验证必填字段
  if (!current_url) {
    return res.status(400).json({ 
      code: 400, 
      message: '当前链接不能为空', 
      data: null 
    });
  }
  
  if (!submitter) {
    return res.status(400).json({ 
      code: 400, 
      message: '提交人不能为空', 
      data: null 
    });
  }
  
  if (!content) {
    return res.status(400).json({ 
      code: 400, 
      message: '内容不能为空', 
      data: null 
    });
  }
  
  // 验证字段长度
  if (current_url.length > 255) {
    return res.status(400).json({ 
      code: 400, 
      message: '当前链接长度不能超过255个字符', 
      data: null 
    });
  }
  
  if (submitter.length > 100) {
    return res.status(400).json({ 
      code: 400, 
      message: '提交人长度不能超过100个字符', 
      data: null 
    });
  }
  
  controller.create(req, res);
});

// 以下路由需要身份验证
router.use(authMiddleware);

// 获取留言列表
router.get('/', (req, res) => {
  const { current_url, submitter, start_time, end_time, page, limit } = req.query;
  
  // 手动验证字段类型
  if (current_url && typeof current_url !== 'string') {
    return res.status(400).json({ 
      code: 400, 
      message: '当前链接必须是字符串', 
      data: null 
    });
  }
  
  if (submitter && typeof submitter !== 'string') {
    return res.status(400).json({ 
      code: 400, 
      message: '提交人必须是字符串', 
      data: null 
    });
  }
  
  if (start_time && isNaN(parseInt(start_time))) {
    return res.status(400).json({ 
      code: 400, 
      message: '开始时间必须是数字', 
      data: null 
    });
  }
  
  if (end_time && isNaN(parseInt(end_time))) {
    return res.status(400).json({ 
      code: 400, 
      message: '结束时间必须是数字', 
      data: null 
    });
  }
  
  if (page && (isNaN(parseInt(page)) || parseInt(page) <= 0)) {
    return res.status(400).json({ 
      code: 400, 
      message: '页码必须是大于0的整数', 
      data: null 
    });
  }
  
  if (limit && (isNaN(parseInt(limit)) || parseInt(limit) <= 0)) {
    return res.status(400).json({ 
      code: 400, 
      message: '每页条数必须是大于0的整数', 
      data: null 
    });
  }
  
  controller.findAll(req, res);
});

// 获取留言详情
router.get('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  
  if (isNaN(id) || id <= 0) {
    return res.status(400).json({ 
      code: 400, 
      message: 'ID必须是大于0的整数', 
      data: null 
    });
  }
  
  controller.findOne(req, res);
});

// 更新留言
router.put('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  
  if (isNaN(id) || id <= 0) {
    return res.status(400).json({ 
      code: 400, 
      message: 'ID必须是大于0的整数', 
      data: null 
    });
  }
  
  const { current_url, submitter, content, remark } = req.body;
  
  // 手动验证字段类型
  if (current_url && typeof current_url !== 'string') {
    return res.status(400).json({ 
      code: 400, 
      message: '当前链接必须是字符串', 
      data: null 
    });
  }
  
  if (submitter && typeof submitter !== 'string') {
    return res.status(400).json({ 
      code: 400, 
      message: '提交人必须是字符串', 
      data: null 
    });
  }
  
  if (content && typeof content !== 'string') {
    return res.status(400).json({ 
      code: 400, 
      message: '内容必须是字符串', 
      data: null 
    });
  }
  
  if (remark && typeof remark !== 'string') {
    return res.status(400).json({ 
      code: 400, 
      message: '备注必须是字符串', 
      data: null 
    });
  }
  
  // 验证字段长度
  if (current_url && current_url.length > 255) {
    return res.status(400).json({ 
      code: 400, 
      message: '当前链接长度不能超过255个字符', 
      data: null 
    });
  }
  
  if (submitter && submitter.length > 100) {
    return res.status(400).json({ 
      code: 400, 
      message: '提交人长度不能超过100个字符', 
      data: null 
    });
  }
  
  controller.update(req, res);
});

// 删除留言
router.delete('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  
  if (isNaN(id) || id <= 0) {
    return res.status(400).json({ 
      code: 400, 
      message: 'ID必须是大于0的整数', 
      data: null 
    });
  }
  
  controller.remove(req, res);
});

// 批量删除留言
router.post('/batch-remove', (req, res) => {
  const { ids } = req.body;
  
  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ 
      code: 400, 
      message: 'ids必须是非空数组', 
      data: null 
    });
  }
  
  controller.batchRemove(req, res);
});

module.exports = router;

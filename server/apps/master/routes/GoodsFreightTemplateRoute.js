/**
 * 运费模板路由
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const GoodsFreightTemplateController = require('../controllers/GoodsFreightTemplateController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new GoodsFreightTemplateController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/goods-freight-template:
 *   get:
 *     summary: 获取运费模板列表
 *     description: 获取所有运费模板及其区域配置信息，支持分页和筛选
 *     tags: [运费模板]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 运费模板名称（支持模糊搜索）
 *         example: "标准快递"
 *       - in: query
 *         name: chargeType
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: 计费方式（1:按件计费, 2:按重量计费, 3:按体积计费）
 *     responses:
 *       200:
 *         description: 成功获取运费模板列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取运费模板列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "175160355448819712"
 *                           name:
 *                             type: string
 *                             example: "标准运费模板"
 *                           chargeType:
 *                             type: integer
 *                             example: 1
 *                           chargeTypeText:
 *                             type: string
 *                             example: "按件计费"
 *                           createdAt:
 *                             type: integer
 *                             format: int64
 *                             example: 1672531200000
 *                             description: 创建时间戳（毫秒）
 *                           createdBy:
 *                             type: string
 *                             example: "175160355448819712"
 *                             description: 创建人ID
 *                           deliveryAreas:
 *                             type: string
 *                             example: "广东-东莞市、广东-云浮市、广东-肇庆市"
 *                             description: 配送区域，格式为"省份-城市"，多个区域用"、"分隔
 *                           freightConfigs:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   example: "191800977978953728"
 *                                 firstItem:
 *                                   type: integer
 *                                   example: 1
 *                                   description: 首件/首重/首体积数量
 *                                 firstFee:
 *                                   type: number
 *                                   format: decimal
 *                                   example: 10.00
 *                                   description: 首件/首重/首体积费用（元）
 *                                 additionalItem:
 *                                   type: integer
 *                                   example: 1
 *                                   description: 续件/续重/续体积数量
 *                                 additionalFee:
 *                                   type: number
 *                                   format: decimal
 *                                   example: 3.00
 *                                   description: 续件/续重/续体积费用（元）
 *                                 isDefault:
 *                                   type: boolean
 *                                   example: true
 *                                 regionRelations:
 *                                   type: array
 *                                   items:
 *                                     type: object
 *                                     properties:
 *                                       regionCode:
 *                                         type: string
 *                                         example: "310000,310100"
 *                                         description: 区域编码
 *                                       regionName:
 *                                         type: string
 *                                         example: "市辖区"
 *                                         description: 区域名称
 *                                       parentName:
 *                                         type: string
 *                                         example: "上海"
 *                                         description: 上级区域名称
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           example: 2
 *                         currentPage:
 *                           type: integer
 *                           example: 1
 *                         totalPage:
 *                           type: integer
 *                           example: 1
 */
// 获取运费模板列表
protectedRouter.get('/', (req, res) => controller.getGoodsFreightTemplates(req, res));

/**
 * @swagger
 * /api/master/goods-freight-template/charge-type-options:
 *   get:
 *     summary: 获取计费类型选项
 *     description: 获取所有可用的计费类型选项，用于前端下拉框等
 *     tags: [运费模板]
 *     responses:
 *       200:
 *         description: 成功获取计费类型选项
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取计费类型选项成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: integer
 *                         example: 1
 *                         description: 计费类型值
 *                       label:
 *                         type: string
 *                         example: "按件计费"
 *                         description: 计费类型显示文本
 *                       unit:
 *                         type: string
 *                         example: "件"
 *                         description: 计费单位
 */
// 获取计费类型选项（注意：这个路由必须放在 /:id 路由之前）
protectedRouter.get('/charge-type-options', (req, res) => controller.getChargeTypeOptions(req, res));

/**
 * @swagger
 * /api/master/goods-freight-template/{id}:
 *   get:
 *     summary: 获取指定ID的运费模板
 *     description: 根据ID获取运费模板及其区域配置详情
 *     tags: [运费模板]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 运费模板ID
 *     responses:
 *       200:
 *         description: 成功获取运费模板详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取运费模板成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "175160355448819712"
 *                     name:
 *                       type: string
 *                       example: "标准运费模板"
 *                     chargeType:
 *                       type: integer
 *                       example: 1
 *                     chargeTypeText:
 *                       type: string
 *                       example: "按件计费"
 *                     isDefault:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       example: "1683123456"
 *                     updatedAt:
 *                       type: string
 *                       example: "1683123456"
 *                     regionConfigs:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "175160355448819712"
 *                           regionCodes:
 *                             type: string
 *                             example: "0"
 *                           regionNames:
 *                             type: string
 *                             example: "全国"
 *                           firstItem:
 *                             type: integer
 *                             example: 1
 *                           firstFee:
 *                             type: string
 *                             example: "10"
 *                           additionalItem:
 *                             type: integer
 *                             example: 1
 *                           additionalFee:
 *                             type: string
 *                             example: "5"
 */
// 获取指定ID的运费模板
protectedRouter.get('/:id', (req, res) => controller.getGoodsFreightTemplateById(req, res));

/**
 * @swagger
 * /api/master/goods-freight-template:
 *   post:
 *     summary: 添加运费模板
 *     description: 创建新的运费模板及其区域配置
 *     tags: [运费模板]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - chargeType
 *               - freightConfigs
 *             properties:
 *               name:
 *                 type: string
 *                 example: "标准快递运费"
 *                 description: 运费模板名称
 *               chargeType:
 *                 type: integer
 *                 example: 1
 *                 description: 计费方式（1:按件计费, 2:按重量计费, 3:按体积计费）
 *               freightConfigs:
 *                 type: array
 *                 description: 运费配置列表
 *                 items:
 *                   type: object
 *                   properties:
 *                     firstItem:
 *                       type: integer
 *                       example: 1
 *                       description: 首件/首重/首体积数量
 *                     firstFee:
 *                       type: number
 *                       format: decimal
 *                       example: 8.00
 *                       description: 首件/首重/首体积费用（元）
 *                     additionalItem:
 *                       type: integer
 *                       example: 1
 *                       description: 续件/续重/续体积数量
 *                     additionalFee:
 *                       type: number
 *                       format: decimal
 *                       example: 3.00
 *                       description: 续件/续重/续体积费用（元）
 *                     isDefault:
 *                       type: boolean
 *                       example: false
 *                       description: 是否为默认配置（全国）
 *                     areas:
 *                       type: array
 *                       description: 适用区域列表（默认配置时为空）
 *                       items:
 *                         type: object
 *                         properties:
 *                           code:
 *                             type: string
 *                             example: "110000"
 *                             description: 区域编码
 *                           name:
 *                             type: string
 *                             example: "北京市"
 *                             description: 区域名称
 *                           parentName:
 *                             type: string
 *                             example: "华北地区"
 *                             description: 上级区域名称
 *     responses:
 *       200:
 *         description: 成功添加运费模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 添加运费模板成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "191800977903456256"
 *                     name:
 *                       type: string
 *                       example: "标准快递运费"
 *                     chargeType:
 *                       type: integer
 *                       example: 1
 *                     chargeTypeText:
 *                       type: string
 *                       example: "按件数"
 *                     freightConfigs:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "191800977978953728"
 *                           firstItem:
 *                             type: integer
 *                             example: 1
 *                           firstFee:
 *                             type: number
 *                             format: decimal
 *                             example: 8.00
 *                           additionalItem:
 *                             type: integer
 *                             example: 1
 *                           additionalFee:
 *                             type: number
 *                             format: decimal
 *                             example: 3.00
 *                           isDefault:
 *                             type: boolean
 *                             example: true
 *                           regionRelations:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 regionCode:
 *                                   type: string
 *                                   example: "110000"
 *                                 regionName:
 *                                   type: string
 *                                   example: "北京市"
 *                                 parentName:
 *                                   type: string
 *                                   example: "华北地区"
 */
// 添加运费模板
protectedRouter.post('/', (req, res) => controller.addGoodsFreightTemplate(req, res));

/**
 * @swagger
 * /api/master/goods-freight-template/{id}:
 *   put:
 *     summary: 更新运费模板
 *     description: 更新指定ID的运费模板及其区域配置
 *     tags: [运费模板]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 运费模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "标准运费模板更新"
 *               chargeType:
 *                 type: integer
 *                 example: 1
 *               isDefault:
 *                 type: boolean
 *                 example: true
 *               regionConfigs:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "175160355448819712"
 *                       description: 区域配置ID，更新时需提供
 *                     regionCodes:
 *                       type: string
 *                       example: "0"
 *                     firstItem:
 *                       type: integer
 *                       example: 1
 *                     firstFee:
 *                       type: number
 *                       example: 10
 *                     additionalItem:
 *                       type: integer
 *                       example: 1
 *                     additionalFee:
 *                       type: number
 *                       example: 5
 *     responses:
 *       200:
 *         description: 成功更新运费模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新运费模板成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "175160355448819712"
 */
// 更新运费模板
protectedRouter.put('/:id', (req, res) => controller.updateGoodsFreightTemplate(req, res));

/**
 * @swagger
 * /api/master/goods-freight-template/{id}:
 *   delete:
 *     summary: 删除运费模板
 *     description: 删除指定ID的运费模板及其关联的区域配置
 *     tags: [运费模板]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 运费模板ID
 *     responses:
 *       200:
 *         description: 成功删除运费模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除运费模板成功
 */
// 删除运费模板
protectedRouter.delete('/:id', (req, res) => controller.deleteGoodsFreightTemplate(req, res));

module.exports = router;

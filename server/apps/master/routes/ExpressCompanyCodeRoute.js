const express = require('express');
const router = express.Router();
const ExpressCompanyCodeController = require('../controllers/ExpressCompanyCodeController');

// 创建控制器实例
const expressCompanyCodeController = new ExpressCompanyCodeController();

// 获取快递公司编码列表
router.get('/', expressCompanyCodeController.getExpressCompanyCodes.bind(expressCompanyCodeController));

// 根据ID获取快递公司编码
router.get('/id/:id', expressCompanyCodeController.getExpressCompanyCodeById.bind(expressCompanyCodeController));

// 根据编码获取快递公司编码
router.get('/code/:code', expressCompanyCodeController.getExpressCompanyCodeByCode.bind(expressCompanyCodeController));

module.exports = router;

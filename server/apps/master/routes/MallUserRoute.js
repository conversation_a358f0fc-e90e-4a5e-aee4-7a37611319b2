const express = require('express');
const router = express.Router();
const MallUserController = require('../controllers/MallUserController');
const { authMiddleware } = require('../../../core/middleware/AuthMiddleware');

/**
 * 商城前端用户路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  const mallUserController = new MallUserController(prisma);

  /**
   * @swagger
   * /api/master/mall/users:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取用户列表
   *     description: 获取商城前端用户列表，需要管理员权限
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: username
   *         schema:
   *           type: string
   *         description: 用户名（模糊查询）
   *       - in: query
   *         name: nickname
   *         schema:
   *           type: string
   *         description: 昵称（模糊查询）
   *       - in: query
   *         name: status
   *         schema:
   *           type: integer
   *           enum: [0, 1]
   *         description: 状态（0-禁用，1-正常）
   *     responses:
   *       200:
   *         description: 获取成功
   *       401:
   *         description: 未授权
   */
  router.get('/mall/users', authMiddleware, mallUserController.list.bind(mallUserController));

  /**
   * @swagger
   * /api/master/mall/users/{id}:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取用户详情
   *     description: 获取商城前端用户详情，需要管理员权限
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: 用户ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       404:
   *         description: 用户不存在
   *       401:
   *         description: 未授权
   */
  router.get('/mall/users/:id', authMiddleware, mallUserController.detail.bind(mallUserController));

  /**
   * @swagger
   * /api/master/mall/users/{id}/status:
   *   put:
   *     tags: [商城用户管理]
   *     summary: 更新用户状态
   *     description: 更新商城前端用户状态，需要管理员权限
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: 用户ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - status
   *             properties:
   *               status:
   *                 type: integer
   *                 enum: [0, 1]
   *                 description: 状态（0-禁用，1-正常）
   *     responses:
   *       200:
   *         description: 更新成功
   *       400:
   *         description: 请求参数错误
   *       404:
   *         description: 用户不存在
   *       401:
   *         description: 未授权
   */
  router.put('/mall/users/:id/status', authMiddleware, mallUserController.updateStatus.bind(mallUserController));

  /**
   * @swagger
   * /api/master/mall/users/{id}:
   *   delete:
   *     tags: [商城用户管理]
   *     summary: 删除用户
   *     description: 删除商城前端用户，需要管理员权限
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: 用户ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       404:
   *         description: 用户不存在
   *       401:
   *         description: 未授权
   */
  router.delete('/mall/users/:id', authMiddleware, mallUserController.delete.bind(mallUserController));

  return router;
};

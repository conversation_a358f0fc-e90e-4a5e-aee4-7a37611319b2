const Joi = require('joi');
const BaseDto = require('../../../../../core/dto/BaseDto');

/**
 * 商城用户数据传输对象
 * 用于验证商城用户接口的请求参数
 */
class MallUserDto extends BaseDto {
  /**
   * 验证创建商城用户的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateCreate(data) {
    const schema = Joi.object({
      username: Joi.string().required().min(3).max(50).messages({
        'string.base': '用户名必须是字符串',
        'string.empty': '用户名不能为空',
        'string.min': '用户名长度不能少于3个字符',
        'string.max': '用户名长度不能超过50个字符',
        'any.required': '用户名是必填项'
      }),
      password: Joi.string().required().min(6).max(50).messages({
        'string.base': '密码必须是字符串',
        'string.empty': '密码不能为空',
        'string.min': '密码长度不能少于6个字符',
        'string.max': '密码长度不能超过50个字符',
        'any.required': '密码是必填项'
      }),
      nickname: Joi.string().max(50).allow(null, '').messages({
        'string.base': '昵称必须是字符串',
        'string.max': '昵称长度不能超过50个字符'
      }),
      phone: Joi.string().pattern(/^1[3-9]\d{9}$/).allow(null, '').messages({
        'string.base': '手机号必须是字符串',
        'string.pattern.base': '手机号格式不正确，必须是11位手机号'
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.base': '邮箱必须是字符串',
        'string.email': '邮箱格式不正确'
      }),
      avatar: Joi.string().uri().allow(null, '').messages({
        'string.base': '头像地址必须是字符串',
        'string.uri': '头像地址必须是有效的URL'
      }),
      status: Joi.number().integer().valid(0, 1).default(1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(正常)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证更新商城用户的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).required().messages({
        'any.required': 'ID不能为空',
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      username: Joi.string().min(3).max(50).messages({
        'string.base': '用户名必须是字符串',
        'string.empty': '用户名不能为空',
        'string.min': '用户名长度不能少于3个字符',
        'string.max': '用户名长度不能超过50个字符'
      }),
      password: Joi.string().min(6).max(50).messages({
        'string.base': '密码必须是字符串',
        'string.empty': '密码不能为空',
        'string.min': '密码长度不能少于6个字符',
        'string.max': '密码长度不能超过50个字符'
      }),
      nickname: Joi.string().max(50).allow(null, '').messages({
        'string.base': '昵称必须是字符串',
        'string.max': '昵称长度不能超过50个字符'
      }),
      phone: Joi.string().pattern(/^1[3-9]\d{9}$/).allow(null, '').messages({
        'string.base': '手机号必须是字符串',
        'string.pattern.base': '手机号格式不正确，必须是11位手机号'
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.base': '邮箱必须是字符串',
        'string.email': '邮箱格式不正确'
      }),
      avatar: Joi.string().uri().allow(null, '').messages({
        'string.base': '头像地址必须是字符串',
        'string.uri': '头像地址必须是有效的URL'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(正常)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      }),
      createdBy: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '创建者ID格式不正确，必须为数字字符串'
      }),
      updatedBy: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '更新者ID格式不正确，必须为数字字符串'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证查询商城用户的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateQuery(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      username: Joi.string().messages({
        'string.base': '用户名必须是字符串'
      }),
      nickname: Joi.string().messages({
        'string.base': '昵称必须是字符串'
      }),
      email: Joi.string().messages({
        'string.base': '邮箱必须是字符串'
      }),
      phone: Joi.string().messages({
        'string.base': '手机号必须是字符串'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(正常)'
      }),
      keyword: Joi.string().messages({
        'string.base': '关键字必须是字符串'
      }),
      realname_auth_status: Joi.number().integer().valid(0, 1, 2, 3).messages({
        'number.base': '实名认证状态必须是数字',
        'number.integer': '实名认证状态必须是整数',
        'any.only': '实名认证状态只能是0(未认证)、1(等待审核)、2(认证通过)或3(认证失败)'
      }),
      // 分页参数
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码不能小于1'
      }),
      pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
        'number.base': '每页条数必须是数字',
        'number.integer': '每页条数必须是整数',
        'number.min': '每页条数不能小于1',
        'number.max': '每页条数不能超过100'
      })
    });

    return this.validate(data, schema);
  }
}

module.exports = MallUserDto;

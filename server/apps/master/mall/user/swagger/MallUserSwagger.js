/**
 * 商城用户管理 Swagger 模型定义
 */

module.exports = {
  schemas: {
    // 商城用户基础模型
    MallUser: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: '用户ID，16位雪花算法'
        },
        username: {
          type: 'string',
          description: '用户名，唯一'
        },
        nickname: {
          type: 'string',
          description: '用户昵称'
        },
        avatar: {
          type: 'string',
          description: '头像地址'
        },
        phone: {
          type: 'string',
          description: '手机号'
        },
        email: {
          type: 'string',
          description: '邮箱'
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-正常，0-禁用'
        },
        last_login_ip: {
          type: 'string',
          description: '最后登录IP'
        },
        last_login_time: {
          type: 'string',
          description: '最后登录时间戳（毫秒）'
        },
        login_count: {
          type: 'integer',
          description: '登录次数'
        },
        created_at: {
          type: 'string',
          description: '创建时间戳（毫秒）'
        },
        updated_at: {
          type: 'string',
          description: '更新时间戳（毫秒）'
        },
        remark: {
          type: 'string',
          description: '备注信息'
        }
      }
    },
    
    // 创建商城用户请求
    MallUserCreateRequest: {
      type: 'object',
      required: ['username', 'password'],
      properties: {
        username: {
          type: 'string',
          description: '用户名，必填，唯一'
        },
        password: {
          type: 'string',
          description: '密码，必填'
        },
        nickname: {
          type: 'string',
          description: '用户昵称'
        },
        avatar: {
          type: 'string',
          description: '头像地址'
        },
        phone: {
          type: 'string',
          description: '手机号'
        },
        email: {
          type: 'string',
          description: '邮箱'
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          default: 1,
          description: '状态：1-正常，0-禁用'
        },
        remark: {
          type: 'string',
          description: '备注信息'
        }
      }
    },
    
    // 更新商城用户请求
    MallUserUpdateRequest: {
      type: 'object',
      properties: {
        username: {
          type: 'string',
          description: '用户名'
        },
        password: {
          type: 'string',
          description: '密码'
        },
        nickname: {
          type: 'string',
          description: '用户昵称'
        },
        avatar: {
          type: 'string',
          description: '头像地址'
        },
        phone: {
          type: 'string',
          description: '手机号'
        },
        email: {
          type: 'string',
          description: '邮箱'
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-正常，0-禁用'
        },
        remark: {
          type: 'string',
          description: '备注信息'
        }
      }
    }
  },
  
  responses: {
    // 商城用户响应
    MallUserResponse: {
      description: '商城用户详情',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              code: {
                type: 'integer',
                description: '状态码',
                example: 200
              },
              message: {
                type: 'string',
                description: '提示信息',
                example: '操作成功'
              },
              data: {
                $ref: '#/components/schemas/MallUser'
              }
            }
          }
        }
      }
    },
    
    // 商城用户列表响应
    MallUserListResponse: {
      description: '商城用户列表',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              code: {
                type: 'integer',
                description: '状态码',
                example: 200
              },
              message: {
                type: 'string',
                description: '提示信息',
                example: '操作成功'
              },
              data: {
                type: 'object',
                properties: {
                  list: {
                    type: 'array',
                    items: {
                      $ref: '#/components/schemas/MallUser'
                    }
                  },
                  total: {
                    type: 'integer',
                    description: '总记录数',
                    example: 100
                  },
                  page: {
                    type: 'integer',
                    description: '当前页码',
                    example: 1
                  },
                  pageSize: {
                    type: 'integer',
                    description: '每页记录数',
                    example: 10
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};

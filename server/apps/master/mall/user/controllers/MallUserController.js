const BaseController = require('../../../../../core/controllers/BaseController');
const MallUserService = require('../services/MallUserService');
const MallUserDto = require('../dto/MallUserDto');

/**
 * 商城用户管理控制器
 * 处理商城用户相关的请求和响应
 */
class MallUserController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.prisma = prisma;
    this.userService = new MallUserService(prisma);
    
    // 当前时间戳（毫秒）
    this.getCurrentTimestamp = () => Date.now();
  }

  /**
   * 创建商城用户
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async create(req, res) {
    try {
      // 验证请求数据
      const { error, value } = MallUserDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message);
      }
      
      // 添加创建者信息
      value.created_by = req.user?.id;
      
      // 创建用户
      const user = await this.userService.create(value);
      
      // 处理BigInt序列化
      const serializedUser = JSON.parse(JSON.stringify(user, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      res.status(200).json({
        code: 200,
        message: '创建商城用户成功',
        data: serializedUser
      });
    } catch (err) {
      console.error('创建商城用户失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新商城用户
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async update(req, res) {
    try {
      const data = { ...req.body, id: req.params.id };
      const { error, value } = MallUserDto.validateUpdate(data);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      value.updated_by = req.user?.id;
      const result = await this.userService.update(value.id, value);
      
      // 处理BigInt序列化
      const serializedResult = JSON.parse(JSON.stringify(result, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      res.status(200).json({
        code: 200,
        message: '更新商城用户成功',
        data: serializedResult
      });
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除商城用户
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async delete(req, res) {
    try {
      // 获取用户ID
      const id = req.params.id;
      if (!id) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      // 调用服务删除用户
      const result = await this.userService.delete(id);
      if (!result) {
        return this.fail(res, '删除失败', 500);
      }

      return this.success(res, null, '删除成功');
    } catch (err) {
      console.error('删除商城用户失败:', err);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取用户实名认证信息
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getRealnameAuth(req, res) {
    try {
      // 获取用户ID
      const userId = req.params.userId;
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      // 使用用户服务获取实名认证信息
      const authInfo = await this.userService.getRealnameAuth(userId);

      if (!authInfo) {
        return this.success(res, null, '用户暂无实名认证信息');
      }

      // 处理BigInt序列化
      const serializedAuthInfo = JSON.parse(JSON.stringify(authInfo, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));

      return this.success(res, serializedAuthInfo, '获取实名认证信息成功');
    } catch (err) {
      console.error('获取实名认证信息失败:', err);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 审核通过实名认证
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async approveRealnameAuth(req, res) {
    try {
      // 获取用户ID
      const userId = req.params.userId;
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      const currentTime = this.getCurrentTimestamp();
      const operatorId = req.user?.id || null;

      // 开始事务
      const result = await this.prisma.$transaction(async (prisma) => {
        // 1. 查询实名认证信息
        const authInfo = await prisma.MallUserRealnameAuth.findFirst({
          where: {
            user_id: BigInt(userId),
            deleted_at: null
          }
        });

        if (!authInfo) {
          throw new Error('用户暂无实名认证信息');
        }

        if (authInfo.auth_status !== 1) {
          throw new Error('只能审核处于等待审核状态的认证信息');
        }

        // 2. 更新实名认证状态
        const updatedAuth = await prisma.MallUserRealnameAuth.update({
          where: { id: authInfo.id },
          data: {
            auth_status: 2, // 审核通过
            auth_date: new Date(),
            updated_at: currentTime
          }
        });

        // 3. 更新用户表中的实名认证状态
        await prisma.MasterMallUser.update({
          where: { id: BigInt(userId) },
          data: {
            realname_auth_status: 2, // 审核通过
            updated_at: currentTime
          }
        });

        // 4. 记录审核日志（暂时注释，因为日志表不存在）
        /*
        try {
          await prisma.$executeRaw`
            INSERT INTO "base"."mall_user_realname_auth_logs" (
              "id", "user_id", "auth_id", "action", "action_text", "operator_id", "remark", "created_at"
            ) VALUES (
              ${BigInt(currentTime)}, ${BigInt(userId)}, ${authInfo.id}, 'approve', '审核通过', ${operatorId ? BigInt(operatorId) : null}, '审核通过', ${currentTime}
            )
          `;
        } catch (logError) {
          // 日志记录失败不影响主流程，只记录错误
          console.error('记录审核日志失败:', logError);
        }
        */
        // 日志表尚未创建，暂时跳过日志记录步骤

        return updatedAuth;
      });

      // 处理BigInt序列化
      const serializedResult = JSON.parse(JSON.stringify(result, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));

      return this.success(res, serializedResult, '审核通过成功');
    } catch (err) {
      console.error('审核通过失败:', err);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 拒绝实名认证
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async rejectRealnameAuth(req, res) {
    try {
      // 获取用户ID和拒绝原因
      const userId = req.params.userId;
      const { reason } = req.body;

      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      if (!reason || reason.trim() === '') {
        return this.fail(res, '拒绝原因不能为空', 400);
      }

      const currentTime = this.getCurrentTimestamp();
      const operatorId = req.user?.id || null;

      // 开始事务
      const result = await this.prisma.$transaction(async (prisma) => {
        // 1. 查询实名认证信息
        const authInfo = await prisma.MallUserRealnameAuth.findFirst({
          where: {
            user_id: BigInt(userId),
            deleted_at: null
          }
        });

        if (!authInfo) {
          throw new Error('用户暂无实名认证信息');
        }

        if (authInfo.auth_status !== 1) {
          throw new Error('只能审核处于等待审核状态的认证信息');
        }

        // 2. 更新实名认证状态
        const updatedAuth = await prisma.MallUserRealnameAuth.update({
          where: { id: authInfo.id },
          data: {
            auth_status: 3, // 审核失败
            audit_remark: reason,
            updated_at: currentTime
          }
        });

        // 3. 更新用户表中的实名认证状态
        await prisma.MasterMallUser.update({
          where: { id: BigInt(userId) },
          data: {
            realname_auth_status: 3, // 审核失败
            updated_at: currentTime
          }
        });

        // 4. 记录审核日志（暂时注释，因为日志表不存在）
        /*
        try {
          await prisma.$executeRaw`
            INSERT INTO "base"."mall_user_realname_auth_logs" (
              "id", "user_id", "auth_id", "action", "action_text", "operator_id", "remark", "created_at"
            ) VALUES (
              ${BigInt(currentTime)}, ${BigInt(userId)}, ${authInfo.id}, 'reject', '拒绝认证', ${operatorId ? BigInt(operatorId) : null}, ${reason}, ${currentTime}
            )
          `;
        } catch (logError) {
          // 日志记录失败不影响主流程，只记录错误
          console.error('记录审核日志失败:', logError);
        }
        */
        // 日志表尚未创建，暂时跳过日志记录步骤

        return updatedAuth;
      });

      // 处理BigInt序列化
      const serializedResult = JSON.parse(JSON.stringify(result, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));

      return this.success(res, serializedResult, '拒绝认证成功');
    } catch (err) {
      console.error('拒绝认证失败:', err);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取商城用户详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getById(req, res) {
    try {
      const user = await this.userService.getById(req.params.id);
      
      // 处理BigInt序列化
      const serializedUser = JSON.parse(JSON.stringify(user, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      res.status(200).json({
        code: 200,
        message: '获取商城用户详情成功',
        data: serializedUser
      });
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取商城用户列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async list(req, res) {
    try {
      
      // 参数校验（包含分页参数）
      const { error, value } = MallUserDto.validateQuery(req.query);
      if (error) {
        console.error('参数验证失败:', error.details[0].message);
        return this.fail(res, error.details[0].message, 400);
      }
      
      // 提取分页参数
      const { page, page_size, ...filters } = value;
      const pageSize = page_size;
  
      // 组装分页参数
      const pagination = this.getPagination({ page, pageSize });
      
      // 调用服务获取用户列表
      const result = await this.userService.list({ ...filters, ...pagination });
      
      if (!result || !result.list) {
        console.error('服务层返回的数据不完整:', result);
        return this.fail(res, '获取用户列表失败，服务器内部错误', 500);
      }
      
      console.log('获取到用户列表，总数:', result.total);
      
      // 处理BigInt序列化
      const serializedItems = JSON.parse(JSON.stringify(result.list, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));

      const totalPage = Math.ceil(result.total / pageSize);
      res.status(200).json({
        success: true,
        message: '获取商城用户列表成功',
        code: 200,
        data: {
          items: serializedItems,
          pageInfo: {
            total: result.total,
            currentPage: parseInt(page),
            totalPage: totalPage
          }
        }
      });
    } catch (err) {
      console.error('获取商城用户列表失败:', err);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }
}

module.exports = MallUserController;

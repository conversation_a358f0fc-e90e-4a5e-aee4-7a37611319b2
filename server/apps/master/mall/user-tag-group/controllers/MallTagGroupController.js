const BaseController = require('../../../../../core/controllers/BaseController');
const MallTagGroupService = require('../services/MallTagGroupService');
const MallTagGroupDto = require('../dto/MallTagGroupDto');

/**
 * 商城会员标签组控制器
 */
class MallTagGroupController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.prisma = prisma;
    this.tagGroupService = new MallTagGroupService(prisma);
  }

  /**
   * 创建商城会员标签组
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async create(req, res) {
    try {
      console.log('创建会员标签组请求数据:', req.body);
      
      // 处理前端传递的下划线命名字段
      const formattedData = {
        tagGroupName: req.body.tag_group_name || req.body.tagGroupName,
        tagGroupSort: req.body.tag_group_sort || req.body.tagGroupSort,
        tagGroupBuildin: req.body.tag_group_buildin !== undefined ? parseInt(req.body.tag_group_buildin) : (req.body.tagGroupBuildin !== undefined ? parseInt(req.body.tagGroupBuildin) : undefined),
        tagGroupEnable: req.body.tag_group_enable !== undefined ? parseInt(req.body.tag_group_enable) : (req.body.tagGroupEnable !== undefined ? parseInt(req.body.tagGroupEnable) : undefined),
        subsiteId: req.body.subsite_id || req.body.subsiteId,
        status: req.body.status !== undefined ? parseInt(req.body.status) : undefined,
        remark: req.body.remark
      };
      
      console.log('格式化后的数据:', formattedData);
      
      // 去除未定义的字段
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key] === undefined) {
          delete formattedData[key];
        }
      });
      
      console.log('去除未定义字段后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallTagGroupDto.validateCreate(formattedData);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      console.log('验证后的数据:', value);
      
      // 添加创建者信息
      value.createdBy = req.user?.id;
      
      // 创建标签组
      const tagGroup = await this.tagGroupService.create(value);
      
      return this.success(res, tagGroup, '创建商城会员标签组成功');
    } catch (error) {
      console.error('创建商城会员标签组失败:', error);
      return this.fail(res, error.message || '创建商城会员标签组失败', 500);
    }
  }

  /**
   * 更新商城会员标签组
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async update(req, res) {
    try {
      console.log('更新会员标签组请求数据:', req.body);
      
      // 处理前端传递的下划线命名字段
      const formattedData = {
        id: req.params.id,
        tagGroupName: req.body.tag_group_name || req.body.tagGroupName,
        tagGroupSort: req.body.tag_group_sort || req.body.tagGroupSort,
        tagGroupBuildin: req.body.tag_group_buildin !== undefined ? parseInt(req.body.tag_group_buildin) : (req.body.tagGroupBuildin !== undefined ? parseInt(req.body.tagGroupBuildin) : undefined),
        tagGroupEnable: req.body.tag_group_enable !== undefined ? parseInt(req.body.tag_group_enable) : (req.body.tagGroupEnable !== undefined ? parseInt(req.body.tagGroupEnable) : undefined),
        subsiteId: req.body.subsite_id || req.body.subsiteId,
        status: req.body.status !== undefined ? parseInt(req.body.status) : undefined,
        remark: req.body.remark
      };
      
      console.log('格式化后的数据:', formattedData);
      
      // 去除未定义的字段
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key] === undefined) {
          delete formattedData[key];
        }
      });
      
      console.log('去除未定义字段后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallTagGroupDto.validateUpdate(formattedData);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      console.log('验证后的数据:', value);
      
      // 添加更新者信息
      value.updatedBy = req.user?.id;
      
      // 更新标签组
      const tagGroup = await this.tagGroupService.update(req.params.id, value);
      
      return this.success(res, tagGroup, '更新商城会员标签组成功');
    } catch (error) {
      console.error('更新商城会员标签组失败:', error);
      return this.fail(res, error.message || '更新商城会员标签组失败', 500);
    }
  }

  /**
   * 删除商城会员标签组
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async delete(req, res) {
    try {
      const id = req.params.id;
      const updatedBy = req.user?.id;
      
      // 删除标签组
      const result = await this.tagGroupService.delete(id, updatedBy);
      
      return this.success(res, result, '删除商城会员标签组成功');
    } catch (error) {
      console.error('删除商城会员标签组失败:', error);
      return this.fail(res, error.message || '删除商城会员标签组失败', 500);
    }
  }

  /**
   * 获取商城会员标签组详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getDetail(req, res) {
    try {
      const id = req.params.id;
      
      // 获取标签组详情
      const tagGroup = await this.tagGroupService.getDetail(id);
      
      return this.success(res, tagGroup, '获取商城会员标签组详情成功');
    } catch (error) {
      console.error('获取商城会员标签组详情失败:', error);
      return this.fail(res, error.message || '获取商城会员标签组详情失败', 500);
    }
  }

  /**
   * 获取商城会员标签组列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getList(req, res) {
    try {
      // 处理查询参数
      const queryParams = {
        id: req.query.id,
        tagGroupName: req.query.tag_group_name || req.query.tagGroupName,
        tagGroupBuildin: req.query.tag_group_buildin !== undefined ? parseInt(req.query.tag_group_buildin) : (req.query.tagGroupBuildin !== undefined ? parseInt(req.query.tagGroupBuildin) : undefined),
        tagGroupEnable: req.query.tag_group_enable !== undefined ? parseInt(req.query.tag_group_enable) : (req.query.tagGroupEnable !== undefined ? parseInt(req.query.tagGroupEnable) : undefined),
        subsiteId: req.query.subsite_id || req.query.subsiteId,
        status: req.query.status !== undefined ? parseInt(req.query.status) : undefined,
        keyword: req.query.keyword,
        page: req.query.page ? parseInt(req.query.page) : 1,
        pageSize: req.query.pageSize ? parseInt(req.query.pageSize) : 10,
        sortField: req.query.sortField || 'tag_group_sort',
        sortOrder: req.query.sortOrder || 'asc'
      };
      
      // 验证查询参数
      const { error, value } = MallTagGroupDto.validateQuery(queryParams);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      // 获取标签组列表
      const result = await this.tagGroupService.getList(value);
      
      return this.success(res, result, '获取商城会员标签组列表成功');
    } catch (error) {
      console.error('获取商城会员标签组列表失败:', error);
      return this.fail(res, error.message || '获取商城会员标签组列表失败', 500);
    }
  }

  /**
   * 获取所有启用的标签组（用于下拉选择）
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getAllEnabled(req, res) {
    try {
      const queryParams = {
        subsiteId: req.query.subsite_id || req.query.subsiteId
      };
      
      // 获取所有启用的标签组
      const tagGroups = await this.tagGroupService.getAllEnabled(queryParams);
      
      return this.success(res, tagGroups, '获取所有启用的标签组成功');
    } catch (error) {
      console.error('获取所有启用的标签组失败:', error);
      return this.fail(res, error.message || '获取所有启用的标签组失败', 500);
    }
  }
}

module.exports = MallTagGroupController;

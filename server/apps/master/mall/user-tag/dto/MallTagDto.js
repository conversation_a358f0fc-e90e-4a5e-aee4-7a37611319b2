const Joi = require('joi');

/**
 * 商城会员标签数据传输对象
 */
class MallTagDto {
  /**
   * 验证创建商城会员标签的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateCreate(data) {
    const schema = Joi.object({
      tag_name: Joi.string().max(50).required().messages({
        'any.required': '标签名称不能为空',
        'string.base': '标签名称必须是字符串',
        'string.max': '标签名称长度不能超过50个字符'
      }),
      tag_group_id: Joi.string().pattern(/^[0-9]+$/).required().messages({
        'any.required': '标签组ID不能为空',
        'string.pattern.base': '标签组ID格式不正确，必须为数字字符串'
      }),
      sort: Joi.number().integer().min(0).default(0).messages({
        'number.base': '排序值必须是数字',
        'number.integer': '排序值必须是整数',
        'number.min': '排序值不能小于0'
      }),
      tag_buildin: Joi.number().integer().valid(0, 1).default(0).messages({
        'number.base': '是否系统内置必须是数字',
        'number.integer': '是否系统内置必须是整数',
        'any.only': '是否系统内置只能是0(否)或1(是)'
      }),
      tag_enable: Joi.number().integer().valid(0, 1).default(1).messages({
        'number.base': '是否启用必须是数字',
        'number.integer': '是否启用必须是整数',
        'any.only': '是否启用只能是0(禁用)或1(启用)'
      }),
      subsite_id: Joi.string().pattern(/^[0-9]+$/).allow(null, '').messages({
        'string.pattern.base': '子站点ID格式不正确，必须为数字字符串'
      }),
      status: Joi.number().integer().valid(0, 1).default(1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      }),
      createdBy: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '创建者ID格式不正确，必须为数字字符串'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证更新商城会员标签的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).required().messages({
        'any.required': 'ID不能为空',
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      tag_name: Joi.string().max(50).messages({
        'string.base': '标签名称必须是字符串',
        'string.max': '标签名称长度不能超过50个字符'
      }),
      tag_group_id: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '标签组ID格式不正确，必须为数字字符串'
      }),
      sort: Joi.number().integer().min(0).messages({
        'number.base': '排序值必须是数字',
        'number.integer': '排序值必须是整数',
        'number.min': '排序值不能小于0'
      }),
      tag_buildin: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否系统内置必须是数字',
        'number.integer': '是否系统内置必须是整数',
        'any.only': '是否系统内置只能是0(否)或1(是)'
      }),
      tag_enable: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否启用必须是数字',
        'number.integer': '是否启用必须是整数',
        'any.only': '是否启用只能是0(禁用)或1(启用)'
      }),
      subsite_id: Joi.string().pattern(/^[0-9]+$/).allow(null, '').messages({
        'string.pattern.base': '子站点ID格式不正确，必须为数字字符串'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      }),
      updatedBy: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '更新者ID格式不正确，必须为数字字符串'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证查询商城会员标签的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateQuery(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      tag_name: Joi.string().messages({
        'string.base': '标签名称必须是字符串'
      }),
      tag_group_id: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '标签组ID格式不正确，必须为数字字符串'
      }),
      tag_buildin: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否系统内置必须是数字',
        'number.integer': '是否系统内置必须是整数',
        'any.only': '是否系统内置只能是0(否)或1(是)'
      }),
      tag_enable: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否启用必须是数字',
        'number.integer': '是否启用必须是整数',
        'any.only': '是否启用只能是0(禁用)或1(启用)'
      }),
      subsite_id: Joi.string().pattern(/^[0-9]+$/).allow(null, '').messages({
        'string.pattern.base': '子站点ID格式不正确，必须为数字字符串'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      keyword: Joi.string().allow('').messages({
        'string.base': '关键词必须是字符串'
      }),
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码不能小于1'
      }),
      pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
        'number.base': '每页条数必须是数字',
        'number.integer': '每页条数必须是整数',
        'number.min': '每页条数不能小于1',
        'number.max': '每页条数不能大于100'
      }),
      sortField: Joi.string().valid('id', 'tag_title', 'tag_sort', 'created_at', 'updated_at').default('tag_sort').messages({
        'string.base': '排序字段必须是字符串',
        'any.only': '排序字段只能是id、tag_title、tag_sort、created_at、updated_at'
      }),
      sortOrder: Joi.string().valid('asc', 'desc').default('asc').messages({
        'string.base': '排序方向必须是字符串',
        'any.only': '排序方向只能是asc或desc'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 通用验证方法
   * @param {Object} data 要验证的数据
   * @param {Object} schema Joi验证模式
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validate(data, schema) {
    const { error, value } = schema.validate(data, { stripUnknown: true });
    return { error, value };
  }
}

module.exports = MallTagDto;

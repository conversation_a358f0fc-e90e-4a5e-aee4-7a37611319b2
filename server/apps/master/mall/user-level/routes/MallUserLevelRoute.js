const express = require('express');
const router = express.Router();
const MallUserLevelController = require('../controllers/MallUserLevelController');
const { prisma } = require('../../../../../core/database/prisma');
const RouterConfig = require('../../../../../core/routes/RouterConfig');

// 获取base schema的prisma客户端
const controller = new MallUserLevelController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/mall/user-level:
 *   get:
 *     tags: [商城管理/会员管理/等级管理]
 *     summary: 获取商城会员等级列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallUserLevelListResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.get('/', controller.list.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user-level:
 *   post:
 *     tags: [商城管理/会员管理/等级管理]
 *     summary: 创建商城会员等级
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MallUserLevelCreateRequest'
 *     responses:
 *       201:
 *         $ref: '#/components/responses/MallUserLevelResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.post('/', controller.create.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user-level/{id}:
 *   put:
 *     tags: [商城管理/会员管理/等级管理]
 *     summary: 更新商城会员等级
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MallUserLevelUpdateRequest'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallUserLevelResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 等级不存在
 */
protectedRouter.put('/:id', controller.update.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user-level/{id}:
 *   delete:
 *     tags: [商城管理/会员管理/等级管理]
 *     summary: 删除商城会员等级
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       204:
 *         description: 删除成功
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 等级不存在
 */
protectedRouter.delete('/:id', controller.delete.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user-level/{id}:
 *   get:
 *     tags: [商城管理/会员管理/等级管理]
 *     summary: 获取商城会员等级详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallUserLevelResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 等级不存在
 */
protectedRouter.get('/:id', controller.getById.bind(controller));

module.exports = router;

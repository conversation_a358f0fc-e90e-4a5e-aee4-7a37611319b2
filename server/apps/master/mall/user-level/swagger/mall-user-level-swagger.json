{"openapi": "3.0.0", "info": {"title": "商城会员等级管理 API", "description": "商城会员等级管理相关接口", "version": "1.0.0"}, "servers": [{"url": "/api/v1"}], "tags": [{"name": "商城管理/会员管理/等级管理", "description": "商城会员等级相关操作"}], "paths": {"/master/mall/user-level": {"get": {"tags": ["商城管理/会员管理/等级管理"], "summary": "获取商城会员等级列表", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页条数", "schema": {"type": "integer", "default": 10}}, {"name": "levelName", "in": "query", "description": "等级名称", "schema": {"type": "string"}}, {"name": "isSystem", "in": "query", "description": "是否系统内置：1-是，0-否", "schema": {"type": "integer", "enum": [0, 1]}}, {"name": "status", "in": "query", "description": "状态：1-启用，0-禁用", "schema": {"type": "integer", "enum": [0, 1]}}, {"name": "keyword", "in": "query", "description": "关键字搜索", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "获取商城会员等级列表成功"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/MallUserLevel"}}, "total": {"type": "integer", "description": "总记录数"}, "page": {"type": "integer", "description": "当前页码"}, "pageSize": {"type": "integer", "description": "每页记录数"}}}}}}}}, "401": {"description": "未授权，请先登录"}}}, "post": {"tags": ["商城管理/会员管理/等级管理"], "summary": "创建商城会员等级", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserLevelCreateRequest"}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "创建商城会员等级成功"}, "data": {"$ref": "#/components/schemas/MallUserLevel"}}}}}}, "400": {"description": "参数错误"}, "401": {"description": "未授权，请先登录"}}}}, "/master/mall/user-level/{id}": {"get": {"tags": ["商城管理/会员管理/等级管理"], "summary": "获取商城会员等级详情", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "等级ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "获取商城会员等级详情成功"}, "data": {"$ref": "#/components/schemas/MallUserLevel"}}}}}}, "401": {"description": "未授权，请先登录"}, "404": {"description": "等级不存在"}}}, "put": {"tags": ["商城管理/会员管理/等级管理"], "summary": "更新商城会员等级", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "等级ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserLevelUpdateRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "更新商城会员等级成功"}, "data": {"$ref": "#/components/schemas/MallUserLevel"}}}}}}, "400": {"description": "参数错误"}, "401": {"description": "未授权，请先登录"}, "404": {"description": "等级不存在"}}}, "delete": {"tags": ["商城管理/会员管理/等级管理"], "summary": "删除商城会员等级", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "等级ID", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "删除成功"}, "401": {"description": "未授权，请先登录"}, "404": {"description": "等级不存在"}}}}}, "components": {"schemas": {"MallUserLevel": {"type": "object", "properties": {"id": {"type": "string", "description": "等级ID，16位雪花算法"}, "level_name": {"type": "string", "description": "等级名称，唯一"}, "experience_value": {"type": "string", "description": "升级所需经验值"}, "total_consumption": {"type": "number", "format": "float", "description": "升级所需累计消费金额（元）"}, "level_icon": {"type": "string", "description": "等级图标URL地址"}, "discount_rate": {"type": "number", "format": "float", "description": "折扣率（百分比），例如：95表示95折"}, "is_system": {"type": "integer", "enum": [0, 1], "description": "是否系统内置：1-是，0-否"}, "status": {"type": "integer", "enum": [0, 1], "description": "状态：1-启用，0-禁用"}, "created_at": {"type": "string", "description": "创建时间戳（毫秒）"}, "updated_at": {"type": "string", "description": "更新时间戳（毫秒）"}, "remark": {"type": "string", "description": "备注信息"}}}, "MallUserLevelCreateRequest": {"type": "object", "required": ["levelName"], "properties": {"levelName": {"type": "string", "description": "等级名称，必填，唯一"}, "experienceValue": {"type": "integer", "description": "升级所需经验值", "default": 0}, "totalConsumption": {"type": "number", "format": "float", "description": "升级所需累计消费金额（元）", "default": 0}, "levelIcon": {"type": "string", "description": "等级图标URL地址"}, "discountRate": {"type": "number", "format": "float", "description": "折扣率（百分比），例如：95表示95折", "default": 100}, "isSystem": {"type": "integer", "enum": [0, 1], "description": "是否系统内置：1-是，0-否", "default": 0}, "status": {"type": "integer", "enum": [0, 1], "description": "状态：1-启用，0-禁用", "default": 1}, "remark": {"type": "string", "description": "备注信息"}}}, "MallUserLevelUpdateRequest": {"type": "object", "properties": {"levelName": {"type": "string", "description": "等级名称，唯一"}, "experienceValue": {"type": "integer", "description": "升级所需经验值"}, "totalConsumption": {"type": "number", "format": "float", "description": "升级所需累计消费金额（元）"}, "levelIcon": {"type": "string", "description": "等级图标URL地址"}, "discountRate": {"type": "number", "format": "float", "description": "折扣率（百分比），例如：95表示95折"}, "isSystem": {"type": "integer", "enum": [0, 1], "description": "是否系统内置：1-是，0-否"}, "status": {"type": "integer", "enum": [0, 1], "description": "状态：1-启用，0-禁用"}, "remark": {"type": "string", "description": "备注信息"}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}
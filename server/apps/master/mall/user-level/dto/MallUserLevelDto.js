const Joi = require('joi');
const BaseDto = require('../../../../../core/dto/BaseDto');

/**
 * 商城会员等级数据传输对象
 * 用于验证商城会员等级接口的请求参数
 */
class MallUserLevelDto extends BaseDto {
  /**
   * 验证创建商城会员等级的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateCreate(data) {
    const schema = Joi.object({
      levelName: Joi.string().required().max(50).messages({
        'string.base': '等级名称必须是字符串',
        'string.empty': '等级名称不能为空',
        'string.max': '等级名称长度不能超过50个字符',
        'any.required': '等级名称是必填项'
      }),
      experienceValue: Joi.number().integer().min(0).default(0).messages({
        'number.base': '升级经验值必须是数字',
        'number.integer': '升级经验值必须是整数',
        'number.min': '升级经验值不能小于0'
      }),
      totalConsumption: Joi.number().precision(2).min(0).default(0).messages({
        'number.base': '累计消费金额必须是数字',
        'number.min': '累计消费金额不能小于0'
      }),
      levelIcon: Joi.string().uri().allow(null, '').messages({
        'string.base': '等级图标必须是字符串',
        'string.uri': '等级图标必须是有效的URL'
      }),
      discountRate: Joi.number().precision(2).min(0).max(100).default(100).messages({
        'number.base': '折扣率必须是数字',
        'number.min': '折扣率不能小于0',
        'number.max': '折扣率不能大于100'
      }),
      isSystem: Joi.number().integer().valid(0, 1).default(0).messages({
        'number.base': '是否系统内置必须是数字',
        'number.integer': '是否系统内置必须是整数',
        'any.only': '是否系统内置只能是0(否)或1(是)'
      }),
      status: Joi.number().integer().valid(0, 1).default(1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证更新商城会员等级的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).required().messages({
        'any.required': 'ID不能为空',
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      levelName: Joi.string().max(50).messages({
        'string.base': '等级名称必须是字符串',
        'string.max': '等级名称长度不能超过50个字符'
      }),
      experienceValue: Joi.number().integer().min(0).messages({
        'number.base': '升级经验值必须是数字',
        'number.integer': '升级经验值必须是整数',
        'number.min': '升级经验值不能小于0'
      }),
      totalConsumption: Joi.number().precision(2).min(0).messages({
        'number.base': '累计消费金额必须是数字',
        'number.min': '累计消费金额不能小于0'
      }),
      levelIcon: Joi.string().uri().allow(null, '').messages({
        'string.base': '等级图标必须是字符串',
        'string.uri': '等级图标必须是有效的URL'
      }),
      discountRate: Joi.number().precision(2).min(0).max(100).messages({
        'number.base': '折扣率必须是数字',
        'number.min': '折扣率不能小于0',
        'number.max': '折扣率不能大于100'
      }),
      isSystem: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否系统内置必须是数字',
        'number.integer': '是否系统内置必须是整数',
        'any.only': '是否系统内置只能是0(否)或1(是)'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      remark: Joi.string().max(200).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过200个字符'
      }),
      updatedBy: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '更新者ID格式不正确，必须为数字字符串'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证查询商城会员等级的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateQuery(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      levelName: Joi.string().allow('').optional().messages({
        'string.base': '等级名称必须是字符串'
      }),
      level_name: Joi.string().allow('').optional().messages({
        'string.base': '等级名称必须是字符串'
      }),
      isSystem: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否系统内置必须是数字',
        'number.integer': '是否系统内置必须是整数',
        'any.only': '是否系统内置只能是0(否)或1(是)'
      }),
      is_system: Joi.number().integer().valid(0, 1).messages({
        'number.base': '是否系统内置必须是数字',
        'number.integer': '是否系统内置必须是整数',
        'any.only': '是否系统内置只能是0(否)或1(是)'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      keyword: Joi.string().allow('').optional().messages({
        'string.base': '关键字必须是字符串'
      }),
      // 分页参数
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码不能小于1'
      }),
      pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
        'number.base': '每页条数必须是数字',
        'number.integer': '每页条数必须是整数',
        'number.min': '每页条数不能小于1',
        'number.max': '每页条数不能超过100'
      })
    });

    return this.validate(data, schema);
  }
}

module.exports = MallUserLevelDto;

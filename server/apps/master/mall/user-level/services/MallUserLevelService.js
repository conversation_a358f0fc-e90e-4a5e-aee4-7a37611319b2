const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

/**
 * 商城会员等级服务类
 * 处理商城会员等级相关的业务逻辑
 */
class MallUserLevelService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取商城会员等级列表
   * @param {Object} params 查询参数，包含分页和筛选条件
   * @returns {Object} 包含等级列表、总数和分页信息的对象
   */
  async list(params = {}) {
    // 直接使用传入的分页参数，不再重新设置默认值
    // 因为控制器已经处理过默认值
    const { skip, take, page, pageSize, ...filters } = params;
    const skipValue = skip || 0;
    // 如果有take参数则使用，否则使用pageSize参数，再没有则使用默认值10
    const takeValue = take || parseInt(pageSize) || 10;

    // 构建查询条件
    const where = { deleted_at: null };
    const allowKeys = ['id', 'status', 'is_system'];
    
    // 精确匹配字段
    for (const key of allowKeys) {
      if (filters[key] !== undefined && filters[key] !== '') {
        where[key] = filters[key];
      }
    }
    
    // 模糊查询字段
    if (filters.levelName) {
      where.level_name = { contains: filters.levelName };
    } else if (filters.level_name) {
      where.level_name = { contains: filters.level_name };
    }
    
    // 关键字模糊搜索
    if (filters.keyword && filters.keyword.trim() !== '') {
      const keyword = filters.keyword.trim();
      where.OR = [
        { level_name: { contains: keyword } }
      ];
    }

    // 执行查询
    const [total, levels] = await Promise.all([
      this.prisma.MallUserLevel.count({ where }),
      this.prisma.MallUserLevel.findMany({
        where,
        skip: skipValue,
        take: takeValue,
        orderBy: {
          created_at: 'desc'
        }
      })
    ]);

    // 确保返回正确的分页参数
    return {
      list: levels,
      total,
      page: parseInt(page) || 1,
      pageSize: takeValue
    };
  }

  /**
   * 创建商城会员等级
   * @param {Object} levelData 等级数据
   * @returns {Object} 创建的等级对象
   */
  async create(levelData) {
    // 检查等级名称是否已存在（包括已删除的记录）
    const existingLevel = await this.prisma.MallUserLevel.findFirst({
      where: { 
        level_name: levelData.levelName
      }
    });

    // 如果找到同名记录，且该记录未被删除，则报错
    if (existingLevel && existingLevel.deleted_at === null) {
      throw new Error('等级名称已存在');
    }
    
    // 如果找到同名记录，但该记录已被删除，则可以使用同名创建新记录
    // 但需要先删除旧记录，以避免数据库唯一约束冲突
    if (existingLevel && existingLevel.deleted_at !== null) {
      // 彻底删除已软删除的同名记录
      await this.prisma.MallUserLevel.delete({
        where: { id: existingLevel.id }
      });
      console.log('已删除旧的同名记录:', existingLevel.id);
    }

    // 生成雪花ID
    const id = generateSnowflakeId();
    
    // 获取当前时间戳
    const now = BigInt(Date.now());
    
    // 创建等级
    console.log('创建会员等级数据:', levelData); // 添加日志，帮助调试
    
    // 确保 level_name 字段有值
    if (!levelData.levelName) {
      throw new Error('等级名称不能为空');
    }
    
    const createData = {
      id,
      level_name: levelData.levelName,
      experience_value: BigInt(levelData.experienceValue || 0),
      total_consumption: levelData.totalConsumption || 0,
      level_icon: levelData.levelIcon || null,
      discount_rate: levelData.discountRate || 100,
      is_system: levelData.isSystem || 0,
      status: levelData.status !== undefined ? parseInt(levelData.status) : 1,
      created_at: now,
      updated_at: now,
      created_by: levelData.created_by ? BigInt(levelData.created_by) : null,
      updated_by: levelData.created_by ? BigInt(levelData.created_by) : null,
      remark: levelData.remark || null
    };
    
    console.log('实际创建数据:', createData);
    
    const newLevel = await this.prisma.MallUserLevel.create({
      data: createData
    });

    return newLevel;
  }

  /**
   * 更新商城会员等级
   * @param {string} id 等级ID
   * @param {Object} levelData 更新的等级数据
   * @returns {Object} 更新后的等级对象
   */
  async update(id, levelData) {
    // 检查等级是否存在
    const existingLevel = await this.prisma.MallUserLevel.findUnique({
      where: { id: BigInt(id) }
    });

    if (!existingLevel) {
      throw new Error('等级不存在');
    }

    // 如果要更新等级名称，检查新名称是否已被使用
    if (levelData.levelName && levelData.levelName !== existingLevel.level_name) {
      const levelWithSameName = await this.prisma.MallUserLevel.findFirst({
        where: { 
          level_name: levelData.levelName,
          deleted_at: null,
          id: { not: BigInt(id) }
        }
      });

      if (levelWithSameName) {
        throw new Error('等级名称已存在');
      }
    }

    // 如果是系统内置等级，不允许修改is_system字段
    if (existingLevel.is_system === 1 && parseInt(levelData.isSystem) === 0) {
      throw new Error('系统内置等级不允许修改为非系统内置');
    }
  
    console.log('原始 isSystem 值:', levelData.is_system, '类型:', typeof levelData.isSystem);
    
    // 更新时间戳
    const now = BigInt(Date.now());
    
    // 构建更新数据
    const updateData = {};
    
    console.log('服务层收到的更新数据:', levelData);
    console.log('去除未定义字段后的数据:', Object.fromEntries(Object.entries(levelData).filter(([_, v]) => v !== undefined)));
    
    // 只更新提供的字段
    if (levelData.levelName !== undefined) {
      console.log('更新等级名称:', levelData.levelName);
      updateData.level_name = levelData.levelName;
    } else if (levelData.level_name !== undefined) {
      console.log('更新等级名称(level_name):', levelData.level_name);
      updateData.level_name = levelData.level_name;
    }
    if (levelData.experienceValue !== undefined) updateData.experience_value = BigInt(levelData.experienceValue);
    if (levelData.totalConsumption !== undefined) updateData.total_consumption = levelData.totalConsumption;
    if (levelData.levelIcon !== undefined) updateData.level_icon = levelData.levelIcon;
    if (levelData.discountRate !== undefined) updateData.discount_rate = levelData.discountRate;
    
    // 特别处理isSystem字段，确保0和1都能被正确处理
    if (levelData.isSystem !== undefined) {
      const isSystemValue = parseInt(levelData.isSystem);
      console.log('处理isSystem字段:', levelData.isSystem, '转换后:', isSystemValue);
      updateData.is_system = isSystemValue;
    }
    
    if (levelData.status !== undefined) {
      console.log('处理status字段:', levelData.status);
      updateData.status = parseInt(levelData.status);
    }
    if (levelData.remark !== undefined) updateData.remark = levelData.remark;
    
    // 添加审计字段
    updateData.updated_at = now;
    if (levelData.updated_by) updateData.updated_by = BigInt(levelData.updated_by);
    
    console.log('实际更新数据:', updateData);
    
    // 更新等级
    const updatedLevel = await this.prisma.MallUserLevel.update({
      where: { id: BigInt(id) },
      data: updateData
    });

    return updatedLevel;
  }

  /**
   * 删除商城会员等级（软删除）
   * @param {string} id 等级ID
   * @param {string} updatedBy 操作人ID
   * @returns {Object} 删除结果
   */
  async delete(id, updatedBy) {
    // 检查等级是否存在
    const existingLevel = await this.prisma.MallUserLevel.findUnique({
      where: { id: BigInt(id) }
    });

    if (!existingLevel) {
      throw new Error('等级不存在');
    }

    // 检查是否为系统内置等级
    if (existingLevel.is_system === 1) {
      throw new Error('系统内置等级不允许删除');
    }

    // 注意：原来的会员关联检查代码已移除，因为 MasterMallUser 模型中没有 level_id 字段
    // 如果需要检查会员关联，请根据实际数据模型结构添加相应的代码
    console.log('删除等级:', id);

    // 软删除等级
    const now = BigInt(Date.now());
    await this.prisma.MallUserLevel.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: now,
        updated_at: now,
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });

    return { success: true };
  }

  /**
   * 根据ID获取商城会员等级详情
   * @param {string} id 等级ID
   * @returns {Object} 等级详情
   */
  async getById(id) {
    const level = await this.prisma.MallUserLevel.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!level) {
      throw new Error('等级不存在');
    }

    return level;
  }
}

module.exports = MallUserLevelService;

/**
 * 计费类型枚举
 * 定义运费模板的计费方式
 */

const ChargeTypeEnum = {
  /**
   * 按件计费
   */
  CHARGE_BY_PIECE: 1,
  
  /**
   * 按重量计费
   */
  CHARGE_BY_WEIGHT: 2,
  
  /**
   * 按体积计费
   */
  CHARGE_BY_VOLUME: 3,
  
  /**
   * 计费类型文本映射
   */
  TYPE_TEXT_MAP: {
    1: '按件计费',
    2: '按重量计费',
    3: '按体积计费'
  },
  
  /**
   * 计费单位文本映射
   */
  UNIT_TEXT_MAP: {
    1: '件',
    2: 'kg',
    3: 'm³'
  },
  
  /**
   * 所有有效的计费类型
   */
  ALL_TYPES: [1, 2, 3],
  
  /**
   * 获取计费类型文本
   * @param {number} chargeType - 计费类型
   * @returns {string} 计费类型文本
   */
  getChargeTypeText(chargeType) {
    return this.TYPE_TEXT_MAP[chargeType] || '未知计费方式';
  },
  
  /**
   * 获取计费单位文本
   * @param {number} chargeType - 计费类型
   * @returns {string} 计费单位文本
   */
  getChargeUnitText(chargeType) {
    return this.UNIT_TEXT_MAP[chargeType] || '个';
  },
  
  /**
   * 验证计费类型是否有效
   * @param {number} chargeType - 计费类型
   * @returns {boolean} 是否有效
   */
  isValidChargeType(chargeType) {
    return this.ALL_TYPES.includes(chargeType);
  },
  
  /**
   * 获取所有计费类型选项（用于前端下拉框）
   * @returns {Array} 计费类型选项数组
   */
  getAllOptions() {
    return this.ALL_TYPES.map(type => ({
      value: type,
      label: this.getChargeTypeText(type),
      unit: this.getChargeUnitText(type)
    }));
  }
};

module.exports = ChargeTypeEnum;
/**
 * 订单状态枚举
 * 定义订单在不同生命周期阶段的状态
 */

const OrderStatusEnum = {
  /**
   * 待付款 - 订单已创建但尚未支付
   */
  PENDING_PAYMENT: 0,
  
  /**
   * 待发货 - 订单已支付但尚未发货
   */
  PENDING_SHIPMENT: 1,
  
  /**
   * 已发货 - 订单已发货但尚未确认收货
   */
  SHIPPED: 2,
  
  /**
   * 交易成功 - 订单已确认收货并完成
   */
  COMPLETED: 3,
  
  /**
   * 已关闭 - 订单被用户或系统取消、退款完成
   */
  CANCELLED: 4,
  
  /**
   * 退款中 - 订单正在退款处理中
   */
  REFUNDING: 5,
  
  /**
   * 根据状态码获取状态名称
   * @param {number} statusCode - 状态码
   * @returns {string} - 状态名称
   */
  getStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.PENDING_PAYMENT:
        return '待付款';
      case this.PENDING_SHIPMENT:
        return '待发货';
      case this.SHIPPED:
        return '已发货';
      case this.COMPLETED:
        return '交易成功';
      case this.CANCELLED:
        return '已关闭';
      case this.REFUNDING:
        return '退款中';
      default:
        return '未知状态';
    }
  }
};

module.exports = OrderStatusEnum;

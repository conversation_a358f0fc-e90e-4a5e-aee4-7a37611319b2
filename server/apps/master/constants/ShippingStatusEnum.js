/**
 * 发货状态枚举
 * 定义订单发货状态的各种状态
 */

const ShippingStatusEnum = {
  /**
   * 未发货
   */
  UNSHIPPED: 0,
  
  /**
   * 已发货
   */
  SHIPPED: 1,
  
  /**
   * 部分发货
   */
  PARTIAL_SHIPPED: 2,
  
  /**
   * 已收货
   */
  RECEIVED: 3,
  
  /**
   * 已退货
   */
  RETURNED: 4,
  
  /**
   * 根据状态码获取状态名称
   * @param {number} statusCode - 状态码
   * @returns {string} - 状态名称
   */
  getStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.UNSHIPPED:
        return '未发货';
      case this.SHIPPED:
        return '已发货';
      case this.PARTIAL_SHIPPED:
        return '部分发货';
      case this.RECEIVED:
        return '已收货';
      case this.RETURNED:
        return '已退货';
      default:
        return '未知状态';
    }
  }
};

module.exports = ShippingStatusEnum; 
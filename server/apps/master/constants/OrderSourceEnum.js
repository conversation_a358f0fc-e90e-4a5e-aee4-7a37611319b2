/**
 * 订单来源枚举
 */
const OrderSourceEnum = {
  // 系统创建
  SYSTEM: 0,
  // 后台创建
  ADMIN: 1,
  // 商城下单
  MALL: 2,
  // 手动录单
  MANUAL: 3,
  
  // 获取订单来源文本
  getSourceText(sourceCode) {
    switch (parseInt(sourceCode)) {
      case this.SYSTEM:
        return 系统创建;
      case this.ADMIN:
        return 后台创建;
      case this.MALL:
        return 商城下单;
      case this.MANUAL:
        return 手动录单;
      default:
        return 未知来源;
    }
  }
};

module.exports = OrderSourceEnum;

/**
 * 支付记录状态枚举
 * 定义支付记录的各种状态
 */

const PaymentRecordStatusEnum = {
  /**
   * 待支付 - 支付记录已创建但尚未支付
   */
  PENDING: 0,
  
  /**
   * 支付中 - 正在进行支付处理
   */
  PROCESSING: 1,
  
  /**
   * 支付成功 - 支付已完成
   */
  SUCCESS: 2,
  
  /**
   * 支付失败 - 支付处理失败
   */
  FAILED: 3,
  
  /**
   * 已退款 - 支付已全额退款
   */
  REFUNDED: 4,
  
  /**
   * 部分退款 - 支付已部分退款
   */
  PARTIAL_REFUNDED: 5,
  
  /**
   * 根据状态码获取状态名称
   * @param {number} statusCode - 状态码
   * @returns {string} - 状态名称
   */
  getStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.PENDING:
        return '待支付';
      case this.PROCESSING:
        return '支付中';
      case this.SUCCESS:
        return '支付成功';
      case this.FAILED:
        return '支付失败';
      case this.REFUNDED:
        return '已退款';
      case this.PARTIAL_REFUNDED:
        return '部分退款';
      default:
        return '未知状态';
    }
  },
  
  /**
   * 检查是否为成功状态
   * @param {number} statusCode - 状态码
   * @returns {boolean} - 是否为成功状态
   */
  isSuccess(statusCode) {
    return parseInt(statusCode) === this.SUCCESS;
  },
  
  /**
   * 检查是否为失败状态
   * @param {number} statusCode - 状态码
   * @returns {boolean} - 是否为失败状态
   */
  isFailed(statusCode) {
    return parseInt(statusCode) === this.FAILED;
  },
  
  /**
   * 检查是否为退款状态
   * @param {number} statusCode - 状态码
   * @returns {boolean} - 是否为退款状态
   */
  isRefunded(statusCode) {
    const status = parseInt(statusCode);
    return status === this.REFUNDED || status === this.PARTIAL_REFUNDED;
  }
};

module.exports = PaymentRecordStatusEnum;

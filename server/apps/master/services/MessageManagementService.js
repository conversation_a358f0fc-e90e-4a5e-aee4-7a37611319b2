/**
 * 管理员消息管理服务
 * 处理管理员对用户消息的管理操作
 */
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class MessageManagementService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取所有用户消息列表（管理员视图）
   * @param {Object} options - 查询选项
   * @returns {Object} 消息列表和分页信息
   */
  async getAllUserMessages(options = {}) {
    const {
      page = 1,
      pageSize = 20,
      messageType,
      isRead,
      priority,
      userId,
      startDate,
      endDate,
      keyword
    } = options;

    const offset = (page - 1) * pageSize;

    try {
      // 构建查询条件
      let whereConditions = `m."deleted_at" IS NULL`;
      
      if (messageType) {
        whereConditions += ` AND "message_type" = '${messageType}'`;
      }
      
      if (isRead !== null && isRead !== undefined) {
        whereConditions += ` AND "is_read" = ${isRead}`;
      }
      
      if (priority !== null && priority !== undefined) {
        whereConditions += ` AND "priority" = ${priority}`;
      }
      
      if (userId) {
        whereConditions += ` AND "user_id" = ${BigInt(userId)}`;
      }
      
      if (startDate) {
        whereConditions += ` AND "created_at" >= ${BigInt(new Date(startDate).getTime())}`;
      }
      
      if (endDate) {
        whereConditions += ` AND "created_at" <= ${BigInt(new Date(endDate).getTime())}`;
      }
      
      if (keyword) {
        whereConditions += ` AND ("title" ILIKE '%${keyword}%' OR "content" ILIKE '%${keyword}%')`;
      }

      // 获取总数
      const total = await this.prisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count
        FROM "base"."mall_user_message" m
        WHERE ${whereConditions}
      `);

      // 获取消息列表（包含用户信息）
      const messages = await this.prisma.$queryRawUnsafe(`
        SELECT 
          m."id", m."user_id", m."title", m."content", m."message_type", 
          m."priority", m."is_read", m."action_url", m."action_text", 
          m."icon", m."created_at", m."updated_at", m."remark",
          u."username", u."nickname", u."phone", u."email"
        FROM "base"."mall_user_message" m
        LEFT JOIN "base"."mall_user" u ON m."user_id" = u."id"
        WHERE ${whereConditions}
        ORDER BY m."created_at" DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `);

      // 转换数据格式
      const formattedMessages = messages.map(msg => ({
        id: msg.id.toString(),
        userId: msg.user_id.toString(),
        title: msg.title,
        content: msg.content,
        messageType: msg.message_type,
        priority: msg.priority,
        isRead: msg.is_read,
        actionUrl: msg.action_url,
        actionText: msg.action_text,
        icon: msg.icon,
        createdAt: Number(msg.created_at),
        updatedAt: Number(msg.updated_at),
        remark: msg.remark,
        user: {
          username: msg.username,
          nickname: msg.nickname,
          phone: msg.phone,
          email: msg.email
        }
      }));

      return {
        messages: formattedMessages,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: Number(total[0].count),
          totalPages: Math.ceil(Number(total[0].count) / pageSize)
        }
      };
    } catch (error) {
      console.error('获取用户消息列表失败:', error);
      throw new Error('获取用户消息列表失败');
    }
  }

  /**
   * 获取消息统计信息
   * @returns {Object} 统计信息
   */
  async getMessageStatistics() {
    try {
      const stats = await this.prisma.$queryRawUnsafe(`
        SELECT 
          COUNT(*) as total_messages,
          COUNT(CASE WHEN "is_read" = false THEN 1 END) as unread_messages,
          COUNT(CASE WHEN "message_type" = 'system' THEN 1 END) as system_messages,
          COUNT(CASE WHEN "message_type" = 'order' THEN 1 END) as order_messages,
          COUNT(CASE WHEN "message_type" = 'promotion' THEN 1 END) as promotion_messages,
          COUNT(CASE WHEN "message_type" = 'service' THEN 1 END) as service_messages,
          COUNT(CASE WHEN "priority" = 3 THEN 1 END) as urgent_messages,
          COUNT(CASE WHEN "created_at" >= ${BigInt(Date.now() - 24 * 60 * 60 * 1000)} THEN 1 END) as today_messages
        FROM "base"."mall_user_message"
        WHERE "deleted_at" IS NULL
      `);

      return {
        totalMessages: Number(stats[0].total_messages),
        unreadMessages: Number(stats[0].unread_messages),
        systemMessages: Number(stats[0].system_messages),
        orderMessages: Number(stats[0].order_messages),
        promotionMessages: Number(stats[0].promotion_messages),
        serviceMessages: Number(stats[0].service_messages),
        urgentMessages: Number(stats[0].urgent_messages),
        todayMessages: Number(stats[0].today_messages)
      };
    } catch (error) {
      console.error('获取消息统计失败:', error);
      throw new Error('获取消息统计失败');
    }
  }

  /**
   * 发送消息给特定用户
   * @param {Object} messageData - 消息数据
   * @returns {Object} 创建的消息
   */
  async sendMessageToUser(messageData) {
    const {
      userId,
      title,
      content,
      messageType = 'system',
      priority = 1,
      actionUrl,
      actionText,
      icon,
      adminId
    } = messageData;

    try {
      const messageId = generateSnowflakeId();
      const now = BigInt(Date.now());

      await this.prisma.$queryRawUnsafe(`
        INSERT INTO "base"."mall_user_message" (
          "id", "user_id", "title", "content", "message_type", 
          "priority", "is_read", "action_url", "action_text", 
          "icon", "created_at", "updated_at", "created_by"
        ) VALUES (
          ${messageId}, ${BigInt(userId)}, '${title}', '${content}', '${messageType}',
          ${priority}, false, ${actionUrl ? `'${actionUrl}'` : 'NULL'}, ${actionText ? `'${actionText}'` : 'NULL'},
          ${icon ? `'${icon}'` : 'NULL'}, ${now}, ${now}, ${adminId ? BigInt(adminId) : 'NULL'}
        )
      `);

      return {
        id: messageId.toString(),
        userId: userId.toString(),
        title,
        content,
        messageType,
        priority,
        isRead: false,
        actionUrl,
        actionText,
        icon,
        createdAt: Number(now),
        updatedAt: Number(now)
      };
    } catch (error) {
      console.error('发送消息失败:', error);
      throw new Error('发送消息失败');
    }
  }

  /**
   * 批量发送消息给匹配条件的用户
   * @param {Object} messageData - 消息数据
   * @param {Object} userConditions - 用户筛选条件
   * @returns {Object} 发送结果
   */
  async sendBulkMessage(messageData, userConditions) {
    const {
      title,
      content,
      messageType = 'system',
      priority = 1,
      actionUrl,
      actionText,
      icon,
      adminId
    } = messageData;

    const {
      userLevel,
      registrationDateStart,
      registrationDateEnd,
      lastLoginStart,
      lastLoginEnd,
      status // 使用 status 字段而不是 isActive
    } = userConditions;

    try {
      // 构建用户筛选条件
      let userWhereConditions = `"deleted_at" IS NULL`;

      if (userLevel) {
        // 注意：mall_user表中可能没有level字段，这里先注释掉
        // userWhereConditions += ` AND "level" = ${userLevel}`;
      }

      if (registrationDateStart) {
        userWhereConditions += ` AND "created_at" >= ${BigInt(new Date(registrationDateStart).getTime())}`;
      }

      if (registrationDateEnd) {
        userWhereConditions += ` AND "created_at" <= ${BigInt(new Date(registrationDateEnd).getTime())}`;
      }

      if (lastLoginStart) {
        userWhereConditions += ` AND "last_login_time" >= ${BigInt(new Date(lastLoginStart).getTime())}`;
      }

      if (lastLoginEnd) {
        userWhereConditions += ` AND "last_login_time" <= ${BigInt(new Date(lastLoginEnd).getTime())}`;
      }

      if (status !== null && status !== undefined) {
        userWhereConditions += ` AND "status" = ${status}`;
      }

      // 获取符合条件的用户
      const users = await this.prisma.$queryRawUnsafe(`
        SELECT "id" FROM "base"."mall_user"
        WHERE ${userWhereConditions}
      `);

      if (users.length === 0) {
        return {
          success: true,
          sentCount: 0,
          message: '没有找到符合条件的用户'
        };
      }

      // 批量插入消息
      const now = BigInt(Date.now());
      const values = users.map(user => {
        const messageId = generateSnowflakeId();
        return `(${messageId}, ${user.id}, '${title}', '${content}', '${messageType}', ${priority}, false, ${actionUrl ? `'${actionUrl}'` : 'NULL'}, ${actionText ? `'${actionText}'` : 'NULL'}, ${icon ? `'${icon}'` : 'NULL'}, ${now}, ${now}, ${adminId ? BigInt(adminId) : 'NULL'})`;
      }).join(',');

      await this.prisma.$queryRawUnsafe(`
        INSERT INTO "base"."mall_user_message" (
          "id", "user_id", "title", "content", "message_type", 
          "priority", "is_read", "action_url", "action_text", 
          "icon", "created_at", "updated_at", "created_by"
        ) VALUES ${values}
      `);

      return {
        success: true,
        sentCount: users.length,
        message: `成功发送消息给 ${users.length} 个用户`
      };
    } catch (error) {
      console.error('批量发送消息失败:', error);
      throw new Error('批量发送消息失败');
    }
  }

  /**
   * 删除消息（管理员操作）
   * @param {string} messageId - 消息ID
   * @returns {boolean} 删除结果
   */
  async deleteMessage(messageId) {
    try {
      await this.prisma.$queryRawUnsafe(`
        UPDATE "base"."mall_user_message"
        SET "deleted_at" = ${BigInt(Date.now())}, "updated_at" = ${BigInt(Date.now())}
        WHERE "id" = ${BigInt(messageId)}
        AND "deleted_at" IS NULL
      `);

      return true;
    } catch (error) {
      console.error('删除消息失败:', error);
      throw new Error('删除消息失败');
    }
  }

  /**
   * 获取用户列表（用于发送消息时选择用户）
   * @param {Object} options - 查询选项
   * @returns {Object} 用户列表
   */
  async getUserList(options = {}) {
    const { page = 1, pageSize = 20, keyword } = options;
    const offset = (page - 1) * pageSize;

    try {
      let whereConditions = `"deleted_at" IS NULL`;
      
      if (keyword) {
        whereConditions += ` AND ("username" ILIKE '%${keyword}%' OR "nickname" ILIKE '%${keyword}%' OR "phone" ILIKE '%${keyword}%')`;
      }

      const total = await this.prisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count
        FROM "base"."mall_user"
        WHERE ${whereConditions}
      `);

      const users = await this.prisma.$queryRawUnsafe(`
        SELECT "id", "username", "nickname", "phone", "email", "status", "created_at", "last_login_time"
        FROM "base"."mall_user"
        WHERE ${whereConditions}
        ORDER BY "created_at" DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `);

      return {
        users: users.map(user => ({
          id: user.id.toString(),
          username: user.username,
          nickname: user.nickname,
          phone: user.phone,
          email: user.email,
          status: user.status,
          createdAt: Number(user.created_at),
          lastLoginAt: user.last_login_time ? Number(user.last_login_time) : null
        })),
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: Number(total[0].count),
          totalPages: Math.ceil(Number(total[0].count) / pageSize)
        }
      };
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw new Error('获取用户列表失败');
    }
  }

  /**
   * 根据用户ID获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Object|null} 用户信息
   */
  async getUserById(userId) {
    try {
      const user = await this.prisma.$queryRawUnsafe(`
        SELECT "id", "username", "nickname", "phone", "email", "status", "created_at", "last_login_time"
        FROM "base"."mall_user"
        WHERE "id" = $1::bigint AND "deleted_at" IS NULL
      `, userId);

      if (user && user.length > 0) {
        const userData = user[0];
        return {
          id: userData.id.toString(),
          username: userData.username,
          nickname: userData.nickname,
          phone: userData.phone,
          email: userData.email,
          status: userData.status,
          avatar: null, // 如果有头像字段，可以在这里添加
          createdAt: Number(userData.created_at),
          lastLoginAt: userData.last_login_time ? Number(userData.last_login_time) : null
        };
      }

      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw new Error('获取用户信息失败');
    }
  }
}

module.exports = MessageManagementService;

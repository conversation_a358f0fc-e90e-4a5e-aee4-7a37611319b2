/**
 * 支付记录服务
 * 处理支付记录相关的业务逻辑
 */
const PaymentRecordModel = require('../models/PaymentRecordModel');
const OrderModel = require('../models/OrderModel/OrderModel');
const PaymentRecordStatusEnum = require('../constants/PaymentRecordStatusEnum');
const PaymentMethodEnum = require('../constants/PaymentMethodEnum');
const OrderStatusEnum = require('../constants/OrderStatusEnum');
const PaymentStatusEnum = require('../constants/PaymentStatusEnum');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class PaymentRecordService {
  /**
   * 创建支付记录
   * @param {Object} data - 支付记录数据
   * @returns {Promise<Object>} - 创建结果
   */
  static async createPaymentRecord(data) {
    try {
      const {
        orderId,
        userId,
        paymentMethodId,
        paymentAmount,
        description,
        clientIp,
        userAgent
      } = data;

      // 验证订单是否存在
      const orderModel = new OrderModel();
      const order = await orderModel.getOrderById(orderId);
      if (!order) {
        throw new Error('订单不存在');
      }

      // 验证订单状态
      console.log('订单状态检查:', {
        orderId: orderId,
        orderStatus: order.orderStatus,
        orderStatusType: typeof order.orderStatus,
        pendingPaymentEnum: OrderStatusEnum.PENDING_PAYMENT,
        pendingPaymentType: typeof OrderStatusEnum.PENDING_PAYMENT,
        isEqual: order.orderStatus === OrderStatusEnum.PENDING_PAYMENT,
        fullOrder: order
      });

      if (order.orderStatus !== OrderStatusEnum.PENDING_PAYMENT) {
        throw new Error('订单状态不允许支付');
      }

      // 生成支付流水号
      const paymentSn = this.generatePaymentSn();

      // 创建支付记录
      const paymentRecord = await PaymentRecordModel.create({
        payment_sn: paymentSn,
        order_id: BigInt(orderId),
        user_id: BigInt(userId),
        payment_method_id: paymentMethodId,
        payment_method_name: PaymentMethodEnum.getName(paymentMethodId),
        payment_amount: paymentAmount,
        payment_status: PaymentRecordStatusEnum.PENDING,
        description: description || '订单支付',
        client_ip: clientIp,
        user_agent: userAgent,
        expire_time: BigInt(Date.now() + 30 * 60 * 1000), // 30分钟后过期
        created_by: BigInt(userId)
      });

      return {
        success: true,
        data: paymentRecord,
        message: '支付记录创建成功'
      };
    } catch (error) {
      console.error('创建支付记录失败:', error);
      return {
        success: false,
        message: error.message || '创建支付记录失败'
      };
    }
  }

  /**
   * 更新支付记录状态
   * @param {string} paymentSn - 支付流水号
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} - 更新结果
   */
  static async updatePaymentStatus(paymentSn, updateData) {
    try {
      const {
        status,
        thirdPartyTradeNo,
        paymentResult,
        notifyTime
      } = updateData;

      // 获取支付记录
      const paymentRecord = await PaymentRecordModel.findByPaymentSn(paymentSn);
      if (!paymentRecord) {
        throw new Error('支付记录不存在');
      }

      // 更新支付记录
      const updatedRecord = await PaymentRecordModel.updateStatus(
        paymentRecord.id,
        status,
        {
          third_party_trade_no: thirdPartyTradeNo,
          payment_result: paymentResult ? JSON.stringify(paymentResult) : null,
          notify_time: notifyTime ? BigInt(notifyTime) : BigInt(Date.now())
        }
      );

      // 如果支付成功，更新订单状态
      if (status === PaymentRecordStatusEnum.SUCCESS) {
        await this.handlePaymentSuccess(paymentRecord);
      }

      return {
        success: true,
        data: updatedRecord,
        message: '支付状态更新成功'
      };
    } catch (error) {
      console.error('更新支付状态失败:', error);
      return {
        success: false,
        message: error.message || '更新支付状态失败'
      };
    }
  }

  /**
   * 处理支付成功
   * @param {Object} paymentRecord - 支付记录
   * @returns {Promise<void>}
   */
  static async handlePaymentSuccess(paymentRecord) {
    try {
      // 使用 Prisma 直接更新订单状态
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      await prisma.orders.update({
        where: {
          id: BigInt(paymentRecord.order_id)
        },
        data: {
          order_status: OrderStatusEnum.PENDING_SHIPMENT, // 1: 待发货
          payment_status: 2, // 2: 已支付 (根据数据库表结构)
          payment_method_id: paymentRecord.payment_method_id,
          payment_sn: paymentRecord.payment_sn,
          paid_amount: paymentRecord.payment_amount,
          paid_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });

      console.log(`订单 ${paymentRecord.order_id} 支付成功，已更新订单状态`);
    } catch (error) {
      console.error('处理支付成功失败:', error);
      throw error;
    }
  }

  /**
   * 根据支付流水号获取支付记录
   * @param {string} paymentSn - 支付流水号
   * @returns {Promise<Object>} - 查询结果
   */
  static async getPaymentRecord(paymentSn) {
    try {
      const paymentRecord = await PaymentRecordModel.findByPaymentSn(paymentSn);
      
      if (!paymentRecord) {
        return {
          success: false,
          message: '支付记录不存在'
        };
      }

      return {
        success: true,
        data: paymentRecord,
        message: '获取支付记录成功'
      };
    } catch (error) {
      console.error('获取支付记录失败:', error);
      return {
        success: false,
        message: error.message || '获取支付记录失败'
      };
    }
  }

  /**
   * 根据订单ID获取支付记录列表
   * @param {string|number} orderId - 订单ID
   * @returns {Promise<Object>} - 查询结果
   */
  static async getPaymentRecordsByOrderId(orderId) {
    try {
      const paymentRecords = await PaymentRecordModel.findByOrderId(orderId);
      
      return {
        success: true,
        data: paymentRecords,
        message: '获取支付记录列表成功'
      };
    } catch (error) {
      console.error('获取支付记录列表失败:', error);
      return {
        success: false,
        message: error.message || '获取支付记录列表失败'
      };
    }
  }

  /**
   * 处理订单退款
   * @param {string} orderId - 订单ID
   * @param {string} refundReason - 退款原因
   * @param {Object} operator - 操作人信息
   * @param {number} refundAmount - 退款金额（可选，不传则全额退款）
   * @param {string} refundType - 退款类型（full/partial）
   * @returns {Promise<Object>} - 退款结果
   */
  static async processOrderRefund(orderId, refundReason, operator, refundAmount = null, refundType = 'full') {
    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      const WechatPayService = require('./WechatPayService');

      // 使用事务处理退款
      return await prisma.$transaction(async (tx) => {
        // 1. 获取订单信息
        const order = await tx.orders.findFirst({
          where: {
            id: BigInt(orderId),
            deleted_at: null
          }
        });

        if (!order) {
          throw new Error('订单不存在');
        }

        // 2. 验证订单状态
        // 允许退款的订单状态：待发货(1)、已关闭(4)
        const allowedOrderStatuses = [1, 4]; // 待发货、已关闭
        if (!allowedOrderStatuses.includes(order.order_status)) {
          throw new Error(`当前订单状态不允许退款，只有待发货和已关闭状态的订单可以退款`);
        }

        // 只有已支付的订单可以退款
        if (order.payment_status !== 2) {
          throw new Error('订单未支付，无法退款');
        }

        // 3. 获取支付记录
        const paymentRecord = await tx.payment_records.findFirst({
          where: {
            order_id: BigInt(orderId),
            payment_status: PaymentRecordStatusEnum.SUCCESS,
            deleted_at: null
          },
          orderBy: {
            created_at: 'desc'
          }
        });

        if (!paymentRecord) {
          throw new Error('未找到成功的支付记录');
        }

        // 4. 计算退款金额
        const orderTotalAmount = parseFloat(order.total_amount);
        let finalRefundAmount = orderTotalAmount; // 默认全额退款

        if (refundType === 'partial' && refundAmount) {
          finalRefundAmount = parseFloat(refundAmount);
          // 验证退款金额不能超过订单总金额
          if (finalRefundAmount > orderTotalAmount) {
            throw new Error(`退款金额不能超过订单总金额 ¥${orderTotalAmount}`);
          }
          if (finalRefundAmount <= 0) {
            throw new Error('退款金额必须大于0');
          }
        }

        // 5. 生成退款单号
        const refundSn = `RF${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`;

        // 6. 调用微信退款接口
        const wechatPayService = new WechatPayService();
        const refundResult = await wechatPayService.createRefund({
          outTradeNo: paymentRecord.payment_sn,
          outRefundNo: refundSn,
          refundAmount: finalRefundAmount,
          totalAmount: orderTotalAmount,
          refundReason: refundReason || '订单退款'
        });

        if (!refundResult.success) {
          throw new Error(`微信退款失败: ${refundResult.error}`);
        }

        // 7. 更新支付记录状态
        const isFullRefund = finalRefundAmount >= orderTotalAmount;
        await tx.payment_records.update({
          where: {
            id: paymentRecord.id
          },
          data: {
            payment_status: isFullRefund ? PaymentRecordStatusEnum.REFUNDED : PaymentRecordStatusEnum.PARTIAL_REFUNDED,
            refund_amount: finalRefundAmount,
            refund_reason: refundReason,
            refund_time: BigInt(Date.now()),
            updated_at: BigInt(Date.now())
          }
        });

        // 8. 更新订单状态
        const newOrderStatus = isFullRefund ? 5 : order.order_status; // 全额退款改为已退款，部分退款保持原状态
        const newPaymentStatus = isFullRefund ? 4 : 3; // 全额退款改为已退款，部分退款改为退款中

        await tx.orders.update({
          where: {
            id: BigInt(orderId)
          },
          data: {
            order_status: newOrderStatus,
            payment_status: newPaymentStatus,
            updated_at: BigInt(Date.now())
          }
        });

        return {
          success: true,
          data: {
            orderId: orderId,
            refundSn: refundSn,
            refundAmount: finalRefundAmount,
            refundType: refundType,
            isFullRefund: isFullRefund,
            wechatRefundId: refundResult.data.refundId
          },
          message: `${isFullRefund ? '全额' : '部分'}退款申请成功`
        };
      });
    } catch (error) {
      console.error('处理订单退款失败:', error);
      return {
        success: false,
        message: error.message || '退款处理失败'
      };
    }
  }

  /**
   * 生成支付流水号
   * @returns {string} - 支付流水号
   */
  static generatePaymentSn() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `PAY${timestamp}${random}`;
  }

  /**
   * 检查支付记录是否过期
   * @param {Object} paymentRecord - 支付记录
   * @returns {boolean} - 是否过期
   */
  static isPaymentExpired(paymentRecord) {
    if (!paymentRecord.expire_time) {
      return false;
    }
    
    return Date.now() > Number(paymentRecord.expire_time);
  }

  /**
   * 获取支付记录统计信息
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 统计结果
   */
  static async getPaymentStatistics(options = {}) {
    try {
      const {
        userId,
        startTime,
        endTime
      } = options;

      // 这里可以添加更复杂的统计逻辑
      const result = await PaymentRecordModel.findMany({
        userId,
        startTime,
        endTime,
        page: 1,
        pageSize: 1000 // 获取足够多的记录用于统计
      });

      const statistics = {
        totalCount: result.total,
        successCount: 0,
        failedCount: 0,
        pendingCount: 0,
        totalAmount: 0,
        successAmount: 0
      };

      result.records.forEach(record => {
        const amount = Number(record.payment_amount);
        
        switch (record.payment_status) {
          case PaymentRecordStatusEnum.SUCCESS:
            statistics.successCount++;
            statistics.successAmount += amount;
            break;
          case PaymentRecordStatusEnum.FAILED:
            statistics.failedCount++;
            break;
          case PaymentRecordStatusEnum.PENDING:
          case PaymentRecordStatusEnum.PROCESSING:
            statistics.pendingCount++;
            break;
        }
        
        statistics.totalAmount += amount;
      });

      return {
        success: true,
        data: statistics,
        message: '获取支付统计成功'
      };
    } catch (error) {
      console.error('获取支付统计失败:', error);
      return {
        success: false,
        message: error.message || '获取支付统计失败'
      };
    }
  }
}

module.exports = PaymentRecordService;

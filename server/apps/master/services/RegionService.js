/**
 * 区域服务
 * 处理区域相关的业务逻辑
 * @typedef {import('../models/RegionModel')} RegionModel
 * @typedef {import('../dto/RegionDto')} RegionDto
 */
const regionModel = require('../models/RegionModel');
const RegionDto = require('../dto/RegionDto');

class RegionService {
  /**
   * 获取区域树结构
   * @param {number} parentId - 父级ID，默认为0表示获取顶级
   * @param {Boolean} excludeStreet - 是否排除街道级数据，默认为 false
   * @param {Boolean} onlyProvinceCity - 是否只获取省市级数据（过滤区/县级），默认为 false
   * @returns {Promise<Array>} - 树形结构的区域数据
   */
  async getRegionTree(parentId = 0, excludeStreet = false, onlyProvinceCity = false) {
    try {
      const regionTree = await regionModel.getRegionTree(parentId, excludeStreet, onlyProvinceCity);
      return regionTree;
    } catch (error) {
      console.error('获取区域树结构失败:', error);
      throw new Error(`获取区域树结构失败: ${error.message}`);
    }
  }
  
  /**
   * 根据区域代码获取区域名称
   * @param {string|Array<string>} regionCodes - 区域代码或区域代码数组，多个用逗号分隔的代码将被拆分
   * @returns {Promise<string>} - 区域名称，多个以逗号分隔
   */
  async getRegionNamesByCodes(regionCodes) {
    try {
      // 特殊处理：null、undefined或空字符串
      if (regionCodes === null || regionCodes === undefined || regionCodes === '') {
        return '';
      }
      
      // 特殊处理：regionCodes为0或'0'
      if (regionCodes === 0 || regionCodes === '0') {
        return '全国';
      }
      
      // 如果是字符串，按逗号分隔
      const codeArray = Array.isArray(regionCodes) 
        ? regionCodes 
        : String(regionCodes).split(',').filter(code => code);
      
      if (codeArray.length === 0) {
        return '';
      }
      
      // 调用模型获取区域名称
      const regionNames = await regionModel.getRegionNamesByCodes(codeArray);
      return regionNames;
    } catch (error) {
      console.error('根据区域代码获取区域名称失败:', error);
      // 发生错误时返回空字符串，不影响主流程
      return '';
    }
  }
}

module.exports = new RegionService();

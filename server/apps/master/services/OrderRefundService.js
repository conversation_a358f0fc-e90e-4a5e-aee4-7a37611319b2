/**
 * 订单退款服务
 * 处理订单退款相关的业务逻辑
 */
const OrderModel = require('../models/OrderModel/OrderModel');
const OrderLogModel = require('../models/OrderModel/OrderLogModel');
const PaymentStatusEnum = require('../constants/PaymentStatusEnum');
const OrderStatusEnum = require('../constants/OrderStatusEnum');

class OrderRefundService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 确认订单退款
   * @param {string|BigInt} orderId - 订单ID
   * @param {string} refundReason - 退款原因
   * @param {Object} currentUser - 当前操作用户
   * @returns {Promise<Object>} - 退款结果
   */
  async confirmOrderRefund(orderId, refundReason, currentUser) {
    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 获取并验证订单信息
        const orderModel = new OrderModel(tx);
        const orderLogModel = new OrderLogModel(tx);
        
        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          return {
            success: false,
            message: '订单不存在',
            code: 404
          };
        }

        // 调试日志：输出订单信息
        console.log('=== 订单退款调试信息 ===');
        console.log('订单ID:', orderId);
        console.log('订单号:', order.id); // 使用订单ID作为订单编号
        console.log('订单原始数据:', JSON.stringify(order, (key, value) => 
          typeof value === 'bigint' ? value.toString() : value, 2));

        // 2. 验证订单是否为内置渠道
        let isValidChannel = false;
        let channelInfo = '无渠道信息';

        // 检查order.channel对象
        if (order.channel) {
          console.log('订单渠道信息:', JSON.stringify(order.channel, (key, value) => 
            typeof value === 'bigint' ? value.toString() : value, 2));
          
          // 注意：OrderModel已将字段名转换为驼峰命名，所以使用isBuiltIn而不是is_built_in
          const isBuiltInValue = order.channel.isBuiltIn; // 驼峰命名
          channelInfo = `渠道ID: ${order.channel.id}, 渠道名称: ${order.channel.name}, isBuiltIn: ${isBuiltInValue} (类型: ${typeof isBuiltInValue})`;
          
          // 验证是否为内置渠道 - isBuiltIn应为1(int2类型)
          if (parseInt(isBuiltInValue) === 1) {
            isValidChannel = true;
          }
          console.log('渠道isBuiltIn值:', isBuiltInValue, '转换后:', parseInt(isBuiltInValue), '验证结果:', isValidChannel);
        } else {
          // 如果没有channel关联，尝试通过channel_id查询
          console.log('订单没有直接关联channel，尝试通过channel_id查询');
          
          // 注意：这里也要使用驼峰命名
          const channelId = order.channelId; // 驼峰命名
          if (channelId) {
            console.log('订单channelId:', channelId);
            
            try {
              const channel = await tx.channel.findFirst({
                where: {
                  id: BigInt(channelId),
                  deleted_at: null
                }
              });
              
              if (channel) {
                console.log('通过channelId查询到渠道:', JSON.stringify(channel, (key, value) => 
                  typeof value === 'bigint' ? value.toString() : value, 2));
                
                // 这里直接查询数据库，字段名是下划线形式
                const isBuiltInValue = channel.is_built_in;
                channelInfo = `渠道ID: ${channel.id}, 渠道名称: ${channel.name}, is_built_in: ${isBuiltInValue} (类型: ${typeof isBuiltInValue})`;
                
                // 验证是否为内置渠道 - is_built_in应为1(int2类型)
                if (parseInt(isBuiltInValue) === 1) {
                  isValidChannel = true;
                }
                console.log('渠道is_built_in值:', isBuiltInValue, '转换后:', parseInt(isBuiltInValue), '验证结果:', isValidChannel);
              } else {
                channelInfo = `通过channelId: ${channelId} 未找到对应渠道`;
              }
            } catch (error) {
              console.error('查询渠道信息失败:', error);
              channelInfo = `查询渠道失败: ${error.message}`;
            }
          } else {
            console.log('订单没有channelId字段');
          }
        }

        console.log('渠道验证结果:', channelInfo);
        console.log('是否为有效内置渠道:', isValidChannel);
        console.log('========================');

        if (!isValidChannel) {
          return {
            success: false,
            message: `只允许内置渠道的订单进行退款确认。当前${channelInfo}`,
            code: 400
          };
        }

        // 3. 验证订单支付状态是否允许退款
        // 注意：OrderModel已将字段名转换为驼峰命名，所以使用paymentStatus而不是payment_status
        const paymentStatus = order.paymentStatus; // 驼峰命名
        console.log('订单支付状态:', paymentStatus, '(类型:', typeof paymentStatus, ')');
        console.log('PaymentStatusEnum.PAID:', PaymentStatusEnum.PAID);
        console.log('PaymentStatusEnum.REFUNDING:', PaymentStatusEnum.REFUNDING);
        
        // 允许已支付或退款中的订单进行退款确认
        if (paymentStatus !== PaymentStatusEnum.PAID && paymentStatus !== PaymentStatusEnum.REFUNDING) {
          const statusName = PaymentStatusEnum.getStatusName(paymentStatus);
          return {
            success: false,
            message: `订单支付状态必须为已支付或退款中才能确认退款。当前支付状态: ${paymentStatus} (${statusName})`,
            code: 400
          };
        }

        // 4. 验证订单是否有支付流水
        // 注意：OrderModel已将字段名转换为驼峰命名，所以使用paymentSn而不是payment_sn
        const paymentSn = order.paymentSn; // 驼峰命名
        console.log('订单支付流水号:', paymentSn);
        
        if (!paymentSn) {
          return {
            success: false,
            message: '订单没有支付流水，无法进行退款',
            code: 400
          };
        }

        // 5. 使用订单的支付金额作为退款金额
        // 注意：OrderModel已将字段名转换为驼峰命名，所以使用paidAmount而不是paid_amount
        const refundAmount = parseFloat(order.paidAmount); // 驼峰命名
        console.log('订单支付金额:', order.paidAmount, '转换后退款金额:', refundAmount);
        
        // 验证支付金额是否有效
        if (!refundAmount || refundAmount <= 0) {
          return {
            success: false,
            message: '订单支付金额无效，无法进行退款',
            code: 400
          };
        }

        // 6. 更新订单支付状态和订单状态为已关闭
        console.log('开始更新订单支付状态和订单状态...');
        const updatedOrder = await tx.orders.update({
          where: {
            id: BigInt(orderId)
          },
          data: {
            payment_status: PaymentStatusEnum.REFUNDED,
            order_status: OrderStatusEnum.CANCELLED,  // 退款成功后将订单状态设置为关闭
            updated_at: BigInt(Date.now())
          },
          include: {
            channel: true
          }
        });
        console.log('订单状态更新成功:', {
          orderId: updatedOrder.id.toString(),
          oldPaymentStatus: paymentStatus,
          newPaymentStatus: updatedOrder.payment_status,
          oldOrderStatus: order.orderStatus,
          newOrderStatus: updatedOrder.order_status,
          PaymentStatusEnum_REFUNDED: PaymentStatusEnum.REFUNDED,
          OrderStatusEnum_CANCELLED: OrderStatusEnum.CANCELLED
        });

        // 7. 记录订单退款日志
        console.log('开始记录订单退款日志...');
        try {
          await orderLogModel.createLog({
            orderId: BigInt(orderId),
            logType: 'refund_confirm',
            logContent: `订单退款确认成功，退款金额：￥${refundAmount}，退款原因：${refundReason || '无'}，订单状态已更新为已关闭`,
            operatorId: currentUser.id ? BigInt(currentUser.id) : null,
            operatorName: currentUser.username || '系统',
            operatorRole: currentUser.role || 'admin',
            operationTime: BigInt(Date.now()),
            operationIp: currentUser.ip || '127.0.0.1',
            operationPlatform: '中控台'
          }, tx);
          console.log('订单退款日志记录成功');
        } catch (logError) {
          console.error('记录订单退款日志失败:', logError);
          // 日志记录失败不应该影响整个退款流程，继续执行
        }

        // 8. 调用微信SDK进行实际退款操作（伪代码）
        const wechatRefundResult = await this.callWechatRefundSDK({
          orderId: order.id.toString(),
          orderSn: order.orderSn,
          paymentSn: order.paymentSn,
          refundAmount: refundAmount,
          totalAmount: parseFloat(order.totalAmount),
          refundReason: refundReason || '订单退款'
        });

        // 如果微信退款失败，回滚订单状态
        if (!wechatRefundResult.success) {
          throw new Error(`微信退款失败: ${wechatRefundResult.message}`);
        }

        return {
          success: true,
          data: {
            orderId: updatedOrder.id.toString(),
            orderSn: updatedOrder.id.toString(), // 使用订单ID作为订单编号
            refundAmount: refundAmount,
            refundTime: Date.now(),
            wechatRefundSn: wechatRefundResult.refundSn
          },
          message: '订单退款确认成功，订单已关闭'
        };

      } catch (error) {
        console.error('确认订单退款事务失败:', error);
        throw error;
      }
    });
  }

  /**
   * 调用微信SDK进行退款（伪代码）
   * @param {Object} refundData - 退款数据
   * @returns {Promise<Object>} - 退款结果
   */
  async callWechatRefundSDK(refundData) {
    try {
      // ========== 微信退款SDK调用伪代码开始 ==========
      
      console.log('准备调用微信退款SDK，退款参数：', refundData);
      
      // 1. 初始化微信支付SDK
      // const WeChatPay = require('wechatpay-node-v3');
      // const pay = new WeChatPay({
      //   appid: process.env.WECHAT_APPID,
      //   mchid: process.env.WECHAT_MCHID,
      //   publicKey: process.env.WECHAT_PUBLIC_KEY,
      //   privateKey: process.env.WECHAT_PRIVATE_KEY
      // });

      // 2. 构建退款请求参数
      // const refundParams = {
      //   transaction_id: refundData.paymentSn,      // 微信支付交易号
      //   out_refund_no: `RF${refundData.orderId}${Date.now()}`,  // 商户退款单号
      //   reason: refundData.refundReason,           // 退款原因
      //   amount: {
      //     refund: Math.round(refundData.refundAmount * 100),    // 退款金额（分）
      //     total: Math.round(refundData.totalAmount * 100),      // 原订单金额（分）
      //     currency: 'CNY'                          // 币种
      //   },
      //   notify_url: `${process.env.API_BASE_URL}/api/master/order-refund/wechat-callback`
      // };

      // 3. 发起退款请求
      // const refundResult = await pay.refunds({
      //   ...refundParams
      // });

      // 4. 处理退款结果
      // if (refundResult.status === 'SUCCESS') {
      //   return {
      //     success: true,
      //     refundSn: refundResult.refund_id,
      //     message: '微信退款成功'
      //   };
      // } else {
      //   return {
      //     success: false,
      //     message: `微信退款失败: ${refundResult.status}`
      //   };
      // }

      // ========== 微信退款SDK调用伪代码结束 ==========
      
      // 临时模拟成功响应（实际使用时删除此部分）
      await new Promise(resolve => setTimeout(resolve, 100)); // 模拟网络延迟
      
      return {
        success: true,
        refundSn: `WX_REFUND_${Date.now()}`,
        message: '微信退款成功（模拟）'
      };

    } catch (error) {
      console.error('调用微信退款SDK失败:', error);
      return {
        success: false,
        message: error.message || '微信退款接口调用失败'
      };
    }
  }
}

module.exports = OrderRefundService; 
/**
 * 商品SPU服务
 */
const GoodsSpuModel = require('../models/GoodsSpuModel');
const GoodsCategoryModel = require('../models/GoodsCategoryModel');
const GoodsBrandModel = require('../models/GoodsBrandModel');
const GoodsFreightTemplateModel = require('../models/GoodsFreightTemplateModel');
const GoodsTagModel = require('../models/GoodsTagModel');
const GoodsServiceModel = require('../models/GoodsServiceModel');
const OrderItemModel = require('../models/OrderModel/OrderItemModel');
const GoodsDeleteLogModel = require('../models/GoodsDeleteLogModel');
const { prisma } = require('../../../core/database/prisma');
const { Prisma } = require('@prisma/client');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 安全地序列化包含BigInt的对象
 * @param {Object} obj 要序列化的对象
 * @returns {string} 序列化后的JSON字符串
 */
function safeStringify(obj) {
  return JSON.stringify(obj, (key, value) => 
    typeof value === 'bigint' ? value.toString() : value
  );
}

class GoodsSpuService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prismaInstance) {
    this.prisma = prismaInstance || prisma;
    this.goodsSpuModel = new GoodsSpuModel(this.prisma);
    this.goodsCategoryModel = new GoodsCategoryModel(this.prisma);
    this.goodsBrandModel = new GoodsBrandModel(this.prisma);
    this.goodsFreightTemplateModel = new GoodsFreightTemplateModel(this.prisma); // 直接实例化
    this.goodsTagModel = new GoodsTagModel(this.prisma);
    this.goodsServiceModel = new GoodsServiceModel(this.prisma);
    this.orderItemModel = new OrderItemModel(this.prisma);
    this.goodsDeleteLogModel = new GoodsDeleteLogModel(this.prisma);
  }

  /**
   * 添加商品
   * @param {Object} productData - 商品数据
   * @param {BigInt|null} userId - 当前用户ID
   * @returns {Object} - 添加结果
   */
  async addProduct(productData, userId) {
    // 解构请求数据
    const { 
      basicInfo, 
      associations, 
      logisticsForm, 
      deliveryForm, 
      attributeValues, 
      specifications, 
      detailForm, 
      otherForm 
    } = productData;

    // 开始事务
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 验证关联数据是否存在
        await this.validateRelatedEntities(
          basicInfo.categoryId,
          basicInfo.brandId,
          logisticsForm.isFreeShipping === '2' || logisticsForm.isFreeShipping === 2 ? logisticsForm.freightTemplateId : null,
          associations.tagIds,
          associations.serviceIds,
          attributeValues?.map(attr => attr.attributeItemId) || []
        );

        // 2. 生成唯一slug
        const slug = await this.goodsSpuModel.generate_unique_slug(basicInfo.name);

        // 3. 判断商品是否为虚拟商品（默认为实物商品）
        const isVirtual = otherForm.isVirtual ? parseInt(otherForm.isVirtual) : 0; // 0表示实物商品，1表示虚拟商品
        const isShippingRequired = otherForm.isShippingRequired ? parseInt(otherForm.isShippingRequired) : (isVirtual === 0 ? 1 : 0); // 1表示需要配送，0表示不需要配送
        
        // 4. 生成雪花ID
        const spuId = generateSnowflakeId();
        
        // 5. 准备SPU数据
        const spuData = {
          id: spuId,
          name: basicInfo.name,
          subtitle: basicInfo.subtitle || null,
          slug,
          description: detailForm.content,
          goods_brand_id: BigInt(basicInfo.brandId),
          goods_freight_template_id: logisticsForm.isFreeShipping === '2' || logisticsForm.isFreeShipping === 2 ? BigInt(logisticsForm.freightTemplateId) : null,
          is_free_shipping: parseInt(logisticsForm.isFreeShipping),
          meta_title: otherForm.seoTitle || null,
          meta_keywords: otherForm.keywords || null,
          meta_description: otherForm.seoDescription || null,
          sort_order: otherForm.sort || 100,
          status: otherForm.status,
          total_sales: otherForm.totalSales || 0,
          total_stock: 0, // 将在处理SKU时更新
          is_virtual: isVirtual, // 0表示实物商品，1表示虚拟商品
          is_shipping_required: isShippingRequired, // 1表示需要配送，0表示不需要配送
          // 保存配送信息
          delivery_area: deliveryForm.deliveryArea && deliveryForm.deliveryArea.length > 0 ? deliveryForm.deliveryArea.join(',') : null,
          delivery_time: deliveryForm.deliveryTime ? BigInt(deliveryForm.deliveryTime) : null,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now()),
          published_at: otherForm.status === 1 ? BigInt(Date.now()) : null,
          // 添加创建人和更新人信息
          created_by: userId,
          updated_by: userId
        };

        // 6. 创建SPU
        const newSpu = await this.goodsSpuModel.add_spu(spuData, tx);

        // 7. 添加分类关联
        await this.goodsSpuModel.add_category_association(spuId, basicInfo.categoryId, tx);

        // 8. 添加SPU图片
        // 将字符串数组转换为对象数组
        const spuImages = basicInfo.spuImages.map((url, index) => ({
          url,
          sortOrder: index,
          isDefault: index === 0 // 第一张图片为默认图片
        }));
        await this.goodsSpuModel.add_spu_images(spuId, spuImages, tx);

        // 9. 添加视频（如果有）
        if (basicInfo.video) {
          let videoData = basicInfo.video;
          // 如果视频是字符串，转换为对象
          if (typeof videoData === 'string') {
            videoData = {
              url: videoData,
              coverUrl: '', // 前端可能未提供封面
              sortOrder: 0
            };
          }
          await this.goodsSpuModel.add_video(spuId, videoData, tx);
        }

        // 10. 添加标签关联（如果有）
        if (associations.tagIds && associations.tagIds.length > 0) {
          await this.goodsSpuModel.add_tag_associations(spuId, associations.tagIds, tx);
        }

        // 11. 添加服务关联（如果有）
        if (associations.serviceIds && associations.serviceIds.length > 0) {
          await this.goodsSpuModel.add_service_associations(spuId, associations.serviceIds, tx);
        }

        // 12. 添加属性值（如果有）
        if (attributeValues && attributeValues.length > 0) {
          await this.goodsSpuModel.add_attribute_values(spuId, attributeValues, tx);
        }

        // 13. 处理SKU
        // 将前端的规格数据转换为后端需要的格式
        const skuInfo = this.convertSpecificationsToSkuInfo(specifications);
        // 检查SKU编码是否重复
        await this.validateSkuCodesForAdd(tx, skuInfo);

        // 14. 处理SKU
        await this.processSkus(tx, spuId, skuInfo);

        // 返回创建的SPU ID
        return { 
          success: true, 
          data: { 
            spuId,
            slug
          }
        };
      } catch (error) {
        console.error('添加商品失败', error);
        throw error;
      }
    }, {
      // 设置较长的事务超时时间，确保有足够时间完成所有操作
      timeout: 30000
    });
  }

  /**
   * 验证关联实体是否存在
   * @param {number|string} categoryId - 分类ID
   * @param {number|string} brandId - 品牌ID
   * @param {number|string} freightTemplateId - 运费模板ID
   * @param {Array<number|string>} tagIds - 标签ID数组
   * @param {Array<number|string>} serviceIds - 服务ID数组
   * @param {Array<number|string>} attributeItemIds - 属性项ID数组
   */
  async validateRelatedEntities(categoryId, brandId, freightTemplateId, tagIds, serviceIds, attributeItemIds) {
    try {
      // 统一将所有ID转换为字符串，避免精度丢失
      const categoryIdStr = String(categoryId);
      const brandIdStr = String(brandId);
      const freightTemplateIdStr = freightTemplateId ? String(freightTemplateId) : null;
      
      // 输出调试信息
      console.log("验证关联记录，参数类型：", {
        categoryId: typeof categoryId,
        brandId: typeof brandId,
        freightTemplateId: typeof freightTemplateId
      });
      console.log("验证关联记录，参数值：", {
        categoryId: categoryIdStr,
        brandId: brandIdStr,
        freightTemplateId: freightTemplateIdStr
      });

      // 检查分类是否存在 - 使用模型方法
      const category = await this.goodsCategoryModel.getGoodsCategoryById(categoryIdStr);
      if (!category) {
        throw new Error(`关联记录不存在: 商品分类 [ID: ${categoryIdStr}]`);
      }

      // 检查品牌是否存在
      const brand = await this.goodsBrandModel.getGoodsBrandById(brandIdStr);
      if (!brand) {
        throw new Error(`关联记录不存在: 商品品牌 [ID: ${brandIdStr}]`);
      }

      // 检查运费模板是否存在
      if (freightTemplateIdStr) {
        const template = await this.goodsFreightTemplateModel.getGoodsFreightTemplateById(freightTemplateIdStr);
        if (!template) {
          throw new Error(`关联记录不存在: 运费模板 [ID: ${freightTemplateIdStr}]`);
        }
      }

      if (tagIds && tagIds.length > 0) {
        for (const tagId of tagIds) {
          const tagIdStr = String(tagId);
          const tag = await this.goodsTagModel.getTagById(tagIdStr);
          if (!tag) {
            throw new Error(`关联记录不存在: 商品标签 [ID: ${tagIdStr}]`);
          }
        }
      }

      if (serviceIds && serviceIds.length > 0) {
        for (const serviceId of serviceIds) {
          const serviceIdStr = String(serviceId);
          const service = await this.goodsServiceModel.getGoodsServiceById(serviceIdStr);
          if (!service) {
            throw new Error(`关联记录不存在: 商品服务 [ID: ${serviceIdStr}]`);
          }
        }
      }

      // 检查属性项是否存在 - 使用辅助方法
      if (attributeItemIds && attributeItemIds.length > 0) {
        for (const attributeItemId of attributeItemIds) {
          const attributeItemIdStr = String(attributeItemId);
          const attributeItem = await this.getAttributeItemById(attributeItemIdStr);
          if (!attributeItem) {
            throw new Error(`关联记录不存在: 商品属性项 [ID: ${attributeItemIdStr}]`);
          }
        }
      }
      
    } catch (error) {
      if (error.message.includes('关联记录不存在')) {
        throw error;
      }
      throw new Error('验证关联实体失败，请检查数据库连接');
    }
  }
  
  /**
   * 根据ID获取属性项
   * @param {number} id - 属性项ID
   * @returns {Promise<Object|null>} - 属性项对象或null
   */
  async getAttributeItemById(id) {
    try {
      const attributeItem = await this.prisma.goodsAttributeItem.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });
      return attributeItem;
    } catch (error) {
      console.error(`获取属性项ID ${id} 失败:`, error);
      return null;
    }
  }

  /**
   * 增量更新图片（避免删除重建导致关联数据丢失）
   * @param {Object} tx - 事务对象
   * @param {string|number} spuId - 商品SPU ID
   * @param {Array} newImages - 新的图片数据数组
   */
  async updateImagesIncremental(tx, spuId, newImages) {
    // 获取现有图片
    const existingImages = await tx.goodsImage.findMany({
      where: { goods_spu_id: BigInt(spuId) },
      orderBy: { sort_order: 'asc' }
    });

    // 如果没有新图片，删除所有现有图片
    if (!newImages || newImages.length === 0) {
      if (existingImages.length > 0) {
        await tx.goodsImage.deleteMany({
          where: { goods_spu_id: BigInt(spuId) }
        });
        console.log(`删除了 ${existingImages.length} 张图片`);
      }
      return;
    }

    // 处理图片更新
    const operations = [];

    // 更新或创建图片
    for (let i = 0; i < newImages.length; i++) {
      const newImage = newImages[i];
      const existingImage = existingImages[i];

      if (existingImage) {
        // 检查是否需要更新
        const hasChanges =
          existingImage.image_url !== newImage.url ||
          existingImage.sort_order !== newImage.sortOrder ||
          existingImage.is_default !== newImage.isDefault;

        if (hasChanges) {
          operations.push(
            tx.goodsImage.update({
              where: { id: existingImage.id },
              data: {
                image_url: newImage.url,
                sort_order: newImage.sortOrder,
                is_default: newImage.isDefault,
                updated_at: BigInt(Date.now())
              }
            })
          );
        }
      } else {
        // 创建新图片
        operations.push(
          tx.goodsImage.create({
            data: {
              goods_spu_id: BigInt(spuId),
              image_url: newImage.url,
              sort_order: newImage.sortOrder,
              is_default: newImage.isDefault,
              created_at: BigInt(Date.now()),
              updated_at: BigInt(Date.now())
            }
          })
        );
      }
    }

    // 删除多余的图片
    if (existingImages.length > newImages.length) {
      const extraImageIds = existingImages.slice(newImages.length).map(img => img.id);
      operations.push(
        tx.goodsImage.deleteMany({
          where: { id: { in: extraImageIds } }
        })
      );
      console.log(`将删除 ${extraImageIds.length} 张多余的图片`);
    }

    // 执行所有操作
    await Promise.all(operations);
    console.log(`图片更新完成：${newImages.length} 张图片`);
  }

  /**
   * 增量更新视频（避免删除重建导致关联数据丢失）
   * @param {Object} tx - 事务对象
   * @param {string|number} spuId - 商品SPU ID
   * @param {Object|string|null} newVideoData - 新的视频数据
   */
  async updateVideosIncremental(tx, spuId, newVideoData) {
    // 获取现有视频
    const existingVideos = await tx.goodsVideo.findMany({
      where: { goods_spu_id: BigInt(spuId) }
    });

    // 如果没有新视频数据，删除所有现有视频
    if (!newVideoData) {
      if (existingVideos.length > 0) {
        await tx.goodsVideo.deleteMany({
          where: { goods_spu_id: BigInt(spuId) }
        });
        console.log(`删除了 ${existingVideos.length} 个视频`);
      }
      return;
    }

    // 标准化新视频数据
    let videoData = newVideoData;
    if (typeof videoData === 'string') {
      videoData = {
        url: videoData,
        coverUrl: '',
        sortOrder: 0
      };
    }

    // 如果存在视频，检查是否需要更新
    if (existingVideos.length > 0) {
      const existingVideo = existingVideos[0]; // 假设只有一个视频

      // 检查是否有变化
      const hasChanges =
        existingVideo.video_url !== videoData.url ||
        existingVideo.cover_image_url !== (videoData.coverUrl || '') ||
        existingVideo.sort_order !== (videoData.sortOrder || 0);

      if (hasChanges) {
        // 更新现有视频
        await tx.goodsVideo.update({
          where: { id: existingVideo.id },
          data: {
            video_url: videoData.url,
            cover_image_url: videoData.coverUrl || '',
            sort_order: videoData.sortOrder || 0,
            updated_at: BigInt(Date.now())
          }
        });
        console.log(`更新了视频 ID: ${existingVideo.id}`);
      } else {
        console.log('视频数据无变化，跳过更新');
      }

      // 如果有多个视频，删除多余的（保留第一个）
      if (existingVideos.length > 1) {
        const extraVideoIds = existingVideos.slice(1).map(v => v.id);
        await tx.goodsVideo.deleteMany({
          where: { id: { in: extraVideoIds } }
        });
        console.log(`删除了 ${extraVideoIds.length} 个多余的视频`);
      }
    } else {
      // 没有现有视频，创建新视频
      await tx.goodsVideo.create({
        data: {
          goods_spu_id: BigInt(spuId),
          video_url: videoData.url,
          cover_image_url: videoData.coverUrl || '',
          sort_order: videoData.sortOrder || 0,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      console.log('创建了新视频');
    }
  }

  /**
   * 增量更新SKU（避免删除重建导致关联数据丢失）
   * @param {Object} tx - 事务对象
   * @param {string|number} spuId - 商品SPU ID
   * @param {Object} skuInfo - SKU信息
   */
  async updateSkusIncremental(tx, spuId, skuInfo) {
    const { skuType, specDefinitions, skuList } = skuInfo;

    // 获取现有SKU
    const existingSkus = await tx.goodsSku.findMany({
      where: { goods_spu_id: BigInt(spuId) },
      include: {
        goods_sku_specification_values: {
          include: {
            goods_specification_value: {
              include: {
                goods_specification_name: true
              }
            }
          }
        }
      }
    });

    console.log(`现有SKU数量: ${existingSkus.length}, 新SKU数量: ${skuList.length}`);

    let totalStock = 0;
    const updateOperations = [];
    const createOperations = [];
    const processedSkuIds = new Set();

    // 处理规格定义（如果是多规格商品）
    const specValueIdMap = {};
    if ((skuType === 2 || skuType === '2') && specDefinitions && specDefinitions.length > 0) {
      for (const specDef of specDefinitions) {
        const specName = await this.goodsSpuModel.find_or_create_spec_name(specDef.name, tx);
        specValueIdMap[specDef.name] = {};

        for (const value of specDef.values) {
          const specValue = await this.goodsSpuModel.find_or_create_spec_value(specName.id, value, tx);
          specValueIdMap[specDef.name][value] = specValue.id;
        }
      }
    }

    // 处理每个新SKU
    for (const newSku of skuList) {
      // 查找匹配的现有SKU（通过SKU编码匹配）
      const existingSku = existingSkus.find(sku => sku.sku_code === newSku.skuCode);

      if (existingSku) {
        // 更新现有SKU
        const hasChanges =
          existingSku.sales_price !== newSku.salesPrice ||
          existingSku.stock !== newSku.stock ||
          existingSku.sales_volume !== (newSku.salesVolume || 0) ||
          existingSku.market_price !== (newSku.marketPrice || null) ||
          existingSku.cost_price !== (newSku.costPrice || null) ||
          existingSku.weight !== (newSku.weight || null) ||
          existingSku.volume !== (newSku.volume || null) ||
          existingSku.unit !== (newSku.unit || null);

        if (hasChanges) {
          updateOperations.push(
            tx.goodsSku.update({
              where: { id: existingSku.id },
              data: {
                sales_price: newSku.salesPrice,
                stock: newSku.stock,
                sales_volume: newSku.salesVolume || 0,
                market_price: newSku.marketPrice || null,
                cost_price: newSku.costPrice || null,
                weight: newSku.weight || null,
                volume: newSku.volume || null,
                unit: typeof newSku.unit === 'string' ? newSku.unit : (newSku.unit ? String(newSku.unit) : null),
                updated_at: BigInt(Date.now())
              }
            })
          );
          console.log(`将更新SKU: ${existingSku.sku_code}`);
        } else {
          console.log(`SKU ${existingSku.sku_code} 无变化，跳过更新`);
        }

        processedSkuIds.add(existingSku.id);
      } else {
        // 创建新SKU
        const skuId = generateSnowflakeId();
        const skuData = {
          id: skuId,
          sku_code: newSku.skuCode,
          sales_price: newSku.salesPrice,
          stock: newSku.stock,
          sales_volume: newSku.salesVolume || 0,
          unit: typeof newSku.unit === 'string' ? newSku.unit : (newSku.unit ? String(newSku.unit) : null),
          weight: newSku.weight || null,
          volume: newSku.volume || null,
          cost_price: newSku.costPrice || null,
          market_price: newSku.marketPrice || null,
          low_stock_threshold: null,
          barcode: null,
          is_enabled: 1,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        };

        // 将SKU创建操作和关联数据创建分开
        createOperations.push({
          type: 'sku',
          operation: () => this.goodsSpuModel.add_sku(spuId, skuData, tx),
          skuId: skuId,
          newSku: newSku
        });
        console.log(`将创建新SKU: ${newSku.skuCode}`);
      }

      totalStock += newSku.stock;
    }

    // 先删除不再需要的SKU（需要先删除关联数据）
    const skusToDelete = existingSkus.filter(sku => !processedSkuIds.has(sku.id));
    if (skusToDelete.length > 0) {
      const skuIdsToDelete = skusToDelete.map(sku => sku.id);

      // 1. 先删除SKU的规格值关联
      await tx.goodsSkuSpecificationValue.deleteMany({
        where: { goods_sku_id: { in: skuIdsToDelete } }
      });

      // 2. 删除SKU图片（SKU图片存储在goodsImage表中，通过goods_sku_id字段关联）
      await tx.goodsImage.deleteMany({
        where: { goods_sku_id: { in: skuIdsToDelete } }
      });

      // 3. 最后删除SKU
      await tx.goodsSku.deleteMany({
        where: { id: { in: skuIdsToDelete } }
      });

      console.log(`已删除 ${skusToDelete.length} 个不再需要的SKU及其关联数据`);
    }

    // 按顺序执行操作
    // 1. 先执行更新操作
    if (updateOperations.length > 0) {
      await Promise.all(updateOperations);
      console.log(`已更新 ${updateOperations.length} 个SKU`);
    }

    // 2. 再执行SKU创建操作
    const createdSkus = [];
    for (const createOp of createOperations) {
      if (createOp.type === 'sku') {
        const result = await createOp.operation();
        createdSkus.push({
          skuId: createOp.skuId,
          newSku: createOp.newSku,
          result: result
        });
        console.log(`已创建SKU: ${createOp.newSku.skuCode}`);
      }
    }

    // 3. 最后创建关联数据
    const associationOperations = [];
    for (const createdSku of createdSkus) {
      const { skuId, newSku } = createdSku;

      // 处理规格值关联（如果是多规格商品）
      if ((skuType === 2 || skuType === '2') && newSku.specs) {
        for (const [specName, specValue] of Object.entries(newSku.specs)) {
          const specValueId = specValueIdMap[specName][specValue];
          if (specValueId) {
            associationOperations.push(
              this.goodsSpuModel.add_sku_spec_value(skuId, specValueId, tx)
            );
          }
        }
      }

      // 处理SKU图片
      if (newSku.image) {
        const imageUrl = typeof newSku.image === 'string' ? newSku.image : newSku.image.url;
        if (imageUrl) {
          associationOperations.push(
            this.goodsSpuModel.add_sku_image(spuId, skuId, imageUrl, tx)
          );
        }
      }
    }

    if (associationOperations.length > 0) {
      await Promise.all(associationOperations);
      console.log(`已创建 ${associationOperations.length} 个关联记录`);
    }

    // 更新SPU总库存
    await this.goodsSpuModel.update_spu_total_stock(spuId, totalStock, tx);

    console.log(`SKU增量更新完成：总库存=${totalStock}`);
  }

  /**
   * 验证SKU编码是否重复（用于添加商品时）
   * @param {Object} tx - 事务对象
   * @param {Object} skuInfo - SKU信息
   */
  async validateSkuCodesForAdd(tx, skuInfo) {
    const { skuList } = skuInfo;

    // 1. 检查同一商品内SKU编码是否重复
    const skuCodes = skuList.map(sku => sku.skuCode);
    const duplicateCodesInSameProduct = skuCodes.filter((code, index) => skuCodes.indexOf(code) !== index);

    if (duplicateCodesInSameProduct.length > 0) {
      const uniqueDuplicates = [...new Set(duplicateCodesInSameProduct)];
      throw new Error(`同一商品内SKU编码重复: ${uniqueDuplicates.join(', ')}，请使用不同的编码`);
    }

    // 2. 检查SKU编码是否已被其他商品使用
    for (const sku of skuList) {
      const existingSku = await tx.goodsSku.findFirst({
        where: { sku_code: sku.skuCode }
      });

      if (existingSku) {
        throw new Error(`SKU编码 "${sku.skuCode}" 已存在，请使用其他编码`);
      }
    }
  }

  /**
   * 验证SKU编码是否重复（用于编辑商品时）
   * @param {Object} tx - 事务对象
   * @param {string|number} currentSpuId - 当前商品SPU ID
   * @param {Object} skuInfo - SKU信息
   */
  async validateSkuCodes(tx, currentSpuId, skuInfo) {
    const { skuList } = skuInfo;

    // 1. 检查同一商品内SKU编码是否重复
    const skuCodes = skuList.map(sku => sku.skuCode);
    const duplicateCodesInSameProduct = skuCodes.filter((code, index) => skuCodes.indexOf(code) !== index);

    if (duplicateCodesInSameProduct.length > 0) {
      const uniqueDuplicates = [...new Set(duplicateCodesInSameProduct)];
      throw new Error(`同一商品内SKU编码重复: ${uniqueDuplicates.join(', ')}，请使用不同的编码`);
    }

    // 2. 检查SKU编码是否被其他商品使用
    for (const sku of skuList) {
      const existingSku = await tx.goodsSku.findFirst({
        where: {
          sku_code: sku.skuCode,
          goods_spu_id: {
            not: BigInt(currentSpuId) // 排除当前商品的SKU
          }
        }
      });

      if (existingSku) {
        throw new Error(`SKU编码 "${sku.skuCode}" 已被其他商品使用，请使用其他编码`);
      }
    }
  }

  /**
   * 处理SKU相关数据
   * @param {Object} tx - 事务对象
   * @param {number} spuId - SPU ID
   * @param {Object} skuInfo - SKU信息
   */
  async processSkus(tx, spuId, skuInfo, updateTotalSales = true) {
    const { skuType, specDefinitions, skuList } = skuInfo;
    let totalStock = 0;

    // 处理规格和SKU
    const specValueIdMap = {};
    

    
    // 如果是多规格商品 (skuType=2)
    if ((skuType === 2 || skuType === '2') && specDefinitions && specDefinitions.length > 0) {
      // 处理规格定义
      for (const specDef of specDefinitions) {
        // 查找或创建规格名
        const specName = await this.goodsSpuModel.find_or_create_spec_name(specDef.name, tx);
        specValueIdMap[specDef.name] = {};
        
        // 处理规格值
        for (const value of specDef.values) {
          const specValue = await this.goodsSpuModel.find_or_create_spec_value(specName.id, value, tx);
          specValueIdMap[specDef.name][value] = specValue.id;
        }
      }
    }
    
    // 处理SKU列表
    for (const sku of skuList) {
      // 直接使用SKU编码（已在外部验证过）
      const skuCode = sku.skuCode;
      
      // 生成雪花ID作为SKU ID
      const skuId = generateSnowflakeId();
      
      // 准备SKU数据
      const skuData = {
        id: skuId, // 添加唯一ID，避免ID冲突
        sku_code: skuCode, // 使用检查过的SKU编码
        sales_price: sku.salesPrice, // 使用 sales_price 字段
        stock: sku.stock,
        sales_volume: sku.salesVolume || 0, // 添加销售数量字段，默认为0
        unit: typeof sku.unit === 'string' ? sku.unit : (sku.unit ? String(sku.unit) : null),
        weight: sku.weight || null,
        volume: sku.volume || null,
        cost_price: sku.costPrice || null,
        market_price: sku.marketPrice || null, // 使用前端提供的市场价
        low_stock_threshold: null, // 前端未提供
        barcode: null, // 前端未提供
        is_enabled: 1, // 默认启用，使用整数 1
        created_at: BigInt(Date.now()), // 显式赋值 created_at
        updated_at: BigInt(Date.now()) // 显式赋值 updated_at
      };
      
      // 创建SKU
      const newSku = await this.goodsSpuModel.add_sku(spuId, skuData, tx);
      
      // 累加总库存
      totalStock += sku.stock;
      
      // 如果是多规格商品，处理规格值关联
      if ((skuType === 2 || skuType === '2') && sku.specs) {
        console.log('处理多规格商品规格值关联:', JSON.stringify(sku.specs));
        for (const [specName, specValue] of Object.entries(sku.specs)) {
          console.log(`处理规格名称: ${specName}, 规格值: ${specValue}`);
          const specValueId = specValueIdMap[specName][specValue];
          console.log(`找到规格值 ID: ${specValueId}`);
          if (specValueId) {
            await this.goodsSpuModel.add_sku_spec_value(skuId, specValueId, tx);
            console.log(`成功添加SKU规格值关联: SKU ID ${skuId}, 规格值 ID ${specValueId}`);
          }
        }
      }
      
      // 处理SKU特定图片（如果有）
      if (sku.image) {
        // 如果image是字符串，直接使用；如果是对象，使用url属性
        const imageUrl = typeof sku.image === 'string' ? sku.image : sku.image.url;
        if (imageUrl) {
          await this.goodsSpuModel.add_sku_image(spuId, skuId, imageUrl, tx);
        }
      }
    }
    
    // 更新SPU总库存
    await this.goodsSpuModel.update_spu_total_stock(spuId, totalStock, tx);
  }

  /**
   * 将前端规格数据转换为后端需要的SKU信息格式
   * @param {Object} specifications - 前端传入的规格数据
   * @returns {Object} - 转换后的SKU信息
   */
  convertSpecificationsToSkuInfo(specifications) {
    const { skuType, individual, multiple } = specifications;
    
    // 将skuType转换为数字类型：1表示单规格，2表示多规格
    const skuTypeValue = parseInt(skuType);
    
    // 单规格商品
    if (skuTypeValue === 1 && individual) {
      return {
        skuType: skuTypeValue,
        skuList: [{
          skuCode: individual.code,
          salesPrice: individual.price,
          marketPrice: individual.marketPrice,
          costPrice: individual.costPrice,
          stock: individual.stock,
          salesVolume: individual.salesVolume || 0, // 添加销售数量字段
          weight: individual.weight,
          volume: individual.volume,
          unit: individual.unit,
          image: individual.image ? { url: individual.image } : null
        }]
      };
    }
    
    // 多规格商品
    if (skuTypeValue === 2 && multiple) {
      // 转换规格定义
      const specDefinitions = multiple.specs.map(spec => ({
        name: spec.name,
        values: spec.values
      }));
      
      // 转换SKU列表
      const skuList = multiple.skuList.map(sku => ({
        specs: sku.specs,
        skuCode: sku.code,
        salesPrice: sku.price,
        marketPrice: sku.marketPrice,
        costPrice: sku.costPrice,
        stock: sku.stock,
        salesVolume: sku.salesVolume || 0, // 添加销售数量字段
        weight: sku.weight,
        volume: sku.volume,
        unit: sku.unit,
        image: sku.image ? { url: sku.image } : null
      }));
      
      return {
        skuType: skuTypeValue,
        specDefinitions,
        skuList
      };
    }
    
    // 默认返回空的单规格商品
    return {
      skuType: 'single',
      skuList: []
    };
  }

  /**
   * 获取商品列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 分页查询结果
   */
  async getProductList(params) {
    try {
      // 调用模型层方法获取商品列表
      const result = await this.goodsSpuModel.getGoodsSpuList(params);
      
      return {
        success: true,
        message: '获取商品列表成功',
        data: {
          total: result.total,
          items: result.items,
          page: parseInt(params.page) || 1,
          pageSize: parseInt(params.pageSize) || 20,
          totalPages: Math.ceil(result.total / (parseInt(params.pageSize) || 20))
        }
      };
    } catch (error) {
      console.error('获取商品列表失败', error);
      return {
        success: false,
        message: '获取商品列表失败',
        error: error.message
      };
    }
  }

  /**
   * 更新商品
   * @param {string|number} id - 商品ID
   * @param {Object} productData - 商品数据（与添加商品接口使用相同的数据结构）
   * @param {BigInt|null} userId - 当前用户ID
   * @returns {Object} - 更新结果
   */
  async updateGoodsSpu(id, productData, userId) {
    // 解构请求数据
    const { 
      basicInfo, 
      associations, 
      logisticsForm, 
      deliveryForm, 
      attributeValues, 
      specifications, 
      detailForm, 
      otherForm 
    } = productData;

    // 开始事务
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 检查商品是否存在
        const existingSpu = await this.goodsSpuModel.getGoodsSpuById(id, tx);
        if (!existingSpu) {
          throw new Error(`ID为${id}的商品不存在`);
        }

        // 1. 验证关联数据是否存在
        await this.validateRelatedEntities(
          basicInfo.categoryId,
          basicInfo.brandId,
          logisticsForm.isFreeShipping === '2' || logisticsForm.isFreeShipping === 2 ? logisticsForm.freightTemplateId : null,
          associations.tagIds,
          associations.serviceIds,
          attributeValues?.map(attr => attr.attributeItemId) || []
        );

        // 处理虚拟商品和配送需求字段
        const isVirtual = otherForm.isVirtual ? parseInt(otherForm.isVirtual) : 0; // 0表示实物商品，1表示虚拟商品
        const isShippingRequired = otherForm.isShippingRequired ? parseInt(otherForm.isShippingRequired) : (isVirtual === 0 ? 1 : 0); // 1表示需要配送，0表示不需要配送
        
        // 2. 准备SPU更新数据
        const spuUpdateData = {
          name: basicInfo.name,
          subtitle: basicInfo.subtitle || null,
          description: detailForm.content,
          goods_brand_id: BigInt(basicInfo.brandId),
          goods_freight_template_id: logisticsForm.isFreeShipping === '2' || logisticsForm.isFreeShipping === 2 ? BigInt(logisticsForm.freightTemplateId) : null,
          is_free_shipping: parseInt(logisticsForm.isFreeShipping),
          meta_title: otherForm.seoTitle || null,
          meta_keywords: otherForm.keywords || null,
          meta_description: otherForm.seoDescription || null,
          sort_order: otherForm.sort || 100,
          status: otherForm.status,
          is_virtual: isVirtual, // 0表示实物商品，1表示虚拟商品
          is_shipping_required: isShippingRequired, // 1表示需要配送，0表示不需要配送
          // 保存配送信息
          delivery_area: deliveryForm.deliveryArea && deliveryForm.deliveryArea.length > 0 ? deliveryForm.deliveryArea.join(',') : null,
          delivery_time: null,
          updated_at: BigInt(Date.now()),
          updated_by: userId ? BigInt(userId) : null,
        };

        // 如果前端传入了totalSales，则使用前端的值
        if (otherForm.totalSales !== undefined && otherForm.totalSales !== null) {
          spuUpdateData.total_sales = parseInt(otherForm.totalSales);
        }

        // 如果状态为上架且之前不是上架状态，设置发布时间
        if (otherForm.status === 1 && existingSpu.status !== 1) {
          spuUpdateData.published_at = BigInt(Date.now());
        }

        // 3. 更新SPU
        const updatedSpu = await this.goodsSpuModel.update_spu(id, spuUpdateData, tx);
        
        // 4. 更新分类关联
        // 删除原有分类关联
        await this.goodsSpuModel.delete_category_associations(id, tx);
        // 添加新分类关联
        await this.goodsSpuModel.add_category_association(id, basicInfo.categoryId, tx);
        
        // 5. 更新SPU图片（使用增量更新策略）
        const spuImages = basicInfo.spuImages.map((url, index) => ({
          url,
          sortOrder: index,
          isDefault: index === 0 // 第一张图片为默认图片
        }));
        await this.updateImagesIncremental(tx, id, spuImages);
        
        // 6. 更新视频（使用增量更新策略）
        await this.updateVideosIncremental(tx, id, basicInfo.video);
        
        // 7. 更新标签关联
        // 删除原有标签关联
        await this.goodsSpuModel.delete_tag_associations(id, tx);
        // 添加新标签关联（如果有）
        if (associations.tagIds && associations.tagIds.length > 0) {
          await this.goodsSpuModel.add_tag_associations(id, associations.tagIds, tx);
        }
        
        // 8. 更新服务关联
        // 删除原有服务关联
        await this.goodsSpuModel.delete_service_associations(id, tx);
        // 添加新服务关联（如果有）
        if (associations.serviceIds && associations.serviceIds.length > 0) {
          await this.goodsSpuModel.add_service_associations(id, associations.serviceIds, tx);
        }
        
        // 9. 更新属性值
        // 删除原有属性值
        await this.goodsSpuModel.delete_attribute_values(id, tx);
        // 添加新属性值（如果有）
        if (attributeValues && attributeValues.length > 0) {
          await this.goodsSpuModel.add_attribute_values(id, attributeValues, tx);
        }
        
        // 10. 更新SKU（使用增量更新策略）
        // 将前端的规格数据转换为后端需要的格式
        const skuInfo = this.convertSpecificationsToSkuInfo(specifications);
        // 先检查SKU编码是否重复
        await this.validateSkuCodes(tx, id, skuInfo);
        // 使用增量更新SKU
        await this.updateSkusIncremental(tx, id, skuInfo);
        
        // 返回更新结果
        return { 
          success: true, 
          data: { 
            spuId: updatedSpu.id,
            slug: updatedSpu.slug
          }
        };
      } catch (error) {
        console.error('更新商品失败', error);
        throw error;
      }
    }, {
      // 设置较长的事务超时时间，确保有足够时间完成所有操作
      timeout: 30000
    });
  }

  /**
   * 根据ID获取商品
   * @param {string|number} id - 商品ID
   * @returns {Promise<Object|null>} - 商品对象或null
   */
  async getGoodsSpuById(id) {
    try {
      return await this.goodsSpuModel.getGoodsSpuById(id);
    } catch (error) {
      console.error(`获取商品ID ${id} 失败:`, error);
      return null;
    }
  }

  /**
   * 获取商品详情
   * @param {string|number} id - 商品ID
   * @returns {Object} - 商品详情
   */
  async getProductDetail(id) {
    try {
      // 验证商品ID
      if (!id) {
        throw new Error('商品ID不能为空');
      }
      
      // 获取商品详情
      const productDetail = await this.goodsSpuModel.getGoodsSpuDetailById(id);
      
      if (!productDetail) {
        throw new Error(`ID为${id}的商品不存在`);
      }
      
      // 处理商品详情数据，整理成前端需要的格式
      const result = {
        // 基本信息
        basicInfo: {
          id: productDetail.id,
          name: productDetail.name,
          subtitle: productDetail.subtitle,
          brandId: productDetail.goodsBrandId,
          brandName: productDetail.goodsBrands?.name,
          // 分类信息 - 只使用分类ID，不再使用分类名称
          categoryId: productDetail.goodsCategoryAssociations?.[0]?.goodsCategoryId,
          status: productDetail.status,
          unit: productDetail.unit,
          totalSales: productDetail.totalSales,
          totalStock: productDetail.totalStock,
          visitCount: productDetail.visitCount,
          createdAt: productDetail.createdAt,
          updatedAt: productDetail.updatedAt
        },
        
        // 媒体信息
        mediaInfo: {
          images: productDetail.goodsImages?.map(img => ({
            id: img.id,
            url: img.imageUrl,
            sort: img.sortOrder,
            isDefault: img.isDefault
          })) || [],
          videos: productDetail.goodsVideos?.map(video => ({
            id: video.id,
            url: video.videoUrl,
            coverUrl: video.coverUrl,
            sort: video.sortOrder
          })) || []
        },
        
        // 关联信息
        relatedInfo: {
          tags: productDetail.goodsTagAssociations?.map(tag => ({
            id: tag.goodsTagId,
            name: tag.goodsTags?.name
          })) || [],
          services: productDetail.goodsServiceAssociations?.map(service => ({
            id: service.goodsServiceId,
            name: service.goodsService?.name,
            description: service.goodsService?.description
          })) || []
        },
        
        // 属性值信息 - 前端添加商品时传递的attributeValues
        attributeValues: productDetail.goodsAttributeValues?.map(attr => ({
          attributeItemId: attr.goodsAttributeItemId,
          attributeItemName: attr.goodsAttributeItems?.name,
          attributeType: attr.goodsAttributeItems?.type,
          value: attr.value
        })) || [],
        
        // 规格和SKU信息
        skuInfo: {
          skuType: productDetail.goodsSkus?.length > 1 ? 2 : 1,
          skuList: productDetail.goodsSkus?.map(sku => {
            // 打印调试信息
            console.log('SKU规格值关联数据:', JSON.stringify(sku.goodsSkuSpecificationValues, null, 2));
            
            // 提取规格信息
            const specs = {};
            if (sku.goodsSkuSpecificationValues && sku.goodsSkuSpecificationValues.length > 0) {
              sku.goodsSkuSpecificationValues.forEach(specValue => {
                console.log('规格值关联项:', JSON.stringify(specValue, null, 2));
                const specName = specValue.goodsSpecificationValue?.goodsSpecificationName?.name;
                const value = specValue.goodsSpecificationValue?.value;
                console.log('规格名称:', specName, '规格值:', value);
                if (specName && value) {
                  specs[specName] = value;
                }
              });
            } else {
              console.log('该SKU没有规格值关联数据');
            }
            
            // 生成SKU名称，如果有规格则组合规格值，否则使用SPU名称
            let skuName = '';
            
            // 如果有规格值，则组合规格值作为SKU名称
            if (Object.keys(specs).length > 0) {
              const specValues = Object.values(specs).join(' ');
              skuName = `${productDetail.name} ${specValues}`;
            } else {
              // 如果SKU没有规格值，直接使用SPU名称
              skuName = productDetail.name;
            }
            
            return {
              id: sku.id,
              skuCode: sku.skuCode,
              skuName: skuName, // 添加SKU名称字段
              salesPrice: sku.salesPrice,
              marketPrice: sku.marketPrice,
              costPrice: sku.costPrice,
              stock: sku.stock,
              salesVolume: sku.salesVolume || 0,
              unit: sku.unit || '',
              lowStockThreshold: sku.lowStockThreshold,
              weight: sku.weight,
              volume: sku.volume,
              barcode: sku.barcode,
              isEnabled: sku.isEnabled,
              specs: specs,
              image: sku.goodsImages?.[0] ? {
                id: sku.goodsImages[0].id,
                url: sku.goodsImages[0].imageUrl
              } : null
            };
          }) || []
        },
        
        // 商品详情
        detailInfo: {
          detailHtml: productDetail.detailHtml,
          detailMobile: productDetail.detailMobile
        },
        
        // 商品详情表单
        detailForm: {
          content: productDetail.description
        },
        
        // 物流表单
        logisticsForm: {
          isFreeShipping: productDetail.isFreeShipping?.toString() || '2', // 默认不包邮
          // 当isFreeShipping为1（包邮）时，freightTemplateId应为null
          // 当isFreeShipping为2（不包邮）时，freightTemplateId应有值
          freightTemplateId: productDetail.isFreeShipping === 1 || productDetail.isFreeShipping === '1' ? null : productDetail.goodsFreightTemplateId
        },
        
        // 配送信息
        deliveryForm: {
          // 配送区域始终为逗号分隔的字符串，返回为数组格式
          deliveryArea: productDetail.deliveryArea ? 
            // 将逗号分隔的字符串转换为数组
            productDetail.deliveryArea.split(',').filter(Boolean) : [],
          deliveryTime: productDetail.deliveryTime // 直接返回数据库存储的毫秒时间戳
        },
        
        // 其他信息
        otherInfo: {
          metaTitle: productDetail.metaTitle,
          metaKeywords: productDetail.metaKeywords,
          metaDescription: productDetail.metaDescription,
          sortOrder: productDetail.sortOrder,
          status: productDetail.status // 直接使用数字类型，不转换为字符串
        }
      };
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取商品详情失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 软删除商品
   * @param {string|number} id - 商品ID
   * @returns {Promise<Object>} - 软删除结果
   */
  async softDeleteProduct(id) {
    try {
      if (!id) {
        return {
          success: false,
          message: '商品ID不能为空'
        };
      }

      // 先检查商品是否存在且未被删除
      const existingSpu = await this.goodsSpuModel.getGoodsSpuById(id);
      if (!existingSpu) {
        return {
          success: false,
          message: `ID为${id}的商品不存在`
        };
      }

      // 检查商品是否被订单使用
      let orderItemsCount = 0;
      try {
        // 使用OrderItemModel检查商品是否被订单使用
        const result = await this.orderItemModel.count({
          where: {
            goods_spu_id: BigInt(id)
          }
        });
        orderItemsCount = result;
        console.log(`检查商品ID ${id} 的订单使用情况，共有 ${orderItemsCount} 个订单使用该商品`);
      } catch (err) {
        console.error('检查订单使用时出错:', err);
        // 如果检查失败，我们假设没有订单使用该商品
        orderItemsCount = 0;
      }

      if (orderItemsCount > 0) {
        return {
          success: false,
          message: `该商品已被${orderItemsCount}个订单使用，无法永久删除`,
          data: { ordersCount: orderItemsCount }
        };
      }

      // 执行软删除操作
      await this.goodsSpuModel.softDeleteGoodsSpu(id);
      
      return {
        success: true,
        message: '商品已移至回收站',
        data: { id }
      };
    } catch (error) {
      console.error('软删除商品失败', error);
      return {
        success: false,
        message: '软删除商品失败',
        error: error.message
      };
    }
  }

  /**
   * 恢复已删除的商品
   * @param {string|number} id - 商品ID
   * @returns {Promise<Object>} - 恢复结果
   */
  async restoreProduct(id) {
    try {
      if (!id) {
        return {
          success: false,
          message: '商品ID不能为空'
        };
      }

      // 先检查商品是否存在
      const existingSpu = await this.prisma.goodsSpu.findUnique({
        where: { id: BigInt(id) }
      });
      
      if (!existingSpu) {
        return {
          success: false,
          message: `ID为${id}的商品不存在`
        };
      }

      // 检查商品是否已经被删除
      if (existingSpu.deleted_at === null) {
        return {
          success: false,
          message: `ID为${id}的商品未被删除，无需恢复`
        };
      }

      // 执行恢复操作
      await this.goodsSpuModel.restoreGoodsSpu(id);
      
      return {
        success: true,
        message: '商品已成功恢复',
        data: { id }
      };
    } catch (error) {
      console.error('恢复商品失败', error);
      return {
        success: false,
        message: '恢复商品失败',
        error: error.message
      };
    }
  }

  /**
   * 永久删除商品
   * @param {string|number} id - 商品ID
   * @param {Object} options - 其他选项
   * @param {string} options.reason - 删除原因
   * @param {string|number} options.userId - 操作人用户ID
   * @param {string} options.ipAddress - 操作人 IP 地址
   * @returns {Promise<Object>} - 删除结果
   */
  async permanentDeleteProduct(id, options = {}) {
    try {
      if (!id) {
        return {
          success: false,
          message: '商品ID不能为空'
        };
      }

      // 先检查商品是否存在
      const existingSpu = await this.prisma.goodsSpu.findUnique({
        where: { id: BigInt(id) },
        include: {
          goods_skus: true // 包含关联的SKU数据
        }
      });
      
      if (!existingSpu) {
        return {
          success: false,
          message: `ID为${id}的商品不存在`
        };
      }

      // 检查商品是否被订单使用
      let orderItemsCount = 0;
      try {
        // 使用正确的表名order_items而不是orderItem
        orderItemsCount = await this.prisma.order_items.count({
          where: {
            goods_spu_id: BigInt(id)
          }
        });
      } catch (err) {
        // 如果检查失败，我们假设没有订单使用该商品
        orderItemsCount = 0;
      }

      if (orderItemsCount > 0) {
        return {
          success: false,
          message: `该商品已被${orderItemsCount}个订单使用，无法永久删除`,
          data: { ordersCount: orderItemsCount }
        };
      }

      // 在删除前先记录删除日志
      try {
        // 处理BigInt类型，使其可以被JSON序列化
        const processSpuData = (data) => {
          if (!data) return data;
          return JSON.parse(JSON.stringify(data, (key, value) => 
            typeof value === 'bigint' ? value.toString() : value
          ));
        };
        
        // 处理商品数据以便于存储
        const spuData = processSpuData(existingSpu);
        const skusData = processSpuData(existingSpu.goods_skus);
        
        // 删除商品前先记录删除日志
        await this.goodsDeleteLogModel.createDeleteLog({
          goodsSpuId: id,
          goodsSpuData: spuData,
          goodsSkusData: skusData,
          reason: options.reason || '管理员手动删除',
          createdBy: options.userId,
          ipAddress: options.ipAddress
        });
      } catch (logError) {
        // 即使日志记录失败，也继续删除操作
      }

      // 执行永久删除操作
      await this.goodsSpuModel.permanentDeleteGoodsSpu(id);
      
      return {
        success: true,
        message: '商品已永久删除',
        data: { id }
      };
    } catch (error) {
      console.error('永久删除商品失败', error);
      return {
        success: false,
        message: '永久删除商品失败',
        error: error.message
      };
    }
  }
}

module.exports = GoodsSpuService;

/**
 * 微信支付服务类
 */
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const axios = require('axios');
const { getWechatPayConfig, getFallbackConfig } = require('../../../config/wechat-pay.config');

class WechatPayService {
  constructor() {
    this.config = null;
    this.baseURL = 'https://api.mch.weixin.qq.com';
    this.configLoaded = false;
  }

  /**
   * 初始化配置
   */
  async initConfig() {
    if (!this.configLoaded) {
      this.config = await getWechatPayConfig();
      this.configLoaded = true;
    }
    return this.config;
  }

  /**
   * 生成随机字符串
   * @param {number} length 长度
   * @returns {string} 随机字符串
   */
  generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成测试金额 (0.01-0.05元)
   * @returns {number} 金额(分)
   */
  generateTestAmount() {
    const { min, max } = this.config.testAmount;
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 生成签名
   * @param {string} method HTTP方法
   * @param {string} url 请求URL
   * @param {number} timestamp 时间戳
   * @param {string} nonceStr 随机字符串
   * @param {string} body 请求体
   * @returns {string} 签名
   */
  generateSignature(method, url, timestamp, nonceStr, body) {
    try {
      let privateKey;

      // 检查证书是否为Buffer格式（数据库存储）还是文件路径（后备方案）
      if (this.config.certs.privateKey instanceof Buffer) {
        privateKey = this.config.certs.privateKey.toString('utf8');
      } else if (typeof this.config.certs.privateKey === 'string') {
        // 如果是文件路径，读取文件
        privateKey = fs.readFileSync(this.config.certs.privateKey, 'utf8');
      } else {
        throw new Error('私钥配置无效');
      }

      // 构造签名串
      const signStr = `${method}\n${url}\n${timestamp}\n${nonceStr}\n${body}\n`;

      // 使用私钥签名
      const sign = crypto.createSign('RSA-SHA256');
      sign.update(signStr);
      return sign.sign(privateKey, 'base64');
    } catch (error) {
      console.error('生成签名失败:', error);
      throw new Error('生成签名失败');
    }
  }

  /**
   * 生成Authorization头
   * @param {string} method HTTP方法
   * @param {string} url 请求URL
   * @param {string} body 请求体
   * @returns {string} Authorization头
   */
  generateAuthHeader(method, url, body) {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonceStr = this.generateNonceStr();
    const signature = this.generateSignature(method, url, timestamp, nonceStr, body);
    
    return `WECHATPAY2-SHA256-RSA2048 mchid="${this.config.mchId}",nonce_str="${nonceStr}",signature="${signature}",timestamp="${timestamp}",serial_no="${this.config.serialNo}"`;
  }

  /**
   * 创建Native支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise<Object>} 支付结果
   */
  async createNativeOrder(orderData) {
    try {
      // 确保配置已加载
      await this.initConfig();

      if (!this.config) {
        throw new Error('微信支付配置未找到');
      }

      const url = '/v3/pay/transactions/native';
      const amount = orderData.amount || this.generateTestAmount();
      
      const requestBody = {
        appid: this.config.appId,
        mchid: this.config.mchId,
        description: orderData.description || '微信支付测试',
        out_trade_no: orderData.outTradeNo || `TEST_${Date.now()}`,
        time_expire: this.getExpireTime(),
        notify_url: this.config.notifyUrl,
        amount: {
          total: amount,
          currency: 'CNY'
        }
      };

      const body = JSON.stringify(requestBody);
      const authHeader = this.generateAuthHeader('POST', url, body);

      const response = await axios.post(`${this.baseURL}${url}`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': authHeader,
          'User-Agent': 'WechatPay-Node-SDK'
        }
      });

      return {
        success: true,
        data: {
          codeUrl: response.data.code_url,
          outTradeNo: requestBody.out_trade_no,
          amount: amount,
          description: requestBody.description
        }
      };
    } catch (error) {
      console.error('创建微信支付订单失败:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message || '创建支付订单失败'
      };
    }
  }

  /**
   * 获取过期时间
   * @returns {string} ISO格式的过期时间
   */
  getExpireTime() {
    const expireTime = new Date();
    expireTime.setMinutes(expireTime.getMinutes() + this.config.timeExpire);
    return expireTime.toISOString();
  }

  /**
   * 查询订单状态
   * @param {string} outTradeNo 商户订单号
   * @returns {Promise<Object>} 查询结果
   */
  async queryOrder(outTradeNo) {
    try {
      // 确保配置已加载
      await this.initConfig();

      if (!this.config) {
        throw new Error('微信支付配置未找到');
      }

      const url = `/v3/pay/transactions/out-trade-no/${outTradeNo}`;
      const authHeader = this.generateAuthHeader('GET', url, '');

      const response = await axios.get(`${this.baseURL}${url}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': authHeader,
          'User-Agent': 'WechatPay-Node-SDK'
        },
        params: {
          mchid: this.config.mchId
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('查询订单失败:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message || '查询订单失败'
      };
    }
  }

  /**
   * 解密微信支付通知数据
   * @param {Object} notifyData - 通知数据
   * @returns {Object|null} - 解密后的数据
   */
  decryptNotifyData(notifyData) {
    try {
      if (!notifyData.resource) {
        throw new Error('通知数据格式错误');
      }

      const { ciphertext, nonce, associated_data } = notifyData.resource;

      if (!ciphertext || !nonce || !associated_data) {
        throw new Error('通知数据缺少必要字段');
      }

      console.log('开始解密微信支付通知数据...');
      console.log('密文长度:', ciphertext.length);
      console.log('随机数:', nonce);
      console.log('附加数据:', associated_data);

      // 将base64编码的密文转换为Buffer
      const ciphertextBuffer = Buffer.from(ciphertext, 'base64');

      // 微信支付V3的AEAD_AES_256_GCM格式：密文 + 16字节认证标签
      if (ciphertextBuffer.length < 16) {
        throw new Error('密文长度不足');
      }

      // 分离密文和认证标签
      const authTagLength = 16; // GCM模式的认证标签长度为16字节
      const actualCiphertext = ciphertextBuffer.slice(0, -authTagLength);
      const authTag = ciphertextBuffer.slice(-authTagLength);

      console.log('实际密文长度:', actualCiphertext.length);
      console.log('认证标签长度:', authTag.length);

      // 使用APIv3密钥解密
      const key = Buffer.from(this.config.apiV3Key, 'utf8');
      const nonceBuffer = Buffer.from(nonce, 'utf8');
      const aadBuffer = Buffer.from(associated_data, 'utf8');

      console.log('密钥长度:', key.length);
      console.log('随机数长度:', nonceBuffer.length);

      // 创建解密器
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, nonceBuffer);
      decipher.setAuthTag(authTag);
      decipher.setAAD(aadBuffer);

      // 解密数据
      let decrypted = decipher.update(actualCiphertext, null, 'utf8');
      decrypted += decipher.final('utf8');

      console.log('解密成功，数据长度:', decrypted.length);

      const result = JSON.parse(decrypted);
      console.log('解密后的支付数据:', result);

      return result;
    } catch (error) {
      console.error('解密微信支付通知数据失败:', error);
      console.error('错误堆栈:', error.stack);
      return null;
    }
  }

  /**
   * 生成测试金额（1-5分）
   * @returns {number} 测试金额（分）
   */
  generateTestAmount() {
    const min = this.config.testAmount?.min || 1;
    const max = this.config.testAmount?.max || 5;
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 申请退款
   * @param {Object} refundData 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async createRefund(refundData) {
    try {
      // 确保配置已加载
      await this.initConfig();

      if (!this.config) {
        throw new Error('微信支付配置未找到');
      }

      const {
        outTradeNo,
        outRefundNo,
        refundAmount,
        totalAmount,
        refundReason
      } = refundData;

      const url = '/v3/refund/domestic/refunds';

      const requestBody = {
        out_trade_no: outTradeNo,
        out_refund_no: outRefundNo,
        reason: refundReason || '订单退款',
        notify_url: `${process.env.API_BASE_URL || 'http://localhost:4000'}/api/v1/master/wechat-pay/refund-notify`,
        amount: {
          refund: Math.round(refundAmount * 100), // 退款金额（分）
          total: Math.round(totalAmount * 100),   // 原订单金额（分）
          currency: 'CNY'
        }
      };

      const body = JSON.stringify(requestBody);
      const authHeader = this.generateAuthHeader('POST', url, body);

      console.log('发起微信退款请求:', requestBody);

      const response = await axios.post(`${this.baseURL}${url}`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': authHeader,
          'User-Agent': 'WechatPay-Node-SDK'
        }
      });

      console.log('微信退款响应:', response.data);

      return {
        success: true,
        data: {
          refundId: response.data.refund_id,
          outRefundNo: response.data.out_refund_no,
          refundStatus: response.data.status,
          refundAmount: response.data.amount.refund / 100, // 转换为元
          createTime: response.data.create_time
        }
      };
    } catch (error) {
      console.error('微信退款失败:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message || '退款申请失败'
      };
    }
  }

  /**
   * 查询退款状态
   * @param {string} outRefundNo 商户退款单号
   * @returns {Promise<Object>} 查询结果
   */
  async queryRefund(outRefundNo) {
    try {
      // 确保配置已加载
      await this.initConfig();

      if (!this.config) {
        throw new Error('微信支付配置未找到');
      }

      const url = `/v3/refund/domestic/refunds/${outRefundNo}`;
      const authHeader = this.generateAuthHeader('GET', url, '');

      const response = await axios.get(`${this.baseURL}${url}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': authHeader,
          'User-Agent': 'WechatPay-Node-SDK'
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('查询退款状态失败:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message || '查询退款状态失败'
      };
    }
  }

  /**
   * 处理支付回调
   * @param {Object} callbackData 回调数据
   * @returns {Object} 处理结果
   */
  async handlePaymentCallback(callbackData) {
    try {
      // 这里应该验证回调签名和处理业务逻辑
      console.log('收到微信支付回调:', callbackData);

      return {
        success: true,
        message: 'SUCCESS'
      };
    } catch (error) {
      console.error('处理支付回调失败:', error);
      return {
        success: false,
        message: 'FAIL'
      };
    }
  }
}

module.exports = WechatPayService;

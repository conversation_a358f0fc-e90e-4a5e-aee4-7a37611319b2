/**
 * 订单退款控制器
 * 处理订单退款相关的控制逻辑
 */
const BaseController = require('../../../core/controllers/BaseController');
const OrderRefundService = require('../services/OrderRefundService');
const OrderModel = require('../models/OrderModel/OrderModel');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class OrderRefundController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.orderRefundService = new OrderRefundService(prisma);
  }

  /**
   * 检查并创建内置渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async ensureBuiltInChannel(req, res) {
    try {
      // 查找现有的内置渠道 - is_built_in为1(int2类型)
      const existingChannel = await this.prisma.channel.findFirst({
        where: {
          is_built_in: 1,
          deleted_at: null
        }
      });

      if (existingChannel) {
        console.log('找到现有内置渠道:', JSON.stringify(existingChannel, (key, value) => 
          typeof value === 'bigint' ? value.toString() : value, 2));
        
        return this.success(res, {
          found: true,
          channel: {
            id: existingChannel.id.toString(),
            name: existingChannel.name,
            isBuiltIn: existingChannel.is_built_in
          }
        }, '找到现有内置渠道');
      }

      // 如果没有内置渠道，创建一个 - is_built_in设置为1(int2类型)
      const channelId = generateSnowflakeId();
      const newChannel = await this.prisma.channel.create({
        data: {
          id: BigInt(channelId),
          name: '系统内置渠道',
          icon_url: null,
          is_built_in: 1, // 明确设置为整数1
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now()),
          created_by: req.user?.id ? BigInt(req.user.id) : null,
          updated_by: req.user?.id ? BigInt(req.user.id) : null
        }
      });

      console.log('成功创建内置渠道:', JSON.stringify(newChannel, (key, value) => 
        typeof value === 'bigint' ? value.toString() : value, 2));

      this.success(res, {
        found: false,
        created: true,
        channel: {
          id: newChannel.id.toString(),
          name: newChannel.name,
          isBuiltIn: newChannel.is_built_in
        }
      }, '成功创建内置渠道');

    } catch (error) {
      console.error('检查并创建内置渠道失败:', error);
      this.fail(res, '检查并创建内置渠道失败: ' + error.message, 500);
    }
  }

  /**
   * 调试订单渠道信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async debugOrderChannel(req, res) {
    try {
      const { orderId } = req.params;
      
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 获取订单信息（用于调试）
      const orderModel = new OrderModel(this.prisma);
      const order = await orderModel.getOrderById(orderId);
      
      if (!order) {
        return this.fail(res, '订单不存在', 404);
      }

      // 构造调试信息
      const debugInfo = {
        orderId: orderId,
        orderSn: order.id, // 使用订单ID作为订单编号
        hasChannel: !!order.channel,
        channelData: order.channel || null,
        channelId: order.channel_id || order.channelId || null,
        paymentStatus: order.payment_status || order.paymentStatus,
        paidAmount: order.paid_amount || order.paidAmount,
        paymentSn: order.payment_sn || order.paymentSn
      };

      // 如果没有直接的channel关联，尝试通过channel_id查询
      if (!order.channel && (order.channel_id || order.channelId)) {
        try {
          const channelId = order.channel_id || order.channelId;
          const channel = await this.prisma.channel.findFirst({
            where: {
              id: BigInt(channelId),
              deleted_at: null
            }
          });
          
          debugInfo.manualChannelQuery = {
            channelId: channelId,
            found: !!channel,
            channelData: channel || null
          };
          
        } catch (error) {
          debugInfo.manualChannelQueryError = error.message;
        }
      }

      // 获取所有渠道信息
      const allChannels = await this.prisma.channel.findMany({
        where: {
          deleted_at: null
        },
        select: {
          id: true,
          name: true,
          is_built_in: true
        }
      });

      debugInfo.allChannels = allChannels.map(ch => ({
        id: ch.id.toString(),
        name: ch.name,
        isBuiltIn: ch.is_built_in
      }));

      this.success(res, debugInfo, '订单渠道调试信息');
    } catch (error) {
      console.error('调试订单渠道失败:', error);
      this.fail(res, '调试订单渠道失败: ' + error.message, 500);
    }
  }

  /**
   * 确认订单退款
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async confirmOrderRefund(req, res) {
    try {
      const { orderId } = req.params;
      const { refundReason } = req.body;
      
      // 获取当前用户信息
      const currentUser = req.user || { id: '0', username: '系统', role: 'admin' };

      // 验证必填参数
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 调用服务层处理退款确认（退款金额将自动取订单的支付金额）
      const result = await this.orderRefundService.confirmOrderRefund(
        orderId,
        refundReason,
        currentUser
      );

      if (result.success) {
        this.success(res, result.data, '订单退款确认成功，订单已关闭');
      } else {
        this.fail(res, result.message, result.code || 400);
      }
    } catch (error) {
      console.error('确认订单退款失败:', error);
      this.fail(res, '确认订单退款失败: ' + error.message, 500);
    }
  }
}

module.exports = OrderRefundController; 
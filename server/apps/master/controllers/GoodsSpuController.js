/**
 * 商品SPU控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const GoodsSpuService = require('../services/GoodsSpuService');
const GoodsSpuDto = require('../dto/GoodsSpuDto');
const { goodsSpuCreateSchema, goodsSpuUpdateSchema } = require('../dto/GoodsSpuDto');

class GoodsSpuController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.goodsSpuService = new GoodsSpuService(prisma);
  }

  /**
   * 添加商品
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async addProduct(req, res) {
    try {
      // 验证请求数据
      const { error, value } = GoodsSpuDto.validateCreate(req.body);
      if (error) {
        console.error('商品数据验证失败:', JSON.stringify(error, null, 2));
        console.error('请求体:', JSON.stringify(req.body, null, 2));
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;

      // 调用服务添加商品，并传递用户ID
      const result = await this.goodsSpuService.addProduct(value, userId);

      // 返回成功响应
      this.success(res, result.data, '商品添加成功', 200);
    } catch (error) {
      // 记录错误日志
      console.error('添加商品失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }

      // 处理SKU编码重复错误
      if (error.message.includes('SKU编码') && (error.message.includes('已存在') || error.message.includes('已被其他商品使用') || error.message.includes('同一商品内SKU编码重复'))) {
        return res.status(200).json({
          code: 400,
          message: error.message,
          data: 400
        });
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 编辑商品
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateGoodsSpu(req, res) {
    try {
      const { id } = req.params;
      const productData = req.body;
      
      // 使用与添加商品相同的验证规则
      const { error, value } = GoodsSpuDto.validateCreate(productData);
      if (error) {
        console.error('商品数据验证失败:', JSON.stringify(error, null, 2));
        console.error('请求体:', JSON.stringify(productData, null, 2));
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }
      
      // 检查商品是否存在
      const existingSpu = await this.goodsSpuService.getGoodsSpuById(id);
      if (!existingSpu) {
        return this.fail(res, `ID为${id}的商品不存在`, 404);
      }
      
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许更新
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法更新商品', 401);
      }
      
      // 更新商品信息
      const result = await this.goodsSpuService.updateGoodsSpu(id, value, userId);
      
      if (result.success) {
        this.success(res, result.data, '商品更新成功');
      } else {
        this.fail(res, result.message || '商品更新失败', 500);
      }
    } catch (error) {
      console.error('更新商品时发生错误:', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }

      // 处理SKU编码重复错误
      if (error.message.includes('SKU编码') && (error.message.includes('已存在') || error.message.includes('已被其他商品使用') || error.message.includes('同一商品内SKU编码重复'))) {
        return res.status(200).json({
          code: 400,
          message: error.message,
          data: 400
        });
      }

      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取商品列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProductList(req, res) {
    try {
      // 验证请求参数
      const { error, value } = GoodsSpuDto.validateListQuery(req.query);
      if (error) {
        return this.fail(res, '请求参数验证失败', 400);
      }
      
      // 使用基类的分页方法
      const { page, pageSize, skip, take } = this.getPagination(req.query);
      
      // 将分页参数添加到查询参数中
      const options = {
        ...value,
        skip,
        take,
        page,
        pageSize
      };
      
      // 调用服务获取商品列表
      const result = await this.goodsSpuService.getProductList(options);
      
      if (result.success) {
        // 使用基类的标准列表响应方法
        this.successList(
          res,
          result.data.items,
          result.data.total,
          page,
          pageSize,
          '获取商品列表成功'
        );
      } else {
        this.fail(res, result.message || '获取商品列表失败', 500);
      }
    } catch (error) {
      console.error('获取商品列表失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 软删除商品（移至回收站）
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async softDeleteProduct(req, res) {
    try {
      const { id } = req.params;
      if (!id) {
        return this.fail(res, '商品ID不能为空', 400);
      }

      const result = await this.goodsSpuService.softDeleteProduct(id);
      
      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('软删除商品失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 恢复已删除的商品
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async restoreProduct(req, res) {
    try {
      const { id } = req.params;
      if (!id) {
        return this.fail(res, '商品ID不能为空', 400);
      }

      const result = await this.goodsSpuService.restoreProduct(id);
      
      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('恢复商品失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 永久删除商品
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async permanentDeleteProduct(req, res) {
    try {
      const { id } = req.params;
      const { reason } = req.body; // 从请求体中获取删除原因
      
      if (!id) {
        return this.fail(res, '商品ID不能为空', 400);
      }

      // 获取当前用户ID和IP地址
      const userId = req.user ? req.user.id : null;
      const ipAddress = req.ip || req.connection.remoteAddress;
      
      // 准备删除选项
      const deleteOptions = {
        reason: reason || '管理员手动删除',
        userId,
        ipAddress
      };
      
      const result = await this.goodsSpuService.permanentDeleteProduct(id, deleteOptions);
      
      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('永久删除商品失败:', error);
      
      // 修改错误处理逻辑
      let message = '永久删除商品失败';
      let code = 500;
      
      if (error.message) {
        message = error.message;
      }
      
      // 使用handleDbError处理数据库错误
      const dbError = this.handleDbError(error);
      console.log('handleDbError处理结果:', dbError);
      
      this.fail(res, dbError.message || message, dbError.code || code);
    }
  }

  /**
   * 获取商品详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProductDetail(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '商品ID不能为空', 400);
      }
      
      // 调用服务获取商品详情
      const result = await this.goodsSpuService.getProductDetail(id);
      
      if (result.success) {
        this.success(res, result.data, '获取商品详情成功');
      } else {
        this.fail(res, result.message || '商品不存在', 404);
      }
    } catch (error) {
      console.error('获取商品详情失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
  
  /**
   * 处理数据库错误
   * @param {Error} error - 数据库错误对象
   * @returns {Object} 标准化的错误信息
   */
  handleDbError(error) {
    // Prisma 错误处理
    if (error.code) {
      switch (error.code) {
        case 'P2002': // 唯一约束冲突
          return {
            message: '记录已存在',
            code: 400
          };
        case 'P2003': // 外键约束失败
          return {
            message: '关联记录不存在',
            code: 400
          };
        case 'P2025': // 记录不存在
          return {
            message: '记录不存在',
            code: 404
          };
        default:
          return {
            message: '数据库操作失败',
            code: 500
          };
      }
    }
    
    return {
      message: error.message || '未知错误',
      code: 500
    };
  }
}

module.exports = GoodsSpuController;

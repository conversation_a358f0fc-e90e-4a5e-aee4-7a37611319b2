const BaseController = require('../../../core/controllers/BaseController');
const GoodsServiceService = require('../services/GoodsServiceService');
const { snakeToCamel } = require('../../../shared/utils/format');

/**
 * 商品服务控制器
 */
class GoodsServiceController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.goodsServiceService = new GoodsServiceService(prisma);
  }

  /**
   * 获取所有商品服务
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getAllServices(req, res) {
    try {
      const { name, description, startTime, endTime } = req.query;
      // 使用基类的分页方法
      const { page, pageSize } = this.getPagination(req.query);
      
      const options = {
        name,
        description,
        startTime,
        endTime,
        page,
        pageSize
      };
      
      const result = await this.goodsServiceService.getAllServices(options);
      
      // 转换数据为驼峰格式
      const camelCaseItems = result.items.map(item => snakeToCamel(item));
      
      // 使用基类的标准列表响应方法
      this.successList(
        res,
        camelCaseItems,
        result.total,
        page,
        pageSize,
        '获取商品服务列表成功'
      );
    } catch (error) {
      console.error('获取商品服务列表失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 根据ID获取商品服务
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getServiceById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '服务ID不能为空', 400);
      }
      
      const service = await this.goodsServiceService.getServiceById(id);
      
      // 转换数据为驼峰格式
      const camelCaseService = snakeToCamel(service);
      
      this.success(res, camelCaseService, '获取服务详情成功');
    } catch (error) {
      console.error(`获取ID为${req.params.id}的服务失败:`, error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 创建商品服务
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async createService(req, res) {
    try {
      const serviceData = req.body;
      
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许创建
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法创建服务', 401);
      }
      
      const newService = await this.goodsServiceService.createService(serviceData, userId);
      
      this.success(res, newService, '服务创建成功', 200);
    } catch (error) {
      console.error('创建商品服务失败:', error);
      
      if (error.name === 'ZodError') {
        return this.fail(res, '数据验证失败', 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新商品服务
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateService(req, res) {
    try {
      const { id } = req.params;
      const serviceData = req.body;
      
      if (!id) {
        return this.fail(res, '服务ID不能为空', 400);
      }
      
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许更新
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法更新服务', 401);
      }
      
      const updatedService = await this.goodsServiceService.updateService(id, serviceData, userId);
      
      this.success(res, updatedService, '服务更新成功');
    } catch (error) {
      console.error(`更新ID为${req.params.id}的服务失败:`, error);
      
      if (error.name === 'ZodError') {
        return this.fail(res, '数据验证失败', 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除商品服务
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteService(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '服务ID不能为空', 400);
      }
      
      await this.goodsServiceService.deleteService(id);
      this.success(res, null, '服务删除成功');
    } catch (error) {
      console.error(`删除ID为${req.params.id}的服务失败:`, error);
      
      if (error.message.includes('关联商品')) {
        return this.fail(res, error.message, 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = GoodsServiceController;

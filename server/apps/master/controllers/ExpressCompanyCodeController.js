const BaseController = require('../../../core/controllers/BaseController');
const ExpressCompanyCodeService = require('../services/ExpressCompanyCodeService');
const ExpressCompanyCodeDto = require('../dto/ExpressCompanyCodeDto');

/**
 * 快递公司编码控制器
 */
class ExpressCompanyCodeController extends BaseController {
  
  /**
   * 构造函数
   */
  constructor() {
    super();
    this.expressCompanyCodeService = new ExpressCompanyCodeService();
  }

  /**
   * 获取快递公司编码列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getExpressCompanyCodes(req, res) {
    try {
      // 构建查询选项
      const options = {};
      
      // 判断是否需要获取全部数据
      if (req.query.getAllData === 'true') {
        options.getAllData = true;
      } else {
        // 分页参数
        // 兼容page/pageSize和skip/limit两种分页方式
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || parseInt(req.query.limit) || 10;
        
        // 将page/pageSize转换为skip/take格式
        options.skip = (page - 1) * pageSize;
        options.take = pageSize;
        
        // 记录原始分页参数以便后续使用
        options.page = page;
        options.pageSize = pageSize;
      }
      
      // 搜索条件
      if (req.query.company_name) {
        options.company_name = req.query.company_name;
      }
      
      if (req.query.company_code) {
        options.company_code = req.query.company_code;
      }
      
      if (req.query.company_type) {
        options.company_type = req.query.company_type;
      }
      
      // 时间范围
      if (req.query.startTime) {
        options.startTime = req.query.startTime;
      }
      
      if (req.query.endTime) {
        options.endTime = req.query.endTime;
      }
      
      // 验证查询参数
      const validation = ExpressCompanyCodeDto.validateQueryParams(options);
      if (!validation.isValid) {
        return this.fail(res, validation.errors.join(', '));
      }
      
      // 获取数据
      const result = await this.expressCompanyCodeService.getAllExpressCompanyCodes(options);
      
      // 返回成功响应
      if (options.getAllData) {
        // 如果获取全部数据，只返回数据列表
        this.success(res, result.data);
      } else {
        // 使用与订单列表相同的分页格式
        // 使用存储的原始分页参数
        const page = options.page || 1;
        const pageSize = options.pageSize || 10;
        
        this.success(res, {
          items: result.data,
          pageInfo: {
            total: result.total,
            currentPage: page,
            totalPage: Math.ceil(result.total / pageSize)
          }
        });
      }
    } catch (error) {
      console.error('获取快递公司编码列表失败:', error);
      this.fail(res, error.message || '获取快递公司编码列表失败');
    }
  }

  /**
   * 根据ID获取快递公司编码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getExpressCompanyCodeById(req, res) {
    try {
      const id = req.params.id;
      
      // 获取数据
      const expressCompanyCode = await this.expressCompanyCodeService.getExpressCompanyCodeById(id);
      
      // 返回成功响应
      this.success(res, expressCompanyCode);
    } catch (error) {
      console.error('获取快递公司编码详情失败:', error);
      this.fail(res, error.message || '获取快递公司编码详情失败');
    }
  }

  /**
   * 根据编码获取快递公司编码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getExpressCompanyCodeByCode(req, res) {
    try {
      const code = req.params.code;
      
      // 获取数据
      const expressCompanyCode = await this.expressCompanyCodeService.getExpressCompanyCodeByCode(code);
      
      // 返回成功响应
      this.success(res, expressCompanyCode);
    } catch (error) {
      console.error('获取快递公司编码详情失败:', error);
      this.fail(res, error.message || '获取快递公司编码详情失败');
    }
  }
}

module.exports = ExpressCompanyCodeController;

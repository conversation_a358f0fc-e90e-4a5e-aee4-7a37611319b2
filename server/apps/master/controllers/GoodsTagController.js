// @ts-ignore
const BaseController = require('../../../core/controllers/BaseController');
const GoodsTagService = require('../services/GoodsTagService');
const { snakeToCamel } = require('../../../shared/utils/format');

/**
 * 商品标签控制器
 */
class GoodsTagController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端
   */
  constructor(prisma) {
    super(prisma);
    this.goodsTagService = new GoodsTagService(prisma);
  }
  /**
   * 获取所有商品标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getAllTags(req, res) {
    try {
      const { name, is_enabled, description, startTime, endTime } = req.query;
      // 使用基类的分页方法
      const { page, pageSize } = this.getPagination(req.query);
      
      const options = {
        name,
        is_enabled,
        description,
        startTime,
        endTime,
        page,
        pageSize
      };
      
      const result = await this.goodsTagService.getAllTags(options);
      
      // 转换数据为驼峰格式
      const camelCaseItems = result.items.map(item => snakeToCamel(item));
      
      // 使用基类的标准列表响应方法
      this.successList(
        res,
        camelCaseItems,
        result.total,
        page,
        pageSize,
        '获取商品标签列表成功'
      );
    } catch (error) {
      console.error('获取商品标签列表失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 根据ID获取商品标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getTagById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '标签ID不能为空', 400);
      }
      
      const tag = await this.goodsTagService.getTagById(id);
      
      this.success(res, tag, '获取标签详情成功');
    } catch (error) {
      console.error(`获取ID为${req.params.id}的标签失败:`, error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 创建商品标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async createTag(req, res) {
    try {
      const tagData = req.body;
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许创建
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法创建标签', 401);
      }
      
      // 将驼峰命名转换为下划线命名
      const formattedData = {
        name: tagData.name,
        image_url: tagData.imageUrl, // 将 imageUrl 转换为 image_url
        description: tagData.description,
        sort_order: tagData.sortOrder,
        is_enabled: tagData.isEnabled
      };
      
      const newTag = await this.goodsTagService.createTag(formattedData, userId);
      
      this.success(res, newTag, '标签创建成功', 200);
    } catch (error) {
      console.error('创建商品标签失败:', error);
      
      if (error.name === 'ZodError') {
        return this.fail(res, '数据验证失败', 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新商品标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateTag(req, res) {
    try {
      const { id } = req.params;
      const tagData = req.body;
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许更新
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法更新标签', 401);
      }
      
      if (!id) {
        return this.fail(res, '标签ID不能为空', 400);
      }
      
      // 将驼峰命名转换为下划线命名
      const formattedData = {
        name: tagData.name,
        image_url: tagData.imageUrl, // 将 imageUrl 转换为 image_url
        description: tagData.description,
        sort_order: tagData.sortOrder,
        is_enabled: tagData.isEnabled
      };
      
      const updatedTag = await this.goodsTagService.updateTag(id, formattedData, userId);
      
      this.success(res, updatedTag, '标签更新成功');
    } catch (error) {
      console.error(`更新ID为${req.params.id}的标签失败:`, error);
      
      if (error.name === 'ZodError') {
        return this.fail(res, '数据验证失败', 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除商品标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteTag(req, res) {
    try {
      const { id } = req.params;
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许删除
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法删除标签', 401);
      }
      
      if (!id) {
        return this.fail(res, '标签ID不能为空', 400);
      }
      
      await this.goodsTagService.deleteTag(id, userId);
      
      this.success(res, null, '标签删除成功');
    } catch (error) {
      console.error(`删除ID为${req.params.id}的标签失败:`, error);
      
      if (error.message.includes('正在被使用')) {
        return this.fail(res, error.message, 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = GoodsTagController;

/**
 * 商品SKU控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const GoodsSkuModel = require('../models/GoodsSkuModel');
const Joi = require('joi');

class GoodsSkuController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
  }

  /**
   * 根据SKU ID数组批量获取商品信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSkusByIds(req, res) {
    try {
      // 验证请求数据
      const schema = Joi.object({
        skuIds: Joi.array().items(Joi.string().required()).min(1).required().description('商品SKU ID数组')
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      // 获取SKU信息
      const skuInfoList = await GoodsSkuModel.getSkusByIds(value.skuIds);
      
      // 返回成功响应
      this.success(res, skuInfoList, '批量获取商品信息成功');
    } catch (error) {
      console.error('批量获取商品信息失败:', error);
      
      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 处理数据库错误
   * @param {Error} error - 数据库错误对象
   * @returns {Object} 标准化的错误信息
   */
  handleDbError(error) {
    // Prisma 错误处理
    if (error.code) {
      switch (error.code) {
        case 'P2002': // 唯一约束冲突
          return {
            message: '记录已存在',
            code: 400
          };
        case 'P2003': // 外键约束失败
          return {
            message: '关联记录不存在',
            code: 400
          };
        case 'P2025': // 记录不存在
          return {
            message: '记录不存在',
            code: 404
          };
        default:
          return {
            message: '数据库操作失败',
            code: 500
          };
      }
    }
    
    return {
      message: error.message || '未知错误',
      code: 500
    };
  }
}

module.exports = GoodsSkuController;

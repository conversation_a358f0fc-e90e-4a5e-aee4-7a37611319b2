const BaseController = require('../../../core/controllers/BaseController');
const MallUserService = require('../services/MallUserService');

/**
 * 商城前端用户控制器
 */
class MallUserController extends BaseController {
  constructor(prisma) {
    super();
    this.mallUserService = new MallUserService(prisma);
  }

  /**
   * 获取验证码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getCaptcha(req, res) {
    try {
      const { phone } = req.query;
      
      // 验证手机号
      if (!phone) {
        return this.fail(res, '手机号不能为空', 400);
      }
      
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }
      
      // 模拟发送验证码
      // 实际项目中应该调用短信服务发送验证码
      // 这里暂时固定返回8888作为验证码
      const captcha = '8888';
      
      // 将验证码存储到数据库或缓存中，这里简化处理
      // 实际项目中应该将验证码和手机号关联存储，并设置过期时间
      
      return this.success(res, { captcha }, '验证码发送成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 用户注册
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async register(req, res) {
    try {
      // 验证必填字段
      const requiredFields = ['username', 'password', 'phone', 'captcha'];
      if (!this.validateFields(req.body, requiredFields)) {
        return this.fail(res, '用户名、密码、手机号和验证码不能为空', 400);
      }

      const { username, phone, captcha } = req.body;

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 验证验证码
      // 实际项目中应该从数据库或缓存中获取验证码进行比对
      // 这里暂时固定使用8888作为验证码
      if (captcha !== '8888') {
        return this.fail(res, '验证码不正确', 400);
      }

      // 检查用户名是否已存在
      const existUser = await this.mallUserService.findByUsername(username);
      if (existUser) {
        return this.fail(res, '用户名已存在', 400);
      }

      // 检查手机号是否已被注册
      const existPhone = await this.mallUserService.findByPhone(phone);
      if (existPhone) {
        return this.fail(res, '该手机号已被注册', 400);
      }

      // 创建用户
      const now = Date.now();
      const userData = {
        ...req.body,
        created_at: now,
        updated_at: now
      };

      const user = await this.mallUserService.create(userData);
      // 移除密码字段
      delete user.password;
      
      return this.success(res, user, '注册成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 用户登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async login(req, res) {
    try {
      const { username, password } = req.body;
      
      // 验证必填字段
      if (!username || !password) {
        return this.fail(res, '用户名和密码不能为空', 400);
      }

      // 验证用户
      const user = await this.mallUserService.verifyUser(username, password);
      if (!user) {
        return this.fail(res, '用户名或密码错误', 400);
      }

      // 检查用户状态
      if (user.status !== 1) {
        return this.fail(res, '账号已被禁用', 403);
      }

      // 更新登录信息
      const now = Date.now();
      const loginIp = req.ip || req.headers['x-forwarded-for'] || '';
      await this.mallUserService.updateLoginInfo(user.id, {
        last_login_ip: loginIp,
        last_login_time: now,
        login_count: (user.login_count || 0) + 1,
        updated_at: now
      });

      // 生成简单的会话标识，而不是 JWT
      const sessionId = `${user.id}_${now}_${Math.random().toString(36).substring(2, 15)}`;

      // 移除密码字段
      delete user.password;

      return this.success(res, { user, token: sessionId }, '登录成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取用户列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async list(req, res) {
    try {
      const { page = 1, pageSize = 10, ...query } = req.query;
      const result = await this.mallUserService.list({
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        ...query
      });

      return this.successList(res, result.items, result.total, result.currentPage, result.totalPage);
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取用户详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async detail(req, res) {
    try {
      const { id } = req.params;
      const user = await this.mallUserService.findById(BigInt(id));
      
      if (!user) {
        return this.fail(res, '用户不存在', 404);
      }

      // 移除密码字段
      delete user.password;
      
      return this.success(res, user);
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 更新用户状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;
      
      if (status === undefined) {
        return this.fail(res, '状态参数不能为空', 400);
      }

      const user = await this.mallUserService.findById(BigInt(id));
      if (!user) {
        return this.fail(res, '用户不存在', 404);
      }

      const now = Date.now();
      await this.mallUserService.update(BigInt(id), {
        status: parseInt(status),
        updated_at: now,
        updated_by: req.user?.id
      });
      
      return this.success(res, null, '更新状态成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 删除用户
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      const user = await this.mallUserService.findById(BigInt(id));
      
      if (!user) {
        return this.fail(res, '用户不存在', 404);
      }

      // 软删除
      const now = Date.now();
      await this.mallUserService.update(BigInt(id), {
        deleted_at: now,
        updated_at: now,
        updated_by: req.user?.id
      });
      
      return this.success(res, null, '删除成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
}

module.exports = MallUserController;

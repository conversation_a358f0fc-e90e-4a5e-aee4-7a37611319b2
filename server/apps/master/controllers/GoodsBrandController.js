const BaseController = require('../../../core/controllers/BaseController');
const GoodsBrandService = require('../services/GoodsBrandService');
const { snakeToCamel } = require('../../../shared/utils/format');

/**
 * 商品品牌控制器
 */
class GoodsBrandController extends BaseController {
  /**
   * 构造函数
   * @param {PrismaClient} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.goodsBrandService = new GoodsBrandService(prisma);
  }
  /**
   * 获取所有商品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getAllBrands(req, res) {
    try {
      const { 
        id,
        name, 
        description, 
        sortField, 
        sortOrder, 
        startTime, 
        endTime 
      } = req.query;
      // 使用基类的分页方法
      const { page, pageSize } = this.getPagination(req.query);
      
      const options = {
        id,
        name,
        description,
        sortField,
        sortOrder,
        startTime,
        endTime,
        page,
        pageSize
      };
      
      const result = await this.goodsBrandService.getAllBrands(options);
      
      // 转换数据为驼峰格式
      const camelCaseItems = result.items.map(item => snakeToCamel(item));
      
      // 使用基类的标准列表响应方法
      this.successList(
        res,
        camelCaseItems,
        result.total,
        page,
        pageSize,
        '获取商品品牌列表成功'
      );
    } catch (error) {
      console.error('获取商品品牌列表失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 根据ID获取商品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getBrandById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '品牌ID不能为空', 400);
      }
      
      const brand = await this.goodsBrandService.getBrandById(id);
      
      // 转换数据为驼峰格式
      const camelCaseBrand = snakeToCamel(brand);
      
      this.success(res, camelCaseBrand, '获取品牌详情成功');
    } catch (error) {
      console.error(`获取ID为${req.params.id}的品牌失败:`, error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 创建商品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async createBrand(req, res) {
    try {
      // 获取请求数据并转换字段名
      const brandData = req.body;
      
      // 将驼峰命名转换为下划线命名
      const formattedData = {
        name: brandData.name,
        logo_url: brandData.logoUrl, // 将 logoUrl 转换为 logo_url
        description: brandData.description
      };
      
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许创建
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法创建品牌', 401);
      }
      
      const newBrand = await this.goodsBrandService.createBrand(formattedData, userId);
      
      this.success(res, newBrand, '品牌创建成功', 200);
    } catch (error) {
      console.error('创建商品品牌失败:', error);
      
      if (error.name === 'ZodError') {
        return this.fail(res, '数据验证失败', 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新商品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateBrand(req, res) {
    try {
      const { id } = req.params;
      const brandData = req.body;
      
      if (!id) {
        return this.fail(res, '品牌ID不能为空', 400);
      }
      
      // 将驼峰命名转换为下划线命名
      const formattedData = {
        name: brandData.name,
        logo_url: brandData.logoUrl, // 将 logoUrl 转换为 logo_url
        description: brandData.description
      };
      
      // 从请求中获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      // 如果用户ID不存在，不允许更新
      if (!userId) {
        return this.fail(res, '未登录或用户信息不完整，无法更新品牌', 401);
      }
      
      const updatedBrand = await this.goodsBrandService.updateBrand(id, formattedData, userId);
      
      this.success(res, updatedBrand, '品牌更新成功');
    } catch (error) {
      console.error(`更新ID为${req.params.id}的品牌失败:`, error);
      
      if (error.name === 'ZodError') {
        return this.fail(res, '数据验证失败', 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除商品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteBrand(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '品牌ID不能为空', 400);
      }
      
      await this.goodsBrandService.deleteBrand(id);
      
      this.success(res, null, '品牌删除成功');
    } catch (error) {
      console.error(`删除ID为${req.params.id}的品牌失败:`, error);
      
      if (error.message.includes('有关联商品')) {
        return this.fail(res, error.message, 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = GoodsBrandController;

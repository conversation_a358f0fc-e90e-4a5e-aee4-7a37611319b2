/**
 * 自主品牌规则码控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const SelfBrandRuleCodeDto = require('../dto/SelfBrandRuleCodeDto');
const SelfBrandRuleCodeModel = require('../models/SelfBrandRuleCodeModel');

class SelfBrandRuleCodeController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super();
  }
  /**
   * 获取自主品牌规则码列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getSelfBrandRuleCodeList(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.query, SelfBrandRuleCodeDto.getListValidate());
      
      // 获取列表数据
      const { rows, total } = await SelfBrandRuleCodeModel.getSelfBrandRuleCodeList(params);
      
      // 返回成功响应
      return this.success(res, {
        rows,
        total,
        page: parseInt(params.page),
        limit: parseInt(params.limit)
      });
    } catch (error) {
      console.error('获取自主品牌规则码列表失败:', error);
      return this.error(res, error.message || '获取自主品牌规则码列表失败');
    }
  }

  /**
   * 获取自主品牌规则码详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getSelfBrandRuleCodeDetail(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.params, SelfBrandRuleCodeDto.getDetailValidate());
      
      // 获取详情数据
      const detail = await SelfBrandRuleCodeModel.getSelfBrandRuleCodeDetail(params.id);
      
      if (!detail) {
        return this.notFound(res, '自主品牌规则码不存在');
      }
      
      // 返回成功响应
      return this.success(res, detail);
    } catch (error) {
      console.error('获取自主品牌规则码详情失败:', error);
      return this.error(res, error.message || '获取自主品牌规则码详情失败');
    }
  }

  /**
   * 创建自主品牌规则码
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async createSelfBrandRuleCode(req, res) {
    try {
      // 参数验证
      const data = await this.validate(req.body, SelfBrandRuleCodeDto.createValidate());
      
      // 检查品牌名称是否已存在
      const existing = await SelfBrandRuleCodeModel.getSelfBrandRuleCodeByName(data.name);
      if (existing) {
        return this.badRequest(res, '该品牌名称已存在规则码');
      }
      
      // 创建自主品牌规则码
      const result = await SelfBrandRuleCodeModel.createSelfBrandRuleCode(data);
      
      // 返回成功响应
      return this.created(res, result);
    } catch (error) {
      console.error('创建自主品牌规则码失败:', error);
      return this.error(res, error.message || '创建自主品牌规则码失败');
    }
  }

  /**
   * 更新自主品牌规则码
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateSelfBrandRuleCode(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.params, SelfBrandRuleCodeDto.getDetailValidate());
      const data = await this.validate(req.body, SelfBrandRuleCodeDto.updateValidate());
      
      // 检查规则码是否存在
      const exists = await SelfBrandRuleCodeModel.getSelfBrandRuleCodeDetail(params.id);
      if (!exists) {
        return this.notFound(res, '自主品牌规则码不存在');
      }
      
      // 如果更新了品牌名称，检查是否与其他记录冲突
      if (data.name && data.name !== exists.name) {
        const existing = await SelfBrandRuleCodeModel.getSelfBrandRuleCodeByName(data.name);
        if (existing && existing.id !== params.id) {
          return this.badRequest(res, '该品牌名称已存在规则码');
        }
      }
      
      // 更新自主品牌规则码
      const result = await SelfBrandRuleCodeModel.updateSelfBrandRuleCode(params.id, data);
      
      // 返回成功响应
      return this.success(res, result);
    } catch (error) {
      console.error('更新自主品牌规则码失败:', error);
      return this.error(res, error.message || '更新自主品牌规则码失败');
    }
  }

  /**
   * 删除自主品牌规则码
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteSelfBrandRuleCode(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.params, SelfBrandRuleCodeDto.deleteValidate());
      
      // 检查规则码是否存在
      const exists = await SelfBrandRuleCodeModel.getSelfBrandRuleCodeDetail(params.id);
      if (!exists) {
        return this.notFound(res, '自主品牌规则码不存在');
      }
      
      // 删除自主品牌规则码
      await SelfBrandRuleCodeModel.deleteSelfBrandRuleCode(params.id);
      
      // 返回成功响应
      return this.success(res, { message: '删除自主品牌规则码成功' });
    } catch (error) {
      console.error('删除自主品牌规则码失败:', error);
      return this.error(res, error.message || '删除自主品牌规则码失败');
    }
  }
}

module.exports = SelfBrandRuleCodeController;

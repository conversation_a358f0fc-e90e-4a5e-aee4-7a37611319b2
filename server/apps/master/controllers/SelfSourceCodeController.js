/**
 * 自主品牌溯源码控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const SelfSourceCodeDto = require('../dto/SelfSourceCodeDto');
const SelfSourceCodeModel = require('../models/SelfSourceCodeModel');
const prismaManager = require('../../../core/prisma');

class SelfSourceCodeController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super(prismaManager.getClient('base'));
    this.selfSourceCodeModel = SelfSourceCodeModel;
  }
  
  /**
   * 将下划线命名转换为驼峰命名并进行数据格式处理
   * @param {Object|Array} data 需要转换的数据
   * @returns {Object|Array} 转换后的数据
   */
  _convertToCamelCase(data) {
    // 如果是数组，递归处理每个元素
    if (Array.isArray(data)) {
      return data.map(item => this._convertToCamelCase(item));
    }
    
    // 如果是Date对象，转换为毫秒时间戳
    if (data instanceof Date) {
      return data.getTime(); 
    }
    
    // 如果不是对象或为空，直接返回
    if (!data || typeof data !== 'object') {
      // 如果是BigInt类型，为了防止精度丢失，转换为字符串
      if (typeof data === 'bigint') {
        return data.toString();
      }
      return data;
    }
    
    // 递归处理对象
    const result = {};
    
    for (const key in data) {
      // 跳过原型链上的属性
      if (!Object.prototype.hasOwnProperty.call(data, key)) continue;
      
      // 将下划线命名转换为驼峰命名
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      
      // 获取字段值
      const value = data[key];
      
      // 判断是否为ID字段
      if ((key === 'id' || camelKey === 'id' || key === 'brand_id' || camelKey === 'brandId') && 
          (typeof value === 'bigint' || (typeof value === 'number' && value > Number.MAX_SAFE_INTEGER))) {
        // 将ID字段转换为字符串，防止精度丢失
        result[camelKey] = value.toString();
        continue;
      }
      
      // 判断是否为日期字段
      const isDateField = key.includes('date') || key.includes('time') || key.includes('_at');
      
      // 如果是日期字符串，转换为毫秒时间戳
      if (isDateField && typeof value === 'string' && value.includes('T')) {
        result[camelKey] = new Date(value).getTime();
      } else {
        // 递归处理嵌套对象或数组
        result[camelKey] = this._convertToCamelCase(value);
      }
    }
    
    return result;
  }

  /**
   * 将驼峰命名转换为下划线命名
   * @param {Object} data 需要转换的数据
   * @returns {Object} 转换后的数据
   */
  _convertToSnakeCase(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    // 如果是数组，递归处理每个元素
    if (Array.isArray(data)) {
      return data.map(item => this._convertToSnakeCase(item));
    }
    
    const result = {};
    
    for (const key in data) {
      // 跳过原型链上的属性
      if (!Object.prototype.hasOwnProperty.call(data, key)) continue;
      
      // 将驼峰命名转换为下划线命名
      const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      
      // 获取字段值
      const value = data[key];
      
      // 如果值是对象或数组，递归处理
      if (value !== null && typeof value === 'object') {
        result[snakeKey] = this._convertToSnakeCase(value);
      } else {
        result[snakeKey] = value;
      }
    }
    
    return result;
  }

  /**
   * 获取溯源码列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getSelfSourceCodeList(req, res) {
    try {
      // 打印请求信息，用于调试
      console.log('=== 溯源码列表接口调试信息 ===');
      console.log('请求方法:', req.method);
      console.log('完整URL:', req.originalUrl);
      console.log('请求头信息:', JSON.stringify(req.headers, null, 2));
      console.log('查询参数 (req.query):', JSON.stringify(req.query, null, 2));
      console.log('路径参数 (req.params):', JSON.stringify(req.params, null, 2));
      console.log('请求体 (req.body):', JSON.stringify(req.body, null, 2));
      
      // 打印查询参数的类型信息
      console.log('查询参数类型详情:');
      for (const key in req.query) {
        console.log(`- ${key}: ${typeof req.query[key]} = ${req.query[key]}`);
        if (key === 'brandId' || key === 'brand_id') {
          try {
            console.log(`  - 是否为数字: ${!isNaN(Number(req.query[key]))}`); 
            console.log(`  - 是否超过安全整数范围: ${BigInt(req.query[key]) > BigInt(Number.MAX_SAFE_INTEGER)}`);
            console.log(`  - 转换为BigInt后的值: ${BigInt(req.query[key])}`); 
          } catch (error) {
            console.error(`  - 转换参数时出错: ${error.message}`);
          }
        }
      }
      
      // 检查是否有brandId参数，如果有，则调用根据品牌ID查询的方法
      const brandId = req.query.brandId || req.query.brand_id;
      if (brandId) {
        console.log(`检测到brandId参数: ${brandId}，调用根据品牌ID查询的方法`);
        return this.getSourceCodesByBrandId(req, res);
      }
      
      // 参数验证
      const { error, value: params } = SelfSourceCodeDto.getListValidate().validate(req.query);
      if (error) {
        return this.fail(res, `参数验证失败: ${error.message}`);
      }
      
      // 转换驼峰命名为下划线命名
      const snakeCaseParams = this._convertToSnakeCase(params);
      
      // 处理分页参数，确保使用正确的limit和pageSize
      const limit = parseInt(params.pageSize || params.limit || 20);
      const page = parseInt(params.page || 1);
      
      // 将处理后的分页参数赋值回参数对象
      snakeCaseParams.limit = limit;
      snakeCaseParams.page = page;
      
      console.log('处理后的参数:', JSON.stringify(snakeCaseParams, null, 2));
      
      // 获取列表数据
      const { rows, total } = await this.selfSourceCodeModel.getSelfSourceCodeList(snakeCaseParams);
      
      // 将数据转换为驼峰命名格式
      const camelCaseRows = this._convertToCamelCase(rows);
      
      // 使用BaseController中的successList方法返回标准列表数据
      return this.successList(
        res,
        camelCaseRows,
        total,
        page,
        limit,
        '获取溯源码列表成功'
      );
    } catch (error) {
      console.error('获取溯源码列表失败:', error);
      return this.fail(res, error.message || '获取溯源码列表失败');
    }
  }

  /**
   * 获取溯源码详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getSelfSourceCodeDetail(req, res) {
    try {
      // 参数验证
      const { error, value: params } = SelfSourceCodeDto.getDetailValidate().validate(req.params);
      if (error) {
        return this.fail(res, `参数验证失败: ${error.message}`);
      }
      
      // 获取详情数据
      const detail = await this.selfSourceCodeModel.getSelfSourceCodeDetail(params.id);
      
      if (!detail) {
        return this.notFound(res, '溯源码不存在');
      }
      
      // 将数据转换为驼峰命名格式
      const camelCaseDetail = this._convertToCamelCase(detail);
      
      // 返回成功响应
      return this.success(res, camelCaseDetail, '获取溯源码详情成功');
    } catch (error) {
      console.error('获取溯源码详情失败:', error);
      return this.fail(res, error.message || '获取溯源码详情失败');
    }
  }

  /**
   * 生成溯源码
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async generateSelfSourceCodes(req, res) {
    try {
      // 参数验证
      const { error, value: data } = SelfSourceCodeDto.generateValidate().validate(req.body);
      if (error) {
        return this.fail(res, `参数验证失败: ${error.message}`);
      }
      
      // 处理驼峰和下划线命名
      const brandId = data.brandId || data.brand_id;
      const count = data.count || 1;
      const userId = req.user?.id || 0; // 获取当前登录用户ID，如果没有则使用0
      
      // 校验生成数量范围
      if (count <= 0 || count > 1000) {
        return this.fail(res, '生成数量必须在1-1000之间');
      }

      console.log(`开始生成溯源码，品牌ID: ${brandId}, 数量: ${count}, 创建者ID: ${userId}`);
      const startTime = Date.now();
      
      // 生成溯源码 - 传入创建人ID作为created_by和updated_by
      const sourceCodes = await this.selfSourceCodeModel.generateSelfSourceCodes(brandId, count, userId);
      
      const endTime = Date.now();
      const timeUsed = (endTime - startTime) / 1000;
      console.log(`溯源码生成完成，共生成 ${sourceCodes.length} 个溯源码，耗时 ${timeUsed} 秒`);
      
      // 将数据转换为驼峰命名格式
      const camelCaseSourceCodes = this._convertToCamelCase(sourceCodes);
      
      // 返回成功响应
      return this.success(res, camelCaseSourceCodes, `成功生成 ${sourceCodes.length} 个溯源码`);
    } catch (error) {
      console.error('生成溯源码失败:', error);
      
      // 根据错误类型返回不同的状态
      if (error.message.includes('自主品牌不存在')) {
        return this.notFound(res, error.message);
      } else if (error.message.includes('生成数量必须在') || error.message.includes('参数验证失败')) {
        return this.fail(res, error.message);
      } else if (error.message.includes('正在生成溯源码')) {
        return this.fail(res, error.message, 429); // Too Many Requests
      }
      
      return this.fail(res, error.message || '生成溯源码失败');
    }
  }

  /**
   * 根据自主品牌ID查询溯源码列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getSourceCodesByBrandId(req, res) {
    try {
      // 打印完整请求信息，用于调试
      console.log('=== 根据自主品牌ID查询溯源码调试信息 ===');
      console.log('请求方法:', req.method);
      console.log('完整URL:', req.originalUrl);
      console.log('请求头信息:', JSON.stringify(req.headers, null, 2));
      console.log('查询参数 (req.query):', JSON.stringify(req.query, null, 2));
      console.log('路径参数 (req.params):', JSON.stringify(req.params, null, 2));
      console.log('请求体 (req.body):', JSON.stringify(req.body, null, 2));
      
      // 分析URL中的查询参数
      console.log('原始URL解析:');
      const urlParts = req.originalUrl.split('?');
      if (urlParts.length > 1) {
        const queryString = urlParts[1];
        console.log('- 查询字符串:', queryString);
        
        // 手动解析查询参数
        const params = {};
        queryString.split('&').forEach(param => {
          const [key, value] = param.split('=');
          if (key && value) {
            params[key] = decodeURIComponent(value);
          }
        });
        console.log('- 手动解析的参数:', JSON.stringify(params, null, 2));
        
        // 如果手动解析到brandId，使用它
        if (params.brandId) {
          console.log(`- 从手动解析中找到brandId: ${params.brandId}`);
          req.query.brandId = params.brandId;
        }
      }
      
      // 从查询参数中获取数据，更加全面地检查可能的参数名称
      console.log('开始提取brandId参数...');
      console.log('- req.query.brandId =', req.query.brandId, typeof req.query.brandId);
      console.log('- req.query.brand_id =', req.query.brand_id, typeof req.query.brand_id);
      let brandId = req.query.brandId || req.query.brand_id || req.params.brandId;
      console.log(`提取到的brandId: ${brandId}, 类型: ${typeof brandId}`);
      
      // 如果还是找不到，尝试从路径中提取
      if (!brandId && req.originalUrl) {
        const match = req.originalUrl.match(/\/by-brand\/([^\/\?]+)/);
        if (match && match[1]) {
          brandId = match[1];
          console.log(`从路径中提取到brandId: ${brandId}`);
        }
      }
      
      // 检查brandId是否为空
      if (!brandId) {
        console.error('错误: 自主品牌ID为空');
        return this.fail(res, '自主品牌ID不能为空');
      }
      
      // 处理雪花ID（大数字）
      try {
        // 尝试将brandId转换为字符串，确保不会丢失精度
        if (typeof brandId === 'number' && brandId > Number.MAX_SAFE_INTEGER) {
          console.log(`警告: brandId ${brandId} 超出安全整数范围，转换为字符串`);
          brandId = brandId.toString();
        }
        
        // 如果是字符串类型的数字，确保其有效
        if (typeof brandId === 'string' && !/^\d+$/.test(brandId)) {
          console.log(`警告: brandId ${brandId} 不是有效的数字字符串`);
          return this.fail(res, '自主品牌ID格式不正确', 400);
        }
        
        console.log(`处理后的brandId: ${brandId}, 类型: ${typeof brandId}`);
      } catch (error) {
        console.error('处理brandId时出错:', error);
        return this.fail(res, '处理自主品牌ID时出错', 400);
      }
      
      const queryParams = {
        ...req.query,
        brandId
      };
      
      console.log('处理后的查询参数:', JSON.stringify(queryParams));
      
      // 参数验证
      const { error, value: params } = SelfSourceCodeDto.getByBrandIdValidate().validate(queryParams);
      if (error) {
        return this.fail(res, `参数验证失败: ${error.message}`);
      }

      // 检查品牌ID是否存在
      if (!params.brandId) {
        return this.fail(res, '自主品牌ID不能为空', 400);
      }
      
      // 转换驼峰命名为下划线命名
      const snakeCaseParams = this._convertToSnakeCase(params);
      
      // 处理分页参数，确保使用正确的limit和pageSize
      const limit = parseInt(params.pageSize || params.limit || 20);
      const page = parseInt(params.page || 1);
      
      // 将处理后的分页参数赋值回参数对象
      snakeCaseParams.limit = limit;
      snakeCaseParams.page = page;
      
      console.log(`根据自主品牌ID [${params.brandId}] 查询溯源码列表，参数:`, snakeCaseParams);
      
      // 获取列表数据
      const { rows, total } = await this.selfSourceCodeModel.getSourceCodesByBrandId(snakeCaseParams);
      
      // 将数据转换为驼峰命名格式
      const camelCaseRows = this._convertToCamelCase(rows);
      
      // 使用BaseController中的successList方法返回标准列表数据
      return this.successList(
        res,
        camelCaseRows,
        total,
        page,
        limit,
        '获取溯源码列表成功'
      );
    } catch (error) {
      console.error('获取溯源码列表失败:', error);
      
      // 根据错误类型返回不同的反馈
      if (error.message.includes('自主品牌ID不能为空')) {
        return this.fail(res, error.message, 400);
      }
      
      return this.fail(res, error.message || '获取溯源码列表失败');
    }
  }

  /**
   * 批量更新溯源码状态
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateSelfSourceCodesStatus(req, res) {
    try {
      // 参数验证
      const { error, value: data } = SelfSourceCodeDto.updateStatusValidate().validate(req.body);
      if (error) {
        return this.fail(res, `参数验证失败: ${error.message}`);
      }
      
      // 批量更新状态
      const result = await this.selfSourceCodeModel.updateSelfSourceCodesStatus(data.ids, data.status);
      
      // 返回成功响应
      return this.success(res, result, `成功更新 ${result.count} 个溯源码状态`);
    } catch (error) {
      console.error('更新溯源码状态失败:', error);
      return this.fail(res, error.message || '更新溯源码状态失败');
    }
  }

  /**
   * 批量删除溯源码
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteSelfSourceCodes(req, res) {
    try {
      // 从请求体获取ID数组
      const { ids } = req.body;
      
      if (!Array.isArray(ids) || ids.length === 0) {
        return this.fail(res, '请提供有效的溯源码ID数组');
      }
      
      // 批量删除
      const result = await this.selfSourceCodeModel.deleteSelfSourceCodes(ids);
      
      // 返回成功响应
      return this.success(res, result, `成功删除 ${result.count} 个溯源码`);
    } catch (error) {
      console.error('删除溯源码失败:', error);
      return this.fail(res, error.message || '删除溯源码失败');
    }
  }
}

module.exports = SelfSourceCodeController;

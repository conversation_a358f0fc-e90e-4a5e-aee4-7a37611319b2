const BaseController = require('../../../../../core/controllers/BaseController');
const ChannelService = require('../services/ChannelService');
const ChannelDto = require('../dto/ChannelDto');
const { prisma } = require('../../../../../core/database/prisma');
const PlatformModel = require('../../platform/models/PlatformModel');
const StoreModel = require('../../store/models/StoreModel');

/**
 * 渠道管理控制器
 * 处理渠道管理相关HTTP请求
 */
class ChannelController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super(prisma);
  }
  /**
   * 获取渠道列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getChannelList(req, res) {
    try {
      // 验证查询参数
      const { error, value } = ChannelDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 调用服务获取渠道列表
      const result = await ChannelService.getChannelList(value);
      
      // 使用successList方法返回带有success字段的响应格式
      return this.successList(
        res,
        result.data.list,
        result.data.pagination.total,
        result.data.pagination.page,
        result.data.pagination.pageSize,
        '获取渠道列表成功',
        200
      );
    } catch (error) {
      console.error('获取渠道列表失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 获取渠道详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getChannelById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '渠道ID不能为空', null, 400);
      }

      // 调用服务获取渠道详情
      const result = await ChannelService.getChannelById(id);
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '获取渠道详情成功', 200);
    } catch (error) {
      console.error('获取渠道详情失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 创建渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createChannel(req, res) {
    try {
      // 验证请求体
      const { error, value } = ChannelDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务创建渠道
      const result = await ChannelService.createChannel(value, userId);
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '创建渠道成功', 200);
    } catch (error) {
      console.error('创建渠道失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 更新渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateChannel(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '渠道ID不能为空', null, 400);
      }

      // 验证请求体
      const { error, value } = ChannelDto.validateUpdate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务更新渠道
      const result = await ChannelService.updateChannel(id, value, userId);
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '更新渠道成功', 200);
    } catch (error) {
      console.error('更新渠道失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 删除渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteChannel(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '渠道ID不能为空', null, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务删除渠道
      const result = await ChannelService.deleteChannel(id, userId);
      
      // 检查服务层返回的状态码
      if (result.code !== 200) {
        return this.fail(res, result.message, null, result.code);
      }
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '删除渠道成功', 200);
    } catch (error) {
      console.error('删除渠道失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 获取渠道-平台-店铺树状结构
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getChannelPlatformStoreTree(req, res) {
    try {
      // 获取所有可用渠道
      const channelResult = await ChannelService.getChannelList({
        page: 1,
        pageSize: 1000 // 设置较大的数值以获取所有渠道
      });
      
      const channels = channelResult.data.list;
      const tree = [];
      
      // 为每个渠道构建树结构
      for (const channel of channels) {
        // 获取该渠道下的所有平台
        const platforms = await PlatformModel.getPlatformsByChannelId(channel.id);
        
        const platformNodes = [];
        
        // 为每个平台构建子树结构
        for (const platform of platforms) {
          // 获取该平台下的所有店铺
          const stores = await StoreModel.getStoresByPlatformId(platform.id);
          
          const storeNodes = stores.map(store => ({
            id: store.id,
            name: store.name,
            code: store.code,
            type: 'store',
            status: store.status,
            parentId: platform.id
          }));
          
          platformNodes.push({
            id: platform.id,
            name: platform.name,
            code: platform.code,
            type: 'platform',
            status: platform.status,
            parentId: channel.id,
            children: storeNodes
          });
        }
        
        tree.push({
          id: channel.id,
          name: channel.name,
          type: 'channel',
          isBuiltIn: channel.is_built_in,
          children: platformNodes
        });
      }
      
      return this.success(res, tree, '获取渠道-平台-店铺树状结构成功', 200);
    } catch (error) {
      console.error('获取渠道-平台-店铺树状结构失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }
}

module.exports = ChannelController;

const Joi = require('joi');

/**
 * 渠道管理DTO
 * 用于验证渠道管理相关请求数据
 */
class ChannelDto {
  /**
   * 创建渠道验证
   * @param {Object} data - 请求数据
   * @returns {Object} - 验证结果
   */
  static validateCreate(data) {
    const schema = Joi.object({
      id: Joi.any().strip().optional().description('前端可传递但会被忽略的ID字段'),
      name: Joi.string().max(100).required().messages({
        'string.base': '渠道名称必须是字符串',
        'string.empty': '渠道名称不能为空',
        'string.max': '渠道名称最多100个字符',
        'any.required': '渠道名称是必填项'
      }),
      iconUrl: Joi.string().max(255).allow(null, '').messages({
        'string.base': '渠道图标URL必须是字符串',
        'string.max': '渠道图标URL最多255个字符'
      }),
      isBuiltIn: Joi.number().valid(0, 1).default(0).messages({
        'number.base': '是否内置必须是数字',
        'number.valid': '是否内置必须是0或1'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 更新渠道验证
   * @param {Object} data - 请求数据
   * @returns {Object} - 验证结果
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      id: Joi.any().strip().optional().description('前端可传递但会被忽略的ID字段'),
      name: Joi.string().max(100).messages({
        'string.base': '渠道名称必须是字符串',
        'string.empty': '渠道名称不能为空',
        'string.max': '渠道名称最多100个字符'
      }),
      iconUrl: Joi.string().max(255).allow(null, '').messages({
        'string.base': '渠道图标URL必须是字符串',
        'string.max': '渠道图标URL最多255个字符'
      }),
      isBuiltIn: Joi.number().valid(0, 1).messages({
        'number.base': '是否内置必须是数字',
        'number.valid': '是否内置必须是0或1'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 查询渠道列表验证
   * @param {Object} data - 请求数据
   * @returns {Object} - 验证结果
   */
  static validateQuery(data) {
    const schema = Joi.object({
      name: Joi.string().allow(null, '').messages({
        'string.base': '渠道名称必须是字符串'
      }),
      isBuiltIn: Joi.number().valid(0, 1).allow(null).messages({
        'number.base': '是否内置必须是数字',
        'number.valid': '是否内置必须是0或1'
      }),
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码最小为1'
      }),
      pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
        'number.base': '每页条数必须是数字',
        'number.integer': '每页条数必须是整数',
        'number.min': '每页条数最小为1',
        'number.max': '每页条数最大为100'
      }),
      startTime: Joi.number().allow(null).messages({
        'number.base': '开始时间必须是时间戳数字'
      }),
      endTime: Joi.number().allow(null).messages({
        'number.base': '结束时间必须是时间戳数字'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }
}

module.exports = ChannelDto;

const path = require('path');
const BaseModule = require('../../../core/module/BaseModule');

/**
 * 平台管理模块
 */
class PlatformManagementModule extends BaseModule {
  constructor(config, moduleManager) {
    super(config, moduleManager);
  }

  /**
   * 初始化路由
   */
  async initRoutes() {
    // 加载渠道管理路由
    const channelRoutes = require('./channel/routes/ChannelRoute');
    
    // 加载平台管理路由
    const platformRoutes = require('./platform/routes/PlatformRoute');
    
    // 加载店铺管理路由
    const storeRoutes = require('./store/routes/StoreRoute');
    
    // 注册渠道管理路由
    this.router.use('/channel', channelRoutes);
    
    // 注册平台管理路由
    this.router.use('/platform', platformRoutes);
    
    // 注册店铺管理路由
    this.router.use('/store', storeRoutes);
    
    console.log(`平台管理模块路由已初始化，前缀：${this.config.routes.prefix}`);
  }

  /**
   * 初始化 Swagger 文档
   */
  async initSwagger() {
    // 调用父类方法，利用基类中实现的动态扫描注册机制
    await super.initSwagger();
  }
}

module.exports = PlatformManagementModule;

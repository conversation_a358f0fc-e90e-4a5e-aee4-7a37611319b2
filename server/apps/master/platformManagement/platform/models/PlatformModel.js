const { v4: uuidv4 } = require('uuid');
const { prisma } = require('../../../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

/**
 * 平台管理模型
 * 处理平台管理相关数据库操作
 */
class PlatformModel {
  constructor() {
    this.prisma = prisma;
    this.tableName = 'platform';
    this.schema = 'base';
  }

  /**
   * 处理查询结果
   * 将BigInt转为字符串，避免JSON序列化问题
   * @param {Object|Array} result - 查询结果
   * @returns {Object|Array} - 处理后的结果
   */
  processResult(result) {
    if (!result) return result;
    
    // 处理原始数据
    
    const process = (item) => {
      // 创建一个新对象来存储处理后的数据
      const processed = {};
      
      // 处理所有字段，包括 BigInt 类型转换
      for (const key in item) {
        if (typeof item[key] === 'bigint') {
          // 将 BigInt 转换为字符串而不是 Number，避免精度丢失
          processed[key] = item[key].toString();
        } else if (key === 'spider_config' && typeof item[key] === 'string') {
          try {
            processed[key] = JSON.parse(item[key]);
          } catch (e) {
            processed[key] = item[key];
          }
        } else {
          processed[key] = item[key];
        }
      }
      
      return processed;
    };
    
    // 处理数组或单个对象
    if (Array.isArray(result)) {
      return result.map(process);
    } else {
      return process(result);
    }
  }

  /**
   * 获取平台列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} - 平台列表
   */
  async getPlatformList(params) {
    const {
      page = 1,
      pageSize = 10,
      name = '',
      code = '',
      status = null,
      channelId = null
    } = params;
    
    const offset = (page - 1) * pageSize;
    
    // 构建 WHERE 子句和查询参数
    let whereConditions = ['p.deleted_at IS NULL'];
    const queryParams = [];
    
    if (name) {
      whereConditions.push(`p.name LIKE $${queryParams.length + 1}`);
      queryParams.push(`%${name}%`);
    }
    
    if (code) {
      whereConditions.push(`p.code LIKE $${queryParams.length + 1}`);
      queryParams.push(`%${code}%`);
    }
    
    if (status !== null) {
      whereConditions.push(`p.status = $${queryParams.length + 1}`);
      queryParams.push(status);
    }
    
    if (channelId) {
      whereConditions.push(`p.channel_id = $${queryParams.length + 1}`);
      queryParams.push(BigInt(channelId));
    }
    
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM "${this.schema}"."${this.tableName}" p 
      ${whereClause}
    `;
    
    const countResult = await this.prisma.$queryRawUnsafe(countQuery, ...queryParams);
    const total = parseInt(countResult[0].total);
    
    // 获取分页数据
    const dataQuery = `
      SELECT 
        p.id, 
        p.name, 
        p.code, 
        p.channel_id, 
        COALESCE(p.spider_config, '{}'::jsonb) as spider_config, 
        p.status, 
        p.login_url,
        COALESCE(p.created_at, 0) as created_at, 
        COALESCE(p.updated_at, 0) as updated_at, 
        p.deleted_at, 
        p.created_by, 
        p.updated_by,
        c.name as channel_name
      FROM "${this.schema}"."${this.tableName}" p
      LEFT JOIN "${this.schema}"."channel" c ON p.channel_id = c.id
      ${whereClause} 
      ORDER BY p.created_at DESC 
      LIMIT ${pageSize} OFFSET ${offset}
    `;
    
    const platforms = await this.prisma.$queryRawUnsafe(dataQuery, ...queryParams);
    
    return {
      list: this.processResult(platforms),
      pagination: {
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * 根据ID获取平台
   * @param {string} id - 平台ID
   * @returns {Promise<Object>} - 平台信息
   */
  /**
   * 根据ID获取平台信息
   * @param {string|BigInt} id - 平台ID
   * @returns {Promise<Object|null>} - 平台信息或null
   */
  async getPlatformById(id) {
    try {
      // 确保ID为BigInt类型
      const bigIntId = BigInt(id);
      
      // 查询平台信息，同时获取关联的渠道名称
      const query = `
        SELECT p.*, c.name as channel_name 
        FROM "${this.schema}"."${this.tableName}" p
        LEFT JOIN "${this.schema}"."channel" c ON p.channel_id = c.id
        WHERE p.id = $1 AND p.deleted_at IS NULL
      `;
      
      const result = await this.prisma.$queryRawUnsafe(query, bigIntId);
      
      if (result && result.length > 0) {
        return this.processResult(result[0]);
      }
      
      return null;
    } catch (error) {
      console.error('获取平台信息失败:', error.message);
      return null;
    }
  }

  /**
   * 创建平台
   * @param {Object} data - 平台数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建的平台
   */
  async createPlatform(data, userId) {
    const id = generateSnowflakeId();
    const now = BigInt(Date.now());
    
    const query = `
      INSERT INTO "${this.schema}"."${this.tableName}" (
        "id", "name", "code", "channel_id", "spider_config", "status", "login_url",
        "created_at", "updated_at", "created_by", "updated_by"
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;
    
    const result = await this.prisma.$queryRawUnsafe(
      query,
      id,
      data.name,
      data.code,
      BigInt(data.channelId),
      data.spiderConfig || {},
      data.status || 1,
      data.loginUrl || null,
      now,
      now,
      BigInt(userId),
      BigInt(userId)
    );
    
    return this.processResult(result[0]);
  }

  /**
   * 更新平台
   * @param {string} id - 平台ID
   * @param {Object} data - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新后的平台
   */
  async updatePlatform(id, data, userId) {
    const now = BigInt(Date.now());
    // 确保 id 和 userId 是 BigInt 类型
    const bigIntId = BigInt(id);
    const bigIntUserId = userId ? BigInt(userId) : null;
    
    // 构建更新字段和参数
    let updateFields = ['"updated_at" = $1', '"updated_by" = $2'];
    const queryParams = [now, bigIntUserId];
    let paramIndex = 3;
    
    if (data.name !== undefined) {
      updateFields.push(`"name" = $${paramIndex}`);
      queryParams.push(data.name);
      paramIndex++;
    }
    
    if (data.code !== undefined) {
      updateFields.push(`"code" = $${paramIndex}`);
      queryParams.push(data.code);
      paramIndex++;
    }
    
    if (data.channelId !== undefined) {
      updateFields.push(`"channel_id" = $${paramIndex}`);
      queryParams.push(BigInt(data.channelId));
      paramIndex++;
    }
    
    if (data.spiderConfig !== undefined) {
      updateFields.push(`"spider_config" = $${paramIndex}`);
      queryParams.push(data.spiderConfig);
      paramIndex++;
    }
    
    if (data.status !== undefined) {
      updateFields.push(`"status" = $${paramIndex}`);
      queryParams.push(data.status);
      paramIndex++;
    }
    
    if (data.loginUrl !== undefined) {
      updateFields.push(`"login_url" = $${paramIndex}`);
      queryParams.push(data.loginUrl);
      paramIndex++;
    }
    
    // 添加ID作为最后一个参数
    queryParams.push(bigIntId);
    
    const query = `
      UPDATE "${this.schema}"."${this.tableName}"
      SET ${updateFields.join(', ')}
      WHERE "id" = $${paramIndex} AND "deleted_at" IS NULL
      RETURNING *
    `;
    
    const result = await this.prisma.$queryRawUnsafe(query, ...queryParams);
    return this.processResult(result[0]);
  }

  /**
   * 删除平台
   * @param {string} id - 平台ID
   * @param {BigInt} userId - 删除者ID
   * @returns {Promise<Object>} - 删除结果
   */
  /**
   * 删除平台（软删除）
   * @param {string|BigInt} id - 平台ID
   * @param {string|BigInt} userId - 操作用户ID
   * @returns {Promise<Object|null>} - 删除后的平台信息或null
   */
  async deletePlatform(id, userId) {
    try {
      const now = BigInt(Date.now());
      
      // 确保 id 和 userId 是 BigInt 类型
      const bigIntId = BigInt(id);
      const bigIntUserId = BigInt(userId);
      
      // 使用原始 SQL 查询进行软删除
      const query = `
        UPDATE "${this.schema}"."${this.tableName}"
        SET "deleted_at" = $1, "updated_at" = $2, "updated_by" = $3
        WHERE "id" = $4 AND "deleted_at" IS NULL
        RETURNING *
      `;
      
      const result = await this.prisma.$queryRawUnsafe(
        query, 
        now, 
        now, 
        bigIntUserId, 
        bigIntId
      );
      
      if (result && result.length > 0) {
        return this.processResult(result[0]);
      }
      
      return null;
    } catch (error) {
      console.error('删除平台失败:', error.message);
      return null;
    }
  }

  /**
   * 检查平台名称是否存在
   * @param {string} name - 平台名称
   * @param {string} excludeId - 排除的平台ID（用于更新时检查）
   * @returns {Promise<boolean>} - 是否存在
   */
  async checkNameExists(name, excludeId = null) {
    let query = `
      SELECT COUNT(*) as count 
      FROM "${this.schema}"."${this.tableName}" 
      WHERE "name" = $1 
      AND "deleted_at" IS NULL
    `;
    
    let params = [name];
    
    if (excludeId) {
      query += ` AND "id" != $2`;
      // 确保 excludeId 是 BigInt 类型
      params.push(BigInt(excludeId));
    }
    
    const count = await this.prisma.$queryRawUnsafe(query, ...params);
    return parseInt(count[0].count) > 0;
  }

  /**
   * 检查平台代码是否存在
   * @param {string} code - 平台代码
   * @param {string} excludeId - 排除的平台ID（用于更新时检查）
   * @returns {Promise<boolean>} - 是否存在
   */
  async checkCodeExists(code, excludeId = null) {
    let query = `
      SELECT COUNT(*) as count 
      FROM "${this.schema}"."${this.tableName}" 
      WHERE "code" = $1 
      AND "deleted_at" IS NULL
    `;
    
    let params = [code];
    
    if (excludeId) {
      query += ` AND "id" != $2`;
      // 确保 excludeId 是 BigInt 类型
      params.push(BigInt(excludeId));
    }
    
    const count = await this.prisma.$queryRawUnsafe(query, ...params);
    return parseInt(count[0].count) > 0;
  }

  /**
   * 检查渠道是否被平台使用
   * @param {string} channelId - 渠道ID
   * @returns {Promise<boolean>} - 是否被使用
   */
  async checkChannelInUse(channelId) {
    try {
      const bigIntId = BigInt(channelId);
      
      const query = `
        SELECT COUNT(*) as count
        FROM "${this.schema}"."${this.tableName}"
        WHERE channel_id = $1 AND deleted_at IS NULL
      `;
      
      const result = await this.prisma.$queryRawUnsafe(query, bigIntId);
      return parseInt(result[0].count) > 0;
    } catch (error) {
      console.error('检查渠道使用状态失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取使用指定渠道的平台列表
   * @param {string} channelId - 渠道ID
   * @returns {Promise<Array>} - 平台列表
   */
  async getPlatformsByChannelId(channelId) {
    try {
      const bigIntId = BigInt(channelId);
      
      const query = `
        SELECT id, name, code
        FROM "${this.schema}"."${this.tableName}"
        WHERE channel_id = $1 AND deleted_at IS NULL
      `;
      
      const result = await this.prisma.$queryRawUnsafe(query, bigIntId);
      return this.processResult(result);
    } catch (error) {
      console.error('获取渠道关联平台失败:', error);
      throw error;
    }
  }
}

module.exports = new PlatformModel();

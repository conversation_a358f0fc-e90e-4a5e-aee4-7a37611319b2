/**
 * 平台管理模块入口文件
 */
const BaseModule = require('../../../../core/module/BaseModule');
const PlatformRoute = require('./routes/PlatformRoute');

/**
 * 平台管理模块
 */
class PlatformModule extends BaseModule {
  /**
   * 构造函数
   * @param {Object} config - 模块配置
   * @param {Object} moduleManager - 模块管理器
   */
  constructor(config, moduleManager) {
    super(config, moduleManager);
  }

  /**
   * 初始化路由
   */
  async initRoutes() {
    // 注册平台管理路由
    this.router.use('/', PlatformRoute);
    
    console.log(`平台管理模块路由已初始化，前缀：${this.config.routes.prefix}`);
  }

  /**
   * 初始化 Swagger 文档
   */
  async initSwagger() {
    // 调用父类方法，利用基类中实现的动态扫描注册机制
    await super.initSwagger();
  }
}

module.exports = PlatformModule;

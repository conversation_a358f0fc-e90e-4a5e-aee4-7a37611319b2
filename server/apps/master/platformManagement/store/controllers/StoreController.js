const BaseController = require('../../../../../core/controllers/BaseController');
const StoreService = require('../services/StoreService');
const StoreDto = require('../dto/StoreDto');
const { prisma } = require('../../../../../core/database/prisma');

/**
 * 店铺管理控制器
 * 处理店铺管理相关HTTP请求
 */
class StoreController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super(prisma);
    
    // 创建StoreDto实例
    this.storeDto = new StoreDto();
    
    // 绑定所有方法的this指向
    this.getStoreList = this.getStoreList.bind(this);
    this.getStoreById = this.getStoreById.bind(this);
    this.createStore = this.createStore.bind(this);
    this.updateStore = this.updateStore.bind(this);
    this.deleteStore = this.deleteStore.bind(this);
    this.getStoresByPlatformId = this.getStoresByPlatformId.bind(this);
  }
  /**
   * 获取店铺列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getStoreList(req, res) {
    try {
      // 验证查询参数
      const { error, value } = this.storeDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 调用服务获取店铺列表
      const result = await StoreService.getStoreList(value);
      
      // 直接返回服务层的响应结果
      return this.success(
        res,
        result.data,
        '获取店铺列表成功',
        200
      );
    } catch (error) {
      console.error('获取店铺列表失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 获取店铺详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getStoreById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '店铺ID不能为空', null, 400);
      }

      // 调用服务获取店铺详情
      const result = await StoreService.getStoreById(id);
      
      // 检查服务层返回的状态码
      if (result.code !== 200) {
        return this.fail(res, result.message, null, result.code);
      }
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '获取店铺详情成功', 200);
    } catch (error) {
      console.error('获取店铺详情失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 创建店铺
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createStore(req, res) {
    try {
      // 验证请求体
      const { error, value } = this.storeDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务创建店铺
      const result = await StoreService.createStore(value, userId);
      
      // 检查服务层返回的状态码
      if (result.code !== 200) {
        return this.fail(res, result.message, null, result.code);
      }
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '创建店铺成功', 200);
    } catch (error) {
      console.error('创建店铺失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 更新店铺
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateStore(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '店铺ID不能为空', null, 400);
      }

      // 验证请求体
      const { error, value } = this.storeDto.validateUpdate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务更新店铺
      const result = await StoreService.updateStore(id, value, userId);
      
      // 检查服务层返回的状态码
      if (result.code !== 200) {
        return this.fail(res, result.message, null, result.code);
      }
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '更新店铺成功', 200);
    } catch (error) {
      console.error('更新店铺失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 删除店铺
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteStore(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '店铺ID不能为空', null, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务删除店铺
      const result = await StoreService.deleteStore(id, userId);
      
      // 检查服务层返回的状态码
      if (result.code !== 200) {
        return this.fail(res, result.message, null, result.code);
      }
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, null, '删除店铺成功', 200);
    } catch (error) {
      console.error('删除店铺失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 获取指定平台下的所有店铺
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getStoresByPlatformId(req, res) {
    try {
      const { platformId } = req.params;
      
      if (!platformId) {
        return this.fail(res, '平台ID不能为空', null, 400);
      }

      // 调用服务获取平台下的店铺列表
      const result = await StoreService.getStoresByPlatformId(platformId);
      
      // 检查服务层返回的状态码
      if (result.code !== 200) {
        return this.fail(res, result.message, null, result.code);
      }
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '获取平台下店铺列表成功', 200);
    } catch (error) {
      console.error('获取平台下店铺列表失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }
}

module.exports = new StoreController();

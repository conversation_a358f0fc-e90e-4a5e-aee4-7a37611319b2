const StoreModel = require('../models/StoreModel');
const SpiderTaskModel = require('../../../../spider/models/SpiderTaskModel');
const SpiderModel = require('../../../../spider/models/SpiderModel');
const scheduleManager = require('../../../../spider/services/ScheduleManagerService');

/**
 * 店铺管理服务
 * 处理店铺管理相关业务逻辑
 */
class StoreService {
  /**
   * 获取店铺列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 店铺列表及分页信息
   */
  async getStoreList(params) {
    try {
      const result = await StoreModel.getStoreList(params);
      
      // 转换字段名为驼峰命名
      const items = result.list.map(item => {
        // 直接返回所有字段，不做多余的非空判断
        const store = {
          id: item.id,
          name: item.name,
          code: item.code,
          platformId: item.platformId || item.platform_id?.toString(),
          platformName: item.platformName || item.platform_name || null,
          channelId: item.channelId || item.channel_id?.toString() || null, // 确保始终有值
          channelName: item.channelName || item.channel_name || null,
          accountConfig: item.accountConfig || item.account_config || {},
          cookies: item.cookies || null, // 添加 cookies 字段
          status: item.status,
          remark: item.remark || null, // 添加 remark 字段
          principalName: item.principalName || item.principal_name || null, // 添加 principal_name 字段
          invoiceTitle: item.invoiceTitle || item.invoice_title || null, // 添加 invoice_title 字段
          isAutoSync: item.isAutoSync || item.is_auto_sync || false, // 添加 is_auto_sync 字段
          createdAt: item.createdAt || item.created_at || null,
          updatedAt: item.updatedAt || item.updated_at || null,
          createdBy: item.createdBy || item.created_by?.toString(),
          updatedBy: item.updatedBy || item.updated_by?.toString(),
          deletedAt: item.deletedAt || item.deleted_at || null
        };
        
        return store;
      });

      const response = {
        code: 200,
        message: '获取店铺列表成功',
        data: {
          items,
          pageInfo: {
            total: result.pagination.total,
            currentPage: result.pagination.page,
            pageSize: result.pagination.pageSize,
            totalPage: result.pagination.totalPages || Math.ceil(result.pagination.total / result.pagination.pageSize)
          }
        }
      };
      
      return response;
    } catch (error) {
      console.error('获取店铺列表失败:', error);
      return {
        code: 500,
        message: '获取店铺列表失败',
        error: error.message
      };
    }
  }

  /**
   * 获取店铺详情
   * @param {string} id - 店铺ID
   * @returns {Promise<Object>} - 店铺详情
   */
  async getStoreById(id) {
    try {
      const result = await StoreModel.getStoreById(id);
      
      if (!result) {
        return {
          code: 404,
          message: '店铺不存在',
          data: null
        };
      }
      
      // 获取关联的爬虫任务
      const spiderTasks = await SpiderTaskModel.findByStoreId(id);
      const formattedTasks = spiderTasks.map(task => ({
        id: task.id.toString(),
        spiderId: task.spider_id.toString(),
        platformId: task.platform_id.toString(),
        storeId: task.store_id.toString(),
        spiderType: task.spider_type,
        cronExpression: task.cron_expression,
        runInterval: task.run_interval,
        lastRunTime: task.last_run_time ? Number(task.last_run_time) : null,
        nextRunTime: task.next_run_time ? Number(task.next_run_time) : null,
        status: task.status,
        createdAt: task.created_at ? Number(task.created_at) : null,
        updatedAt: task.updated_at ? Number(task.updated_at) : null
      }));
      
      // 获取关联的爬虫列表（根据平台ID）
      let spiders = [];
      if (result.platform_id) {
        try {
          const spiderResult = await SpiderModel.findAll({
            platform_id: result.platform_id.toString(),
            page: 1,
            pageSize: 100 // 设置一个较大的值，确保获取所有爬虫
          });
          
          if (spiderResult && spiderResult.items) {
            spiders = spiderResult.items.map(spider => ({
              id: spider.id.toString(),
              platformId: spider.platform_id.toString(),
              name: spider.name,
              code: spider.code,
              spiderType: spider.spider_type,
              remark: spider.remark,
              status: spider.status,
              createdAt: spider.created_at ? Number(spider.created_at) : null,
              updatedAt: spider.updated_at ? Number(spider.updated_at) : null
            }));
          }
        } catch (error) {
          console.error('获取平台关联爬虫失败:', error);
          // 获取爬虫失败不影响整体接口返回
          spiders = [];
        }
      }
      
      // 转换字段名为驼峰命名
      const store = {
        id: result.id,
        name: result.name,
        code: result.code,
        platformId: result.platformId || result.platform_id?.toString(),
        platformName: result.platformName || result.platform_name || null,
        channelId: result.channelId || result.channel_id?.toString() || null, // 确保始终有值
        channelName: result.channelName || result.channel_name || null,
        accountConfig: result.accountConfig || result.account_config || {},
        cookies: result.cookies || null, // 添加 cookies 字段
        status: result.status,
        remark: result.remark || null, // 添加 remark 字段
        principalName: result.principalName || result.principal_name || null, // 添加 principal_name 字段
        invoiceTitle: result.invoiceTitle || result.invoice_title || null, // 添加 invoice_title 字段
        isAutoSync: result.isAutoSync || result.is_auto_sync || false, // 添加 is_auto_sync 字段
        createdAt: result.createdAt || (result.created_at ? Number(result.created_at) : null),
        updatedAt: result.updatedAt || (result.updated_at ? Number(result.updated_at) : null),
        createdBy: result.createdBy || result.created_by?.toString(),
        updatedBy: result.updatedBy || result.updated_by?.toString(),
        deletedAt: result.deletedAt || (result.deleted_at ? Number(result.deleted_at) : null),
        spiderTasks: formattedTasks,
        spiders: spiders // 添加关联的爬虫列表
      };
      
      const response = {
        code: 200,
        message: '获取店铺详情成功',
        data: store
      };
      
      return response;
    } catch (error) {
      console.error('获取店铺详情失败:', error);
      return {
        code: 500,
        message: '获取店铺详情失败',
        error: error.message
      };
    }
  }

  /**
   * 创建店铺
   * @param {Object} data - 店铺数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createStore(data, userId) {
    try {
      // 检查店铺名称是否已存在
      const nameExists = await StoreModel.checkNameExists(data.name);
      if (nameExists) {
        return {
          code: 400,
          message: '店铺名称已存在'
        };
      }

      // 检查店铺代码是否已存在
      const codeExists = await StoreModel.checkCodeExists(data.code);
      if (codeExists) {
        return {
          code: 400,
          message: '店铺代码已存在'
        };
      }

      // 创建店铺
      const createdStore = await StoreModel.createStore(data, userId);
      
      // 获取创建后的完整店铺数据（包含 platform_name 和 channel_name）
      const store = await StoreModel.getStoreById(createdStore.id);
      
      // 转换字段名为驼峰命名
      const responseData = {
        id: store.id,
        name: store.name,
        code: store.code,
        platformId: store.platformId || store.platform_id?.toString(),
        platformName: store.platformName || store.platform_name || null,
        channelId: store.channelId || store.channel_id?.toString(),
        channelName: store.channelName || store.channel_name || null,
        accountConfig: store.accountConfig || store.account_config || {},
        cookies: store.cookies || null, // 添加 cookies 字段
        status: store.status,
        remark: store.remark || null, // 添加 remark 字段
        principalName: store.principalName || store.principal_name || null, // 添加 principal_name 字段
        invoiceTitle: store.invoiceTitle || store.invoice_title || null, // 添加 invoice_title 字段
        isAutoSync: store.isAutoSync || store.is_auto_sync || false, // 添加 is_auto_sync 字段
        createdAt: store.createdAt || (store.created_at ? Number(store.created_at) : null),
        updatedAt: store.updatedAt || (store.updated_at ? Number(store.updated_at) : null),
        createdBy: store.createdBy || store.created_by?.toString(),
        updatedBy: store.updatedBy || store.updated_by?.toString(),
        deletedAt: store.deletedAt || (store.deleted_at ? Number(store.deleted_at) : null)
      };
      
      const response = {
        code: 201,
        message: '创建店铺成功',
        data: responseData
      };
      
      return response;
    } catch (error) {
      console.error('创建店铺失败:', error);
      return {
        code: 500,
        message: '创建店铺失败',
        error: error.message
      };
    }
  }

  /**
   * 更新店铺
   * @param {string} id - 店铺ID
   * @param {Object} data - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新结果
   */
  async updateStore(id, data, userId) {
    try {
      // 检查店铺是否存在
      const store = await StoreModel.getStoreById(id);
      if (!store) {
        return {
          code: 404,
          message: '店铺不存在'
        };
      }

      // 如果更新名称，检查名称是否已存在
      if (data.name && data.name !== store.name) {
        const nameExists = await StoreModel.checkNameExists(data.name, id);
        if (nameExists) {
          return {
            code: 400,
            message: '店铺名称已存在'
          };
        }
      }

      // 如果更新代码，检查代码是否已存在
      if (data.code && data.code !== store.code) {
        const codeExists = await StoreModel.checkCodeExists(data.code, id);
        if (codeExists) {
          return {
            code: 400,
            message: '店铺代码已存在'
          };
        }
      }
      console.log('更新店铺数据:', data);

      // 检查是否更新了自动同步状态或店铺状态
      const isAutoSyncChanged = data.is_auto_sync !== undefined && data.is_auto_sync !== store.is_auto_sync;
      const isStatusChanged = data.status !== undefined && data.status !== store.status;

      // 更新店铺
      await StoreModel.updateStore(id, data, userId);

      // 获取更新后的完整店铺数据
      const updatedStore = await StoreModel.getStoreById(id);

      // 如果自动同步状态或店铺状态发生变化，同步调度器
      if (isAutoSyncChanged || isStatusChanged) {
        try {
          const finalAutoSync = updatedStore.is_auto_sync;
          const finalStatus = updatedStore.status;
          console.log(`店铺 ${id} 状态变更，同步调度器: 自动同步=${finalAutoSync}, 状态=${finalStatus}`);
          await scheduleManager.syncStoreRelatedTasks(id, finalAutoSync, finalStatus);
        } catch (syncError) {
          console.error(`同步店铺 ${id} 相关任务到调度器失败:`, syncError);
          // 不影响店铺更新的成功返回
        }
      }

      const response = {
        code: 200,
        message: '更新店铺成功',
        data: updatedStore
      };

      return response;
    } catch (error) {
      console.error('更新店铺失败:', error);
      return {
        code: 500,
        message: '更新店铺失败',
        error: error.message
      };
    }
  }

  /**
   * 删除店铺
   * @param {string} id - 店铺ID
   * @param {BigInt} userId - 删除者ID
   * @returns {Promise<Object>} - 删除结果
   */
  async deleteStore(id, userId) {
    try {
      // 检查店铺是否存在
      const store = await StoreModel.getStoreById(id);
      if (!store) {
        return {
          code: 404,
          message: '店铺不存在'
        };
      }

      await StoreModel.deleteStore(id, userId);

      return {
        code: 200,
        message: '删除店铺成功'
      };
    } catch (error) {
      console.error('删除店铺失败:', error);
      return {
        code: 500,
        message: '删除店铺失败',
        error: error.message
      };
    }
  }

  /**
   * 获取指定平台下的所有店铺
   * @param {string} platformId - 平台ID
   * @returns {Promise<Object>} - 店铺列表
   */
  async getStoresByPlatformId(platformId) {
    try {
      const stores = await StoreModel.getStoresByPlatformId(platformId);
      
      // 转换字段名为驼峰命名
      const items = stores.map(store => {
        return {
          id: store.id,
          name: store.name,
          code: store.code,
          platformId: store.platformId || store.platform_id?.toString(),
          platformName: store.platformName || store.platform_name || null,
          channelId: store.channelId || store.channel_id?.toString() || null, // 确保始终有值
          channelName: store.channelName || store.channel_name || null,
          accountConfig: store.accountConfig || store.account_config || {},
          cookies: store.cookies || null, // 添加 cookies 字段
          status: store.status,
          remark: store.remark || null, // 添加 remark 字段
          principalName: store.principalName || store.principal_name || null, // 添加 principal_name 字段
          invoiceTitle: store.invoiceTitle || store.invoice_title || null, // 添加 invoice_title 字段
          createdAt: store.createdAt || (store.created_at ? Number(store.created_at) : null),
          updatedAt: store.updatedAt || (store.updated_at ? Number(store.updated_at) : null),
          createdBy: store.createdBy || store.created_by?.toString(),
          updatedBy: store.updatedBy || store.updated_by?.toString(),
          deletedAt: store.deletedAt || store.deleted_at || null
        };
      });
      
      return {
        code: 200,
        message: '获取平台下店铺列表成功',
        data: items
      };
    } catch (error) {
      console.error('获取平台下店铺列表失败:', error);
      return {
        code: 500,
        message: '获取平台下店铺列表失败',
        error: error.message
      };
    }
  }
}

module.exports = new StoreService();

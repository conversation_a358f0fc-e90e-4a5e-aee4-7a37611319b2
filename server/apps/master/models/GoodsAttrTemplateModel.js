const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 判断是否是特殊处理的字段（保持整数值）
 * @param {string} key 字段名
 * @returns {boolean} 是否需要特殊处理
 */
function isIntegerField(key) {
  // 需要保持整数值的字段列表
  const integerFields = ['is_required', 'is_filterable', 'is_enabled'];
  return integerFields.includes(key);
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * 并将指定字段的整数值(0/1)转换为布尔值
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        
        // 对于特殊字段，保持整数值
        if (isIntegerField(key) && typeof obj[key] === 'number') {
          result[camelKey] = obj[key]; // 保持整数值不变
        } else {
          result[camelKey] = convertKeysToCamelCase(obj[key]);
        }
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 如果是id相关字段且值为bigint，返回字符串
        if ((key === 'id' || 
            key.endsWith('_id') || 
            key === 'attribute_set_id' || 
            key === 'attribute_item_id' || 
            key === 'category_id' || 
            key === 'goods_attribute_set_id' || 
            key === 'goods_attribute_item_id') && 
            typeof data[key] === 'bigint') {
          result[key] = data[key].toString();
        } else {
          result[key] = handleBigInt(data[key]);
        }
      }
    }
    return result;
  }
  
  return data;
}

class GoodsAttrTemplateModel {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取分类的完整路径
   * @param {string} categoryId 分类ID
   * @returns {Promise<{id: string, name: string, fullPath: string}>} 返回分类信息，包含完整路径
   */
  async getCategoryFullPath(categoryId) {
    try {
      if (!categoryId) return null;
      
      // 获取分类信息
      const category = await this.prisma.goodsCategory.findFirst({
        where: { 
          id: BigInt(categoryId),
          deleted_at: null 
        }
      });
      
      if (!category) return null;
      
      // 处理BigInt序列化
      const processedCategory = handleBigInt(category);
      const camelCaseCategory = convertKeysToCamelCase(processedCategory);
      
      // 如果没有父分类，直接返回当前分类名称
      if (!camelCaseCategory.goodsParentCategoryId) {
        return {
          id: camelCaseCategory.id,
          name: camelCaseCategory.name,
          fullPath: camelCaseCategory.name
        };
      }
      
      // 如果有父分类，递归获取父分类路径
      const pathParts = [];
      let currentCategory = camelCaseCategory;
      
      // 最多处理4级分类，避免无限循环
      for (let i = 0; i < 4; i++) {
        pathParts.unshift(currentCategory.name);
        
        if (!currentCategory.goodsParentCategoryId) break;
        
        const parentCategory = await this.prisma.goodsCategory.findFirst({
          where: { 
            id: BigInt(currentCategory.goodsParentCategoryId),
            deleted_at: null 
          }
        });
        
        if (!parentCategory) break;
        
        currentCategory = convertKeysToCamelCase(handleBigInt(parentCategory));
      }
      
      return {
        id: camelCaseCategory.id,
        name: camelCaseCategory.name,
        fullPath: pathParts.join('/')
      };
    } catch (error) {
      console.error(`获取分类路径失败:`, error);
      return null;
    }
  }

  /**
   * 获取所有商品属性模板
   * @param {Object} options 查询选项
   * @param {string} options.name 模板名称（模糊搜索）
   * @param {number} options.categoryId 关联的商品分类ID
   * @param {number} options.startTime 创建时间范围的开始时间戳
   * @param {number} options.endTime 创建时间范围的结束时间戳
   * @param {number} options.skip 分页跳过的记录数
   * @param {number} options.take 分页获取的记录数
   * @returns {Promise<Array>} 返回模板列表
   */
  async getAllGoodsAttrTemplates(options = {}) {
    try {
      // 构建查询条件
      const whereCondition = {
        deleted_at: null // 只获取未删除的模板
      };
      
      // 如果提供了名称搜索参数，添加模糊搜索条件
      if (options.name) {
        whereCondition.name = {
          contains: options.name,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 用于存储模板与分类的关联信息
      let templateCategoryMap = {};
      
      // 如果提供了分类ID，添加分类筛选条件
      if (options.categoryId) {
        // 查询与分类关联的模板
        const categoryAssociations = await this.prisma.goodsAttributeSetCategoryAssociation.findMany({
          where: {
            goods_category_id: BigInt(options.categoryId)
          },
          select: {
            goods_attribute_set_id: true,
            goods_category_id: true
          }
        });
        
        // 提取模板ID列表
        const templateIds = categoryAssociations.map(assoc => assoc.goods_attribute_set_id);
        
        // 构建模板与分类的映射关系
        categoryAssociations.forEach(assoc => {
          templateCategoryMap[assoc.goods_attribute_set_id.toString()] = assoc.goods_category_id.toString();
        });
        
        // 如果有关联的模板，添加到查询条件
        if (templateIds.length > 0) {
          whereCondition.id = {
            in: templateIds
          };
        } else {
          // 如果没有关联的模板，返回空数组
          return [];
        }
      } else {
        // 如果没有提供分类ID，获取所有模板的分类关联
        const allAssociations = await this.prisma.goodsAttributeSetCategoryAssociation.findMany({
          select: {
            goods_attribute_set_id: true,
            goods_category_id: true
          }
        });
        
        // 构建模板与分类的映射关系
        allAssociations.forEach(assoc => {
          templateCategoryMap[assoc.goods_attribute_set_id.toString()] = assoc.goods_category_id.toString();
        });
      }
      
      // 如果提供了创建时间范围，添加时间筛选条件
      if (options.startTime || options.endTime) {
        whereCondition.created_at = {};
        
        if (options.startTime) {
          // 前端传入的是毫秒时间戳，直接使用
          const startTimeMs = BigInt(options.startTime);
          whereCondition.created_at.gte = startTimeMs;
        }
        
        if (options.endTime) {
          // 前端传入的是毫秒时间戳，直接使用
          const endTimeMs = BigInt(options.endTime);
          whereCondition.created_at.lte = endTimeMs;
        }
      }
      
      // 从数据库获取所有模板
      const templates = await this.prisma.goodsAttributeSet.findMany({
        where: whereCondition,
        orderBy: {
          sort_order: 'asc' // 按排序字段升序排列
        },
        skip: options.skip || 0,
        take: options.take || 10
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplates = handleBigInt(templates);
      const camelCaseTemplates = convertKeysToCamelCase(processedTemplates);
      
      // 为每个模板添加分类信息和参数项数量
      const templatesWithCategory = await Promise.all(camelCaseTemplates.map(async template => {
        template.sort = template.sortOrder;
        
        // 获取关联的分类ID
        const categoryId = templateCategoryMap[template.id];
        
        if (categoryId) {
          // 获取分类的完整路径
          const categoryInfo = await this.getCategoryFullPath(categoryId);
          
          if (categoryInfo) {
            template.categoryId = categoryInfo.id;
            template.categoryName = categoryInfo.fullPath;
          }
        }
        
        // 获取模板的参数项数量
        try {
          const params = await this.prisma.goodsAttributeItem.count({
            where: { 
              goods_attribute_set_id: BigInt(template.id),
              deleted_at: null // 只统计未删除的参数
            }
          });
          
          // 添加参数项数量字段
          template.paramCount = params;
        } catch (error) {
          console.error(`获取模板ID为${template.id}的参数数量失败:`, error);
          template.paramCount = 0; // 出错时默认为0
        }
        
        return template;
      }));
      
      return templatesWithCategory;
    } catch (error) {
      console.error('获取商品属性模板失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取商品属性模板
   * @param {number} id 模板ID
   * @returns {Promise<Object>} 返回模板对象
   */
  async getGoodsAttrTemplateById(id) {
    try {
      // 获取模板信息
      const template = await this.prisma.goodsAttributeSet.findFirst({
        where: { 
          id: BigInt(id),
          deleted_at: null // 只获取未删除的模板
        }
      });
      
      if (!template) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(template);
      const result = convertKeysToCamelCase(processedTemplate);
      
      // 将sort_order字段映射为sort以保持API兼容性
      result.sort = result.sort_order;
      
      return result;
    } catch (error) {
      console.error(`获取ID为${id}的模板失败:`, error);
      throw error;
    }
  }

  /**
   * 根据名称查找模板
   * @param {string} name 模板名称
   * @returns {Promise<Object|null>} 返回模板对象或null
   */
  async findTemplateByName(name) {
    try {
      const template = await this.prisma.goodsAttributeSet.findFirst({
        where: {
          name: name,
          deleted_at: null // 只查找未删除的模板
        }
      });
      
      if (!template) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(template);
      return convertKeysToCamelCase(processedTemplate);
    } catch (error) {
      console.error(`根据名称查找模板失败:`, error);
      throw error;
    }
  }
  
  /**
   * 根据名称查找模板（排除自身）
   * @param {number} id 排除的模板ID
   * @param {string} name 模板名称
   * @returns {Promise<Object|null>} 返回模板对象或null
   */
  async findTemplateByNameExcludeSelf(id, name) {
    try {
      const template = await this.prisma.goodsAttributeSet.findFirst({
        where: {
          name: name,
          id: { not: BigInt(id) },
          deleted_at: null // 只查找未删除的模板
        }
      });
      
      if (!template) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(template);
      return convertKeysToCamelCase(processedTemplate);
    } catch (error) {
      console.error(`根据名称查找模板（排除自身）失败:`, error);
      throw error;
    }
  }
  
  /**
   * 根据ID查找模板
   * @param {number} id 模板ID
   * @returns {Promise<Object|null>} 返回模板对象或null
   */
  async findTemplateById(id) {
    try {
      return await this.getGoodsAttrTemplateById(id);
    } catch (error) {
      console.error(`根据ID查找模板失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品属性模板
   * @param {Object} templateData 模板数据
   * @param {number} userId 用户ID
   * @returns {Promise<Object>} 返回新创建的模板
   */
  async addGoodsAttrTemplate(templateData, userId = null) {
    try {
      // 创建模板
      // 获取当前时间戳（毫秒）
      const currentTime = Date.now();
      
      // 生成雪花ID
      const templateId = generateSnowflakeId();
      
      const newTemplate = await this.prisma.goodsAttributeSet.create({
        data: {
          id: BigInt(templateId),
          name: templateData.name,
          sort_order: templateData.sort || 0,
          created_at: currentTime,
          updated_at: currentTime,
          created_by: userId ? BigInt(userId) : null,
          updated_by: userId ? BigInt(userId) : null
        }
      });
      
      // 如果提供了分类ID，创建分类关联
      if (templateData.categoryId) {
        try {
          await this.prisma.goodsAttributeSetCategoryAssociation.create({
            data: {
              goods_attribute_set_id: newTemplate.id,
              goods_category_id: BigInt(templateData.categoryId)
            }
          });
          
          // 获取分类信息
          const category = await this.prisma.goodsCategory.findUnique({
            where: { id: BigInt(templateData.categoryId) }
          });
          
          if (category) {
            // 将分类信息添加到模板中
            newTemplate.category = category;
            newTemplate.categoryId = category.id;
          }
        } catch (error) {
          console.error('创建分类关联失败:', error);
          // 即使关联失败，也不影响模板的创建
        }
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(newTemplate);
      const result = convertKeysToCamelCase(processedTemplate);
      
      // 将sort_order字段映射为sort以保持API兼容性
      result.sort = result.sort_order;
      
      return result;
    } catch (error) {
      console.error('添加模板失败:', error);
      throw error;
    }
  }

  /**
   * 根据名称查找参数
   * @param {number} templateId 模板ID
   * @param {string} name 参数名称
   * @returns {Promise<Object|null>} 返回参数对象或null
   */
  async findParamByName(templateId, name) {
    try {
      const param = await this.prisma.goodsAttributeItem.findFirst({
        where: {
          goods_attribute_set_id: BigInt(templateId),
          name: name,
          deleted_at: null // 只查找未删除的参数
        }
      });
      
      if (!param) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParam = handleBigInt(param);
      return convertKeysToCamelCase(processedParam);
    } catch (error) {
      console.error(`根据名称查找参数失败:`, error);
      throw error;
    }
  }
  
  /**
   * 根据名称查找参数（排除自身）
   * @param {number} templateId 模板ID
   * @param {number} paramId 排除的参数ID
   * @param {string} name 参数名称
   * @returns {Promise<Object|null>} 返回参数对象或null
   */
  async findParamByNameExcludeSelf(templateId, paramId, name) {
    try {
      const param = await this.prisma.goodsAttributeItem.findFirst({
        where: {
          goods_attribute_set_id: BigInt(templateId),
          name: name,
          id: { not: BigInt(paramId) },
          deleted_at: null // 只查找未删除的参数
        }
      });
      
      if (!param) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParam = handleBigInt(param);
      return convertKeysToCamelCase(processedParam);
    } catch (error) {
      console.error(`根据名称查找参数（排除自身）失败:`, error);
      throw error;
    }
  }
  
  /**
   * 根据ID查找参数
   * @param {number} paramId 参数ID
   * @returns {Promise<Object|null>} 返回参数对象或null
   */
  async findParamById(paramId) {
    try {
      const param = await this.prisma.goodsAttributeItem.findUnique({
        where: { id: BigInt(paramId) }
      });
      
      if (!param) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParam = handleBigInt(param);
      return convertKeysToCamelCase(processedParam);
    } catch (error) {
      console.error(`根据ID查找参数失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品属性模板参数
   * @param {number} templateId 模板ID
   * @param {Object} paramData 参数数据
   * @param {BigInt|null} userId 用户ID
   * @returns {Promise<Object>} 返回新创建的参数
   */
  async addGoodsAttrTemplateParam(templateId, paramData, userId = null) {
    try {
      // 获取当前时间戳（毫秒）
      const currentTime = Date.now();
      
      // 生成雪花ID
      const paramId = generateSnowflakeId();
      
      // 创建属性参数
      const newParam = await this.prisma.goodsAttributeItem.create({
        data: {
          id: BigInt(paramId),
          goods_attribute_set_id: BigInt(templateId),
          name: paramData.name,
          type: paramData.type, // 文本、数字、单选、多选、下拉选择
          value: paramData.value || undefined, // 直接存储原始字符串，不进行处理
          is_required: parseInt(paramData.isRequired || 0),
          is_filterable: parseInt(paramData.isFilterable || 0),
          sort_order: paramData.sort || 0,
          is_enabled: parseInt(paramData.isEnabled !== undefined ? paramData.isEnabled : 1), // 默认启用
          created_at: currentTime,
          updated_at: currentTime,
          created_by: userId ? BigInt(userId) : null,
          updated_by: userId ? BigInt(userId) : null
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParam = handleBigInt(newParam);
      const result = convertKeysToCamelCase(processedParam);
      
      // 将sort_order字段映射为sort以保持API兼容性
      result.sort = result.sort_order;
      
      return result;
    } catch (error) {
      console.error('添加属性模板参数失败:', error);
      throw error;
    }
  }

  /**
   * 更新属性模板参数
   * @param {number} paramId 参数ID
   * @param {Object} paramData 参数数据
   * @param {BigInt|null} userId 用户ID
   * @returns {Promise<Object>} 返回更新后的参数
   */
  async updateGoodsAttrTemplateParam(paramId, paramData, userId = null) {
    try {
      // 获取当前时间戳（毫秒）
      const currentTime = Date.now();
      
      // 准备更新数据
      const updateData = {};
      
      // 只更新提供的字段
      if (paramData.name !== undefined) updateData.name = paramData.name;
      if (paramData.type !== undefined) updateData.type = paramData.type;
      if (paramData.value !== undefined) updateData.value = paramData.value || undefined;
      if (paramData.isRequired !== undefined) updateData.is_required = parseInt(paramData.isRequired);
      if (paramData.isFilterable !== undefined) updateData.is_filterable = parseInt(paramData.isFilterable);
      if (paramData.sort !== undefined) updateData.sort_order = paramData.sort;
      if (paramData.isEnabled !== undefined) updateData.is_enabled = parseInt(paramData.isEnabled);
      
      // 添加更新时间和更新者ID
      updateData.updated_at = currentTime;
      if (userId) updateData.updated_by = BigInt(userId);
      
      // 更新参数
      const updatedParam = await this.prisma.goodsAttributeItem.update({
        where: { id: BigInt(paramId) },
        data: updateData
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParam = handleBigInt(updatedParam);
      const result = convertKeysToCamelCase(processedParam);
      
      // 将sort_order字段映射为sort以保持API兼容性
      result.sort = result.sort_order;
      
      return result;
    } catch (error) {
      console.error(`更新参数ID为${paramId}的参数失败:`, error);
      throw error;
    }
  }

  /**
   * 删除属性模板参数（软删除）
   * @param {number} paramId 参数ID
   * @returns {Promise<boolean>} 返回是否删除成功
   */
  async deleteGoodsAttrTemplateParam(paramId) {
    try {
      // 检查参数是否存在
      const existingParam = await this.prisma.goodsAttributeItem.findUnique({
        where: { id: BigInt(paramId) }
      });
      
      if (!existingParam) {
        throw new Error(`ID为${paramId}的参数不存在`);
      }
      
      // 获取当前时间戳（毫秒）
      const currentTime = Date.now();
      
      // 软删除参数
      await this.prisma.goodsAttributeItem.update({
        where: { id: BigInt(paramId) },
        data: { deleted_at: currentTime }
      });
      
      return true;
    } catch (error) {
      console.error(`删除参数ID为${paramId}的参数失败:`, error);
      throw error;
    }
  }

  /**
   * 更新商品属性模板
   * @param {number} id 模板ID
   * @param {Object} templateData 模板数据
   * @param {number} userId 用户ID
   * @returns {Promise<Object>} 返回更新后的模板
   */
  async updateGoodsAttrTemplate(id, templateData, userId = null) {
    try {
      const templateId = BigInt(id);
      
      // 更新模板
      // 获取当前时间戳（毫秒）
      const currentTime = Date.now();
      
      const updatedTemplate = await this.prisma.goodsAttributeSet.update({
        where: { id: templateId },
        data: {
          name: templateData.name,
          sort_order: templateData.sort,
          updated_at: currentTime,
          updated_by: userId ? BigInt(userId) : undefined
        }
      });
      
      // 如果提供了分类ID，更新分类关联
      if (templateData.categoryId !== undefined) {
        try {
          // 先删除现有的关联
          await this.prisma.goodsAttributeSetCategoryAssociation.deleteMany({
            where: {
              goods_attribute_set_id: templateId
            }
          });
          
          // 如果有新的分类ID，创建新的关联
          if (templateData.categoryId) {
            await this.prisma.goodsAttributeSetCategoryAssociation.create({
              data: {
                goods_attribute_set_id: templateId,
                goods_category_id: BigInt(templateData.categoryId)
              }
            });
            
            // 获取分类信息
            const category = await this.prisma.goodsCategory.findUnique({
              where: { id: BigInt(templateData.categoryId) }
            });
            
            if (category) {
              // 将分类信息添加到模板中
              updatedTemplate.category = category;
              updatedTemplate.categoryId = category.id;
            }
          }
        } catch (error) {
          console.error('更新分类关联失败:', error);
          // 即使关联失败，也不影响模板的更新
        }
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(updatedTemplate);
      const result = convertKeysToCamelCase(processedTemplate);
      
      // 将sort_order字段映射为sort以保持API兼容性
      result.sort = result.sort_order;
      
      return result;
    } catch (error) {
      console.error(`更新ID为${id}的模板失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品属性模板
   * @param {number} id 模板ID
   * @returns {Promise<boolean>} 返回是否删除成功
   */
  async deleteGoodsAttrTemplate(id) {
    try {
      const templateId = BigInt(id);
      
      // 软删除模板（更新删除时间戳）
      await this.prisma.goodsAttributeSet.update({
        where: { id: templateId },
        data: { deleted_at: Date.now() }
      });
      
      return true;
    } catch (error) {
      console.error(`删除ID为${id}的模板失败:`, error);
      throw error;
    }
  }
  
  /**
   * 获取商品属性模板的所有参数
   * @param {number} templateId 模板ID
   * @returns {Promise<Array>} 返回参数列表
   */
  async getGoodsAttrTemplateParams(templateId) {
    try {
      // 获取模板的所有参数
      const params = await this.prisma.goodsAttributeItem.findMany({
        where: { 
          goods_attribute_set_id: BigInt(templateId),
          deleted_at: null // 只获取未删除的参数
        },
        orderBy: {
          sort_order: 'asc' // 按排序字段升序排列
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParams = handleBigInt(params);
      const camelCaseParams = convertKeysToCamelCase(processedParams);
      
      // 将sort_order字段映射为sort以保持API兼容性
      return camelCaseParams.map(param => {
        param.sort = param.sort_order;
        return param;
      });
    } catch (error) {
      console.error(`获取模板ID为${templateId}的参数失败:`, error);
      throw error;
    }
  }

  /**
   * 检查模板是否有关联的参数
   * @param {number} templateId 模板ID
   * @returns {Promise<Array>} 返回关联的参数列表
   */
  async getRelatedParams(templateId) {
    try {
      return await this.prisma.goodsAttributeItem.findMany({
        where: {
          goods_attribute_set_id: BigInt(templateId),
          deleted_at: null
        }
      });
    } catch (error) {
      console.error(`检查模板ID为${templateId}的关联参数失败:`, error);
      throw error;
    }
  }
  
  /**
   * 获取商品属性模板总数
   * @param {Object} options 查询选项
   * @param {string} options.name 模板名称（模糊搜索）
   * @param {number} options.categoryId 关联的商品分类ID
   * @param {number} options.startTime 创建时间范围的开始时间戳
   * @param {number} options.endTime 创建时间范围的结束时间戳
   * @returns {Promise<number>} 返回模板总数
   */
  async countGoodsAttrTemplates(options = {}) {
    try {
      // 构建查询条件
      const whereCondition = {
        deleted_at: null // 只获取未删除的模板
      };
      
      // 如果提供了名称搜索参数，添加模糊搜索条件
      if (options.name) {
        whereCondition.name = {
          contains: options.name,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 如果提供了分类ID，添加分类筛选条件
      if (options.categoryId) {
        // 查询与分类关联的模板
        const categoryAssociations = await this.prisma.goodsAttributeSetCategoryAssociation.findMany({
          where: {
            goods_category_id: BigInt(options.categoryId)
          },
          select: {
            goods_attribute_set_id: true
          }
        });
        
        // 提取模板ID列表
        const templateIds = categoryAssociations.map(assoc => assoc.goods_attribute_set_id);
        
        // 如果有关联的模板，添加到查询条件
        if (templateIds.length > 0) {
          whereCondition.id = {
            in: templateIds
          };
        } else {
          // 如果没有关联的模板，返回0
          return 0;
        }
      }
      
      // 如果提供了创建时间范围，添加时间筛选条件
      if (options.startTime || options.endTime) {
        whereCondition.created_at = {};
        
        if (options.startTime) {
          // 前端传入的是毫秒时间戳，直接使用
          const startTimeMs = BigInt(options.startTime);
          whereCondition.created_at.gte = startTimeMs;
        }
        
        if (options.endTime) {
          // 前端传入的是毫秒时间戳，直接使用
          const endTimeMs = BigInt(options.endTime);
          whereCondition.created_at.lte = endTimeMs;
        }
      }
      
      // 获取总数
      const count = await this.prisma.goodsAttributeSet.count({
        where: whereCondition
      });
      
      return count;
    } catch (error) {
      console.error('获取商品属性模板总数失败:', error);
      throw error;
    }
  }
}

module.exports = GoodsAttrTemplateModel;

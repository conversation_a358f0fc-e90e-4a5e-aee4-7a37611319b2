const prismaManager = require('../../../core/prisma');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 如果是id字段且值为bigint，返回字符串
        if ((key === 'id' || key.endsWith('_id') || key === 'created_at' || key === 'updated_at' || key === 'deleted_at') && typeof data[key] === 'bigint') {
          result[key] = data[key].toString();
        } else {
          result[key] = handleBigInt(data[key]);
        }
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 商品草稿模型
 */
class GoodsDraftModel {
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 获取用户最新的商品草稿
   * @param {number|string} userId 用户ID
   * @returns {Promise<Object|null>} 最新的草稿数据，如果没有则返回null
   */
  async getLatestDraft(userId) {
    try {
      const userIdBigInt = BigInt(userId);
      
      const draft = await this.prisma.goodsDraft.findFirst({
        where: {
          created_by: userIdBigInt,
          deleted_at: null
        },
        orderBy: {
          updated_at: 'desc'
        }
      });
      
      if (!draft) {
        return null;
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(draft);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('获取最新草稿失败:', error);
      throw error;
    }
  }

  /**
   * 创建商品草稿
   * @param {Object} draftData 草稿数据
   * @returns {Promise<Object>} 创建的草稿数据
   */
  async createDraft(draftData) {
    try {
      // 获取当前时间戳（毫秒）
      const now = BigInt(Date.now());
      
      // 创建草稿
      const draft = await this.prisma.goodsDraft.create({
        data: {
          content: draftData.content,
          created_by: draftData.created_by ? BigInt(draftData.created_by) : null,
          updated_by: draftData.updated_by ? BigInt(draftData.updated_by) : null,
          created_at: now,
          updated_at: now
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(draft);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('创建草稿失败:', error);
      throw error;
    }
  }

  /**
   * 删除商品草稿
   * @param {number|string} id 草稿ID
   * @param {number|string} userId 用户ID（用于验证权限）
   * @returns {Promise<Object>} 删除的草稿数据
   */
  async deleteDraft(id, userId) {
    try {
      const idBigInt = BigInt(id);
      const userIdBigInt = BigInt(userId);
      const now = BigInt(Date.now());
      
      // 查询草稿是否存在且属于该用户
      const existingDraft = await this.prisma.goodsDraft.findFirst({
        where: {
          id: idBigInt,
          created_by: userIdBigInt,
          deleted_at: null
        }
      });
      
      if (!existingDraft) {
        return null;
      }
      
      // 软删除草稿（更新 deleted_at 字段）
      const draft = await this.prisma.goodsDraft.update({
        where: {
          id: idBigInt
        },
        data: {
          deleted_at: now,
          updated_at: now
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(draft);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('删除草稿失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户所有未删除的草稿记录
   * @param {number|string} userId 用户ID
   * @returns {Promise<Array>} 用户的所有未删除草稿
   */
  async getUserDrafts(userId) {
    try {
      const userIdBigInt = BigInt(userId);
      
      // 查询用户所有未删除的草稿
      const drafts = await this.prisma.goodsDraft.findMany({
        where: {
          created_by: userIdBigInt,
          deleted_at: null
        },
        orderBy: {
          updated_at: 'desc'
        }
      });
      
      if (!drafts || drafts.length === 0) {
        return [];
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      return drafts.map(draft => {
        const processedData = handleBigInt(draft);
        return convertKeysToCamelCase(processedData);
      });
    } catch (error) {
      console.error('获取用户草稿列表失败:', error);
      throw error;
    }
  }
}

module.exports = GoodsDraftModel;

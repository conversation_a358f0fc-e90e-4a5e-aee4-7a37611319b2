const slugify = require('slugify');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 如果是id相关字段且值为bigint，返回字符串
        if ((key === 'id' || key.endsWith('_id') || key === 'parent_id' || key === 'goods_parent_category_id') && typeof data[key] === 'bigint') {
          result[key] = data[key].toString();
        } else {
          result[key] = handleBigInt(data[key]);
        }
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 生成唯一的slug
 * @param {string} name 分类名称
 * @returns {Promise<string>} 返回生成的唯一slug
 */
async function generateUniqueSlug(prisma, name) {
  // 使用slugify将名称转换为URL友好的格式
  let baseSlug = slugify(name, {
    lower: true,      // 转换为小写
    strict: true,     // 严格模式，去除非URL友好字符
    locale: 'zh-CN'   // 中文支持
  });
  
  // 如果slugify后为空（可能全是特殊字符），使用随机字符串
  if (!baseSlug) {
    baseSlug = 'category-' + Math.random().toString(36).substring(2, 10);
  }
  
  // 由于数据库中没有slug字段，临时生成一个带时间戳的唯一slug
  const timestamp = new Date().getTime();
  const randomStr = Math.random().toString(36).substring(2, 6);
  return `${baseSlug}-${timestamp}-${randomStr}`;
  
  /* 原始代码暂时注释掉，因为数据库中没有slug字段
  let slug = baseSlug;
  let counter = 1;
  let existingCategory;
  
  // 检查slug是否已存在，如果存在则添加数字后缀
  do {
    existingCategory = await prisma.goodsCategory.findFirst({
      where: { slug: slug }
    });
    
    if (existingCategory) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }
  } while (existingCategory);
  
  return slug;
  */
}

class GoodsCategoryModel {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取所有商品分类
   * @param {Object} options 查询选项
   * @param {string} options.name 分类名称（模糊搜索）
   * @param {number} options.skip 跳过数量
   * @param {number} options.take 获取数量
   * @param {boolean} options.isTreeMode 是否返回树形结构
   * @returns {Promise<Object>} 返回分类数据和总数
   */
  async getAllGoodsCategories(options = {}) {
    try {
      // 构建查询条件
      const whereCondition = {
        deleted_at: null // 只获取未删除的分类
      };
      
      // 如果提供了名称搜索参数，添加模糊搜索条件
      if (options.name) {
        whereCondition.name = {
          contains: options.name,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 获取总数
      const total = await this.prisma.goodsCategory.count({
        where: whereCondition
      });
      
      // 构建分页选项
      const paginationOptions = {
        where: whereCondition,
        orderBy: {
          sort_order: 'asc'
        }
      };
      
      // 添加分页参数
      if (options.skip !== undefined) {
        paginationOptions.skip = options.skip;
      }
      
      if (options.take !== undefined) {
        paginationOptions.take = options.take;
      }
      
      // 从数据库获取分类
      const categories = await this.prisma.goodsCategory.findMany(paginationOptions);

      // 先处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = convertKeysToCamelCase(handleBigInt(categories));
      
      // 如果是按名称搜索或要求扁平结构，返回扁平结构的结果
      if (options.name || options.isTreeMode === false) {
        return {
          data: processedData,
          total
        };
      }
      
      // 否则将扁平结构转换为树形结构
      // 构建树形结构
      const categoryTree = this.buildCategoryTree(processedData);
      return {
        data: categoryTree,
        total
      };
    } catch (error) {
      console.error('获取商品分类失败:', error);
      throw error;
    }
  }

  /**
   * 根据名称获取商品分类
   * @param {string} name 分类名称
   * @param {number} excludeId 排除的分类ID（用于更新时检查）
   * @returns {Promise<Object|null>} 返回分类对象或null
   */
  async getCategoryByName(name, excludeId = null) {
    try {
      const whereCondition = {
        name: name,
        deleted_at: null // 只获取未删除的分类
      };
      
      // 如果提供了excludeId，排除该ID的分类
      if (excludeId) {
        whereCondition.id = {
          not: BigInt(excludeId)
        };
      }
      
      const category = await this.prisma.goodsCategory.findFirst({
        where: whereCondition
      });
      
      if (!category) return null;
      
      // 先处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(category);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`根据名称获取分类失败:`, error);
      throw error;
    }
  }
  
  /**
   * 根据ID获取商品分类
   * @param {number} id 分类ID
   * @returns {Promise<Object>} 返回分类对象
   */
  async getGoodsCategoryById(id) {
    try {
      // 获取分类基本信息
      const category = await this.prisma.goodsCategory.findFirst({
        where: { 
          id: BigInt(id),
          deleted_at: null // 只获取未删除的分类
        }
      });
      
      if (!category) return null;
      
      // 由于我们暂时没有在 schema 中定义属性模板关联，这里先注释掉
      // const attributeSetAssociations = await this.prisma.goods_attribute_set_category_associations.findMany({
      //   where: {
      //     goods_category_id: BigInt(id)
      //   },
      //   include: {
      //     goods_attribute_sets: true
      //   }
      // });
      
      // 临时使用空数组
      const attributeSetAssociations = [];
      
      // 将属性模板信息添加到分类对象中
      // 由于我们暂时没有属性模板关联数据，这里使用空数组
      const attributeSets = [];
      
      // 返回包含属性模板的分类信息
      const result = {
        ...category,
        attributeSets
      };
      
      // 先处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(result);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`获取ID为${id}的分类失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品分类
   * @param {Object} categoryData 分类数据
   * @returns {Promise<Object>} 返回新创建的分类
   */
  async addGoodsCategory(categoryData) {
    try {
      // 处理父分类ID
      const parentId = categoryData.goods_parent_category_id ? BigInt(categoryData.goods_parent_category_id) : null;
      
      // 计算分类层级
      let level = 1;
      if (parentId) {
        const parentCategory = await this.prisma.goodsCategory.findFirst({
          where: { 
            id: parentId,
            deleted_at: null // 只获取未删除的分类
          }
        });
        if (parentCategory) {
          level = (parentCategory.level || 1) + 1;
        }
      }
      
      // 使用事务确保分类和属性模板关联一起创建
      return await this.prisma.$transaction(async (prisma) => {
        // 生成雪花ID
        const categoryId = generateSnowflakeId();
        
        // 日志跟踪分类创建数据
        console.log('商品分类模型 - 添加分类 - 数据:', { 
          categoryData,
          created_by: categoryData.created_by,
          updated_by: categoryData.updated_by 
        });

        // 创建分类 - 移除slug字段
        const newCategory = await prisma.goodsCategory.create({
          data: {
            id: categoryId,
            goods_parent_category_id: parentId,
            name: categoryData.name,
            // 移除 slug 字段
            image_url: categoryData.image_url || null,
            description: categoryData.description || null,
            sort_order: categoryData.sort_order || 0,
            is_enabled: categoryData.is_enabled,
            level: level,
            meta_title: categoryData.meta_title || null,
            meta_keywords: categoryData.meta_keywords || null,
            meta_description: categoryData.meta_description || null,
            // 添加创建人和更新人字段
            created_by: categoryData.created_by || null,
            updated_by: categoryData.updated_by || null
          }
        });
        
        // 日志跟踪创建结果
        console.log('商品分类模型 - 新创建的分类:', { 
          id: newCategory.id,
          created_by: newCategory.created_by,
          updated_by: newCategory.updated_by 
        });
        
        // 由于我们暂时没有在 schema 中定义属性模板关联，这里先注释掉
        // if (categoryData.attributeSetIds && categoryData.attributeSetIds.length > 0) {
        //   const associations = categoryData.attributeSetIds.map(setId => ({
        //     goods_category_id: newCategory.id,
        //     goods_attribute_set_id: BigInt(setId)
        //   }));
        //   
        //   await prisma.goods_attribute_set_category_associations.createMany({
        //     data: associations
        //   });
        // }
        
        // 先处理BigInt序列化问题，再将字段名转换为 camelCase 格式
        const processedData = handleBigInt(newCategory);
        return convertKeysToCamelCase(processedData);
      });
    } catch (error) {
      console.error('添加分类失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品分类
   * @param {number} id 分类ID
   * @param {Object} categoryData 分类数据
   * @returns {Promise<Object>} 返回更新后的分类
   */
  async updateGoodsCategory(id, categoryData) {
    try {
      const categoryId = BigInt(id);
      
      // 首先检查分类是否存在
      const existingCategory = await this.prisma.goodsCategory.findFirst({
        where: { id: categoryId }
      });
      
      if (!existingCategory) {
        throw new Error(`ID为${id}的分类不存在`);
      }
      
      // 处理父分类ID
      const parentId = categoryData.goods_parent_category_id ? BigInt(categoryData.goods_parent_category_id) : null;
      
      // 计算分类层级
      let level = 1;
      if (parentId) {
        const parentCategory = await this.prisma.goodsCategory.findFirst({
          where: { 
            id: parentId,
            deleted_at: null // 只获取未删除的分类
          }
        });
        if (parentCategory) {
          level = (parentCategory.level || 1) + 1;
        }
      }
      
      // 日志跟踪分类更新数据
      console.log('商品分类模型 - 更新分类 - 数据:', { 
        categoryData,
        updated_by: categoryData.updated_by 
      });

      // 使用事务确保分类和属性模板关联一起更新
      return await this.prisma.$transaction(async (prisma) => {
        // 更新分类
        const updatedCategory = await prisma.goodsCategory.update({
          where: { id: categoryId },
          data: {
            goods_parent_category_id: parentId,
            name: categoryData.name,
            image_url: categoryData.image_url,
            description: categoryData.description,
            sort_order: categoryData.sort_order,
            is_enabled: categoryData.is_enabled,
            level: level,
            meta_title: categoryData.meta_title,
            meta_keywords: categoryData.meta_keywords,
            meta_description: categoryData.meta_description,
            updated_at: BigInt(Date.now()),
            // 添加更新人字段
            updated_by: categoryData.updated_by || null
          }
        });
        
        // 日志跟踪更新结果
        console.log('商品分类模型 - 更新后的分类:', { 
          id: updatedCategory.id,
          updated_by: updatedCategory.updated_by 
        });
        
        // 由于我们暂时没有在 schema 中定义属性模板关联，这里先注释掉
        // if (categoryData.attributeSetIds) {
        //   // 删除旧的关联
        //   await prisma.base_goods_attribute_set_category_associations.deleteMany({
        //     where: { goods_category_id: categoryId }
        //   });
        //   
        //   // 创建新的关联
        //   if (categoryData.attributeSetIds.length > 0) {
        //     const associations = categoryData.attributeSetIds.map(setId => ({
        //       goods_category_id: categoryId,
        //       goods_attribute_set_id: BigInt(setId)
        //     }));
        //     
        //     await prisma.goods_attribute_set_category_associations.createMany({
        //       data: associations
        //     });
        //   }
        // }
        
        // 先处理BigInt序列化问题，再将字段名转换为 camelCase 格式
        const processedData = handleBigInt(updatedCategory);
        return convertKeysToCamelCase(processedData);
      });
    } catch (error) {
      console.error(`更新ID为${id}的分类失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品分类
   * @param {number} id 分类ID
   * @returns {Promise<Object>} 返回删除结果
   */
  async deleteGoodsCategory(id) {
    try {
      const categoryId = BigInt(id);
      
      // 检查是否有子分类
      const childCategories = await this.prisma.goodsCategory.findMany({
        where: {
          goods_parent_category_id: categoryId,
          deleted_at: null
        }
      });
      
      if (childCategories.length > 0) {
        throw new Error('该分类下有子分类，无法删除');
      }
      
      // 由于我们暂时没有在 schema 中定义商品关联，这里先注释掉
      // const relatedProducts = await this.prisma.goods_spus.findMany({
      //   where: {
      //     goods_category_id: categoryId,
      //     deleted_at: null
      //   }
      // });
      // 
      // if (relatedProducts.length > 0) {
      //   throw new Error('该分类下有关联商品，无法删除');
      // }
      
      // 临时跳过商品关联检查
      
      // 使用事务确保分类和属性模板关联一起删除
      return await this.prisma.$transaction(async (prisma) => {
        // 由于我们暂时没有在 schema 中定义属性模板关联，这里先注释掉
        // await prisma.goods_attribute_set_category_associations.deleteMany({
        //   where: { goods_category_id: categoryId }
        // });
        
        // 软删除分类
        const deletedCategory = await prisma.goodsCategory.update({
          where: { id: categoryId },
          data: { deleted_at: BigInt(Date.now()) }
        });
        
        // 先处理BigInt序列化问题，再将字段名转换为 camelCase 格式
        const processedData = handleBigInt(deletedCategory);
        return convertKeysToCamelCase(processedData);
      });
    } catch (error) {
      console.error(`删除ID为${id}的分类失败:`, error);
      throw error;
    }
  }

  /**
   * 构建分类树
   * @param {Array} categories 扁平的分类数组
   * @returns {Array} 树形结构的分类数组
   */
  buildCategoryTree(categories) {
    // 创建一个映射表，用于快速查找分类
    const categoryMap = {};
    categories.forEach(category => {
      categoryMap[category.id.toString()] = { ...category, children: [] };
    });
    
    // 构建树结构
    const tree = [];
    categories.forEach(category => {
      const categoryId = category.id.toString();
      const parentId = category.goodsParentCategoryId ? category.goodsParentCategoryId.toString() : null;
      
      if (parentId && categoryMap[parentId]) {
        // 如果有父分类且父分类存在，则将当前分类添加到父分类的children中
        categoryMap[parentId].children.push(categoryMap[categoryId]);
      } else {
        // 如果没有父分类或父分类不存在，则将当前分类添加到树的根节点
        tree.push(categoryMap[categoryId]);
      }
    });
    
    return tree;
  }
  
  /**
   * 根据分类ID获取商品属性模板和参数
   * @param {string|number} categoryId 分类ID
   * @returns {Promise<Object|null>} 返回属性模板和参数数据，如果没有则返回 null
   */
  async getCategoryAttributeTemplate(categoryId) {
    try {
      console.log('获取商品属性模板 - 输入的分类ID类型:', typeof categoryId, '值:', categoryId);
      
      // 确保categoryId是字符串形式，防止BigInt转换错误
      const categoryIdStr = String(categoryId);
      console.log('转换后的分类ID字符串:', categoryIdStr);
      
      // 1. 首先检查分类是否存在
      const category = await this.prisma.goodsCategory.findFirst({
        where: {
          id: BigInt(categoryIdStr),
          deleted_at: null
        }
      });
      
      console.log('查询到的分类信息:', category ? '分类存在' : '分类不存在');

      if (!category) {
        throw new Error(`ID为${categoryId}的商品分类不存在`);
      }
      
      // 2. 获取与该分类关联的属性模板
      // 先获取关联记录，只获取第一个
      const categoryAssociation = await this.prisma.goodsAttributeSetCategoryAssociation.findFirst({
        where: {
          goods_category_id: BigInt(categoryIdStr)
        },
        select: {
          goods_attribute_set_id: true
        }
      });

      // 如果没有关联的属性模板，返回 null
      if (!categoryAssociation) {
        return null;
      }

      console.log('关联的属性模板ID:', categoryAssociation.goods_attribute_set_id.toString());
      
      // 获取属性模板及其属性项
      const attributeSet = await this.prisma.goodsAttributeSet.findFirst({
        where: {
          id: categoryAssociation.goods_attribute_set_id, // 这里已经是BigInt类型，不需要再转换
          deleted_at: null
        },
        include: {
          goods_attribute_items: {
            where: {
              deleted_at: null,
              is_enabled: 1
            },
            orderBy: {
              sort_order: 'asc'
            }
          }
        }
      });

      // 如果没有找到属性模板，返回 null
      if (!attributeSet) {
        return null;
      }

      // 3. 处理数据格式
      const templateData = {
        id: attributeSet.id.toString(),
        name: attributeSet.name,
        sortOrder: attributeSet.sort_order,
        attributes: []
      };

      // 处理属性项
      if (attributeSet.goods_attribute_items && attributeSet.goods_attribute_items.length > 0) {
        templateData.attributes = attributeSet.goods_attribute_items.map(item => ({
          id: item.id.toString(),
          name: item.name,
          type: item.type,
          value: item.value,
          isRequired: item.is_required === 1,
          isFilterable: item.is_filterable === 1,
          sortOrder: item.sort_order
        }));
      }

      return templateData;
    } catch (error) {
      console.error(`获取分类ID为${categoryId}的属性模板失败:`, error);
      throw error;
    }
  }
}

module.exports = GoodsCategoryModel;

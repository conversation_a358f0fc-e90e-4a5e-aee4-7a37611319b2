/**
 * 支付配置模型
 * 负责支付配置的数据库操作
 */
const { prisma } = require('../../../core/database/prisma');

class PaymentConfigModel {
  /**
   * 获取所有支付配置
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 支付配置列表
   */
  static async findMany(options = {}) {
    try {
      const defaultOptions = {
        where: {
          deleted_at: null
        },
        orderBy: {
          created_at: 'desc'
        }
      };

      const mergedOptions = { ...defaultOptions, ...options };
      
      return await prisma.paymentConfigs.findMany(mergedOptions);
    } catch (error) {
      console.error('获取支付配置列表失败:', error);
      throw new Error('获取支付配置列表失败');
    }
  }

  /**
   * 根据ID获取支付配置
   * @param {BigInt} id 配置ID
   * @returns {Promise<Object|null>} 支付配置
   */
  static async findById(id) {
    try {
      return await prisma.paymentConfigs.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });
    } catch (error) {
      console.error('根据ID获取支付配置失败:', error);
      throw new Error('获取支付配置失败');
    }
  }

  /**
   * 根据支付类型获取配置
   * @param {string} paymentType 支付类型
   * @returns {Promise<Array>} 支付配置列表
   */
  static async findByType(paymentType) {
    try {
      return await prisma.paymentConfigs.findMany({
        where: {
          payment_type: paymentType,
          deleted_at: null
        },
        orderBy: {
          is_default: 'desc'
        }
      });
    } catch (error) {
      console.error('根据类型获取支付配置失败:', error);
      throw new Error('获取支付配置失败');
    }
  }

  /**
   * 获取默认配置
   * @param {string} paymentType 支付类型
   * @returns {Promise<Object|null>} 默认支付配置
   */
  static async findDefaultByType(paymentType) {
    try {
      return await prisma.paymentConfigs.findFirst({
        where: {
          payment_type: paymentType,
          is_default: 1,
          enabled: 1,
          deleted_at: null
        }
      });
    } catch (error) {
      console.error('获取默认支付配置失败:', error);
      throw new Error('获取默认支付配置失败');
    }
  }

  /**
   * 创建支付配置
   * @param {Object} data 配置数据
   * @returns {Promise<Object>} 创建的配置
   */
  static async create(data) {
    try {
      return await prisma.paymentConfigs.create({
        data: {
          ...data,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
    } catch (error) {
      console.error('创建支付配置失败:', error);
      throw new Error('创建支付配置失败');
    }
  }

  /**
   * 更新支付配置
   * @param {BigInt} id 配置ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的配置
   */
  static async update(id, data) {
    try {
      return await prisma.paymentConfigs.update({
        where: {
          id: BigInt(id)
        },
        data: {
          ...data,
          updated_at: BigInt(Date.now())
        }
      });
    } catch (error) {
      console.error('更新支付配置失败:', error);
      throw new Error('更新支付配置失败');
    }
  }

  /**
   * 软删除支付配置
   * @param {BigInt} id 配置ID
   * @param {BigInt} userId 操作用户ID
   * @returns {Promise<Object>} 删除结果
   */
  static async softDelete(id, userId = null) {
    try {
      return await prisma.paymentConfigs.update({
        where: {
          id: BigInt(id)
        },
        data: {
          deleted_at: BigInt(Date.now()),
          updated_by: userId ? BigInt(userId) : null,
          updated_at: BigInt(Date.now())
        }
      });
    } catch (error) {
      console.error('删除支付配置失败:', error);
      throw new Error('删除支付配置失败');
    }
  }

  /**
   * 设置默认配置
   * @param {BigInt} id 配置ID
   * @param {string} paymentType 支付类型
   * @returns {Promise<Object>} 更新结果
   */
  static async setDefault(id, paymentType) {
    try {
      // 使用事务确保数据一致性
      return await prisma.$transaction(async (tx) => {
        // 先将该类型的所有配置设为非默认
        await tx.paymentConfigs.updateMany({
          where: {
            payment_type: paymentType,
            deleted_at: null
          },
          data: {
            is_default: 0,
            updated_at: BigInt(Date.now())
          }
        });

        // 再将指定配置设为默认
        return await tx.paymentConfigs.update({
          where: {
            id: BigInt(id)
          },
          data: {
            is_default: 1,
            updated_at: BigInt(Date.now())
          }
        });
      });
    } catch (error) {
      console.error('设置默认配置失败:', error);
      throw new Error('设置默认配置失败');
    }
  }

  /**
   * 检查配置名称是否存在
   * @param {string} paymentType 支付类型
   * @param {string} configName 配置名称
   * @param {BigInt} excludeId 排除的ID（用于更新时检查）
   * @returns {Promise<boolean>} 是否存在
   */
  static async checkNameExists(paymentType, configName, excludeId = null) {
    try {
      const where = {
        payment_type: paymentType,
        config_name: configName,
        deleted_at: null
      };

      if (excludeId) {
        where.id = { not: BigInt(excludeId) };
      }

      const existing = await prisma.paymentConfigs.findFirst({ where });
      return !!existing;
    } catch (error) {
      console.error('检查配置名称失败:', error);
      throw new Error('检查配置名称失败');
    }
  }

  /**
   * 获取支付类型统计
   * @returns {Promise<Array>} 统计结果
   */
  static async getTypeStats() {
    try {
      return await prisma.paymentConfigs.groupBy({
        by: ['payment_type'],
        where: {
          deleted_at: null
        },
        _count: {
          id: true
        },
        _sum: {
          enabled: true
        }
      });
    } catch (error) {
      console.error('获取支付类型统计失败:', error);
      throw new Error('获取支付类型统计失败');
    }
  }
}

module.exports = PaymentConfigModel;

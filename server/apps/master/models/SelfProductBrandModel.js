/**
 * 产品品牌数据模型
 */
const prismaManager = require('../../../core/prisma');

class SelfProductBrandModel {
  constructor() {
    this.prisma = prismaManager.getClient('master');
  }

  /**
   * 获取产品品牌列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.limit 每页条数
   * @param {String} params.brand_code 品牌代码
   * @param {String} params.brand_name 品牌名称
   * @param {Number} params.is_self_brand 是否自主品牌：1-否，2-是
   * @param {Number} params.start_time 开始时间
   * @param {Number} params.end_time 结束时间
   * @returns {Promise<{rows: Array, total: Number}>} 品牌列表和总数
   */
  async getSelfProductBrandList(params) {
    const { page = 1, limit = 20, brand_code, brand_name, is_self_brand, start_time, end_time } = params;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where = {
      deleted_at: null,
    };

    if (brand_code) {
      where.brand_code = {
        contains: brand_code,
      };
    }

    if (brand_name) {
      where.brand_name = {
        contains: brand_name,
      };
    }

    if (is_self_brand) {
      where.is_self_brand = is_self_brand;
    }

    // 创建时间范围查询
    if (start_time && end_time) {
      where.created_at = {
        gte: start_time,
        lte: end_time,
      };
    } else if (start_time) {
      where.created_at = {
        gte: start_time,
      };
    } else if (end_time) {
      where.created_at = {
        lte: end_time,
      };
    }

    // 查询总数
    const total = await this.prisma.self_product_brands.count({ where });

    // 查询列表数据
    const rows = await this.prisma.self_product_brands.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        created_at: 'desc',
      },
    });

    return { rows, total };
  }

  /**
   * 获取产品品牌详情
   * @param {Number} id 产品品牌ID
   * @returns {Promise<Object>} 产品品牌详情
   */
  async getSelfProductBrandDetail(id) {
    return await this.prisma.self_product_brands.findFirst({
      where: {
        id,
        deleted_at: null,
      },
      include: {
        qualifications: {
          where: {
            deleted_at: null,
          },
        },
      },
    });
  }

  /**
   * 创建产品品牌
   * @param {Object} data 产品品牌数据
   * @returns {Promise<Object>} 创建的产品品牌
   */
  async createSelfProductBrand(data) {
    const now = Date.now();

    return await this.prisma.self_product_brands.create({
      data: {
        ...data,
        created_at: now,
        updated_at: now,
      },
    });
  }

  /**
   * 更新产品品牌
   * @param {Number} id 产品品牌ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的产品品牌
   */
  async updateSelfProductBrand(id, data) {
    const now = Date.now();

    return await this.prisma.self_product_brands.update({
      where: {
        id,
      },
      data: {
        ...data,
        updated_at: now,
      },
    });
  }

  /**
   * 删除产品品牌（软删除）
   * @param {Number} id 产品品牌ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteSelfProductBrand(id) {
    const now = Date.now();

    // 开启事务
    return await this.prisma.$transaction(async (tx) => {
      // 检查是否有关联的产品品牌资质
      const qualifications = await tx.self_product_brand_qualifications.findMany({
        where: {
          deleted_at: null,
          id: id,
        },
      });

      if (qualifications.length > 0) {
        throw new Error('该产品品牌有关联的资质信息，无法删除');
      }

      // 软删除产品品牌
      await tx.self_product_brands.update({
        where: {
          id,
        },
        data: {
          deleted_at: now,
        },
      });

      return { success: true };
    });
  }

  /**
   * 根据品牌代码查询产品品牌
   * @param {String} brandCode 品牌代码
   * @returns {Promise<Object>} 产品品牌信息
   */
  async getSelfProductBrandByCode(brandCode) {
    return await this.prisma.self_product_brands.findFirst({
      where: {
        brand_code: brandCode,
        deleted_at: null,
      },
    });
  }
}

module.exports = new SelfProductBrandModel();

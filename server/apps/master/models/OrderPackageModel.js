/**
 * 订单包裹模型
 */
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');
const OrderStatusEnum = require('../constants/OrderStatusEnum');
const ShippingStatusEnum = require('../constants/ShippingStatusEnum');

// 直接创建 Prisma 客户端实例
const prisma = new PrismaClient({
  log: ['error', 'warn']
});

// 生成随机ID的函数，替代nanoid
function generateRandomId(length = 10) {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length)
    .toUpperCase();
}

class OrderPackageModel {
  constructor() {
    // 直接使用全局的Prisma客户端实例
    this.prisma = prisma;
  }

  /**
   * 确保ID为BigInt类型
   * @param {string|number|BigInt} id - 输入的ID
   * @returns {BigInt} - BigInt类型的ID
   */
  ensureBigInt(id) {
    if (typeof id === 'string') {
      return BigInt(id);
    } else if (typeof id === 'number') {
      return BigInt(id);
    } else if (typeof id === 'bigint') {
      return id;
    } else {
      throw new Error(`无效的ID类型: ${typeof id}`);
    }
  }

  /**
   * 创建包裹及包裹项
   * @param {Object} packageData - 包裹数据
   * @param {Array} packageItems - 包裹项数据
   * @returns {Promise<Object>} - 创建的包裹数据
   */
  async createPackageWithItems(packageData, packageItems) {
    try {
      // 使用事务来确保包裹和包裹项一起创建
      return await this.prisma.$transaction(async (prisma) => {
        // 生成包裹编号
        const packageSn = `PKG${generateRandomId(10)}`; // 使用自定义的generateRandomId函数
        
        // 创建包裹
        const newPackage = await prisma.order_packages.create({
          data: {
            order_id: packageData.orderId,
            package_sn: packageSn,
            recipient_name: packageData.recipientName,
            recipient_phone: packageData.recipientPhone,
            region_path_name: packageData.regionPathName,
            street_address: packageData.streetAddress,
            postal_code: packageData.postalCode,
            shipping_method: packageData.shippingMethod,
            remark: packageData.remark,
            shipping_status: ShippingStatusEnum.UNSHIPPED // 初始状态：待发货
          }
        });
        
        // 创建包裹项
        const packageItemsPromises = packageItems.map(item => 
          prisma.order_package_items.create({
            data: {
              package_id: newPackage.id,
              order_item_id: item.orderItemId,
              quantity: item.quantity
            }
          })
        );
        
        await Promise.all(packageItemsPromises);
        
        // 更新订单项的已发货数量
        for (const item of packageItems) {
          await prisma.order_items.update({
            where: { id: item.orderItemId },
            data: {
              shipped_quantity: {
                increment: item.quantity
              }
            }
          });
        }
        
        // 更新订单发货状态
        await this.updateOrderShippingStatus(prisma, packageData.orderId);
        
        return newPackage;
      });
    } catch (error) {
      console.error('创建包裹失败:', error);
      throw error;
    }
  }

  /**
   * 获取包裹列表
   * @param {Object} filters - 过滤条件
   * @param {Number} page - 页码
   * @param {Number} pageSize - 每页条数
   * @returns {Promise<Object>} - 包裹列表和总数
   */
  async getPackages(filters, page, pageSize) {
    try {
      const where = {};
      
      if (filters.orderId) {
        where.order_id = this.ensureBigInt(filters.orderId);
      }
      
      if (filters.packageSn) {
        where.package_sn = { contains: filters.packageSn };
      }
      
      if (filters.trackingNumber) {
        where.tracking_number = { contains: filters.trackingNumber };
      }
      
      if (filters.shippingStatus !== undefined) {
        where.shipping_status = filters.shippingStatus;
      }
      
      if (filters.startTime) {
        where.created_at = {
          ...where.created_at,
          gte: filters.startTime
        };
      }
      
      if (filters.endTime) {
        where.created_at = {
          ...where.created_at,
          lte: filters.endTime
        };
      }
      
      // 查询总数
      const total = await this.prisma.order_packages.count({ where });
      
      // 查询分页数据
      const packages = await this.prisma.order_packages.findMany({
        where,
        orderBy: {
          created_at: 'desc'
        },
        include: {
          order_package_items: {
            include: {
              order_item: true
            }
          }
        },
        skip: (page - 1) * pageSize,
        take: pageSize
      });
      
      return {
        total,
        list: packages
      };
    } catch (error) {
      console.error('获取包裹列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取包裹详情
   * @param {Number} id - 包裹ID
   * @returns {Promise<Object>} - 包裹详情
   */
  async getPackageById(id) {
    try {
      const packageId = this.ensureBigInt(id);
      
      const packageInfo = await this.prisma.order_packages.findFirst({
        where: { 
          id: packageId
        },
        include: {
          order_package_items: {
            include: {
              order_item: true
            }
          }
        }
      });
      
      if (!packageInfo) {
        throw new Error('包裹不存在或已删除');
      }
      
      return packageInfo;
    } catch (error) {
      console.error('获取包裹详情失败:', error);
      throw error;
    }
  }

  /**
   * 更新包裹信息
   * @param {Number} id - 包裹ID
   * @param {Object} data - 更新的数据
   * @returns {Promise<Object>} - 更新后的包裹数据
   */
  async updatePackage(id, data) {
    try {
      const packageId = this.ensureBigInt(id);
      const updatedPackage = await this.prisma.order_packages.update({
        where: { id: packageId },
        data: {
          recipient_name: data.recipientName,
          recipient_phone: data.recipientPhone,
          region_path_name: data.regionPathName,
          street_address: data.streetAddress,
          postal_code: data.postalCode,
          shipping_method: data.shippingMethod,
          remark: data.remark,
          updated_at: BigInt(Date.now())
        }
      });
      
      return updatedPackage;
    } catch (error) {
      console.error('更新包裹信息失败:', error);
      throw error;
    }
  }

  /**
   * 标记包裹为已发货
   * @param {Number} id - 包裹ID
   * @param {Object} shippingData - 物流信息
   * @returns {Promise<Object>} - 更新后的包裹数据
   */
  async shipPackage(id, shippingData) {
    try {
      const packageId = this.ensureBigInt(id);
      return await this.prisma.$transaction(async (prisma) => {
        // 获取包裹信息
        const packageInfo = await prisma.order_packages.findUnique({
          where: { id: packageId },
          include: { 
            orders: true
          }
        });
        
        if (!packageInfo) {
          throw new Error('包裹不存在');
        }
        
        if (packageInfo.shipping_status !== ShippingStatusEnum.UNSHIPPED) {
          throw new Error('包裹已发货，不能重复发货');
        }
        
        // 处理附件URL数组
        let imagesUrlArray = [];
        if (Array.isArray(shippingData.imagesUrl)) {
          // 如果传入的已经是数组，直接使用
          imagesUrlArray = shippingData.imagesUrl;
        } else if (shippingData.imagesUrl) {
          // 如果传入的是单个URL字符串，转为数组
          imagesUrlArray = [shippingData.imagesUrl];
        }
        
        // 处理是否验证快递的值
        const isExpressValidated = shippingData.isExpressValidated === 1 ? 1 : 0;
        
        // 更新包裹发货信息
        const updatedPackage = await prisma.order_packages.update({
          where: { id: packageId },
          data: {
            shipping_company_code: shippingData.shippingCompanyCode,
            shipping_company_name: shippingData.shippingCompanyName,
            tracking_number: shippingData.trackingNumber,
            images_url: imagesUrlArray.length > 0 ? imagesUrlArray : null, // 保存为JSON数组格式
            is_express_validated: isExpressValidated, // 是否验证快递：0-否 1-是
            shipping_status: ShippingStatusEnum.SHIPPED, // 已发货
            shipped_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now())
          }
        });
        
        // 更新订单发货状态
        await this.updateOrderShippingStatus(prisma, packageInfo.order_id);
        
        return updatedPackage;
      });
    } catch (error) {
      console.error('包裹发货失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新订单发货状态
   * @param {Object} prisma - prisma事务客户端
   * @param {Number} orderId - 订单ID
   */
  async updateOrderShippingStatus(prisma, orderId) {
    try {
      // 查询订单是否存在
      const orderExists = await prisma.orders.findFirst({
        where: { id: orderId },
        select: { id: true }
      });
      
      if (!orderExists) {
        return;
      }
      
      // 获取所有订单项
      const orderItems = await prisma.order_items.findMany({
        where: { order_id: orderId }
      });
      
      if (orderItems.length === 0) {
        return;
      }
      
      // 计算总商品数量和已发货数量
      let totalQuantity = 0;
      let totalShippedQuantity = 0;
      
      for (const item of orderItems) {
        totalQuantity += item.quantity;
        totalShippedQuantity += item.shipped_quantity || 0;
      }
      
      // 确定发货状态
      let shippingStatus = ShippingStatusEnum.UNSHIPPED;
      
      if (totalShippedQuantity === 0) {
        shippingStatus = ShippingStatusEnum.UNSHIPPED; // 未发货
      } else if (totalShippedQuantity < totalQuantity) {
        shippingStatus = ShippingStatusEnum.PARTIAL_SHIPPED; // 部分发货
      } else {
        shippingStatus = ShippingStatusEnum.SHIPPED; // 已发货
      }
      
      // 确定订单状态：当完全发货时，订单状态应为已发货
      let orderStatus = null;
      if (shippingStatus === ShippingStatusEnum.SHIPPED) {
        orderStatus = OrderStatusEnum.SHIPPED; // 设置订单状态为已发货(待收货)
      }
      
      // 准备更新数据
      const updateData = {
        shipping_status: shippingStatus,
        updated_at: BigInt(Date.now())
      };
      
      if (shippingStatus === ShippingStatusEnum.SHIPPED) {
        updateData.shipped_at = BigInt(Date.now());
      }
      
      if (orderStatus !== null) {
        updateData.order_status = orderStatus;
      }
      
      // 更新订单状态
      await prisma.orders.update({
        where: { id: orderId },
        data: updateData
      });
      
    } catch (error) {
      console.error('更新订单发货状态失败:', error.message);
      // 不抛出异常，确保发货过程可以继续
    }
  }
  
  /**
   * 根据订单ID获取包裹列表
   * @param {Number} orderId - 订单ID
   * @returns {Promise<Array>} - 包裹列表
   */
  async getPackagesByOrderId(orderId) {
    try {
      console.log('获取订单包裹列表，订单ID:', orderId, '类型:', typeof orderId);
      
      const orderIdBigInt = this.ensureBigInt(orderId);
      console.log('转换后的订单ID:', orderIdBigInt, '类型:', typeof orderIdBigInt);
      
      // 使用正确的表名，移除 deleted_at 字段（该表没有此字段）
      const packages = await this.prisma.order_packages.findMany({
        where: { 
          order_id: orderIdBigInt
        },
        include: {
          order_package_items: true
        },
        orderBy: {
          created_at: 'desc'
        }
      });
      
      console.log('查询到包裹数量:', packages.length);
      return packages;
    } catch (error) {
      // 处理BigInt的JSON序列化辅助函数
      const safeStringify = (obj, indent = null) => {
        return JSON.stringify(obj, (key, value) => 
          typeof value === 'bigint' ? value.toString() : value
        , indent);
      };
      
      console.error('根据订单ID获取包裹列表失败:', error instanceof Error ? error.message : safeStringify(error));
      throw error;
    }
  }

  /**
   * 批量创建多个包裹（统一发货接口）
   * @param {Number} orderId - 订单ID
   * @param {Array} packagesData - 包裹数据数组
   * @returns {Promise<Array>} - 创建的包裹数组
   */
  async createMultiplePackages(orderId, packagesData) {
    try {
      // 确保订单ID是BigInt类型
      const orderIdBigInt = this.ensureBigInt(orderId);
      
      // 查询该订单的详细信息
      const orderInfo = await this.prisma.orders.findUnique({
        where: { id: orderIdBigInt },
        include: {
          order_shipping_info: true,
          order_items: true // 获取订单项，用于验证和更新
        }
      });
      
      
      
      if (!orderInfo) {
        throw new Error('订单不存在');
      }
      
      if (orderInfo.order_status === OrderStatusEnum.CANCELLED) {
        throw new Error('订单状态为“已取消”或“已退款”，不能发货');
      }
      
      // if (orderInfo.payment_status === 0) {
      //   throw new Error('订单未支付，不能发货');
      // }

      // 应用事务处理所有数据库操作
      return await this.prisma.$transaction(async (tx) => {
        const createdPackages = [];

        // 默认收货信息
        const defaultRecipientInfo = orderInfo.order_shipping_info ? {
          recipient_name: orderInfo.order_shipping_info.recipient_name,
          recipient_phone: orderInfo.order_shipping_info.recipient_phone,
          region_path_name: orderInfo.order_shipping_info.region_path_name,
          street_address: orderInfo.order_shipping_info.street_address,
          postal_code: orderInfo.order_shipping_info.postal_code
        } : null;
        
        if (!defaultRecipientInfo) {
          throw new Error('订单没有收货地址信息');
        }
        
        // 处理BigInt的JSON序列化辅助函数
        const safeStringify = (obj, indent = null) => {
          return JSON.stringify(obj, (key, value) => 
            typeof value === 'bigint' ? value.toString() : value
          , indent);
        };
        

        
        // 创建订单项ID和实例的映射，方便快速查找
        const orderItemsMap = {};
        for (const item of orderInfo.order_items) {
          orderItemsMap[item.id] = item;
        }
        
        // 是否自动修正订单项ID
        const autoFixOrderItems = true;
        
        // 验证所有商品都发货，所有拆包裹的商品数量总和必须等于订单详情商品的总数量
        const orderItemQuantityMap = {};
        
        // 初始化订单商品数量映射
        for (const item of orderInfo.order_items) {
          // 计算待发货数量 = 总数量 - 已发货数量
          const pendingQuantity = item.quantity - (item.shipped_quantity || 0);
          if (pendingQuantity > 0) {
            orderItemQuantityMap[item.id] = {
              pendingQuantity,
              plannedQuantity: 0,
              productName: item.product_name
            };
          }
        }
        
        // 统计计划发货的数量
        for (const packageData of packagesData) {
          for (const item of packageData.items) {
            if (orderItemQuantityMap[item.orderItemId]) {
              orderItemQuantityMap[item.orderItemId].plannedQuantity += item.quantity;
            }
          }
        }
        
        // 检查每个订单项的计划发货数量是否等于待发货数量
        for (const [orderItemId, info] of Object.entries(orderItemQuantityMap)) {
          if (info.plannedQuantity !== info.pendingQuantity) {
            throw new Error(`商品 ${info.productName} 的发货数量不等于待发货数量。待发货: ${info.pendingQuantity}, 计划发货: ${info.plannedQuantity}`); 
          }
        }
        

        
        // 遍历处理每个包裹
        for (const packageData of packagesData) {
          // 验证包裹中的商品项
          for (let i = 0; i < packageData.items.length; i++) {
            const item = packageData.items[i];
            
            const orderItem = orderItemsMap[item.orderItemId];
            if (!orderItem) {
              if (autoFixOrderItems && orderInfo.order_items.length > 0) {
                // 自动使用可用的订单项ID
                const availableItem = orderInfo.order_items[0];
                packageData.items[i].orderItemId = availableItem.id;
                continue; // 使用有效的订单项ID后继续处理
              } else {
                throw new Error(`订单项ID ${item.orderItemId} 不存在或不属于当前订单`);
              }
            }
            
            // 验证已发货数量不超过总数量
            const remainingQuantity = orderItem.quantity - orderItem.shipped_quantity;
            if (remainingQuantity < item.quantity) {
              throw new Error(`商品 ${orderItem.product_name} 的发货数量超过了可发货数量，当前可发货: ${remainingQuantity}`);
            }
          }
          
          // 生成包裹编号
          const packageSn = `PKG${generateRandomId(10)}`;
          
          // 使用默认收货地址
          const recipientInfo = defaultRecipientInfo;
          
          // 确定配送方式和物流信息
          const shippingMethod = packageData.shippingMethod || 1; // 默认快递物流
          
          // 处理附件URL数组
          let imagesUrlArray = [];
          if (Array.isArray(packageData.imagesUrl)) {
            // 如果传入的已经是数组，直接使用
            imagesUrlArray = packageData.imagesUrl;
          } else if (packageData.imagesUrl) {
            // 如果传入的是单个URL字符串，转为数组
            imagesUrlArray = [packageData.imagesUrl];
          }

          // 处理是否验证快递的值
          const isExpressValidated = packageData.isExpressValidated === 1 ? 1 : 0;

          // 创建包裹
          const newPackage = await tx.order_packages.create({
            data: {
              order_id: orderIdBigInt,
              package_sn: packageSn,
              recipient_name: recipientInfo.recipient_name,
              recipient_phone: recipientInfo.recipient_phone,
              region_path_name: recipientInfo.region_path_name,
              street_address: recipientInfo.street_address,
              postal_code: recipientInfo.postal_code,
              shipping_method: shippingMethod,
              // 当配送方式为1(快递物流)时需要物流信息，配送方式为2(无需物流)时无需提供物流信息
              shipping_company_code: shippingMethod === 1 ? packageData.shippingCompanyCode : undefined,
              shipping_company_name: shippingMethod === 1 ? packageData.shippingCompanyName : undefined,
              tracking_number: shippingMethod === 1 ? packageData.trackingNumber : undefined,
              images_url: imagesUrlArray.length > 0 ? imagesUrlArray : undefined, // 保存为JSON数组格式
              shipping_origin: packageData.shippingOrigin || undefined, // 发货地
              is_express_validated: isExpressValidated, // 是否验证快递：0-否 1-是
              shipping_status: ShippingStatusEnum.SHIPPED, // 直接设置为已发货
              shipped_at: BigInt(Date.now()),
              remark: packageData.remark || undefined
            }
          });
          
          // 创建包裹商品项并更新已发货数量
          const packageItemPromises = [];
          const updateItemPromises = [];
          
          for (const item of packageData.items) {
            // 创建包裹项
            packageItemPromises.push(
              tx.order_package_items.create({
                data: {
                  package_id: newPackage.id,
                  order_item_id: BigInt(item.orderItemId),
                  quantity: item.quantity
                }
              })
            );
            
            // 更新订单项的已发货数量
            updateItemPromises.push(
              tx.order_items.update({
                where: { id: BigInt(item.orderItemId) },
                data: {
                  shipped_quantity: {
                    increment: item.quantity
                  }
                }
              })
            );
          }
          
          // 并行执行创建和更新操作，提高性能
          await Promise.all([...packageItemPromises, ...updateItemPromises]);
          
          createdPackages.push(newPackage);
        }
        
        // 更新订单发货状态
        await this.updateOrderShippingStatus(tx, orderIdBigInt);
        
        return createdPackages;
      });
    } catch (error) {
      // 处理BigInt的JSON序列化辅助函数
      const safeStringify = (obj, indent = null) => {
        return JSON.stringify(obj, (key, value) => 
          typeof value === 'bigint' ? value.toString() : value
        , indent);
      };
      
      console.error('发货失败:', error instanceof Error ? error.message : safeStringify(error));
      throw error;
    }
  }

  /**
   * 确认收货
   * @param {Number} id - 包裹ID
   * @returns {Promise<Object>} - 更新后的包裹数据
   */
  async confirmReceive(id) {
    try {
      const packageId = this.ensureBigInt(id);
      return await this.prisma.$transaction(async (prisma) => {
        // 获取包裹信息
        const packageInfo = await prisma.order_packages.findUnique({
          where: { id: packageId }
        });
        
        if (!packageInfo) {
          throw new Error('包裹不存在');
        }
        
        if (packageInfo.shipping_status !== ShippingStatusEnum.SHIPPED) {
          throw new Error('包裹未发货，不能确认收货');
        }
        
        // 更新包裹状态为已收货
        const updatedPackage = await prisma.order_packages.update({
          where: { id: packageId },
          data: {
            shipping_status: ShippingStatusEnum.RECEIVED, // 已收货
            updated_at: BigInt(Date.now())
          }
        });
        
        // 获取该订单的所有包裹
        const packages = await prisma.order_packages.findMany({
          where: { order_id: packageInfo.order_id }
        });
        
        // 检查是否所有包裹都已收货
        const allReceived = packages.every(pkg => pkg.shipping_status === ShippingStatusEnum.RECEIVED);
        
        // 如果所有包裹都已收货，更新订单状态为已收货
        if (allReceived) {
          await prisma.orders.update({
            where: { id: packageInfo.order_id },
            data: { 
              order_status: OrderStatusEnum.COMPLETED, // 交易成功
              updated_at: BigInt(Date.now())
            }
          });
        }
        
        return updatedPackage;
      });
    } catch (error) {
      console.error('确认收货失败:', error);
      throw error;
    }
  }
}

module.exports = new OrderPackageModel();

/**
 * 自主品牌溯源码数据模型
 */
const prismaManager = require('../../../core/prisma');
const { generateRandomString, generateFirstRandom, generateSecondRandom } = require('../../../shared/utils/random');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class SelfSourceCodeModel {
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 获取溯源码列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.limit 每页数量
   * @param {String} params.source_code 溯源码
   * @param {String} params.brand_name 品牌名称
   * @param {Number} params.export_status 导出状态：0-未导出，1-已导出
   * @param {Number} params.start_time 创建开始时间戳（毫秒）
   * @param {Number} params.end_time 创建结束时间戳（毫秒）
   * @param {String|Number} params.brand_id 自主品牌ID
   * @returns {Promise<{rows: Array, total: Number}>} 溯源码列表和总数
   */
  async getSelfSourceCodeList(params) {
    const { page = 1, limit = 20, source_code, brand_name, export_status, start_time, end_time, brand_id } = params;
    const skip = (page - 1) * limit;
    
    // 打印参数信息，用于调试
    console.log('=== 溯源码列表参数详情 ===');
    console.log('模型层 getSelfSourceCodeList 接收到的完整参数:', JSON.stringify(params, null, 2));
    console.log('参数类型详情:');
    for (const key in params) {
      console.log(`- ${key}: ${typeof params[key]} = ${params[key]}`);
      if (key === 'brand_id' || key === 'brandId') {
        console.log(`  - 是否为数字: ${!isNaN(Number(params[key]))}`); 
        console.log(`  - 是否超过安全整数范围: ${BigInt(params[key]) > BigInt(Number.MAX_SAFE_INTEGER)}`);
        console.log(`  - 转换为BigInt后的值: ${BigInt(params[key])}`); 
      }
    }
    console.log('=== 溯源码列表参数详情结束 ===');
    
    // 构建查询条件
    const where = {};
    
    // 处理品牌ID参数
    if (brand_id) {
      try {
        // 将brand_id转换为BigInt类型，支持字符串和数字类型
        const bigIntBrandId = typeof brand_id === 'string' ? BigInt(brand_id) : BigInt(brand_id.toString());
        console.log(`成功将brand_id [${brand_id}] 转换为BigInt: ${bigIntBrandId}`);
        
        // 添加到查询条件
        where.brand_id = bigIntBrandId;
      } catch (error) {
        console.error(`将brand_id [${brand_id}] 转换为BigInt时出错:`, error);
        throw new Error(`自主品牌ID格式不正确: ${brand_id}`);
      }
    }
    
    if (source_code) {
      where.source_code = {
        contains: source_code,
      };
    }
    
    if (brand_name) {
      where.brand_name = {
        contains: brand_name,
      };
    }
    
    if (export_status !== undefined) {
      where.export_status = export_status;
    }
    
    if (start_time && end_time) {
      where.created_at = {
        gte: new Date(start_time),
        lte: new Date(end_time),
      };
    } else if (start_time) {
      where.created_at = {
        gte: new Date(start_time),
      };
    } else if (end_time) {
      where.created_at = {
        lte: new Date(end_time),
      };
    }
    
    console.log('最终查询条件:', JSON.stringify(where, (key, value) => 
      typeof value === 'bigint' ? value.toString() : value, 2
    ));

    // 查询溯源码列表
    const [rows, total] = await Promise.all([
      this.prisma.SelfSourceCode.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          created_at: 'desc',
        },
        include: {
          brand: {
            select: {
              id: true,
              name: true,
              image: true,
              trademark_code: true,
            },
          },
        },
      }),
      this.prisma.SelfSourceCode.count({ where }),
    ]);

    return { rows, total };
  }
  
  /**
   * 根据自主品牌ID查询溯源码列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.limit 每页条数
   * @param {Number|String} params.brandId 自主品牌ID（必填）
   * @param {String} params.sourceCode 溯源码
   * @param {String} params.brandName 自主品牌名称
   * @param {Number} params.exportStatus 导出状态（可选）
   * @param {Number} params.startTime 创建开始时间
   * @param {Number} params.endTime 创建结束时间
   * @returns {Promise<{rows: Array, total: Number}>} 溯源码列表和总数
   */
  async getSourceCodesByBrandId(params) {
    // 打印完整参数信息，用于调试
    console.log('=== 溯源码列表参数详情 ===');
    console.log('模型层 getSelfSourceCodeList 接收到的完整参数:', JSON.stringify(params, null, 2));
    console.log('参数类型详情:');
    for (const key in params) {
      console.log(`- ${key}: ${typeof params[key]} = ${params[key]}`);
      if (key === 'brand_id' || key === 'brandId') {
        console.log(`  - 是否为数字: ${!isNaN(Number(params[key]))}`); 
        console.log(`  - 是否超过安全整数范围: ${BigInt(params[key]) > BigInt(Number.MAX_SAFE_INTEGER)}`);
        console.log(`  - 转换为BigInt后的值: ${BigInt(params[key])}`); 
      }
    }
    
    // 修复参数解析问题，同时支持驼峰和下划线命名
    const { 
      page = 1, 
      limit = 20, 
      // 同时支持驼峰和下划线命名
      brandId = params.brand_id, 
      sourceCode = params.source_code, 
      brandName = params.brand_name, 
      exportStatus = params.export_status, 
      startTime = params.start_time, 
      endTime = params.end_time 
    } = params;
    
    const skip = (page - 1) * limit;
    
    console.log(`解析后的参数: page=${page}, limit=${limit}, brandId=${brandId}, sourceCode=${sourceCode}, brandName=${brandName}, exportStatus=${exportStatus}`);

    // 优先使用brand_id，然后才是brandId
    const finalBrandId = params.brand_id || brandId;
    console.log(`最终使用的brandId: ${finalBrandId}`);
    
    if (!finalBrandId) {
      console.error('错误: 自主品牌ID为空');
      throw new Error('自主品牌ID不能为空');
    }
    
    // 处理雪花ID（大数字）
    let bigIntBrandId;
    try {
      // 将brandId转换为BigInt类型，支持字符串和数字类型
      bigIntBrandId = typeof brandId === 'string' ? BigInt(brandId) : BigInt(brandId.toString());
      console.log(`成功将brandId [${brandId}] 转换为BigInt: ${bigIntBrandId}`);
    } catch (error) {
      console.error(`将brandId [${brandId}] 转换为BigInt时出错:`, error);
      throw new Error(`自主品牌ID格式不正确: ${brandId}`);
    }

    // 构建查询条件
    const where = {
      brand_id: bigIntBrandId,
      deleted_at: null,
    };

    // 如果有溯源码条件，添加到查询条件中
    if (sourceCode) {
      where.source_code = {
        contains: sourceCode,
      };
    }

    // 如果有导出状态条件，添加到查询条件中
    if (exportStatus !== undefined) {
      where.is_exported = exportStatus;
    }

    // 如果有时间范围条件，添加到查询条件中
    if (startTime && endTime) {
      where.created_at = {
        gte: startTime,
        lte: endTime,
      };
    } else if (startTime) {
      where.created_at = {
        gte: startTime,
      };
    } else if (endTime) {
      where.created_at = {
        lte: endTime,
      };
    }

    // 如果有品牌名称条件，需要使用关联查询
    let brandFilter = {};
    if (brandName) {
      brandFilter = {
        name: {
          contains: brandName,
        }
      };
    }

    // 查询溯源码列表
    const [rows, total] = await Promise.all([
      this.prisma.SelfSourceCode.findMany({
        where: {
          ...where,
          // 使用关联查询过滤品牌名称
          ...(brandName ? { brand: brandFilter } : {}),
        },
        skip,
        take: limit,
        orderBy: {
          created_at: 'desc',
        },
        include: {
          brand: {
            select: {
              id: true,
              name: true,
              image: true,
              trademark_code: true,
            },
          },
        },
      }),
      this.prisma.SelfSourceCode.count({
        where: {
          ...where,
          ...(brandName ? { brand: brandFilter } : {}),
        },
      }),
    ]);

    // 格式化返回数据，确保包含所有需要的字段
    const formattedRows = rows.map(row => ({
      ...row,
      // 确保数据中包含导出状态字段，如果没有则默认为未导出(0)
      is_exported: row.is_exported !== undefined ? row.is_exported : 0,
      // 确保包含品牌名称
      brand_name: row.brand?.name || '',
    }));

    return { rows: formattedRows, total };
  }

  /**
   * 获取溯源码详情
   * @param {Number|String} id 溯源码ID
   * @returns {Promise<Object>} 溯源码详情
   */
  async getSelfSourceCodeDetail(id) {
    try {
      // 使用findFirst代替findUnique，因为需要添加deleted_at条件
      const sourceCode = await this.prisma.SelfSourceCode.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        include: {
          brand: {
            select: {
              id: true,
              name: true,
              image: true  // 使用正确的字段名image代替logo_url
            }
          }
        }
      });

      if (!sourceCode) {
        throw new Error('溯源码不存在或已删除');
      }

      return sourceCode;
    } catch (error) {
      console.error('获取溯源码详情失败:', error);
      throw error;
    }
  }
  
  /**
   * 根据溯源码字符串查询溯源码详情
   * @param {String} sourceCode 溯源码字符串
   * @returns {Promise<Object>} 溯源码详情
   */
  async getSelfSourceCodeByCode(sourceCode) {
    try {
      const code = await this.prisma.SelfSourceCode.findFirst({
        where: {
          source_code: sourceCode,
          deleted_at: null
        },
        include: {
          brand: {
            select: {
              id: true,
              name: true,
              logo_url: true
            }
          }
        }
      });

      if (!code) {
        throw new Error('溯源码不存在或已删除');
      }

      return code;
    } catch (error) {
      console.error('根据溯源码字符串查询溯源码详情失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新溯源码查询信息
   * @param {BigInt|String} id 溯源码ID
   * @returns {Promise<Object>} 更新后的溯源码
   */
  async updateSourceCodeQueryInfo(id) {
    try {
      const now = BigInt(Date.now());
      const sourceCode = await this.prisma.SelfSourceCode.findUnique({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!sourceCode) {
        throw new Error('溯源码不存在或已删除');
      }

      // 更新查询次数和首次查询时间（如果是第一次查询）
      const updatedSourceCode = await this.prisma.SelfSourceCode.update({
        where: {
          id: BigInt(id)
        },
        data: {
          query_count: sourceCode.query_count + 1,
          first_query_time: sourceCode.first_query_time || now,
          updated_at: now
        }
      });

      return updatedSourceCode;
    } catch (error) {
      console.error('更新溯源码查询信息失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取溯源码查询情况列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.limit 每页数量
   * @param {String} params.sourceCode 溯源码
   * @param {String} params.brandName 品牌名称
   * @param {Number} params.startTime 创建开始时间戳（毫秒）
   * @param {Number} params.endTime 创建结束时间戳（毫秒）
   * @returns {Promise<{rows: Array, total: Number}>} 溯源码查询情况列表和总数
   */
  async getSourceCodeQueryList(params) {
    try {
      const { page = 1, limit = 10, sourceCode, brandName, startTime, endTime } = params;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where = {
        deleted_at: null,
        query_count: {
          gt: 0 // 只查询查询次数大于0的溯源码
        }
      };

      if (sourceCode) {
        where.source_code = {
          contains: sourceCode
        };
      }

      if (startTime) {
        where.created_at = {
          ...where.created_at,
          gte: BigInt(startTime)
        };
      }

      if (endTime) {
        where.created_at = {
          ...where.created_at,
          lte: BigInt(endTime)
        };
      }

      // 如果有品牌名称筛选，需要关联查询
      if (brandName) {
        where.brand = {
          name: {
            contains: brandName
          }
        };
      }

      // 查询数据和总数
      const [rows, total] = await Promise.all([
        this.prisma.SelfSourceCode.findMany({
          where,
          include: {
            brand: {
              select: {
                id: true,
                name: true,
                logo_url: true
              }
            }
          },
          orderBy: {
            query_count: 'desc'
          },
          skip,
          take: limit
        }),
        this.prisma.SelfSourceCode.count({ where })
      ]);

      return {
        rows,
        total
      };
    } catch (error) {
      console.error('获取溯源码查询情况列表失败:', error);
      throw error;
    }
  }

  /**
   * 分布式锁模拟函数
   * 注意：这只是一个模拟函数，实际生产环境中应该使用Redis分布式锁
   * @param {String} lockKey 锁的键名
   * @param {Number} expirationTimeSeconds 锁的过期时间（秒）
   * @returns {Boolean} 是否获取锁成功
   */
  async _tryLock(lockKey, expirationTimeSeconds = 60) {
    // 模拟分布式锁成功
    // 实际生产环境中应该使用Redis等实现分布式锁
    console.log(`获取锁成功: ${lockKey}, 过期时间: ${expirationTimeSeconds}秒`);
    return true;
  }

  /**
   * 释放分布式锁模拟函数
   * @param {String} lockKey 锁的键名
   */
  async _releaseLock(lockKey) {
    // 模拟释放锁
    console.log(`释放锁成功: ${lockKey}`);
  }

  /**
   * 生成溯源码
   * @param {Number|String} brandId 自主品牌ID
   * @param {Number} count 生成数量，默认1个
   * @param {Number|String} creatorId 创建者ID，默认0
   * @returns {Promise<Array>} 生成的溯源码列表
   */
  async generateSelfSourceCodes(brandId, count = 1, userId = 0) {
    if (count <= 0 || count > 1000) {
      throw new Error('生成数量必须在1-1000之间');
    }

    // 将brandId和userId转换为BigInt类型
    const bigIntBrandId = typeof brandId === 'string' ? BigInt(brandId) : brandId;
    const bigIntUserId = typeof userId === 'string' ? BigInt(userId) : userId;
    
    // 检查品牌是否存在
    const brand = await this.prisma.SelfOwnedBrand.findFirst({
      where: {
        id: bigIntBrandId,
        deleted_at: null,
      },
    });

    if (!brand) {
      throw new Error('自主品牌不存在');
    }

    // 获取品牌规则码
    const brandRuleCode = await this.prisma.SelfBrandRuleCode.findFirst({
      where: {
        name: brand.name,
        deleted_at: null,
      },
    });

    // 如果没有找到对应的品牌规则码，使用默认规则
    const rulePrefix = brandRuleCode ? brandRuleCode.rule_code : brand.trademark_code.substring(0, 4).toUpperCase();

    // 尝试获取分布式锁
    const lockKey = `sourceCode:generate:${rulePrefix}`;
    const lockAcquired = await this._tryLock(lockKey, 60); // 60秒锁

    if (!lockAcquired) {
      throw new Error(`规则码 ${rulePrefix} 正在生成溯源码，请稍后重试`);
    }

    try {
      const now = new Date();
      const requiredCount = Math.max(count * 2, 10); // 生成所需数量的2倍或至少10个候选码
      const uniqueCodesMap = new Map();
      const candidateCodes = [];
      
      // 创建更安全的时间戳
      const timestamp = Math.floor(now.getTime());
      
      console.log(`准备生成 ${requiredCount} 个候选溯源码`);

      while (uniqueCodesMap.size < requiredCount) {
        // 生成溯源码
        const rulePrefix = brand.source_code_rule_prefix || '';
        const firstRandom = generateFirstRandom();
        const secondRandom = generateSecondRandom();
        
        // 调试日志
        console.log(`生成溯源码 - rulePrefix: [${rulePrefix}], type: ${typeof rulePrefix}, length: ${rulePrefix.length}`);
        
        // 检测并清理前缀中的无效UTF-8字符
        let cleanPrefix = '';
        try {
          // 如果前缀包含无效字符，尝试清理它们
          if (rulePrefix) {
            // 将字符串转为buffer再转回字符串，可以移除无效字符
            cleanPrefix = Buffer.from(rulePrefix, 'utf8').toString('utf8');
            // 如果转换后长度不同，记录警告信息
            if (cleanPrefix.length !== rulePrefix.length) {
              console.warn(`前缀包含无效字符，已清理: 原长度${rulePrefix.length}, 清理后长度${cleanPrefix.length}`);
            }
          }
        } catch (err) {
          console.error(`清理前缀失败: ${err.message}`);
          // 失败时使用空字符串
          cleanPrefix = '';
        }
        
        const sourceCode = `${cleanPrefix}${firstRandom}${secondRandom}`;
        console.log(`生成的溯源码: [${sourceCode}]`);
        
        // 确保批次内没有重复
        if (!uniqueCodesMap.has(sourceCode)) {
          uniqueCodesMap.set(sourceCode, true);
          // 创建候选溯源码数据，确保与Prisma模型定义一致
          candidateCodes.push({
            brand_id: bigIntBrandId,
            source_code: sourceCode,
            status: 0, // 默认未启用
            created_at: BigInt(timestamp),
            updated_at: BigInt(timestamp),
            // 当userId为0时使用null，避免数据库外键约束问题
            created_by: userId && userId !== '0' ? bigIntUserId : null,
            updated_by: userId && userId !== '0' ? bigIntUserId : null
          });
        }
      }

      console.log(`成功生成 ${candidateCodes.length} 个候选溯源码`);

      // 检查数据库中是否已存在相同的溯源码
      const candidateSourceCodes = candidateCodes.map(code => code.source_code);
      
      const existingCodes = await this.prisma.SelfSourceCode.findMany({
        where: {
          source_code: {
            in: candidateSourceCodes
          },
          deleted_at: null
        },
        select: {
          source_code: true
        }
      });
      
      console.log(`发现 ${existingCodes.length} 个已存在的溯源码，进行过滤`);
      
      // 过滤掉已存在的溯源码
      const existingCodeSet = new Set(existingCodes.map(code => code.source_code));
      const filteredCodes = candidateCodes.filter(code => !existingCodeSet.has(code.source_code));
      
      // 取需要数量的溯源码
      const codesToCreate = filteredCodes.slice(0, count);
      
      if (codesToCreate.length < count) {
        console.warn(`只能生成 ${codesToCreate.length} 个唯一的溯源码，低于请求的 ${count} 个`);
        throw new Error(`只能生成 ${codesToCreate.length} 个唯一的溯源码，低于请求的 ${count} 个`);
      }

      console.log(`开始批量创建 ${codesToCreate.length} 个溯源码`);

      // 开启事务批量创建溯源码
      const sourceCodes = await this.prisma.$transaction(async (tx) => {
        const createdCodes = [];
        
        for (const codeData of codesToCreate) {
          // 使用雪花算法生成ID
          const sourceCodeId = generateSnowflakeId();
          
          // 显式提供ID字段
          // 清理所有字符串字段，确保UTF-8编码有效
          const sanitizeString = (str) => {
            if (!str || typeof str !== 'string') return '';
            try {
              return Buffer.from(str, 'utf8').toString('utf8');
            } catch (e) {
              console.error(`UTF-8清理失败: ${e.message}`);
              return '';
            }
          };

          // 获取原始数据的深拷贝
          const originalData = { ...codeData };
          
          // 清理所有可能的字符串字段
          const safeSourceCode = sanitizeString(codeData.source_code);
          // 如果清理后字符串为空，生成替代字符串
          const finalSourceCode = safeSourceCode || `SAFE_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
          
          // 安全输出数据对象，避免BigInt序列化问题
          // BigInt不能直接被JSON.stringify序列化
          const safeLogObject = (obj) => {
            try {
              // 转换原始对象的浅拷贝
              const safeObj = {};
              for (const key in obj) {
                if (Object.hasOwnProperty.call(obj, key)) {
                  const value = obj[key];
                  // 判断BigInt类型并转换为字符串
                  if (typeof value === 'bigint') {
                    safeObj[key] = value.toString();
                  } else {
                    safeObj[key] = value;
                  }
                }
              }
              return JSON.stringify(safeObj);
            } catch (err) {
              console.error(`序列化对象失败: ${err.message}`);
              return '{"无法序列化的对象"}';
            }
          };

          // 记录原始数据
          console.log('原始数据:', safeLogObject(originalData));
          console.log(`准备创建溯源码: ${finalSourceCode}`);
          
          // 构建安全的数据对象 - 与 Prisma 模型定义保持一致
          // 注意: created_at/updated_at 应该使用 BigInt 毫秒时间戳
          // created_by/updated_by 应该使用 BigInt? 类型
          const nowTimestamp = BigInt(Date.now()); // 当前时间毫秒戳
          
          const safeData = {
            id: BigInt(sourceCodeId),
            brand_id: BigInt(codeData.brand_id),
            source_code: finalSourceCode,
            status: typeof codeData.status === 'number' ? codeData.status : 0,
            // 确保使用BigInt类型的时间戳
            created_at: nowTimestamp,
            updated_at: nowTimestamp,
            // 当userId为0时使用null，避免数据库外键约束问题
            created_by: userId && userId !== '0' ? bigIntUserId : null,
            updated_by: userId && userId !== '0' ? bigIntUserId : null
          };
          
          // 安全输出准备创建的数据
          console.log('安全数据:', safeLogObject(safeData));
          
          const code = await tx.SelfSourceCode.create({
            data: safeData
          });
          
          createdCodes.push(code);
        }
        
        return createdCodes;
      });

      console.log(`成功创建 ${sourceCodes.length} 个溯源码`);
      return sourceCodes;
    } finally {
      // 释放锁
      await this._releaseLock(lockKey);
    }
  }

  /**
   * 批量更新溯源码状态
   * @param {Array<Number|String>} ids 溯源码ID数组
   * @param {Number} status 状态：0-未启用，1-已启用
   * @returns {Promise<Object>} 更新结果
   */
  async updateSelfSourceCodesStatus(ids, status) {
    const now = Date.now();
    
    // 将ID数组转换为BigInt类型
    const bigIntIds = ids.map(id => typeof id === 'string' ? BigInt(id) : id);

    // 开启事务批量更新状态
    return await this.prisma.$transaction(async (tx) => {
      const result = await tx.SelfSourceCode.updateMany({
        where: {
          id: {
            in: bigIntIds,
          },
          deleted_at: null,
        },
        data: {
          status,
          activation_time: status === 1 ? now : null, // 如果状态为启用，记录激活时间
          updated_at: now,
        },
      });

      return { count: result.count };
    });
  }

  /**
   * 批量删除溯源码（软删除）
   * @param {Array<Number|String>} ids 溯源码ID数组
   * @returns {Promise<Object>} 删除结果
   */
  async deleteSelfSourceCodes(ids) {
    const now = Date.now();
    
    // 将ID数组转换为BigInt类型
    const bigIntIds = ids.map(id => typeof id === 'string' ? BigInt(id) : id);

    // 开启事务批量删除
    return await this.prisma.$transaction(async (tx) => {
      const result = await tx.SelfSourceCode.updateMany({
        where: {
          id: {
            in: bigIntIds,
          },
          deleted_at: null,
        },
        data: {
          deleted_at: now,
        },
      });

      return { count: result.count };
    });
  }
}

module.exports = new SelfSourceCodeModel();

const prismaManager = require('../../../core/prisma');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    return Number(data);
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = handleBigInt(data[key]);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 商品规格模型
 */
class GoodsSpecificationModel {
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 获取所有规格名称
   * @returns {Promise<Array>} 规格名称列表
   */
  async getAllSpecNames() {
    try {
      const specNames = await this.prisma.goodsSpecificationName.findMany({
        where: {
          deleted_at: null
        },
        include: {
          goods_specification_values: true
        },
        orderBy: {
          id: 'asc'
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(specNames));
    } catch (error) {
      console.error('获取所有规格名称失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取规格名称
   * @param {string|number} id 规格名称ID
   * @returns {Promise<Object>} 规格名称
   */
  async getSpecNameById(id) {
    try {
      const specName = await this.prisma.goodsSpecificationName.findUnique({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        include: {
          goods_specification_values: true
        }
      });
      
      return specName ? convertKeysToCamelCase(handleBigInt(specName)) : null;
    } catch (error) {
      console.error(`获取规格名称(ID:${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据名称获取规格名称
   * @param {string} name 规格名称
   * @returns {Promise<Object>} 规格名称
   */
  async getSpecNameByName(name) {
    try {
      const specName = await this.prisma.goodsSpecificationName.findFirst({
        where: {
          name: name,
          deleted_at: null
        },
        include: {
          goods_specification_values: true
        }
      });
      
      return specName ? convertKeysToCamelCase(handleBigInt(specName)) : null;
    } catch (error) {
      console.error(`获取规格名称(名称:${name})失败:`, error);
      throw error;
    }
  }

  /**
   * 添加规格名称
   * @param {string} name 规格名称
   * @returns {Promise<Object>} 添加的规格名称
   */
  async addSpecName(name) {
    try {
      const specName = await this.prisma.goodsSpecificationName.create({
        data: {
          name: name
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(specName));
    } catch (error) {
      console.error(`添加规格名称(${name})失败:`, error);
      throw error;
    }
  }

  /**
   * 查找或创建规格名称
   * @param {string} name 规格名称
   * @returns {Promise<Object>} 规格名称
   */
  async findOrCreateSpecName(name) {
    try {
      let specName = await this.getSpecNameByName(name);
      
      if (!specName) {
        specName = await this.addSpecName(name);
      }
      
      return specName;
    } catch (error) {
      console.error(`查找或创建规格名称(${name})失败:`, error);
      throw error;
    }
  }

  /**
   * 获取规格值
   * @param {string|number} specNameId 规格名称ID
   * @param {string} value 规格值
   * @returns {Promise<Object>} 规格值
   */
  async getSpecValue(specNameId, value) {
    try {
      const specValue = await this.prisma.goodsSpecificationValue.findFirst({
        where: {
          goods_specification_name_id: BigInt(specNameId),
          value: value,
          deleted_at: null
        }
      });
      
      return specValue ? convertKeysToCamelCase(handleBigInt(specValue)) : null;
    } catch (error) {
      console.error(`获取规格值(规格名ID:${specNameId}, 值:${value})失败:`, error);
      throw error;
    }
  }

  /**
   * 添加规格值
   * @param {string|number} specNameId 规格名称ID
   * @param {string} value 规格值
   * @returns {Promise<Object>} 添加的规格值
   */
  async addSpecValue(specNameId, value) {
    try {
      const specValue = await this.prisma.goodsSpecificationValue.create({
        data: {
          goods_specification_name_id: BigInt(specNameId),
          value: value
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(specValue));
    } catch (error) {
      console.error(`添加规格值(规格名ID:${specNameId}, 值:${value})失败:`, error);
      throw error;
    }
  }

  /**
   * 查找或创建规格值
   * @param {string|number} specNameId 规格名称ID
   * @param {string} value 规格值
   * @returns {Promise<Object>} 规格值
   */
  async findOrCreateSpecValue(specNameId, value) {
    try {
      let specValue = await this.getSpecValue(specNameId, value);
      
      if (!specValue) {
        specValue = await this.addSpecValue(specNameId, value);
      }
      
      return specValue;
    } catch (error) {
      console.error(`查找或创建规格值(规格名ID:${specNameId}, 值:${value})失败:`, error);
      throw error;
    }
  }

  /**
   * 添加SKU规格值关联
   * @param {string|number} skuId SKU ID
   * @param {string|number} specValueId 规格值ID
   * @returns {Promise<Object>} 添加的关联
   */
  async addSkuSpecValue(skuId, specValueId) {
    try {
      // 这里需要根据实际的数据库表结构来实现
      // 如果有专门的SKU规格值关联表，则使用该表
      // 如果没有，则可能需要在SKU表中添加规格值关联
      
      // 示例实现，假设有专门的SKU规格值关联表
      const skuSpecValue = await this.prisma.goodsSkuSpecificationValue.create({
        data: {
          goods_sku_id: BigInt(skuId),
          goods_specification_value_id: BigInt(specValueId)
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(skuSpecValue));
    } catch (error) {
      console.error(`添加SKU规格值关联(SKU ID:${skuId}, 规格值ID:${specValueId})失败:`, error);
      throw error;
    }
  }

  /**
   * 获取SKU的规格值
   * @param {string|number} skuId SKU ID
   * @returns {Promise<Array>} 规格值列表
   */
  async getSkuSpecValues(skuId) {
    try {
      // 这里需要根据实际的数据库表结构来实现
      // 示例实现，假设有专门的SKU规格值关联表
      const skuSpecValues = await this.prisma.goodsSkuSpecificationValue.findMany({
        where: {
          goods_sku_id: BigInt(skuId)
        },
        include: {
          goods_specification_value: {
            include: {
              goods_specification_name: true
            }
          }
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(skuSpecValues));
    } catch (error) {
      console.error(`获取SKU(ID:${skuId})的规格值失败:`, error);
      throw error;
    }
  }
}

module.exports = new GoodsSpecificationModel();

/**
 * 自主品牌授权数据模型
 */
const prismaManager = require('../../../core/prisma');

class SelfBrandEmpowerModel {
  constructor() {
    this.prisma = prismaManager.getClient('master');
  }

  /**
   * 获取自主品牌授权列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.limit 每页条数
   * @param {String} params.empower_number 授权编号
   * @param {String} params.empower_brand 授权品牌
   * @param {Number} params.start_time 开始时间
   * @param {Number} params.end_time 结束时间
   * @param {Number} params.status 状态
   * @returns {Promise<{rows: Array, total: Number}>} 授权列表和总数
   */
  async getSelfBrandEmpowerList(params) {
    const { page = 1, limit = 20, empower_number, empower_brand, start_time, end_time, status } = params;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where = {
      deleted_at: null,
    };

    if (empower_number) {
      where.empower_number = {
        contains: empower_number,
      };
    }

    if (empower_brand) {
      where.empower_brand = {
        contains: empower_brand,
      };
    }

    if (status !== undefined) {
      where.status = status;
    }

    // 创建时间范围查询
    if (start_time && end_time) {
      where.created_at = {
        gte: start_time,
        lte: end_time,
      };
    } else if (start_time) {
      where.created_at = {
        gte: start_time,
      };
    } else if (end_time) {
      where.created_at = {
        lte: end_time,
      };
    }

    // 查询总数
    const total = await this.prisma.self_brand_empowers.count({ where });

    // 查询列表数据
    const rows = await this.prisma.self_brand_empowers.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        created_at: 'desc',
      },
    });

    return { rows, total };
  }

  /**
   * 获取自主品牌授权详情
   * @param {Number} id 自主品牌授权ID
   * @returns {Promise<Object>} 自主品牌授权详情
   */
  async getSelfBrandEmpowerDetail(id) {
    return await this.prisma.self_brand_empowers.findFirst({
      where: {
        id,
        deleted_at: null,
      },
    });
  }

  /**
   * 创建自主品牌授权
   * @param {Object} data 自主品牌授权数据
   * @returns {Promise<Object>} 创建的自主品牌授权
   */
  async createSelfBrandEmpower(data) {
    const now = Date.now();

    return await this.prisma.self_brand_empowers.create({
      data: {
        ...data,
        created_at: now,
        updated_at: now,
      },
    });
  }

  /**
   * 更新自主品牌授权
   * @param {Number} id 自主品牌授权ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的自主品牌授权
   */
  async updateSelfBrandEmpower(id, data) {
    const now = Date.now();

    return await this.prisma.self_brand_empowers.update({
      where: {
        id,
      },
      data: {
        ...data,
        updated_at: now,
      },
    });
  }

  /**
   * 删除自主品牌授权（软删除）
   * @param {Number} id 自主品牌授权ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteSelfBrandEmpower(id) {
    const now = Date.now();

    await this.prisma.self_brand_empowers.update({
      where: {
        id,
      },
      data: {
        deleted_at: now,
      },
    });

    return { success: true };
  }

  /**
   * 根据授权编号查询授权信息
   * @param {String} empowerNumber 授权编号
   * @returns {Promise<Object>} 授权信息
   */
  async getSelfBrandEmpowerByNumber(empowerNumber) {
    return await this.prisma.self_brand_empowers.findFirst({
      where: {
        empower_number: empowerNumber,
        deleted_at: null,
      },
    });
  }
}

module.exports = new SelfBrandEmpowerModel();

/**
 * 溯源码查询日志数据模型
 */
const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class SelfSourceCodeQueryLogModel {
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 创建溯源码查询日志
   * @param {Object} logData 日志数据
   * @param {BigInt|String} logData.sourceCodeId 溯源码ID
   * @param {String} logData.sourceCode 溯源码
   * @param {String} logData.ip 查询IP
   * @param {String} logData.location 查询地址
   * @returns {Promise<Object>} 创建的日志记录
   */
  async createQueryLog(logData) {
    try {
      const logId = generateSnowflakeId();
      const now = BigInt(Date.now());

      const queryLog = await this.prisma.SelfSourceCodeQueryLog.create({
        data: {
          id: BigInt(logId),
          source_code_id: BigInt(logData.sourceCodeId),
          source_code: logData.sourceCode,
          ip: logData.ip,
          location: logData.location,
          query_time: now,
          created_at: now,
          updated_at: now
        }
      });

      return queryLog;
    } catch (error) {
      console.error('创建溯源码查询日志失败:', error);
      throw error;
    }
  }

  /**
   * 获取溯源码查询日志列表
   * @param {BigInt|String} sourceCodeId 溯源码ID
   * @param {Number} page 页码
   * @param {Number} limit 每页数量
   * @returns {Promise<{rows: Array, total: Number}>} 查询日志列表和总数
   */
  async getQueryLogsBySourceCodeId(sourceCodeId, page = 1, limit = 10) {
    try {
      const skip = (page - 1) * limit;

      const [logs, total] = await Promise.all([
        this.prisma.SelfSourceCodeQueryLog.findMany({
          where: {
            source_code_id: BigInt(sourceCodeId),
            deleted_at: null
          },
          orderBy: {
            query_time: 'desc'
          },
          skip,
          take: limit
        }),
        this.prisma.SelfSourceCodeQueryLog.count({
          where: {
            source_code_id: BigInt(sourceCodeId),
            deleted_at: null
          }
        })
      ]);

      return {
        rows: logs,
        total
      };
    } catch (error) {
      console.error('获取溯源码查询日志列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取溯源码查询日志列表（根据溯源码字符串）
   * @param {String} sourceCode 溯源码
   * @param {Number} page 页码
   * @param {Number} limit 每页数量
   * @returns {Promise<{rows: Array, total: Number}>} 查询日志列表和总数
   */
  async getQueryLogsBySourceCode(sourceCode, page = 1, limit = 10) {
    try {
      const skip = (page - 1) * limit;

      const [logs, total] = await Promise.all([
        this.prisma.SelfSourceCodeQueryLog.findMany({
          where: {
            source_code: sourceCode,
            deleted_at: null
          },
          orderBy: {
            query_time: 'desc'
          },
          skip,
          take: limit
        }),
        this.prisma.SelfSourceCodeQueryLog.count({
          where: {
            source_code: sourceCode,
            deleted_at: null
          }
        })
      ]);

      return {
        rows: logs,
        total
      };
    } catch (error) {
      console.error('获取溯源码查询日志列表失败:', error);
      throw error;
    }
  }
}

module.exports = new SelfSourceCodeQueryLogModel();

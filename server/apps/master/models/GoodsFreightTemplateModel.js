/**
 * 运费模板模型
 */
const GoodsFreightTemplateDto = require('../dto/GoodsFreightTemplateDto');
const GoodsFreightConfigDto = require('../dto/GoodsFreightConfigDto');
const GoodsFreightRegionRelationDto = require('../dto/GoodsFreightRegionRelationDto');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const regionService = require('../services/RegionService');
const ChargeTypeEnum = require('../constants/ChargeTypeEnum');

/**
 * 将Prisma Decimal对象转换为普通数字
 * @param {Object} decimal - Prisma Decimal对象 
 * @returns {number} 转换后的数字
 */
function decimalToNumber(decimal) {
  if (!decimal) return 0;
  
  // 如果已经是数字，直接返回
  if (typeof decimal === 'number') return decimal;
  
  // 处理字符串类型
  if (typeof decimal === 'string') {
    return parseFloat(decimal) || 0;
  }
  
  // 处理Prisma Decimal对象格式
  if (decimal.s !== undefined && decimal.e !== undefined && decimal.d) {
    try {
      // 使用 Decimal 对象的 toString() 方法或者 toNumber() 方法
      if (typeof decimal.toString === 'function') {
        return parseFloat(decimal.toString());
      }
      if (typeof decimal.toNumber === 'function') {
        return decimal.toNumber();
      }
      
      // 如果上述方法不可用，手动解析 Decimal 对象
      const { s, e, d } = decimal;
      // s: 符号位, e: 指数位, d: 数值数组
      let numStr = d.join('');
      
      // 处理小数点位置
      // Prisma Decimal 的 e 表示小数点后的位数（负数）
      const decimalPlaces = -e;
      if (decimalPlaces > 0 && decimalPlaces < numStr.length) {
        const integerPart = numStr.slice(0, numStr.length - decimalPlaces);
        const decimalPart = numStr.slice(-decimalPlaces);
        numStr = `${integerPart || '0'}.${decimalPart}`;
      } else if (decimalPlaces > 0) {
        // 如果小数位数大于数字长度，前面补0
        const paddedZeros = '0'.repeat(decimalPlaces - numStr.length);
        numStr = `0.${paddedZeros}${numStr}`;
      }
      
      let num = parseFloat(numStr);
      
      // 应用符号
      return s === -1 ? -num : num;
    } catch (error) {
      console.error('解析 Decimal 对象失败:', error, decimal);
      return 0;
    }
  }
  
  // 尝试直接转换
  return parseFloat(decimal) || 0;
}

class GoodsFreightTemplateModel {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 将BigInt转换为字符串
   * @param {Object} obj - 包含BigInt的对象
   * @returns {Object} 转换后的对象
   */
  convertBigIntToString(obj) {
    if (obj === null || obj === undefined) {
      return obj;
    }
    
    if (typeof obj === 'bigint') {
      return obj.toString();
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.convertBigIntToString(item));
    }
    
    if (typeof obj === 'object') {
      const result = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          result[key] = this.convertBigIntToString(obj[key]);
        }
      }
      return result;
    }
    
    return obj;
  }

  /**
   * 获取运费模板列表
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Promise<Object>} 运费模板列表和总数
   */
  async getGoodsFreightTemplates(filters = {}, page = 1, pageSize = 10) {
    try {
      const where = {
        deleted_at: null
      };

      // 添加名称过滤
      if (filters.name) {
        where.name = {
          contains: filters.name
        };
      }

      // 添加计价方式过滤
      if (filters.charge_type && !isNaN(Number(filters.charge_type))) {
        where.charge_type = Number(filters.charge_type);
      }

      // 添加创建时间范围过滤，直接使用毫秒级时间戳
      if (filters.start_time && !isNaN(Number(filters.start_time))) {
        where.created_at = {
          ...where.created_at,
          gte: BigInt(filters.start_time)
        };
      }

      if (filters.end_time && !isNaN(Number(filters.end_time))) {
        where.created_at = {
          ...where.created_at,
          lte: BigInt(filters.end_time)
        };
      }

      // 计算分页参数
      const skip = (page - 1) * pageSize;

      // 查询数据
      const [templates, total] = await Promise.all([
        this.prisma.goodsFreightTemplates.findMany({
          where,
          skip,
          take: pageSize,
          orderBy: {
            created_at: 'desc'
          }
        }),
        this.prisma.goodsFreightTemplates.count({ where })
      ]);

      // 转换为DTO
      const templateDtos = templates.map(template => {
        // 将BigInt转换为字符串
        const templateWithStringIds = {
          ...template,
          id: template.id.toString(),
          created_by: template.created_by ? template.created_by.toString() : null,
          updated_by: template.updated_by ? template.updated_by.toString() : null
        };
        return GoodsFreightTemplateDto.fromDbObject(templateWithStringIds);
      });

      // 确保所有BigInt都被转换为字符串
      return this.convertBigIntToString({
        list: templateDtos,
        total
      });
    } catch (error) {
      console.error('获取运费模板列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取运费模板列表（包含运费配置和区域关联）
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Promise<Object>} 运费模板列表和总数
   */
  async getGoodsFreightTemplatesWithRegions(filters = {}, page = 1, pageSize = 10) {
    try {
      const where = {
        deleted_at: null
      };

      // 添加名称过滤
      if (filters.name) {
        if (typeof filters.name === 'string') {
          // 如果是字符串，添加 contains 查询
          where.name = {
            contains: filters.name,
            mode: 'insensitive' // 不区分大小写
          };
        } else if (typeof filters.name === 'object' && filters.name.contains) {
          // 如果已经是对象格式，直接使用
          where.name = filters.name;
        }
      }

      // 添加计价方式过滤
      if (filters.charge_type && !isNaN(Number(filters.charge_type))) {
        where.charge_type = Number(filters.charge_type);
      }
      
      // 添加创建时间范围过滤
      if (filters.start_time && !isNaN(Number(filters.start_time))) {
        where.created_at = {
          ...where.created_at,
          gte: BigInt(filters.start_time)
        };
      }

      if (filters.end_time && !isNaN(Number(filters.end_time))) {
        where.created_at = {
          ...where.created_at,
          lte: BigInt(filters.end_time)
        };
      }

      // 计算分页参数
      const skip = (page - 1) * pageSize;

      // 查询运费模板数据
      const [templates, total] = await Promise.all([
        this.prisma.goodsFreightTemplates.findMany({
          where,
          skip,
          take: pageSize,
          orderBy: {
            created_at: 'desc'
          }
        }),
        this.prisma.goodsFreightTemplates.count({ where })
      ]);
      
      if (templates.length === 0) {
        return { 
          list: [], 
          total,
          page,
          pageSize
        };
      }
      
      // 获取所有模板ID
      const templateIds = templates.map(template => template.id);
      
      // 查询运费配置
      const freightConfigs = await this.prisma.goodsFreightConfig.findMany({
        where: {
          freight_template_id: {
            in: templateIds
          },
          deleted_at: null
        }
      });
      
      // 查询区域关联
      const configIds = freightConfigs.map(config => config.id);
      let regionRelations = [];
      
      if (configIds.length > 0) {
        regionRelations = await this.prisma.goodsFreightRegionRelation.findMany({
          where: {
            freight_config_id: {
              in: configIds
            },
            deleted_at: null
          }
        });
      }
      
      // 按配置ID分组区域关联
      const relationsByConfigId = {};
      for (const relation of regionRelations) {
        const configId = relation.freight_config_id.toString();
        if (!relationsByConfigId[configId]) {
          relationsByConfigId[configId] = [];
        }
        relationsByConfigId[configId].push(relation);
      }
      
      // 按模板ID分组运费配置
      const configsByTemplateId = {};
      for (const config of freightConfigs) {
        const templateId = config.freight_template_id.toString();
        if (!configsByTemplateId[templateId]) {
          configsByTemplateId[templateId] = [];
        }
        
        // 将关联的区域添加到配置中
        const configId = config.id.toString();
        const relations = relationsByConfigId[configId] || [];
        
        configsByTemplateId[templateId].push({
          ...config,
          region_relations: relations
        });
      }
      
      // 构建带有17规则的返回数据
      const formattedTemplates = templates.map(template => {
        // 将BigInt转换为字符串
        const templateId = template.id.toString();
        const templateConfigs = configsByTemplateId[templateId] || [];
        
        // 查找默认配置
        const defaultConfig = templateConfigs.find(config => config.is_default === 1);
        
        // 处理运费配置和区域关联
        const configsWithStringIds = templateConfigs.map(config => {
          // 转换区域关联的BigInt
          const relationsWithStringIds = (config.region_relations || []).map(relation => ({
            ...relation,
            id: relation.id.toString(),
            freight_config_id: relation.freight_config_id.toString(),
            created_by: relation.created_by ? relation.created_by.toString() : null,
            updated_by: relation.updated_by ? relation.updated_by.toString() : null
          }));
          
          // 转换运费配置的BigInt和Decimal
          return new GoodsFreightConfigDto({
            ...config,
            id: config.id.toString(),
            freight_template_id: config.freight_template_id.toString(),
            first_fee: decimalToNumber(config.first_fee),
            additional_fee: decimalToNumber(config.additional_fee),
            created_by: config.created_by ? config.created_by.toString() : null,
            updated_by: config.updated_by ? config.updated_by.toString() : null,
            region_relations: relationsWithStringIds
          });
        });
        
        // 构建模板DTO
        const templateDto = new GoodsFreightTemplateDto({
          ...template,
          id: template.id.toString(),
          created_by: template.created_by ? template.created_by.toString() : null,
          updated_by: template.updated_by ? template.updated_by.toString() : null,
          freight_configs: configsWithStringIds,
          // 如果有默认配置，添加这些属性便于列表展示
          first_item: defaultConfig ? defaultConfig.first_item : null,
          first_fee: defaultConfig ? decimalToNumber(defaultConfig.first_fee) : null,
          additional_item: defaultConfig ? defaultConfig.additional_item : null,
          additional_fee: defaultConfig ? decimalToNumber(defaultConfig.additional_fee) : null
        });

        return templateDto;
      });

      return {
        list: formattedTemplates,
        total,
        page,
        pageSize
      };
    } catch (error) {
      console.error('获取运费模板列表（包含运费配置和区域关联）失败:', error);
      throw error;
    }
  }

  /**
   * 获取计价方式文本
   * @param {number} chargeType - 计价方式代码
   * @returns {string} 计价方式文本
   */
  getChargeTypeText(chargeType) {
    return ChargeTypeEnum.getChargeTypeText(chargeType);
  }

  /**
   * 获取指定ID的运费模板
   * @param {number} id - 运费模板ID
   * @returns {Promise<GoodsFreightTemplateDto|null>} 运费模板DTO
   */
  async getGoodsFreightTemplateById(id) {
    try {
      // 获取模板基本信息
      const template = await this.prisma.goodsFreightTemplates.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!template) {
        return null;
      }

      // 获取运费配置
      const freightConfigs = await this.prisma.goodsFreightConfig.findMany({
        where: {
          freight_template_id: BigInt(id),
          deleted_at: null
        }
      });
      
      // 获取所有运费配置ID
      const configIds = freightConfigs.map(config => config.id);
      
      // 获取区域关联（如果有配置）
      let regionRelations = [];
      if (configIds.length > 0) {
        regionRelations = await this.prisma.goodsFreightRegionRelation.findMany({
          where: {
            freight_config_id: {
              in: configIds
            },
            deleted_at: null
          }
        });
      }
      
      // 将区域关联数据分组到各个运费配置下
      const configsWithRegions = freightConfigs.map(config => {
        // 找到当前配置对应的所有区域关联
        const configRegions = regionRelations.filter(relation => 
          relation.freight_config_id.toString() === config.id.toString()
        );
        
        // 将BigInt转换为字符串，Decimal转换为数字
        return {
          ...config,
          id: config.id.toString(),
          freight_template_id: config.freight_template_id.toString(),
          first_fee: decimalToNumber(config.first_fee),
          additional_fee: decimalToNumber(config.additional_fee),
          created_by: config.created_by ? config.created_by.toString() : null,
          updated_by: config.updated_by ? config.updated_by.toString() : null,
          region_relations: configRegions.map(region => ({
            ...region,
            id: region.id.toString(),
            freight_config_id: region.freight_config_id.toString(),
            created_by: region.created_by ? region.created_by.toString() : null,
            updated_by: region.updated_by ? region.updated_by.toString() : null
          }))
        };
      });

      // 将BigInt ID转换为字符串
      const templateWithStringIds = {
        ...template,
        id: template.id.toString(),
        created_by: template.created_by ? template.created_by.toString() : null,
        updated_by: template.updated_by ? template.updated_by.toString() : null,
        freight_configs: configsWithRegions
      };

      return new GoodsFreightTemplateDto(templateWithStringIds);
    } catch (error) {
      console.error(`获取运费模板(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 添加运费模板
   * @param {GoodsFreightTemplateDto} templateDto - 运费模板DTO
   * @returns {Promise<GoodsFreightTemplateDto>} 添加后的运费模板DTO
   */
  async addGoodsFreightTemplate(templateDto) {
    try {
      // 使用事务确保数据一致性
      return await this.prisma.$transaction(async (tx) => {
        // 准备数据
        const templateData = templateDto.toDbObject();
        
        // 生成雪花ID
        const id = generateSnowflakeId();
        
        // 创建运费模板基本信息
        const createdTemplate = await tx.goodsFreightTemplates.create({
          data: {
            id: BigInt(id),
            name: templateData.name,
            charge_type: templateData.charge_type,
            created_at: BigInt(templateData.created_at),
            updated_at: BigInt(templateData.updated_at),
            created_by: templateData.created_by ? BigInt(templateData.created_by) : null,
            updated_by: templateData.updated_by ? BigInt(templateData.updated_by) : null
          }
        });
        
        // 将创建的模板的ID转换为字符串并返回
        return new GoodsFreightTemplateDto({
          ...createdTemplate,
          id: createdTemplate.id.toString(),
          created_by: createdTemplate.created_by ? createdTemplate.created_by.toString() : null,
          updated_by: createdTemplate.updated_by ? createdTemplate.updated_by.toString() : null
        });
      });
    } catch (error) {
      console.error('添加运费模板失败:', error);
      throw error;
    }
  }

  // 注意：完整的运费模板更新逻辑已移至Service层，使用新的三表结构
  // 这里只保留基础模板信息的更新方法

  /**
   * 删除运费模板
   * @param {number} id - 运费模板ID
   * @param {number} userId - 当前用户ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteGoodsFreightTemplate(id, userId) {
    try {
      // 开启事务确保数据一致性
      return await this.prisma.$transaction(async (tx) => {
        // 检查模板是否存在
        const existingTemplate = await tx.goodsFreightTemplates.findFirst({
          where: {
            id: BigInt(id),
            deleted_at: null
          }
        });

        if (!existingTemplate) {
          throw new Error(`运费模板(ID: ${id})不存在`);
        }

        // TODO: 检查是否有商品使用该运费模板
        // 这里需要根据实际业务逻辑添加检查代码

        // 1. 查询所有相关的运费配置
        const configs = await tx.goodsFreightConfig.findMany({
          where: {
            freight_template_id: BigInt(id),
            deleted_at: null
          }
        });
        
        const configIds = configs.map(config => config.id);
        
        // 2. 软删除所有相关的区域关联
        if (configIds.length > 0) {
          await tx.goodsFreightRegionRelation.updateMany({
            where: {
              freight_config_id: {
                in: configIds
              },
              deleted_at: null
            },
            data: {
              deleted_at: BigInt(Date.now()),
              updated_at: BigInt(Date.now())
            }
          });
        }
        
        // 3. 软删除所有运费配置
        await tx.goodsFreightConfig.updateMany({
          where: {
            freight_template_id: BigInt(id),
            deleted_at: null
          },
          data: {
            deleted_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now()),
            updated_by: userId ? BigInt(userId) : null
          }
        });

        // 4. 软删除运费模板
        await tx.goodsFreightTemplates.update({
          where: {
            id: BigInt(id)
          },
          data: {
            deleted_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now()),
            updated_by: userId ? BigInt(userId) : null
          }
        });

        return true;
      });
    } catch (error) {
      console.error(`删除运费模板(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 添加运费配置
   * @param {GoodsFreightConfigDto} configDto - 运费配置DTO
   * @returns {Promise<GoodsFreightConfigDto>} 添加后的运费配置DTO
   */
  async addGoodsFreightConfig(configDto) {
    try {
      // 准备数据对象
      const configData = configDto.toDbObject();
      
      // 生成雪花ID
      const id = generateSnowflakeId();
      
      // 创建记录
      const createdConfig = await this.prisma.goodsFreightConfig.create({
        data: {
          id: BigInt(id),
          freight_template_id: BigInt(configData.freight_template_id),
          first_item: configData.first_item,
          first_fee: configData.first_fee,
          additional_item: configData.additional_item,
          additional_fee: configData.additional_fee,
          is_default: configData.is_default,
          created_at: BigInt(configData.created_at),
          updated_at: BigInt(configData.updated_at),
          created_by: configData.created_by ? BigInt(configData.created_by) : null,
          updated_by: configData.updated_by ? BigInt(configData.updated_by) : null
        }
      });
      
      // 转换为DTO
      const configWithStringIds = {
        ...createdConfig,
        id: createdConfig.id.toString(),
        freight_template_id: createdConfig.freight_template_id.toString(),
        created_by: createdConfig.created_by ? createdConfig.created_by.toString() : null,
        updated_by: createdConfig.updated_by ? createdConfig.updated_by.toString() : null
      };
      
      return new GoodsFreightConfigDto(configWithStringIds);
    } catch (error) {
      console.error('添加运费配置失败:', error);
      throw error;
    }
  }

  /**
   * 添加运费区域关联
   * @param {GoodsFreightRegionRelationDto} regionDto - 区域关联DTO
   * @returns {Promise<GoodsFreightRegionRelationDto>} 添加后的区域关联DTO
   */
  async addGoodsFreightRegionRelation(regionDto) {
    try {
      // 准备数据对象
      const regionData = regionDto.toDbObject();
      
      // 生成雪花ID
      const id = generateSnowflakeId();
      
      // 创建记录
      const createdRegion = await this.prisma.goodsFreightRegionRelation.create({
        data: {
          id: BigInt(id),
          freight_config_id: BigInt(regionData.freight_config_id),
          region_code: regionData.region_code,
          region_name: regionData.region_name,
          parent_name: regionData.parent_name,
          created_at: BigInt(regionData.created_at),
          updated_at: BigInt(regionData.updated_at)
        }
      });
      
      // 转换为DTO
      const regionWithStringIds = {
        ...createdRegion,
        id: createdRegion.id.toString(),
        freight_config_id: createdRegion.freight_config_id.toString()
      };
      
      return new GoodsFreightRegionRelationDto(regionWithStringIds);
    } catch (error) {
      console.error('添加运费区域关联失败:', error);
      throw error;
    }
  }

  /**
   * 更新运费模板基本信息（不包含运费配置）
   * @param {number} id - 运费模板ID
   * @param {GoodsFreightTemplateDto} templateDto - 运费模板DTO
   * @returns {Promise<GoodsFreightTemplateDto>} 更新后的运费模板基本信息
   */
  async updateGoodsFreightTemplateBase(id, templateDto) {
    try {
      // 准备数据对象
      const templateData = templateDto.toDbObject();
      
      // 更新模板基本信息
      const updatedTemplate = await this.prisma.goodsFreightTemplates.update({
        where: {
          id: BigInt(id)
        },
        data: {
          name: templateData.name,
          charge_type: templateData.charge_type,
          updated_at: BigInt(templateData.updated_at),
          updated_by: templateData.updated_by ? BigInt(templateData.updated_by) : null
        }
      });
      
      // 转换为DTO
      const templateWithStringIds = {
        ...updatedTemplate,
        id: updatedTemplate.id.toString(),
        created_by: updatedTemplate.created_by ? updatedTemplate.created_by.toString() : null,
        updated_by: updatedTemplate.updated_by ? updatedTemplate.updated_by.toString() : null
      };
      
      return new GoodsFreightTemplateDto(templateWithStringIds);
    } catch (error) {
      console.error(`更新运费模板(ID: ${id})基本信息失败:`, error);
      throw error;
    }
  }

  /**
   * 删除运费模板的所有配置（级联删除区域关联）
   * @param {number} templateId - 运费模板ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteAllFreightConfigs(templateId) {
    try {
      // 查询该模板下的所有配置
      const configs = await this.prisma.goodsFreightConfig.findMany({
        where: {
          freight_template_id: BigInt(templateId),
          deleted_at: null
        }
      });
      
      // 获取所有配置ID
      const configIds = configs.map(config => config.id);
      
      // 删除所有区域关联
      if (configIds.length > 0) {
        await this.prisma.goodsFreightRegionRelation.updateMany({
          where: {
            freight_config_id: {
              in: configIds
            },
            deleted_at: null
          },
          data: {
            deleted_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now())
          }
        });
      }
      
      // 删除所有运费配置
      await this.prisma.goodsFreightConfig.updateMany({
        where: {
          freight_template_id: BigInt(templateId),
          deleted_at: null
        },
        data: {
          deleted_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      
      return true;
    } catch (error) {
      console.error(`删除运费模板(ID: ${templateId})的所有配置失败:`, error);
      throw error;
    }
  }

  /**
   * 获取运费配置及其关联区域
   * @param {number} configId - 运费配置ID
   * @returns {Promise<GoodsFreightConfigDto>} 运费配置DTO（包含区域关联）
   */
  async getGoodsFreightConfigWithRegions(configId) {
    try {
      // 查询运费配置
      const config = await this.prisma.goodsFreightConfig.findUnique({
        where: {
          id: BigInt(configId),
          deleted_at: null
        }
      });
      
      if (!config) {
        return null;
      }
      
      // 查询关联的区域
      const regions = await this.prisma.goodsFreightRegionRelation.findMany({
        where: {
          freight_config_id: BigInt(configId),
          deleted_at: null
        }
      });
      
      // 转换为字符串ID
      const configWithStringIds = {
        ...config,
        id: config.id.toString(),
        freight_template_id: config.freight_template_id.toString(),
        created_by: config.created_by ? config.created_by.toString() : null,
        updated_by: config.updated_by ? config.updated_by.toString() : null,
        region_relations: regions.map(region => ({
          ...region,
          id: region.id.toString(),
          freight_config_id: region.freight_config_id.toString(),
          created_by: region.created_by ? region.created_by.toString() : null,
          updated_by: region.updated_by ? region.updated_by.toString() : null
        }))
      };
      
      return new GoodsFreightConfigDto(configWithStringIds);
    } catch (error) {
      console.error(`获取运费配置(ID: ${configId})及区域关联失败:`, error);
      throw error;
    }
  }
}

// 创建并导出模型类
module.exports = GoodsFreightTemplateModel;

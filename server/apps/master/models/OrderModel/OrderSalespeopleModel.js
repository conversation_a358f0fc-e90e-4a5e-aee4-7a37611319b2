/**
 * 订单销售人员模型
 */
const prismaManager = require('../../../../core/prisma');
const { generateSnowflakeId } = require('../../../../shared/utils/snowflake');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 将下划线命名法转换为驼峰命名法
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const convertKeysToCamelCase = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data !== 'object' || data instanceof Date) return data;

  if (Array.isArray(data)) {
    return data.map(item => convertKeysToCamelCase(item));
  }

  const newData = {};
  for (const key in data) {
    const camelCaseKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    newData[camelCaseKey] = convertKeysToCamelCase(data[key]);
  }
  return newData;
};

class OrderSalespeopleModel {
  
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 确保ID是BigInt类型
   * @param {string|number|BigInt} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    return BigInt(id);
  }

  /**
   * 根据订单ID获取销售人员信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} - 销售人员信息
   */
  async getSalespeopleByOrderId(orderId) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);

      // 使用 findFirst 而不是 findUnique，因为 order_id 不是唯一约束
      const salespeople = await this.prisma.order_salespeople.findFirst({
        where: {
          order_id: orderIdBigInt
        }
      });

      if (!salespeople) {
        return null;
      }

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(salespeople);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('获取订单销售人员信息失败:', error);
      throw error;
    }
  }

  /**
   * 创建订单销售人员信息
   * @param {Object} salespeopleData - 销售人员信息数据
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} - 创建的销售人员信息
   */
  async createSalespeople(salespeopleData, tx) {
    try {
      const prismaClient = tx || this.prisma;

      // 生成雪花ID
      const salespeopleId = generateSnowflakeId();

      // 创建销售人员信息
      const newSalespeople = await prismaClient.order_salespeople.create({
        data: {
          id: salespeopleId,
          order_id: this.ensureBigInt(salespeopleData.orderId),
          salesperson_id: salespeopleData.salespersonId ? this.ensureBigInt(salespeopleData.salespersonId) : null
          // 移除不需要的字段
          // salesperson_name: salespeopleData.salespersonName,
          // salesperson_phone: salespeopleData.salespersonPhone,
          // commission_rate: salespeopleData.commissionRate,
          // commission_amount: salespeopleData.commissionAmount
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(newSalespeople);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('创建订单销售人员信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单销售人员信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} salespeopleData - 销售人员信息数据
   * @returns {Promise<Object>} - 更新后的销售人员信息
   */
  async updateSalespeople(orderId, salespeopleData) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);

      // 先查找现有记录
      const existingSalespeople = await this.prisma.order_salespeople.findFirst({
        where: {
          order_id: orderIdBigInt
        }
      });

      if (!existingSalespeople) {
        throw new Error(`未找到订单ID为 ${orderId} 的销售人员信息`);
      }

      // 更新销售人员信息
      const updatedSalespeople = await this.prisma.order_salespeople.update({
        where: {
          id: existingSalespeople.id // 使用主键id进行更新
        },
        data: {
          salesperson_id: salespeopleData.salespersonId ? this.ensureBigInt(salespeopleData.salespersonId) : null,
          updated_at: BigInt(Date.now())
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(updatedSalespeople);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('更新订单销售人员信息失败:', error);
      throw error;
    }
  }
}

module.exports = OrderSalespeopleModel;

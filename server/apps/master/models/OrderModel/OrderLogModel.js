/**
 * 订单日志模型
 */
const prismaManager = require('../../../../core/prisma');
const { generateSnowflakeId } = require('../../../../shared/utils/snowflake');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 将下划线命名法转换为驼峰命名法
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const convertKeysToCamelCase = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data !== 'object' || data instanceof Date) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => convertKeysToCamelCase(item));
  }
  
  const newData = {};
  for (const key in data) {
    const camelCaseKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    newData[camelCaseKey] = convertKeysToCamelCase(data[key]);
  }
  return newData;
};

class OrderLogModel {
  
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 确保ID是BigInt类型
   * @param {string|number|BigInt} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    if (typeof id === 'string' && isNaN(Number(id))) return null;
    return BigInt(id);
  }

  /**
   * 根据订单ID获取日志列表
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Array>} - 日志列表
   */
  async getLogsByOrderId(orderId) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);

      const logs = await this.prisma.order_logs.findMany({
        where: {
          order_id: orderIdBigInt
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedLogs = handleBigInt(logs);
      return convertKeysToCamelCase(processedLogs);
    } catch (error) {
      console.error('获取订单日志列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建订单日志
   * @param {Object} logData - 日志数据
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} - 创建的日志
   */
  async createLog(logData, tx) {
    try {
      const prismaClient = tx || this.prisma;
      
      // 生成雪花ID
      const logId = generateSnowflakeId();
      
      // 创建日志
      // 直接使用原生 SQL 查询来插入数据，避免使用 Prisma 的关系连接
      const newLog = await prismaClient.$queryRaw`
        INSERT INTO base.order_logs (
          id, order_id, operator_id, operator_type, operator_name, action, details, created_at
        ) VALUES (
          ${logId}, 
          ${this.ensureBigInt(logData.orderId)}, 
          ${logData.operatorId ? this.ensureBigInt(logData.operatorId) : null}, 
          ${logData.operatorType || 1}, 
          ${logData.operatorName || null}, 
          ${logData.logType || 'create'}, 
          ${logData.logContent || '订单操作'}, 
          ${logData.operationTime || BigInt(Date.now())}
        ) RETURNING *;
      `;

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(newLog);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('创建订单日志失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建订单日志
   * @param {Array} logsData - 日志数据数组
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<boolean>} - 是否创建成功
   */
  async createLogs(logsData, tx) {
    try {
      const prismaClient = tx || this.prisma;
      
      // 准备批量插入的数据
      const insertValues = logsData.map(log => {
        const logId = generateSnowflakeId();
        return {
          id: logId,
          orderId: this.ensureBigInt(log.orderId),
          operatorId: log.operatorId ? this.ensureBigInt(log.operatorId) : null,
          operatorName: log.operatorName || null,
          operatorType: log.operatorType || 1,
          action: log.logType || 'create',
          details: log.logContent || '订单操作',
          createdAt: log.operationTime || BigInt(Date.now())
        };
      });
      
      // 使用事务批量插入
      await prismaClient.$transaction(async (tx) => {
        for (const item of insertValues) {
          await tx.$executeRaw`
            INSERT INTO base.order_logs (
              id, order_id, operator_id, operator_type, operator_name, action, details, created_at
            ) VALUES (
              ${item.id}, 
              ${item.orderId}, 
              ${item.operatorId}, 
              ${item.operatorType}, 
              ${item.operatorName}, 
              ${item.action}, 
              ${item.details}, 
              ${item.createdAt}
            );
          `;
        }
      });

      return true;
    } catch (error) {
      console.error('批量创建订单日志失败:', error);
      throw error;
    }
  }

  /**
   * 记录订单状态变更日志
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {string} fromStatus - 原状态
   * @param {string} toStatus - 新状态
   * @param {Object} operator - 操作人信息
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} - 创建的日志
   */
  async recordStatusChange(orderId, fromStatus, toStatus, operator, tx) {
    try {
      const logContent = `订单状态从 ${fromStatus} 变更为 ${toStatus}`;
      
      return await this.createLog({
        orderId,
        logType: 'status_change',
        logContent,
        operatorId: operator.id,
        operatorName: operator.name,
        operatorRole: operator.role,
        operationTime: BigInt(Date.now()),
        operationIp: operator.ip,
        operationPlatform: operator.platform
      }, tx);
    } catch (error) {
      console.error('记录订单状态变更日志失败:', error);
      throw error;
    }
  }

  /**
   * 记录订单支付日志
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} paymentInfo - 支付信息
   * @param {Object} operator - 操作人信息
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} - 创建的日志
   */
  async recordPayment(orderId, paymentInfo, operator, tx) {
    try {
      const logContent = `订单支付成功，支付金额：${paymentInfo.amount}，支付方式：${paymentInfo.method}，交易号：${paymentInfo.transactionId}`;
      
      return await this.createLog({
        orderId,
        logType: 'payment',
        logContent,
        operatorId: operator.id,
        operatorName: operator.name,
        operatorRole: operator.role,
        operationTime: BigInt(Date.now()),
        operationIp: operator.ip,
        operationPlatform: operator.platform
      }, tx);
    } catch (error) {
      console.error('记录订单支付日志失败:', error);
      throw error;
    }
  }

  /**
   * 记录订单发货日志
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} shippingInfo - 发货信息
   * @param {Object} operator - 操作人信息
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} - 创建的日志
   */
  async recordShipping(orderId, shippingInfo, operator, tx) {
    try {
      const logContent = `订单已发货，物流公司：${shippingInfo.companyName}，物流单号：${shippingInfo.trackingNumber}`;
      
      return await this.createLog({
        orderId,
        logType: 'shipping',
        logContent,
        operatorId: operator.id,
        operatorName: operator.name,
        operatorRole: operator.role,
        operationTime: BigInt(Date.now()),
        operationIp: operator.ip,
        operationPlatform: operator.platform
      }, tx);
    } catch (error) {
      console.error('记录订单发货日志失败:', error);
      throw error;
    }
  }
}

module.exports = OrderLogModel;

const { prisma } = require('../../../core/database/prisma');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && data !== null) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = handleBigInt(data[key]);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 快递公司编码模型
 */
class ExpressCompanyCodeModel {
  
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取所有快递公司编码
   * @param {Object} options 查询选项
   * @param {string} options.company_name 公司名称（模糊搜索）
   * @param {string} options.company_code 公司编码（模糊搜索）
   * @param {string} options.company_type 公司类型
   * @param {string} options.startTime 创建时间范围开始
   * @param {string} options.endTime 创建时间范围结束
   * @param {number} options.skip 跳过数量
   * @param {number} options.take 获取数量
   * @returns {Promise<Object>} 返回快递公司编码列表和总数
   */
  async getAllExpressCompanyCodes(options = {}) {
    try {
      // 构建查询条件
      const whereCondition = {
        deleted_at: null // 只获取未删除的快递公司编码
      };
      
      // 如果提供了公司名称搜索参数，添加模糊搜索条件
      if (options.company_name) {
        whereCondition.company_name = {
          contains: options.company_name,
          mode: 'insensitive'
        };
      }
      
      // 如果提供了公司编码搜索参数，添加模糊搜索条件
      if (options.company_code) {
        whereCondition.company_code = {
          contains: options.company_code,
          mode: 'insensitive'
        };
      }
      
      // 如果提供了公司类型搜索参数
      if (options.company_type) {
        whereCondition.company_type = options.company_type;
      }
      
      // 如果提供了时间范围
      if (options.startTime && options.endTime) {
        whereCondition.created_at = {
          gte: parseInt(options.startTime),
          lte: parseInt(options.endTime)
        };
      } else if (options.startTime) {
        whereCondition.created_at = {
          gte: parseInt(options.startTime)
        };
      } else if (options.endTime) {
        whereCondition.created_at = {
          lte: parseInt(options.endTime)
        };
      }
      
      // 判断是否获取全部数据
      let expressCompanyCodes;
      let total;
      
      if (options.getAllData) {
        // 获取全部数据，不分页
        expressCompanyCodes = await this.prisma.ExpressCompanyCode.findMany({
          where: whereCondition,
          orderBy: {
            id: 'asc'
          }
        });
        total = expressCompanyCodes.length;
      } else {
        // 分页参数
        const skip = options.skip || 0;
        const take = options.take || 10;
        
        // 从数据库获取快递公司编码和总数
        [expressCompanyCodes, total] = await Promise.all([
          this.prisma.ExpressCompanyCode.findMany({
            where: whereCondition,
            orderBy: {
              id: 'asc'
            },
            skip,
            take
          }),
          this.prisma.ExpressCompanyCode.count({
            where: whereCondition
          })
        ]);
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(expressCompanyCodes);
      const processedExpressCompanyCodes = convertKeysToCamelCase(processedData);
      
      return {
        data: processedExpressCompanyCodes,
        total: Number(total)
      };
    } catch (error) {
      console.error('获取快递公司编码失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取快递公司编码
   * @param {number} id 快递公司编码ID
   * @returns {Promise<Object>} 返回快递公司编码对象
   */
  async getExpressCompanyCodeById(id) {
    try {
      // 获取快递公司编码信息
      const expressCompanyCode = await this.prisma.ExpressCompanyCode.findFirst({
        where: { 
          id: BigInt(id),
          deleted_at: null // 只获取未删除的快递公司编码
        }
      });
      
      if (!expressCompanyCode) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(expressCompanyCode);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`获取ID为${id}的快递公司编码失败:`, error);
      throw error;
    }
  }

  /**
   * 根据编码获取快递公司编码
   * @param {string} code 快递公司编码
   * @returns {Promise<Object>} 返回快递公司编码对象
   */
  async getExpressCompanyCodeByCode(code) {
    try {
      // 获取快递公司编码信息
      const expressCompanyCode = await this.prisma.ExpressCompanyCode.findFirst({
        where: { 
          company_code: code,
          deleted_at: null // 只获取未删除的快递公司编码
        }
      });
      
      if (!expressCompanyCode) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(expressCompanyCode);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`获取编码为${code}的快递公司编码失败:`, error);
      throw error;
    }
  }
}

module.exports = ExpressCompanyCodeModel;

/**
 * 自主品牌规则码数据模型
 */
const prismaManager = require('../../../core/prisma');

class SelfBrandRuleCodeModel {
  constructor() {
    this.prisma = prismaManager.getClient('master');
  }

  /**
   * 获取自主品牌规则码列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.limit 每页条数
   * @param {String} params.name 品牌名称
   * @param {String} params.rule_code 溯源码规则
   * @param {Number} params.start_time 开始时间
   * @param {Number} params.end_time 结束时间
   * @returns {Promise<{rows: Array, total: Number}>} 规则码列表和总数
   */
  async getSelfBrandRuleCodeList(params) {
    const { page = 1, limit = 20, name, rule_code, start_time, end_time } = params;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where = {
      deleted_at: null,
    };

    if (name) {
      where.name = {
        contains: name,
      };
    }

    if (rule_code) {
      where.rule_code = {
        contains: rule_code,
      };
    }

    // 创建时间范围查询
    if (start_time && end_time) {
      where.created_at = {
        gte: start_time,
        lte: end_time,
      };
    } else if (start_time) {
      where.created_at = {
        gte: start_time,
      };
    } else if (end_time) {
      where.created_at = {
        lte: end_time,
      };
    }

    // 查询总数
    const total = await this.prisma.self_brand_rule_codes.count({ where });

    // 查询列表数据
    const rows = await this.prisma.self_brand_rule_codes.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        created_at: 'desc',
      },
    });

    return { rows, total };
  }

  /**
   * 获取自主品牌规则码详情
   * @param {Number} id 品牌规则ID
   * @returns {Promise<Object>} 自主品牌规则码详情
   */
  async getSelfBrandRuleCodeDetail(id) {
    return await this.prisma.self_brand_rule_codes.findFirst({
      where: {
        id,
        deleted_at: null,
      },
    });
  }

  /**
   * 创建自主品牌规则码
   * @param {Object} data 自主品牌规则码数据
   * @returns {Promise<Object>} 创建的自主品牌规则码
   */
  async createSelfBrandRuleCode(data) {
    const now = Date.now();

    return await this.prisma.self_brand_rule_codes.create({
      data: {
        ...data,
        created_at: now,
        updated_at: now,
      },
    });
  }

  /**
   * 更新自主品牌规则码
   * @param {Number} id 品牌规则ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的自主品牌规则码
   */
  async updateSelfBrandRuleCode(id, data) {
    const now = Date.now();

    return await this.prisma.self_brand_rule_codes.update({
      where: {
        id,
      },
      data: {
        ...data,
        updated_at: now,
      },
    });
  }

  /**
   * 删除自主品牌规则码（软删除）
   * @param {Number} id 品牌规则ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteSelfBrandRuleCode(id) {
    const now = Date.now();

    await this.prisma.self_brand_rule_codes.update({
      where: {
        id,
      },
      data: {
        deleted_at: now,
      },
    });

    return { success: true };
  }

  /**
   * 根据品牌名称查询规则码
   * @param {String} name 品牌名称
   * @returns {Promise<Object>} 规则码信息
   */
  async getSelfBrandRuleCodeByName(name) {
    return await this.prisma.self_brand_rule_codes.findFirst({
      where: {
        name,
        deleted_at: null,
      },
    });
  }
}

module.exports = new SelfBrandRuleCodeModel();

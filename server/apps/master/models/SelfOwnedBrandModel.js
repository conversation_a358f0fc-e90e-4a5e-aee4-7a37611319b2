/**
 * 自主品牌数据模型
 */
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class SelfOwnedBrandModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取自主品牌列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.limit 每页条数
   * @param {String} params.name 商标名称
   * @param {String} params.trademark_code 商标代码
   * @param {String} params.registration_type 商标类型
   * @param {Number} params.start_time 开始时间
   * @param {Number} params.end_time 结束时间
   * @param {Number} params.status 状态
   * @returns {Promise<{rows: Array, total: Number}>} 品牌列表和总数
   */
  async getSelfOwnedBrandList(params) {
    const { 
      page = 1, 
      limit = 20, 
      name, 
      trademark_code, 
      registration_type, 
      start_time, 
      end_time, 
      status,
      // 新增筛选参数
      owner_name,
      last_procedure_status
    } = params;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where = {
      deleted_at: null,
    };

    if (name) {
      where.name = {
        contains: name,
      };
    }

    if (trademark_code) {
      where.trademark_code = {
        contains: trademark_code,
      };
    }

    if (registration_type) {
      where.registration_type = {
        contains: registration_type,
      };
    }

    if (status !== undefined) {
      where.status = status;
    }

    // 商标申请人筛选
    if (owner_name) {
      where.owner_name = {
        contains: owner_name,
      };
    }

    // 经营状态筛选
    if (last_procedure_status) {
      where.last_procedure_status = {
        contains: last_procedure_status,
      };
    }
    
    // 商标号筛选
    if (params.register_id) {
      where.register_id = params.register_id;
    }

    // 创建时间范围查询
    if (start_time && end_time) {
      where.created_at = {
        gte: start_time,
        lte: end_time,
      };
    } else if (start_time) {
      where.created_at = {
        gte: start_time,
      };
    } else if (end_time) {
      where.created_at = {
        lte: end_time,
      };
    }

    // 查询总数
    const total = await this.prisma.SelfOwnedBrand.count({ where });

    // 查询列表数据
    const rows = await this.prisma.SelfOwnedBrand.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        created_at: 'desc',
      },
      include: {
        brand_images: true,
      },
    });

    return { rows, total };
  }

  /**
   * 获取自主品牌详情
   * @param {Number|String} id 自主品牌ID，可以是数字或字符串
   * @returns {Promise<Object>} 自主品牌详情
   */
  async getSelfOwnedBrandDetail(id) {
    // 将ID转换为BigInt类型
    const bigIntId = typeof id === 'string' ? BigInt(id) : id;
    
    return await this.prisma.SelfOwnedBrand.findFirst({
      where: {
        id: bigIntId,
        deleted_at: null,
      },
      include: {
        brand_images: true,
      },
    });
  }

  /**
   * 创建自主品牌
   * @param {Object} data 自主品牌数据
   * @returns {Promise<Object>} 创建的自主品牌对象
   */
  async createSelfOwnedBrand(data) {
    const { brand_images = [], ...brandData } = data;
    const now = Date.now();

    try {
      // 生成雪花ID
      const snowflakeId = generateSnowflakeId();
      console.log('生成的雪花ID:', snowflakeId);
      
      // 处理BigInt字段
      let created_by = null;
      if (brandData.created_by) {
        created_by = typeof brandData.created_by === 'bigint' 
          ? brandData.created_by 
          : BigInt(String(brandData.created_by).replace(/[^0-9]/g, ''));
      }
      
      let updated_by = null;
      if (brandData.updated_by) {
        updated_by = typeof brandData.updated_by === 'bigint' 
          ? brandData.updated_by 
          : BigInt(String(brandData.updated_by).replace(/[^0-9]/g, ''));
      }
      
      // 处理register_id字段
      if (brandData.register_id) {
        // 确保register_id不超过13位
        const registerIdStr = String(brandData.register_id).replace(/[^0-9]/g, '');
        if (registerIdStr.length > 13) {
          throw new Error('注册ID超过最大长度13位');
        }
        brandData.register_id = registerIdStr ? BigInt(registerIdStr) : null;
      }
      
      // 处理字符串字段长度
      // 根据数据库模型定义检查并截断超长字段
      if (brandData.name && brandData.name.length > 100) {
        console.warn('商标名称超出长度限制，已截断');
        brandData.name = brandData.name.substring(0, 100);
      }
      
      if (brandData.image && brandData.image.length > 255) {
        console.warn('logo图片url超出长度限制，已截断');
        brandData.image = brandData.image.substring(0, 255);
      }
      
      if (brandData.owner_name && brandData.owner_name.length > 255) {
        console.warn('申请人超出长度限制，已截断');
        brandData.owner_name = brandData.owner_name.substring(0, 255);
      }
      
      if (brandData.owner_address && brandData.owner_address.length > 255) {
        console.warn('申请人地址超出长度限制，已截断');
        brandData.owner_address = brandData.owner_address.substring(0, 255);
      }
      
      if (brandData.agency && brandData.agency.length > 255) {
        console.warn('代理组织机构超出长度限制，已截断');
        brandData.agency = brandData.agency.substring(0, 255);
      }
      
      if (brandData.registration_type && brandData.registration_type.length > 30) {
        console.warn('商标类型超出长度限制，已截断');
        brandData.registration_type = brandData.registration_type.substring(0, 30);
      }
      
      if (brandData.last_procedure_status && brandData.last_procedure_status.length > 30) {
        console.warn('当前状态超出长度限制，已截断');
        brandData.last_procedure_status = brandData.last_procedure_status.substring(0, 30);
      }
      
      if (brandData.exclusive_date_limit && brandData.exclusive_date_limit.length > 60) {
        console.warn('专用权期限超出长度限制，已截断');
        brandData.exclusive_date_limit = brandData.exclusive_date_limit.substring(0, 60);
      }
      
      if (brandData.class_name && brandData.class_name.length > 60) {
        console.warn('商标类名超出长度限制，已截断');
        brandData.class_name = brandData.class_name.substring(0, 60);
      }
      
      if (brandData.trademark_code && brandData.trademark_code.length > 100) {
        console.warn('商标代码超出长度限制，已截断');
        brandData.trademark_code = brandData.trademark_code.substring(0, 100);
      }
      
      if (brandData.source_code_rule && brandData.source_code_rule.length > 100) {
        console.warn('溯源码规则超出长度限制，已截断');
        brandData.source_code_rule = brandData.source_code_rule.substring(0, 100);
      }
      
      if (brandData.brand_location && brandData.brand_location.length > 100) {
        console.warn('品牌位置超出长度限制，已截断');
        brandData.brand_location = brandData.brand_location.substring(0, 100);
      }
      
      // 开启事务
      return await this.prisma.$transaction(async (tx) => {
        // 使用Prisma ORM创建自主品牌
        const brand = await tx.SelfOwnedBrand.create({
          data: {
            ...brandData,
            id: BigInt(snowflakeId),
            created_by: created_by || null,
            updated_by: updated_by || null,
            created_at: now,
            updated_at: now
          }
        });
        
        console.log('创建自主品牌成功:', brand.id);
        
        // 返回创建的品牌，不包含图片
        return brand;
      });
    } catch (error) {
      console.error('创建自主品牌事务失败:', error);
      throw error;
    }
  }
  /**
   * 更新自主品牌
   * @param {Number|String} id 自主品牌ID，可以是数字或字符串
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的自主品牌
   */
  async updateSelfOwnedBrand(id, data) {
    try {
      // 转换ID为BigInt
      const bigIntId = BigInt(id);
      let now = BigInt(Date.now());
      
      // 处理传入的数据 - 将brand_images提取出来单独处理
      const { brand_images: brandImages, updatedBy, ...brandData } = data;
      
      // 处理BigInt字段
      let updated_by = null;
      if (updatedBy) {
        // 确保将updatedBy转换为BigInt类型
        try {
          // 先将任何类型转为字符串，去除非数字字符，再转为BigInt
          const cleanedStr = String(updatedBy).replace(/[^0-9]/g, '');
          if (cleanedStr) {
            updated_by = BigInt(cleanedStr);
            console.log('成功将updatedBy转换为BigInt:', updated_by, '类型:', typeof updated_by);
          } else {
            console.warn('无法从空白字符串转换updatedBy到BigInt');
            updated_by = null;
          }
        } catch (err) {
          console.error('转换updatedBy到BigInt时出错:', err, '原值:', updatedBy, '类型:', typeof updatedBy);
          // 防止转换失败
          updated_by = null;
        }
      }
      
      console.log('处理后的 updated_by:', updated_by, '类型:', typeof updated_by);
      
      // 处理register_id字段
      if (brandData.registerId) {
        // 确保register_id不超过13位
        const registerIdStr = String(brandData.registerId).replace(/[^0-9]/g, '');
        if (registerIdStr.length > 13) {
          throw new Error('注册ID超过最大长度13位');
        }
        brandData.register_id = registerIdStr ? BigInt(registerIdStr) : null;
        delete brandData.registerId;
      }
      
      // 字段长度限制配置
      const fieldLengthLimits = {
        name: 100,
        image: 255,
        owner_name: 255,
        owner_address: 255,
        agency: 255,
        registration_type: 30,
        last_procedure_status: 30,
        exclusive_date_limit: 60,
        class_name: 60,
        trademark_code: 100,
        source_code_rule: 100,
        brand_location: 100,
        product_desc: 1000,  // 假设产品描述有更长的限制
      };
      
      // 检查并截断超长字段
      console.log('开始检查字段长度:');
      for (const [field, maxLength] of Object.entries(fieldLengthLimits)) {
        if (brandData[field] && typeof brandData[field] === 'string') {
          console.log(`字段 ${field}: 当前长度=${brandData[field].length}, 最大长度=${maxLength}`);
          if (brandData[field].length > maxLength) {
            console.warn(`字段 ${field} 超出长度限制，已截断从 ${brandData[field].length} 到 ${maxLength}`);
            brandData[field] = brandData[field].substring(0, maxLength);
          }
        }
      }
      
      // 转换前端驼峰命名到数据库下划线命名
      if (brandData.ownerName) {
        brandData.owner_name = brandData.ownerName;
        delete brandData.ownerName;
      }
      
      if (brandData.ownerAddress) {
        brandData.owner_address = brandData.ownerAddress;
        delete brandData.ownerAddress;
      }
      
      if (brandData.registrationType) {
        brandData.registration_type = brandData.registrationType;
        delete brandData.registrationType;
      }
      
      if (brandData.preAnnDate) {
        brandData.pre_ann_date = brandData.preAnnDate;
        delete brandData.preAnnDate;
      }
      
      if (brandData.applyDate) {
        brandData.apply_date = brandData.applyDate;
        delete brandData.applyDate;
      }
      
      if (brandData.regAnnDate) {
        brandData.reg_ann_date = brandData.regAnnDate;
        delete brandData.regAnnDate;
      }
      
      if (brandData.lastProcedureStatus) {
        brandData.last_procedure_status = brandData.lastProcedureStatus;
        delete brandData.lastProcedureStatus;
      }
      
      if (brandData.productDesc) {
        brandData.product_desc = brandData.productDesc;
        delete brandData.productDesc;
      }
      
      if (brandData.exclusiveDateLimit) {
        brandData.exclusive_date_limit = brandData.exclusiveDateLimit;
        delete brandData.exclusiveDateLimit;
      }
      
      if (brandData.classId) {
        brandData.class_id = brandData.classId;
        delete brandData.classId;
      }
      
      if (brandData.className) {
        brandData.class_name = brandData.className;
        delete brandData.className;
      }
      
      if (brandData.trademarkCode) {
        brandData.trademark_code = brandData.trademarkCode;
        delete brandData.trademarkCode;
      }
      
      if (brandData.sourceCodeRule) {
        brandData.source_code_rule = brandData.sourceCodeRule;
        delete brandData.sourceCodeRule;
      }
      
      if (brandData.brandLocation) {
        brandData.brand_location = brandData.brandLocation;
        delete brandData.brandLocation;
      }
      
      // 处理字符串字段长度
      // 根据数据库模型定义检查并截断超长字段
      if (brandData.name && brandData.name.length > 100) {
        console.warn('商标名称超出长度限制，已截断');
        brandData.name = brandData.name.substring(0, 100);
      }
      
      if (brandData.image && brandData.image.length > 255) {
        console.warn('logo图片url超出长度限制，已截断');
        brandData.image = brandData.image.substring(0, 255);
      }
      
      if (brandData.owner_name && brandData.owner_name.length > 255) {
        console.warn('申请人超出长度限制，已截断');
        brandData.owner_name = brandData.owner_name.substring(0, 255);
      }
      
      if (brandData.owner_address && brandData.owner_address.length > 255) {
        console.warn('申请人地址超出长度限制，已截断');
        brandData.owner_address = brandData.owner_address.substring(0, 255);
      }
      
      if (brandData.agency && brandData.agency.length > 255) {
        console.warn('代理组织机构超出长度限制，已截断');
        brandData.agency = brandData.agency.substring(0, 255);
      }
      
      if (brandData.registration_type && brandData.registration_type.length > 30) {
        console.warn('商标类型超出长度限制，已截断');
        brandData.registration_type = brandData.registration_type.substring(0, 30);
      }
      
      if (brandData.last_procedure_status && brandData.last_procedure_status.length > 30) {
        console.warn('当前状态超出长度限制，已截断');
        brandData.last_procedure_status = brandData.last_procedure_status.substring(0, 30);
      }
      
      if (brandData.exclusive_date_limit && brandData.exclusive_date_limit.length > 60) {
        console.warn('专用权期限超出长度限制，已截断');
        brandData.exclusive_date_limit = brandData.exclusive_date_limit.substring(0, 60);
      }
      
      if (brandData.class_name && brandData.class_name.length > 60) {
        console.warn('商标类名超出长度限制，已截断');
        brandData.class_name = brandData.class_name.substring(0, 60);
      }
      
      if (brandData.trademark_code && brandData.trademark_code.length > 100) {
        console.warn('商标代码超出长度限制，已截断');
        brandData.trademark_code = brandData.trademark_code.substring(0, 100);
      }
      
      if (brandData.source_code_rule && brandData.source_code_rule.length > 100) {
        console.warn('溯源码规则超出长度限制，已截断');
        brandData.source_code_rule = brandData.source_code_rule.substring(0, 100);
      }
      
      if (brandData.brand_location && brandData.brand_location.length > 100) {
        console.warn('品牌位置超出长度限制，已截断');
        brandData.brand_location = brandData.brand_location.substring(0, 100);
      }
      
      // 确保brand_images不在更新数据中
      if (brandData.brand_images) {
        delete brandData.brand_images;
      }
      
      // 开启事务
      return await this.prisma.$transaction(async (tx) => {
        // 检查品牌是否存在
        const existingBrand = await tx.SelfOwnedBrand.findFirst({
          where: {
            id: bigIntId,
            deleted_at: null
          }
        });
        
        if (!existingBrand) {
          throw new Error('自主品牌不存在或已被删除');
        }
        
        // 调试打印：显示所有字段及其长度和类型
        console.log('准备更新自主品牌，字段信息如下:');
        for (const key in brandData) {
          const type = typeof brandData[key];
          if (type === 'string') {
            console.log(`字段 ${key}: 类型=${type}, 值长度=${brandData[key].length}, 值=${brandData[key]}`);
          } else if (brandData[key] === null) {
            console.log(`字段 ${key}: 类型=null`);
          } else if (typeof brandData[key] === 'object') {
            console.log(`字段 ${key}: 类型=${type}, 具体类型=${brandData[key].constructor.name}, 值=${brandData[key]}`);
          } else {
            console.log(`字段 ${key}: 类型=${type}, 值=${brandData[key]}`);
          }
        }
        
        // 更新自主品牌
        try {
          // 创建一个可以处理BigInt的JSON序列化函数
          const safeStringify = (obj) => {
            return JSON.stringify(obj, (key, value) => 
              typeof value === 'bigint' ? value.toString() + 'n' : value
            , 2);
          };
          
          console.log('开始更新自主品牌，数据:');
          // 用不同的方式处理打印对象以防止BigInt序列化错误
          const logData = {};
          for (const key in brandData) {
            logData[key] = brandData[key];
          }
          logData.updated_by = updated_by ? `${updated_by.toString()}n (BigInt)` : null;
          logData.updated_at = now ? `${now.toString()}n (BigInt)` : null;
          
          console.log(safeStringify(logData));
          
          await tx.SelfOwnedBrand.update({
            where: {
              id: bigIntId,
            },
            data: {
              ...brandData,
              updated_by: updated_by,
              updated_at: now
            },
          });
          
          console.log('更新自主品牌成功:', bigIntId);
        } catch (error) {
          console.error('更新自主品牌失败，错误详情:', error);
          console.error('错误消息:', error.message);
          if (error.meta) {
            console.error('错误元数据:', error.meta);
          }
          throw error;
        }

        // 处理品牌附件
        if (brandImages !== undefined) {
          // 删除现有附件
          await tx.SelfOwnedBrandImage.deleteMany({
            where: {
              owned_brand_id: bigIntId,
              deleted_at: null
            },
          });
          
          // 如果提供了新附件，则添加
          if (brandImages && brandImages.length > 0) {
            await tx.SelfOwnedBrandImage.createMany({
              data: brandImages.map(image => ({
                owned_brand_id: bigIntId,
                brand_image: image,
                created_at: now,
                updated_at: now,
              })),
            });
            console.log(`更新品牌附件成功: ${brandImages.length} 个`);
          } else {
            console.log('清除品牌附件成功');
          }
        } else {
          console.log('未提供品牌附件信息，保持现有附件不变');
        }

        // 返回更新后的品牌，包含图片
        return await tx.SelfOwnedBrand.findFirst({
          where: {
            id: bigIntId,
            deleted_at: null
          },
          include: {
            brand_images: true,
          },
        });
      });
    } catch (error) {
      console.error('更新自主品牌事务失败:', error);
      throw error;
    }
  }

  /**
   * 删除自主品牌（软删除）
   * @param {Number|String} id 自主品牌ID，可以是数字或字符串
   * @returns {Promise<Object>} 删除结果
   */
  async deleteSelfOwnedBrand(id) {
    const now = Date.now();
    
    // 将ID转换为BigInt类型
    const bigIntId = typeof id === 'string' ? BigInt(id) : id;

    // 开启事务
    return await this.prisma.$transaction(async (tx) => {
      // 软删除自主品牌
      await tx.SelfOwnedBrand.update({
        where: {
          id: bigIntId,
        },
        data: {
          deleted_at: now,
        },
      });

      // 软删除关联的品牌图片
      await tx.SelfOwnedBrandImage.updateMany({
        where: {
          owned_brand_id: bigIntId,
        },
        data: {
          deleted_at: now,
        },
      });

      return { success: true };
    });
  }
}

module.exports = new SelfOwnedBrandModel();

/**
 * 区域模型
 * 处理区域相关的数据库操作
 */
const prismaManager = require('../../../core/prisma');

class RegionModel {
  constructor() {
      this.prisma = prismaManager.getClient('base');
  }

  /**
   * 获取区域树结构
   * @param {number} parentId - 父级ID，默认为0表示获取顶级
   * @param {Boolean} excludeStreet - 是否排除街道级数据，默认为 false
   * @param {Boolean} onlyProvinceCity - 是否只获取省市级数据（过滤区/县级），默认为 false
   * @returns {Promise<Array>} - 树形结构的区域数据
   */
  async getRegionTree(parentId = 0, excludeStreet = false, onlyProvinceCity = false) {
    try {
      // 构建 SQL 查询
      let sql;
      
      if (onlyProvinceCity) {
        // 只获取省市级数据（level 1 为省级，level 2 为市级）
        sql = `
SELECT id, citycode, cityname, level, parent_id 
FROM base.region 
WHERE level <= 2
ORDER BY level ASC, cityname ASC
`;
      } else if (excludeStreet) {
        // 排除街道级数据的查询
        sql = `
SELECT id, citycode, cityname, level, parent_id 
FROM base.region 
WHERE level < 4
  AND cityname NOT LIKE '%街道%'
ORDER BY level ASC, cityname ASC
`;
      } else {
        // 包含所有级别数据的查询
        sql = `
SELECT id, citycode, cityname, level, parent_id 
FROM base.region 
ORDER BY level ASC, cityname ASC
`;
      }
      
      // 执行查询
      const allRegions = await this.prisma.$queryRawUnsafe(sql);
      
      // 创建区域映射表
      const regionMap = {};
      allRegions.forEach(region => {
        regionMap[region.id] = {
          ...region,
          children: []
        };
      });
      
      // 找出所有根节点（省级）
      const rootNodes = allRegions.filter(region => region.level === 1);
      
      // 构建树结构
      const buildTree = (nodes) => {
        return nodes.map(node => {
          const nodeWithChildren = regionMap[node.id];
          
          // 查找当前节点的所有直接子节点
          const childNodes = allRegions.filter(region => region.parent_id === node.id);
          
          if (childNodes.length > 0) {
            nodeWithChildren.children = buildTree(childNodes);
          }
          
          return nodeWithChildren;
        });
      };
      
      // 从根节点开始构建完整树
      const tree = buildTree(rootNodes);
      
      return tree;
    } catch (error) {
      console.error('getRegionTree 错误:', error);
      throw error;
    }
  }

  /**
   * 根据区域代码获取区域名称
   * @param {Array<string>} regionCodes - 区域代码数组
   * @returns {Promise<string>} - 区域名称，多个以逗号分隔
   */
  async getRegionNamesByCodes(regionCodes) {
    try {
      if (!regionCodes || regionCodes.length === 0) {
        return '';
      }

      // 特殊处理：当regionCodes为0时，返回"全国"
      if (regionCodes.length === 1 && (regionCodes[0] === '0' || regionCodes[0] === 0)) {
        return '全国';
      }

      // 将区域代码转换为整数
      const numericCodes = regionCodes.map(code => parseInt(code, 10));
      
      // 构建参数占位符
      const placeholders = numericCodes.map((_, index) => `$${index + 1}`).join(',');
      
      // 使用 SQL 查询获取区域名称
      const sql = `
SELECT cityname 
FROM base.region 
WHERE citycode IN (${placeholders})
ORDER BY level ASC, cityname ASC
      `;
      
      // 执行查询
      const regions = await this.prisma.$queryRawUnsafe(sql, ...numericCodes);
      
      // 提取名称并用逗号连接
      const regionNames = regions.map(region => region.cityname).join(',');
      
      return regionNames;
    } catch (error) {
      console.error('getRegionNamesByCodes 错误:', error);
      // 错误时返回空字符串，不影响主流程
      return '';
    }
  }
}

module.exports = new RegionModel();

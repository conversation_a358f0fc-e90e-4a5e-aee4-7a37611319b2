const prismaManager = require('../../../core/prisma');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    return Number(data);
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = handleBigInt(data[key]);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 商品SKU模型
 */
class GoodsSkuModel {
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 根据SPU ID获取商品SKU列表
   * @param {string|number} spuId 商品SPU ID
   * @returns {Promise<Array>} 商品SKU列表
   */
  async getSkusBySpuId(spuId) {
    try {
      const skus = await this.prisma.goodsSku.findMany({
        where: {
          goods_spu_id: BigInt(spuId),
          deleted_at: null
        },
        include: {
          goods_images: true
        },
        orderBy: {
          id: 'asc'
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(skus));
    } catch (error) {
      console.error(`获取商品SPU(ID:${spuId})的SKU列表失败:`, error);
      throw error;
    }
  }

  /**
   * 根据ID获取商品SKU
   * @param {string|number} id SKU ID
   * @returns {Promise<Object>} 商品SKU
   */
  async getSkuById(id) {
    try {
      // 使用findFirst查询，可以同时使用id和deleted_at条件
      const bigIntId = BigInt(id);
      
      const sku = await this.prisma.goodsSku.findFirst({
        where: {
          id: bigIntId,
          deleted_at: null
        },
        include: {
          goods_images: true
        }
      });
      
      return sku ? convertKeysToCamelCase(handleBigInt(sku)) : null;
    } catch (error) {
      console.error(`获取商品SKU(ID:${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据SKU编码获取商品SKU
   * @param {string} skuCode SKU编码
   * @returns {Promise<Object>} 商品SKU
   */
  async getSkuByCode(skuCode) {
    try {
      const sku = await this.prisma.goodsSku.findFirst({
        where: {
          sku_code: skuCode,
          deleted_at: null
        },
        include: {
          goods_images: true
        }
      });
      
      return sku ? convertKeysToCamelCase(handleBigInt(sku)) : null;
    } catch (error) {
      console.error(`获取商品SKU(编码:${skuCode})失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品SKU
   * @param {Object} skuData SKU数据
   * @returns {Promise<Object>} 添加的SKU
   */
  async addSku(skuData) {
    try {
      const sku = await this.prisma.goodsSku.create({
        data: {
          goods_spu_id: BigInt(skuData.goodsSpuId),
          sku_code: skuData.skuCode,
          barcode: skuData.barcode || null,
          sales_price: skuData.salesPrice,
          original_price: skuData.originalPrice || null,
          cost_price: skuData.costPrice || null,
          weight: skuData.weight || null,
          volume: skuData.volume || null,
          stock: skuData.stock || 0,
          stock_warning: skuData.stockWarning || null,
          is_default: skuData.isDefault || false,
          is_enabled: skuData.isEnabled || true
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(sku));
    } catch (error) {
      console.error('添加商品SKU失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品SKU
   * @param {string|number} id SKU ID
   * @param {Object} skuData SKU数据
   * @returns {Promise<Object>} 更新后的SKU
   */
  async updateSku(id, skuData) {
    try {
      const sku = await this.prisma.goodsSku.update({
        where: {
          id: BigInt(id)
        },
        data: {
          sku_code: skuData.skuCode,
          barcode: skuData.barcode,
          sales_price: skuData.salesPrice,
          original_price: skuData.originalPrice,
          cost_price: skuData.costPrice,
          weight: skuData.weight,
          volume: skuData.volume,
          stock: skuData.stock,
          stock_warning: skuData.stockWarning,
          is_default: skuData.isDefault,
          is_enabled: skuData.isEnabled
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(sku));
    } catch (error) {
      console.error(`更新商品SKU(ID:${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品SKU
   * @param {string|number} id SKU ID
   * @returns {Promise<void>}
   */
  async deleteSku(id) {
    try {
      await this.prisma.goodsSku.update({
        where: {
          id: BigInt(id)
        },
        data: {
          deleted_at: BigInt(Date.now())
        }
      });
    } catch (error) {
      console.error(`删除商品SKU(ID:${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 更新SKU库存
   * @param {string|number} id SKU ID
   * @param {number} stock 库存数量
   * @returns {Promise<Object>} 更新后的SKU
   */
  async updateSkuStock(id, stock) {
    try {
      const sku = await this.prisma.goodsSku.update({
        where: {
          id: BigInt(id)
        },
        data: {
          stock: stock
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(sku));
    } catch (error) {
      console.error(`更新商品SKU(ID:${id})库存失败:`, error);
      throw error;
    }
  }

  /**
   * 检查SKU编码是否已存在
   * @param {string} skuCode SKU编码
   * @param {string|number} excludeId 排除的SKU ID
   * @returns {Promise<boolean>} 是否存在
   */
  async isSkuCodeExists(skuCode, excludeId = null) {
    try {
      const where = {
        sku_code: skuCode,
        deleted_at: null
      };
      
      if (excludeId) {
        where.id = {
          not: BigInt(excludeId)
        };
      }
      
      const existingSku = await this.prisma.goodsSku.findFirst({
        where
      });
      
      return !!existingSku;
    } catch (error) {
      console.error(`检查SKU编码(${skuCode})是否存在失败:`, error);
      throw error;
    }
  }

  /**
   * 根据SKU ID数组批量查询商品SKU信息
   * @param {Array<string|number>} ids SKU ID数组
   * @returns {Promise<Array>} 商品SKU信息数组
   */
  async getSkusByIds(ids) {
    try {
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return [];
      }
      
      // 将所有ID转换为BigInt
      const bigIntIds = ids.map(id => BigInt(String(id)));
      
      // 查询SKU信息，并关联查询SPU信息和规格值信息
      const skus = await this.prisma.goodsSku.findMany({
        where: {
          id: { in: bigIntIds },
          deleted_at: null
        },
        include: {
          goods_spu: {
            select: {
              id: true,
              name: true,
              spu_code: true,
              // 添加 SPU 图片查询
              goods_images: {
                select: {
                  id: true,
                  image_url: true,
                  sort_order: true,
                  is_default: true
                },
                orderBy: [
                  { is_default: 'desc' },
                  { sort_order: 'asc' }
                ]
              }
            }
          },
          goods_sku_specification_values: {
            select: {
              goods_specification_value_id: true,
              goods_specification_value: {
                select: {
                  id: true,
                  value: true,
                  goods_specification_name: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              }
            }
          },
          // 添加 SKU 商品图片查询
          goods_images: {
            select: {
              id: true,
              image_url: true,
              sort_order: true,
              is_default: true
            },
            orderBy: [
              { is_default: 'desc' },
              { sort_order: 'asc' }
            ]
          }
        }
      });
      
      // 处理查询结果
      const result = skus.map(sku => {
        // 构建SKU名称 - 根据规格值组合
        let skuName = ''; // 默认为空字符串，不使用SKU编码
        
        // 如果有规格值信息，则使用规格值组合作为SKU名称
        if (sku.goods_sku_specification_values && sku.goods_sku_specification_values.length > 0) {
          // 提取规格值
          const specValues = sku.goods_sku_specification_values
            .map(specValue => {
              const value = specValue.goods_specification_value?.value;
              return value || '';
            })
            .filter(value => value !== '');
          
          // 如果有规格值，则使用规格值组合作为SKU名称
          if (specValues.length > 0) {
            skuName = specValues.join(' ');
          }
        }
        
        // 处理商品图片信息
        let imageUrl = '';
        
        // 优先使用 SKU 图片
        if (sku.goods_images && sku.goods_images.length > 0) {
          // 优先使用默认图片，如果没有默认图片则使用第一张图片
          const defaultImage = sku.goods_images.find(img => img.is_default);
          imageUrl = defaultImage ? defaultImage.image_url : 
                    (sku.goods_images[0] ? sku.goods_images[0].image_url : '');
        }
        
        // 如果 SKU 没有图片，则使用 SPU 图片
        if (!imageUrl && sku.goods_spu && sku.goods_spu.goods_images && sku.goods_spu.goods_images.length > 0) {
          const defaultSpuImage = sku.goods_spu.goods_images.find(img => img.is_default);
          imageUrl = defaultSpuImage ? defaultSpuImage.image_url : 
                    (sku.goods_spu.goods_images[0] ? sku.goods_spu.goods_images[0].image_url : '');
        }
        
        return {
          skuId: String(sku.id),
          spuId: String(sku.goods_spu_id),
          skuCode: sku.sku_code,
          skuName: skuName, // 使用规格值组合作为SKU名称
          spuName: sku.goods_spu?.name || '',
          salesPrice: sku.sales_price,
          stock: sku.stock,
          imageUrl: imageUrl // 添加商品图片URL
        };
      });
      
      return result;
    } catch (error) {
      console.error(`根据SKU ID数组查询商品信息失败:`, error);
      throw error;
    }
  }
}

module.exports = new GoodsSkuModel();

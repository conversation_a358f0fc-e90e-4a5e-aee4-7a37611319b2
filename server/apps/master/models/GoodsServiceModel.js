const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 如果是id字段且值为bigint，返回字符串
        if (key === 'id' && typeof data[key] === 'bigint') {
          result[key] = data[key].toString();
        } else {
          result[key] = handleBigInt(data[key]);
        }
      }
    }
    return result;
  }
  
  return data;
}

class GoodsServiceModel {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取所有商品服务
   * @param {Object} options 查询选项
   * @param {string} options.name 服务名称（模糊搜索）
   * @param {string} options.startTime 创建时间范围开始
   * @param {string} options.endTime 创建时间范围结束
   * @param {number} options.skip 跳过数量
   * @param {number} options.take 获取数量
   * @returns {Promise<Object>} 返回服务列表和总数
   */
  async getAllGoodsServices(options = {}) {
    try {
      // 构建查询条件
      const whereCondition = {
        deleted_at: null // 只获取未删除的服务
      };
      
      // 如果提供了名称搜索参数，添加模糊搜索条件
      if (options.name) {
        whereCondition.name = {
          contains: options.name,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 如果提供了描述搜索参数，添加模糊搜索条件
      if (options.description) {
        whereCondition.description = {
          contains: options.description,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 如果提供了创建时间范围，添加时间范围条件
      if (options.startTime || options.endTime) {
        whereCondition.created_at = {};
        
        if (options.startTime) {
          // 直接使用毫秒级时间戳，并作为BigInt处理
          const startTimeMs = BigInt(options.startTime);
          whereCondition.created_at.gte = startTimeMs;
        }
        
        if (options.endTime) {
          // 直接使用毫秒级时间戳，并作为BigInt处理
          const endTimeMs = BigInt(options.endTime);
          whereCondition.created_at.lte = endTimeMs;
        }
      }
      
      // 分页参数
      const skip = options.skip || 0;
      const take = options.take || 10;
      
      // 从数据库获取服务和总数
      const [services, total] = await Promise.all([
        this.prisma.goodsService.findMany({
          where: whereCondition,
          orderBy: {
            id: 'asc'
          },
          skip,
          take
        }),
        this.prisma.goodsService.count({
          where: whereCondition
        })
      ]);
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(services);
      const processedServices = convertKeysToCamelCase(processedData);
      
      return {
        data: processedServices,
        total: Number(total)
      };
    } catch (error) {
      console.error('获取商品服务失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取商品服务
   * @param {number} id 服务ID
   * @returns {Promise<Object>} 返回服务对象
   */
  async getGoodsServiceById(id) {
    try {
      // 获取服务信息
      const service = await this.prisma.goodsService.findFirst({
        where: { 
          id: BigInt(id),
          deleted_at: null // 只获取未删除的服务
        }
      });
      
      if (!service) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(service);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`获取ID为${id}的服务失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品服务
   * @param {Object} serviceData 服务数据
   * @returns {Promise<Object>} 返回新创建的服务
   */
  async addGoodsService(serviceData) {
    try {
      // 生成雪花ID
      const serviceId = generateSnowflakeId();
      
      // 准备创建数据
      const createData = {
        id: serviceId,
        name: serviceData.name,
        image_url: serviceData.image_url || null,
        description: serviceData.description || null
      };
      
      // 如果提供了创建人和更新人，添加到创建数据中
      if (serviceData.created_by) {
        createData.created_by = serviceData.created_by;
      }
      
      if (serviceData.updated_by) {
        createData.updated_by = serviceData.updated_by;
      }
      
      // 创建服务
      const newService = await this.prisma.goodsService.create({
        data: createData
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(newService);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('添加服务失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品服务
   * @param {number} id 服务ID
   * @param {Object} serviceData 服务数据
   * @returns {Promise<Object>} 返回更新后的服务
   */
  async updateGoodsService(id, serviceData) {
    try {
      const serviceId = BigInt(id);
      
      // 首先检查服务是否存在
      const existingService = await this.prisma.goodsService.findUnique({
        where: { id: serviceId }
      });
      
      if (!existingService) {
        throw new Error(`ID为${id}的服务不存在`);
      }
      
      // 准备更新数据
      const updateData = {
        name: serviceData.name,
        image_url: serviceData.image_url,
        description: serviceData.description,
        updated_at: BigInt(Date.now()) // 使用毫秒级时间戳，与其他字段保持一致
      };
      
      // 如果提供了更新人，添加到更新数据中
      if (serviceData.updated_by) {
        updateData.updated_by = serviceData.updated_by;
      }
      
      // 更新服务
      const updatedService = await this.prisma.goodsService.update({
        where: { id: serviceId },
        data: updateData
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(updatedService);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`更新ID为${id}的服务失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品服务
   * @param {number} id 服务ID
   * @returns {Promise<Object>} 返回删除结果
   */
  async deleteGoodsService(id) {
    try {
      const serviceId = BigInt(id);
      
      // 检查是否有关联的商品
      const relatedAssociations = await this.prisma.goodsServiceAssociation.findMany({
        where: {
          goods_service_id: serviceId
        }
      });
      
      if (relatedAssociations.length > 0) {
        throw new Error('该服务下有关联商品，无法删除');
      }
      
      // 软删除服务 - 使用当前时间戳（毫秒）作为BigInt
      const currentTimestamp = BigInt(Date.now());
      const deletedService = await this.prisma.goodsService.update({
        where: { id: serviceId },
        data: { deleted_at: currentTimestamp }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(deletedService);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`删除ID为${id}的服务失败:`, error);
      throw error;
    }
  }
}

module.exports = GoodsServiceModel;

const InventoryService = require('./InventoryService');

/**
 * 商城订单服务
 */
class OrderService {
  constructor(prisma) {
    this.prisma = prisma;
    this.inventoryService = new InventoryService(prisma);
  }

  /**
   * 查询用户订单列表
   * @param {BigInt} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {Number} options.page - 页码
   * @param {Number} options.pageSize - 每页数量
   * @param {Number} options.orderStatus - 订单状态（可选）
   * @param {String} options.id - 订单ID/编号（可选）
   * @param {String} options.sortField - 排序字段（可选）
   * @param {String} options.sortOrder - 排序方式（可选）
   * @returns {Promise<Object>} - 订单列表和总数
   */
  async queryUserOrders(userId, options) {
    const { 
      page = 1, 
      pageSize = 10, 
      orderStatus, 
      id,  // 使用id替代orderSn作为订单编号
      sortField = 'created_at',
      sortOrder = 'desc'
    } = options;

    // 构建查询条件
    const where = {
      user_id: BigInt(userId), // 将用户ID转换为BigInt类型
      deleted_at: null
    };

    // 添加可选过滤条件
    if (orderStatus !== undefined && orderStatus !== null) {
      where.order_status = orderStatus;
    }

    // 如果有搜索关键词，添加查询条件
    if (id) {
      // 判断是否为纯数字，用于订单ID查询
      const isNumeric = /^\d+$/.test(id.trim());
      
      // 构建查询条件
      const searchConditions = [
        // 精确匹配第三方订单编号
        { third_party_order_sn: id },
        // 模糊匹配订单备注
        { remark: { contains: id } }
      ];
      
      // 如果是纯数字并且有效，添加订单ID查询条件
      if (isNumeric && id.length <= 20) { // 限制数字长度，避免超出最大安全整数
        try {
          const orderId = BigInt(id);
          searchConditions.unshift({ id: orderId });
        } catch (error) {
          console.error('转换订单ID失败:', error.message);
          // 如果转换失败，忽略该条件
        }
      }
      
      // 使用Prisma的OR查询条件
      where.OR = searchConditions;
      
      // 安全地输出查询条件，避免BigInt序列化问题
      console.log('构建查询条件:', {
        user_id: where.user_id ? where.user_id.toString() : null,
        order_status: where.order_status,
        OR: where.OR ? where.OR.map(condition => {
          if (condition.id) {
            return { ...condition, id: condition.id.toString() };
          }
          return condition;
        }) : []
      });
    }

    // 构建排序条件
    const orderBy = {};
    orderBy[sortField] = sortOrder.toLowerCase();

    try {
      // 查询总数
      const total = await this.prisma.orders.count({ where });

      // 查询订单列表
      const orders = await this.prisma.orders.findMany({
        where,
        include: {
          order_items: true,
          order_shipping_info: true
        },
        orderBy,
        skip: (page - 1) * pageSize,
        take: pageSize
      });

      return {
        total,
        list: orders,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error('查询用户订单失败:', error);
      throw error;
    }
  }

  /**
   * 根据订单ID查询订单详情
   * @param {BigInt} orderId - 订单ID
   * @param {BigInt} userId - 用户ID（用于验证订单所属）
   * @returns {Promise<Object|null>} - 订单详情或null
   */
  async getOrderDetail(orderId, userId) {
    try {
      // 将订单ID和用户ID都转换为BigInt类型
      const order = await this.prisma.orders.findFirst({
        where: {
          id: BigInt(orderId),
          user_id: BigInt(userId),
          deleted_at: null
        },
        include: {
          order_items: true,
          order_shipping_info: true,
          order_logs: {
            orderBy: {
              created_at: 'desc'
            }
          }
        }
      });

      return order;
    } catch (error) {
      console.error('查询订单详情失败:', error);
      throw error;
    }
  }
  
  /**
   * 查询用户不同订单状态的数量
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} - 各订单状态的数量
   */
  async queryOrderStatusCounts(userId) {
    try {
      // 将用户ID转换为BigInt类型
      const bigIntUserId = BigInt(userId);
      
      console.log('查询用户订单状态数量:', bigIntUserId.toString());
      
      // 查询各状态订单数量
      const counts = await this.prisma.orders.groupBy({
        by: ['order_status'],
        where: {
          user_id: bigIntUserId,
          deleted_at: null
        },
        _count: {
          id: true
        }
      });
      
      console.log('查询结果:', counts);
      
      // 初始化所有可能的订单状态数量为0
      const statusCounts = {
        0: 0, // 待付款
        1: 0, // 待发货
        2: 0, // 待收货
        3: 0, // 已完成
        4: 0, // 已取消
        5: 0  // 已关闭
      };
      
      // 填充查询结果
      counts.forEach(item => {
        statusCounts[item.order_status] = Number(item._count.id);
      });
      
      return {
        success: true,
        data: statusCounts,
        message: '查询订单状态数量成功'
      };
    } catch (error) {
      console.error('查询订单状态数量失败:', error);
      throw error;
    }
  }

  /**
   * 取消订单
   * @param {BigInt} orderId - 订单ID
   * @param {BigInt} userId - 用户ID（用于验证订单所属）
   * @param {String} cancelReason - 取消原因
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 取消结果
   */
  async cancelOrder(orderId, userId, cancelReason, operator) {
    try {
      // 将订单ID和用户ID都转换为BigInt类型
      const bigIntOrderId = BigInt(orderId);
      const bigIntUserId = BigInt(userId);
      
      // 查询订单是否存在（包含订单项信息）
      const order = await this.prisma.orders.findFirst({
        where: {
          id: bigIntOrderId,
          user_id: bigIntUserId,
          deleted_at: null
        },
        include: {
          order_items: true  // 包含订单项，用于后续处理库存和销量
        }
      });
      
      if (!order) {
        throw new Error('订单不存在或已被删除');
      }
      
      // 检查订单状态是否允许取消
      if (order.order_status !== 0) {
        throw new Error(`当前订单状态(${order.order_status})不允许取消，只有待付款的订单才能取消`);
      }
      
      // 开始事务处理
      const result = await this.prisma.$transaction(async (prisma) => {
        // 获取当前时间的毫秒时间戳（BigInt格式）
        const currentTimestamp = BigInt(Date.now());
        
        // 使用库存服务处理库存恢复
        try {
          await this.inventoryService.restoreInventory(order.order_items, prisma);
        } catch (inventoryError) {
          console.error('库存恢复失败:', inventoryError.message);
          // 库存恢复失败不应该阻止订单取消，记录错误继续执行
        }
        
        // 更新订单状态为已取消
        const updatedOrder = await prisma.orders.update({
          where: {
            id: bigIntOrderId
          },
          data: {
            order_status: 4, // 已取消
            cancel_reason: cancelReason || '用户取消',
            cancelled_at: currentTimestamp,
            updated_at: currentTimestamp
          }
        });
        
        // 记录订单日志
        await prisma.order_logs.create({
          data: {
            // 必需字段
            operator_type: 1, // 1 表示用户操作，2 表示系统操作，3 表示管理员操作
            action: '取消订单', // 必需字段，操作类型
            // 关联订单
            orders: {
              connect: {
                id: bigIntOrderId
              }
            },
            // 可选字段
            id: BigInt(Date.now() + Math.floor(Math.random() * 1000)), // 生成唯一ID
            operator_id: operator.id ? BigInt(operator.id) : null, // 可选字段，操作者ID
            operator_name: operator.name || '用户', // 可选字段，操作者名称
            details: JSON.stringify({
              reason: cancelReason || '用户取消',
              inventoryRestored: true, // 标记库存已恢复
              itemsProcessed: order.order_items.length, // 处理的商品项数量
              operator: {
                id: operator.id,
                name: operator.name || '用户',
                role: operator.role || 'user'
              },
              ip: operator.ip || '',
              platform: operator.platform || 'mall'
            }),
            created_at: currentTimestamp
          }
        });
        

        
        // 返回更新后的订单
        return updatedOrder;
      });
      
      // 返回符合要求的数据结构
      return {
        success: true,
        data: {
          id: result.id.toString(),
          orderNumber: result.id.toString(), // 使用订单ID作为订单编号
          orderStatus: 'cancelled'
        },
        message: '订单取消成功，库存和销量已恢复'
      };
    } catch (error) {
      console.error('取消订单失败:', error);
      throw error;
    }
  }
}

module.exports = OrderService;

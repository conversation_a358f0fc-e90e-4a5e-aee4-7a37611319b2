const crypto = require('crypto');

/**
 * 微信公众号服务认证服务
 * 用于处理微信公众号服务器的认证和消息加解密
 */
class WechatAuthService {
  constructor() {
    // 这些值应该从配置文件中读取，这里为了演示先硬编码
    this.token = 'julingcloud_wechat_token_2025';  // 自定义的Token
    this.encodingAESKey = 'PH2Zxq7CwqN8mXRP9Swvb4JQvLKLDMnCvEzKZQzFt6R';  // 消息加解密密钥
    this.appId = 'wx67623f0db557d371';  // 微信公众号的AppID
    this.appSecret = 'f242cb08541ef82da5788ad23dead4b2';  // 微信公众号的AppSecret
    this.accessToken = null;
    this.accessTokenExpireTime = 0;
  }

  /**
   * 验证微信服务器请求的签名
   * @param {string} signature - 微信服务器发送的签名
   * @param {string} timestamp - 时间戳
   * @param {string} nonce - 随机数
   * @returns {boolean} - 验证是否通过
   */
  verifySignature(signature, timestamp, nonce) {
    try {
      // 1. 将token、timestamp、nonce三个参数进行字典序排序
      const arr = [this.token, timestamp, nonce].sort();
      
      // 2. 将三个参数字符串拼接成一个字符串进行sha1加密
      const str = arr.join('');
      const sha1 = crypto.createHash('sha1');
      sha1.update(str);
      const calculatedSignature = sha1.digest('hex');
      
      // 3. 开启调试日志
      console.log('计算的签名:', calculatedSignature);
      console.log('微信的签名:', signature);
      
      // 4. 比对计算的签名和微信服务器传递的签名是否一致
      return calculatedSignature === signature;
    } catch (error) {
      console.error('验证签名失败:', error);
      return false;
    }
  }

  /**
   * 解密微信服务器发送的消息
   * @param {string} encryptedMsg - 加密的消息
   * @returns {string} - 解密后的消息
   */
  decryptMsg(encryptedMsg) {
    try {
      // Base64解码密钥
      const aesKey = Buffer.from(this.encodingAESKey + '=', 'base64');
      
      // 解码消息
      const ciphertext = Buffer.from(encryptedMsg, 'base64');
      
      // 使用AES-CBC模式解密，取前16字节作为初始向量IV
      const iv = aesKey.slice(0, 16);
      const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, iv);
      decipher.setAutoPadding(false); // 关闭自动填充
      
      let decrypted = decipher.update(ciphertext, 'binary', 'utf8');
      decrypted += decipher.final('utf8');
      
      // 去除填充
      decrypted = this._removePKCS7Padding(decrypted);
      
      // 解析消息格式: 网络字节序(4字节) + 消息长度(4字节) + 消息内容 + AppID
      const content = decrypted.slice(8);
      const msgLen = content.readUInt32BE(0);
      const msg = content.slice(4, 4 + msgLen);
      const receivedAppId = content.slice(4 + msgLen);
      
      // 校验AppID
      if (receivedAppId !== this.appId) {
        throw new Error('AppID不匹配');
      }
      
      return msg.toString('utf8');
    } catch (error) {
      console.error('解密消息失败:', error);
      return '';
    }
  }

  /**
   * 加密消息
   * @param {string} msg - 明文消息
   * @returns {string} - 加密后的消息
   */
  encryptMsg(msg) {
    try {
      // Base64解码密钥
      const aesKey = Buffer.from(this.encodingAESKey + '=', 'base64');
      
      // 生成16字节的随机字符串
      const randomStr = crypto.randomBytes(16);
      
      // 消息长度(网络字节序)
      const msgLenBuf = Buffer.alloc(4);
      msgLenBuf.writeUInt32BE(msg.length, 0);
      
      // 拼接明文: 随机字符串 + 消息长度 + 消息内容 + AppID
      const msgBuf = Buffer.from(msg);
      const appIdBuf = Buffer.from(this.appId);
      const bufMsg = Buffer.concat([randomStr, msgLenBuf, msgBuf, appIdBuf]);
      
      // 使用PKCS#7填充
      const padded = this._addPKCS7Padding(bufMsg);
      
      // 使用AES-CBC模式加密，取密钥前16字节作为初始向量IV
      const iv = aesKey.slice(0, 16);
      const cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
      cipher.setAutoPadding(false); // 关闭自动填充，使用自定义的PKCS#7填充
      
      let encrypted = cipher.update(padded, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      return encrypted;
    } catch (error) {
      console.error('加密消息失败:', error);
      return '';
    }
  }

  /**
   * 添加PKCS#7填充
   * @param {Buffer} buf - 需要填充的Buffer
   * @returns {Buffer} - 填充后的Buffer
   */
  _addPKCS7Padding(buf) {
    const blockSize = 32;
    const padLen = blockSize - (buf.length % blockSize);
    const padBuf = Buffer.alloc(padLen, padLen);
    return Buffer.concat([buf, padBuf]);
  }

  /**
   * 去除PKCS#7填充
   * @param {Buffer} buf - 需要去除填充的Buffer
   * @returns {Buffer} - 去除填充后的Buffer
   */
  _removePKCS7Padding(buf) {
    const padLen = buf[buf.length - 1];
    if (padLen < 1 || padLen > 32) {
      return buf;
    }
    return buf.slice(0, buf.length - padLen);
  }

  /**
   * 获取微信接口调用凭证（access_token）
   * @returns {Promise<string>} - 接口调用凭证
   */
  async getAccessToken() {
    try {
      // 检查是否有有效的access_token
      const now = Date.now();
      if (this.accessToken && now < this.accessTokenExpireTime) {
        console.log('使用缓存的access_token:', this.accessToken);
        return this.accessToken;
      }

      // 输出调试信息
      console.log('开始获取新的access_token');
      console.log('appId:', this.appId);
      console.log('appSecret:', this.appSecret ? '已设置' : '未设置');

      // 请求新的access_token
      const axios = require('axios');
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${this.appId}&secret=${this.appSecret}`;
      
      console.log('请求URL:', url);
      
      const response = await axios.get(url);
      const data = response.data;
      
      console.log('API响应:', data);
      
      if (data.errcode) {
        throw new Error(`获取access_token失败: ${data.errmsg} (错误码: ${data.errcode})`);
      }
      
      this.accessToken = data.access_token;
      this.accessTokenExpireTime = now + (data.expires_in * 1000) - 200000; // 提前200秒过期，避免临界问题
      
      console.log('获取到新的access_token:', this.accessToken);
      console.log('过期时间:', new Date(this.accessTokenExpireTime).toLocaleString());
      
      return this.accessToken;
    } catch (error) {
      console.error('获取access_token失败:', error.message);
      if (error.response) {
        console.error('响应状态:', error.response.status);
        console.error('响应数据:', error.response.data);
      }
      throw error;
    }
  }

  /**
   * 创建临时二维码票据
   * @param {string} sceneStr - 场景值字符串，用于标识二维码用途
   * @param {number} expireSeconds - 二维码有效期，单位秒，最大不超过2592000（30天）
   * @returns {Promise<object>} - 二维码票据信息
   */
  async createQRCodeTicket(sceneStr, expireSeconds = 1800) {
    try {
      console.log(`开始创建临时二维码，场景值: ${sceneStr}, 有效期: ${expireSeconds}秒`);
      
      // 获取access_token
      console.log('获取access_token...');
      const accessToken = await this.getAccessToken();
      console.log('获取access_token成功:', accessToken.substring(0, 10) + '...');
      
      const axios = require('axios');
      const url = `https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=${accessToken}`;
      
      // 构建请求数据
      const postData = {
        expire_seconds: expireSeconds,
        action_name: 'QR_STR_SCENE',
        action_info: {
          scene: {
            scene_str: sceneStr
          }
        }
      };
      
      console.log('请求创建二维码URL:', url);
      console.log('请求数据:', JSON.stringify(postData));
      
      const response = await axios.post(url, postData);
      const data = response.data;
      
      console.log('微信创建二维码响应:', data);
      
      if (data.errcode) {
        throw new Error(`创建二维码票据失败: ${data.errmsg} (错误码: ${data.errcode})`);
      }
      
      // 生成二维码URL
      const qrcodeUrl = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${encodeURIComponent(data.ticket)}`;
      console.log('生成的二维码URL:', qrcodeUrl);
      
      // 返回票据信息和二维码URL
      const result = {
        ticket: data.ticket,
        expireSeconds: data.expire_seconds,
        url: data.url,
        qrcodeUrl
      };
      
      console.log('创建二维码成功:', result);
      return result;
    } catch (error) {
      console.error('创建二维码票据失败:', error.message);
      if (error.response) {
        console.error('响应状态:', error.response.status);
        console.error('响应数据:', error.response.data);
      }
      throw error;
    }
  }

  /**
   * 通过授权码获取用户的access_token和openid
   * @param {string} code - 微信授权码
   * @returns {Promise<object>} - 包含access_token和openid的对象
   */
  async getAccessTokenByCode(code) {
    try {
      const axios = require('axios');
      const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${this.appId}&secret=${this.appSecret}&code=${code}&grant_type=authorization_code`;
      
      const response = await axios.get(url);
      const data = response.data;
      
      if (data.errcode) {
        throw new Error(`获取用户access_token失败: ${data.errmsg}`);
      }
      
      return {
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        openid: data.openid,
        scope: data.scope,
        expires_in: data.expires_in
      };
    } catch (error) {
      console.error('获取用户access_token失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息
   * @param {string|null} accessTokenOrOpenid - 用户授权的access_token或者用户的openid
   * @param {string|null} openid - 用户的openid，如果第一个参数是openid，则此参数可为空
   * @returns {Promise<object>} - 用户信息
   */
  async getUserInfo(accessTokenOrOpenid, openid = null) {
    try {
      const axios = require('axios');
      let url;
      
      // 判断是否只传入了openid，如果是，则使用全局access_token
      if (!openid) {
        // 只传入了一个参数，将其视为openid，使用全局access_token
        openid = accessTokenOrOpenid;
        const accessToken = await this.getAccessToken();
        // 使用全局access_token获取用户信息
        url = `https://api.weixin.qq.com/cgi-bin/user/info?access_token=${accessToken}&openid=${openid}&lang=zh_CN`;
        console.log('使用全局access_token获取用户信息:', url);
      } else {
        // 使用用户授权的access_token获取用户信息
        url = `https://api.weixin.qq.com/sns/userinfo?access_token=${accessTokenOrOpenid}&openid=${openid}&lang=zh_CN`;
        console.log('使用用户授权access_token获取用户信息:', url);
      }
      
      const response = await axios.get(url);
      const data = response.data;
      
      if (data.errcode) {
        throw new Error(`获取用户信息失败: ${data.errmsg}`);
      }
      
      return data;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }
}

module.exports = WechatAuthService;

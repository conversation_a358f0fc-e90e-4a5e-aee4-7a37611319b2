const fs = require('fs');
const path = require('path');
const iconv = require('iconv-lite');

/**
 * IP地址归属地查询服务
 * 基于纯真IP数据库(qqwry.dat)实现IP地址归属地查询
 */
class IpLocationService {
  constructor() {
    this.ipData = null;
    this.ipBegin = 0;
    this.ipEnd = 0;
    this.totalIp = 0;
    this.dataPath = path.join(process.cwd(), 'config', 'qqwry.dat');
    this.debug = process.env.NODE_ENV !== 'production';
    
    // 加载IP数据库
    this.loadData();
  }

  /**
   * 加载IP数据库
   */
  loadData() {
    try {
      // 读取IP数据库文件
      this.ipData = fs.readFileSync(this.dataPath);
      
      // 获取索引区
      this.ipBegin = this.readUIntLE(this.ipData, 0, 4);
      this.ipEnd = this.readUIntLE(this.ipData, 4, 4);
      this.totalIp = (this.ipEnd - this.ipBegin) / 7 + 1;
      
      if (this.debug) {
        console.log(`IP数据库加载成功，共有${this.totalIp}条IP记录`);
      }
    } catch (error) {
      console.error('IP数据库加载失败:', error);
      throw new Error(`IP数据库加载失败: ${error.message}`);
    }
  }

  /**
   * 查询IP地址归属地
   * @param {string} ip - IP地址
   * @returns {Object} - IP地址归属地信息
   */
  query(ip) {
    try {
      if (!this.ipData) {
        throw new Error('IP数据库未加载');
      }
      
      // 特殊 IP 地址处理
      if (ip === '127.0.0.1' || ip === 'localhost') {
        return {
          country: '本机地址',
          area: '本地环回'
        };
      }
      
      // 内网IP地址判断
      if (this.isPrivateIP(ip)) {
        return {
          country: '内网IP',
          area: '内部网络'
        };
      }

      // 将IP转换为整数
      const ipInt = this.ip2int(ip);
      if (ipInt === null) {
        throw new Error('无效的IP地址');
      }
      
      // 如果是线上环境，添加调试日志
      if (this.debug) {
        console.log(`查询IP: ${ip}, 整数值: ${ipInt}`);
      }

      // 二分查找
      let low = 0;
      let high = this.totalIp - 1;
      let mid = 0;
      let pos = 0;
      let startIp = 0;
      let endIp = 0;

      while (low <= high) {
        mid = Math.floor((low + high) / 2);
        pos = this.ipBegin + mid * 7;
        startIp = this.readUIntLE(this.ipData, pos, 4);

        if (ipInt < startIp) {
          high = mid - 1;
        } else {
          const redirectOffset = this.readUIntLE(this.ipData, pos + 4, 3) & 0xFFFFFF;
          if (redirectOffset < 0 || redirectOffset >= this.ipData.length) {
            // 无效的重定向偏移量
            low = mid + 1;
            continue;
          }
          endIp = this.readUIntLE(this.ipData, redirectOffset, 4);
          if (ipInt > endIp) {
            low = mid + 1;
          } else {
            // 找到IP范围
            return this.readLocation(pos);
          }
        }
      }

      // 未找到匹配的IP范围
      return {
        country: '未知',
        area: ''
      };
    } catch (error) {
      if (this.debug) {
        console.error('查询IP地址归属地失败:', error);
      }
      return {
        country: '查询失败',
        area: error.message || '未知错误'
      };
    }
  }

  /**
   * 读取地址信息
   * @param {number} offset - 偏移量
   * @returns {Object} - 地址信息
   */
  readLocation(offset) {
    try {
      const recordOffset = this.readUIntLE(this.ipData, offset, 3) & 0xFFFFFF;
      
      // 安全检查
      if (recordOffset < 0 || recordOffset >= this.ipData.length) {
        return {
          country: '未知国家',
          area: '未知地区'
        };
      }
      
      // 获取地址信息
      const countryOffset = this.readUIntLE(this.ipData, recordOffset + 4, 3) & 0xFFFFFF;
      
      // 安全检查
      if (countryOffset < 0 || countryOffset >= this.ipData.length) {
        return {
          country: '未知国家',
          area: '未知地区'
        };
      }
      
      const mode = this.ipData[countryOffset];
      let country, area;

      try {
        // 模式1：国家和地区信息都记录在记录区
        if (mode === 1) {
          const countryOffset2 = this.readUIntLE(this.ipData, countryOffset + 1, 3) & 0xFFFFFF;
          country = this.readString(countryOffset2);
          area = this.readArea(countryOffset + 4);
        } 
        // 模式2：国家信息有偏移量，地区信息紧跟其后
        else if (mode === 2) {
          country = this.readString(countryOffset + 1);
          area = this.readArea(countryOffset + 1 + country.length + 1);
        } 
        // 模式0：国家和地区信息都有偏移量
        else {
          country = this.readString(countryOffset);
          area = this.readArea(countryOffset + country.length + 1);
        }
      } catch (error) {
        if (this.debug) {
          console.error('解析地址信息失败:', error);
        }
        return {
          country: '解析失败',
          area: '未知地区'
        };
      }
      
      // 处理空值或异常值
      country = country || '未知国家';
      area = area || '未知地区';

      return {
        country: country || '未知',
        area: area || ''
      };
    } catch (error) {
      if (this.debug) {
        console.error('读取地址信息失败:', error);
      }
      return {
        country: '读取失败',
        area: '未知地区'
      };
    }
  }

  /**
   * 读取地区信息
   * @param {number} offset - 偏移量
   * @returns {string} - 地区信息
   */
  readArea(offset) {
    try {
      // 安全检查
      if (offset < 0 || offset >= this.ipData.length) {
        return '';
      }
      
      const mode = this.ipData[offset];
      
      // 模式1或模式2，指向偏移量
      if (mode === 1 || mode === 2) {
        const areaOffset = this.readUIntLE(this.ipData, offset + 1, 3) & 0xFFFFFF;
        if (areaOffset < 0 || areaOffset >= this.ipData.length) {
          return '';
        }
        return this.readString(areaOffset);
      } else {
        return this.readString(offset);
      }
    } catch (error) {
      if (this.debug) {
        console.error('读取地区信息失败:', error, 'offset:', offset);
      }
      return '';
    }
  }

  /**
   * 读取字符串
   * @param {number} offset - 偏移量
   * @returns {string} - 字符串
   */
  readString(offset) {
    try {
      // 安全检查
      if (offset < 0 || offset >= this.ipData.length) {
        return '';
      }
      
      // 读取字符串直到\0结束符
      let str = [];
      let i = 0;
      
      while (offset + i < this.ipData.length && this.ipData[offset + i] !== 0) {
        str.push(this.ipData[offset + i]);
        i++;
        
        // 防止无限循环
        if (i > 255) break;
      }
      
      if (str.length === 0) {
        return '';
      }
      
      // 尝试使用iconv-lite解码GBK
      try {
        const buffer = Buffer.from(str);
        // 先尝试使用UTF-8解码，如果失败再尝试GBK
        try {
          const utf8Result = buffer.toString('utf8').trim();
          // 检查是否包含乱码字符
          if (!/\uFFFD/.test(utf8Result) && /^[\u0000-\uFFFF]+$/.test(utf8Result)) {
            return utf8Result;
          }
        } catch (e) {
          // UTF-8解码失败，忽略
        }
        
        // 尝试GBK解码
        return iconv.decode(buffer, 'gbk').trim();
      } catch (e) {
        // 如果所有解码都失败，返回原始字节的十六进制表示
        return Buffer.from(str).toString('hex').replace(/(..)/g, '$1 ').trim();
      }
    } catch (error) {
      if (this.debug) {
        console.error('读取字符串失败:', error, 'offset:', offset);
      }
      return '';
    }
  }

  /**
   * 读取无符号整数
   * @param {Buffer} buffer - 缓冲区
   * @param {number} offset - 偏移量
   * @param {number} length - 长度
   * @returns {number} - 无符号整数
   */
  readUIntLE(buffer, offset, length) {
    try {
      // 安全检查
      if (offset < 0 || offset + length > buffer.length) {
        if (this.debug) {
          console.error('读取无符号整数越界:', offset, length, buffer.length);
        }
        return 0;
      }
      
      let result = 0;
      for (let i = 0; i < length; i++) {
        result |= (buffer[offset + i] << (i * 8));
      }
      return result;
    } catch (error) {
      if (this.debug) {
        console.error('读取无符号整数失败:', error, 'offset:', offset, 'length:', length);
      }
      return 0;
    }
  }

  /**
   * IP地址转整数
   * @param {string} ip - IP地址
   * @returns {number|null} - 整数
   */
  ip2int(ip) {
    const parts = ip.split('.');
    if (parts.length !== 4) {
      return null;
    }

    return ((parseInt(parts[0], 10) << 24) |
            (parseInt(parts[1], 10) << 16) |
            (parseInt(parts[2], 10) << 8) |
            parseInt(parts[3], 10)) >>> 0;
  }
  
  /**
   * 判断是否为内网IP地址
   * @param {string} ip - IP地址
   * @returns {boolean} - 是否为内网IP
   */
  isPrivateIP(ip) {
    const parts = ip.split('.');
    if (parts.length !== 4) {
      return false;
    }
    
    // 10.0.0.0 - **************
    if (parts[0] === '10') {
      return true;
    }
    
    // ********** - **************
    if (parts[0] === '172' && (parseInt(parts[1], 10) >= 16 && parseInt(parts[1], 10) <= 31)) {
      return true;
    }
    
    // *********** - ***************
    if (parts[0] === '192' && parts[1] === '168') {
      return true;
    }
    
    // *********** - *************** (自动私有IP地址)
    if (parts[0] === '169' && parts[1] === '254') {
      return true;
    }
    
    return false;
  }

  /**
   * 获取客户端真实IP地址
   * @param {Object} req - 请求对象
   * @returns {string} - IP地址
   */
  getClientIp(req) {
    // 尝试从各种请求头中获取真实IP
    const forwardedFor = req.headers['x-forwarded-for'];
    if (forwardedFor) {
      // 取第一个IP（最原始的客户端IP）
      const ips = forwardedFor.split(',');
      return ips[0].trim();
    }
    
    // 尝试其他常见的IP请求头
    const realIp = req.headers['x-real-ip'] || 
                   req.headers['x-client-ip'] || 
                   req.headers['cf-connecting-ip'] || 
                   req.headers['true-client-ip'];
    
    if (realIp) {
      return realIp;
    }
    
    // 最后使用连接IP
    return req.connection.remoteAddress?.replace('::ffff:', '') || '127.0.0.1';
  }
}

module.exports = IpLocationService;
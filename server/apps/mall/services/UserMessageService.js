/**
 * 商城用户消息服务
 * 处理用户消息相关的业务逻辑
 */
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class UserMessageService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取用户消息列表
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Object} 消息列表和分页信息
   */
  async getMessageList(userId, options = {}) {
    const {
      page = 1,
      pageSize = 10,
      messageType = null,
      isRead = null,
      priority = null
    } = options;

    // 构建查询条件
    const where = {
      user_id: BigInt(userId),
      deleted_at: null
    };

    if (messageType) {
      where.message_type = messageType;
    }

    if (isRead !== null) {
      where.is_read = isRead;
    }

    if (priority !== null) {
      where.priority = priority;
    }

    // 计算偏移量
    const offset = (page - 1) * pageSize;

    try {
      // 构建查询条件字符串
      let whereConditions = `"user_id" = ${BigInt(userId)} AND "deleted_at" IS NULL`;

      if (messageType) {
        whereConditions += ` AND "message_type" = '${messageType}'`;
      }

      if (isRead !== null) {
        whereConditions += ` AND "is_read" = ${isRead}`;
      }

      if (priority !== null) {
        whereConditions += ` AND "priority" = ${priority}`;
      }

      // 获取总数
      const total = await this.prisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count
        FROM "base"."mall_user_message"
        WHERE ${whereConditions}
      `);

      // 获取消息列表
      const messages = await this.prisma.$queryRawUnsafe(`
        SELECT
          "id", "user_id", "title", "content", "message_type",
          "priority", "is_read", "action_url", "action_text",
          "icon", "created_at", "updated_at", "remark"
        FROM "base"."mall_user_message"
        WHERE ${whereConditions}
        ORDER BY "created_at" DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `);

      // 转换数据格式
      const formattedMessages = messages.map(msg => ({
        id: msg.id.toString(),
        userId: msg.user_id.toString(),
        title: msg.title,
        content: msg.content,
        messageType: msg.message_type,
        priority: msg.priority,
        isRead: msg.is_read,
        actionUrl: msg.action_url,
        actionText: msg.action_text,
        icon: msg.icon,
        createdAt: Number(msg.created_at),
        updatedAt: Number(msg.updated_at),
        remark: msg.remark
      }));

      return {
        messages: formattedMessages,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: Number(total[0].count),
          totalPages: Math.ceil(Number(total[0].count) / pageSize)
        }
      };
    } catch (error) {
      console.error('获取消息列表失败:', error);
      throw new Error('获取消息列表失败');
    }
  }

  /**
   * 获取未读消息数量
   * @param {string} userId - 用户ID
   * @returns {number} 未读消息数量
   */
  async getUnreadCount(userId) {
    try {
      const result = await this.prisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count
        FROM "base"."mall_user_message"
        WHERE "user_id" = ${BigInt(userId)}
        AND "is_read" = false
        AND "deleted_at" IS NULL
      `);

      return Number(result[0].count);
    } catch (error) {
      console.error('获取未读消息数量失败:', error);
      throw new Error('获取未读消息数量失败');
    }
  }

  /**
   * 标记消息为已读
   * @param {string} userId - 用户ID
   * @param {string} messageId - 消息ID
   * @returns {boolean} 操作结果
   */
  async markAsRead(userId, messageId) {
    try {
      await this.prisma.$queryRawUnsafe(`
        UPDATE "base"."mall_user_message"
        SET "is_read" = true, "updated_at" = ${BigInt(Date.now())}
        WHERE "id" = ${BigInt(messageId)}
        AND "user_id" = ${BigInt(userId)}
        AND "deleted_at" IS NULL
      `);

      return true;
    } catch (error) {
      console.error('标记消息已读失败:', error);
      throw new Error('标记消息已读失败');
    }
  }

  /**
   * 批量标记消息为已读
   * @param {string} userId - 用户ID
   * @param {Array} messageIds - 消息ID数组
   * @returns {boolean} 操作结果
   */
  async markMultipleAsRead(userId, messageIds) {
    try {
      const bigIntIds = messageIds.map(id => BigInt(id));
      const idsString = bigIntIds.join(',');

      await this.prisma.$queryRawUnsafe(`
        UPDATE "base"."mall_user_message"
        SET "is_read" = true, "updated_at" = ${BigInt(Date.now())}
        WHERE "user_id" = ${BigInt(userId)}
        AND "id" IN (${idsString})
        AND "deleted_at" IS NULL
      `);

      return true;
    } catch (error) {
      console.error('批量标记消息已读失败:', error);
      throw new Error('批量标记消息已读失败');
    }
  }

  /**
   * 删除消息
   * @param {string} userId - 用户ID
   * @param {string} messageId - 消息ID
   * @returns {boolean} 操作结果
   */
  async deleteMessage(userId, messageId) {
    try {
      await this.prisma.$queryRawUnsafe(`
        UPDATE "base"."mall_user_message"
        SET "deleted_at" = ${BigInt(Date.now())}, "updated_at" = ${BigInt(Date.now())}
        WHERE "id" = ${BigInt(messageId)}
        AND "user_id" = ${BigInt(userId)}
        AND "deleted_at" IS NULL
      `);

      return true;
    } catch (error) {
      console.error('删除消息失败:', error);
      throw new Error('删除消息失败');
    }
  }

  /**
   * 清空所有消息
   * @param {string} userId - 用户ID
   * @returns {boolean} 操作结果
   */
  async clearAllMessages(userId) {
    try {
      await this.prisma.$queryRawUnsafe(`
        UPDATE "base"."mall_user_message"
        SET "deleted_at" = ${BigInt(Date.now())}, "updated_at" = ${BigInt(Date.now())}
        WHERE "user_id" = ${BigInt(userId)}
        AND "deleted_at" IS NULL
      `);

      return true;
    } catch (error) {
      console.error('清空消息失败:', error);
      throw new Error('清空消息失败');
    }
  }

  /**
   * 创建新消息
   * @param {Object} messageData - 消息数据
   * @returns {Object} 创建的消息
   */
  async createMessage(messageData) {
    const {
      userId,
      title,
      content,
      messageType = 'system',
      priority = 1,
      actionUrl = null,
      actionText = null,
      icon = null,
      createdBy = null
    } = messageData;

    try {
      const messageId = generateSnowflakeId();
      const now = BigInt(Date.now());

      await this.prisma.$queryRawUnsafe(`
        INSERT INTO "base"."mall_user_message" (
          "id", "user_id", "title", "content", "message_type",
          "priority", "is_read", "action_url", "action_text",
          "icon", "created_at", "updated_at", "created_by"
        ) VALUES (
          ${messageId}, ${BigInt(userId)}, '${title}', '${content}', '${messageType}',
          ${priority}, false, ${actionUrl ? `'${actionUrl}'` : 'NULL'}, ${actionText ? `'${actionText}'` : 'NULL'},
          ${icon ? `'${icon}'` : 'NULL'}, ${now}, ${now}, ${createdBy ? BigInt(createdBy) : 'NULL'}
        )
      `);

      return {
        id: messageId.toString(),
        userId: userId.toString(),
        title,
        content,
        messageType,
        priority,
        isRead: false,
        actionUrl,
        actionText,
        icon,
        createdAt: Number(now),
        updatedAt: Number(now)
      };
    } catch (error) {
      console.error('创建消息失败:', error);
      throw new Error('创建消息失败');
    }
  }
}

module.exports = UserMessageService;

/**
 * 用户收藏服务类
 */
class UserFavoriteService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 添加收藏（支持单个或多个目标）
   * @param {Object|Array} favoriteData - 收藏数据，可以是单个对象或对象数组
   * @returns {Promise<Object>} - 创建的收藏记录
   */
  async addFavorite(favoriteData) {
    try {
      // 处理单个收藏的情况
      if (!Array.isArray(favoriteData)) {
        return await this._addSingleFavorite(favoriteData);
      }
      
      // 处理批量收藏的情况
      const results = [];
      const errors = [];
      
      for (const item of favoriteData) {
        try {
          const result = await this._addSingleFavorite(item);
          results.push(result);
        } catch (err) {
          errors.push({
            targetId: item.targetId,
            targetType: item.targetType,
            error: err.message
          });
        }
      }
      
      return {
        success: results,
        failed: errors,
        total: favoriteData.length,
        successCount: results.length,
        failedCount: errors.length
      };
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * 添加单个收藏（内部方法）
   * @param {Object} favoriteData - 收藏数据
   * @returns {Promise<Object>} - 创建的收藏记录
   * @private
   */
  async _addSingleFavorite(favoriteData) {
    // 检查是否已经收藏过（包括软删除的记录）
    const existingFavorite = await this.prisma.mallUserFavorite.findFirst({
      where: {
        userId: favoriteData.userId,
        targetId: favoriteData.targetId,
        targetType: favoriteData.targetType,
      },
    });

    // 如果存在且已删除，则恢复
    if (existingFavorite && existingFavorite.deletedAt) {
      return await this.prisma.mallUserFavorite.update({
        where: { id: existingFavorite.id },
        data: { 
          deletedAt: null,
          remark: favoriteData.remark || existingFavorite.remark,
          updatedAt: new Date()
        }
      });
    }

    // 如果已存在且未删除，则返回错误
    if (existingFavorite && !existingFavorite.deletedAt) {
      throw new Error('该目标已收藏');
    }

    // 创建新收藏
    return await this.prisma.mallUserFavorite.create({
      data: {
        userId: favoriteData.userId,
        targetId: favoriteData.targetId,
        targetType: favoriteData.targetType,
        remark: favoriteData.remark
      }
    });
  }

  /**
   * 取消收藏（软删除），支持单个或多个目标
   * @param {number} userId - 用户ID
   * @param {number|Array} targetId - 目标ID或目标ID数组
   * @param {number|Array} targetType - 目标类型或目标类型数组，如果targetId是数组，则targetType也必须是相同长度的数组
   * @returns {Promise<Object>} - 更新后的收藏记录
   */
  async cancelFavorite(userId, targetId, targetType) {
    try {
      // 处理单个取消收藏的情况
      if (!Array.isArray(targetId)) {
        return await this._cancelSingleFavorite(userId, targetId, targetType);
      }
      
      // 处理批量取消收藏的情况
      if (!Array.isArray(targetType) || targetId.length !== targetType.length) {
        throw new Error('目标ID和目标类型必须同时为数组且长度相同');
      }
      
      const results = [];
      const errors = [];
      
      for (let i = 0; i < targetId.length; i++) {
        try {
          const result = await this._cancelSingleFavorite(userId, targetId[i], targetType[i]);
          results.push(result);
        } catch (err) {
          errors.push({
            targetId: targetId[i],
            targetType: targetType[i],
            error: err.message
          });
        }
      }
      
      return {
        success: results,
        failed: errors,
        total: targetId.length,
        successCount: results.length,
        failedCount: errors.length
      };
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * 取消单个收藏（内部方法）
   * @param {number} userId - 用户ID
   * @param {number} targetId - 目标ID
   * @param {number} targetType - 目标类型
   * @returns {Promise<Object>} - 更新后的收藏记录
   * @private
   */
  async _cancelSingleFavorite(userId, targetId, targetType) {
    const favorite = await this.prisma.mallUserFavorite.findFirst({
      where: {
        userId,
        targetId,
        targetType,
        deletedAt: null
      }
    });

    if (!favorite) {
      throw new Error('收藏记录不存在');
    }

    return await this.prisma.mallUserFavorite.update({
      where: { id: favorite.id },
      data: { 
        deletedAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  /**
   * 获取用户收藏列表
   * @param {number} userId - 用户ID
   * @param {number} targetType - 目标类型，可选
   * @param {Object} pagination - 分页参数
   * @returns {Promise<Object>} - 收藏列表和总数
   */
  async getUserFavorites(userId, targetType, pagination) {
    try {
      const { page = 1, pageSize = 10 } = pagination;
      const skip = (page - 1) * pageSize;

      // 构建查询条件
      const where = {
        userId,
        deletedAt: null
      };

      // 如果指定了目标类型，则添加到查询条件
      if (targetType !== undefined) {
        where.targetType = targetType;
      }

      // 查询总数
      const total = await this.prisma.mallUserFavorite.count({ where });

      let favorites;
      
      // 根据目标类型决定查询方式
      if (targetType === 1) { // 商品类型
        // 查询商品收藏列表，并关联查询商品信息
        favorites = await this.prisma.mallUserFavorite.findMany({
          where,
          skip,
          take: pageSize,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            userId: true,
            targetId: true,
            targetType: true,
            createdAt: true,
            updatedAt: true,
            remark: true
          }
        });
        
        // 获取所有商品ID
        const goodsIds = favorites.map(item => item.targetId);
        
        // 查询商品信息
        const goodsInfoList = await this.prisma.goodsSpu.findMany({
          where: {
            id: { in: goodsIds },
            deleted_at: null
          },
          select: {
            id: true,
            name: true,
            subtitle: true,
            goods_skus: {
              where: { deleted_at: null, is_enabled: 1 },
              select: {
                sales_price: true
              },
              orderBy: { sales_price: 'asc' },
              take: 1
            },
            goods_images: {
              where: { is_default: true },
              select: {
                image_url: true
              },
              take: 1
            }
          }
        });
        
        // 将商品信息合并到收藏列表中
        favorites = favorites.map(favorite => {
          const goodsInfo = goodsInfoList.find(goods => goods.id === favorite.targetId);
          return {
            ...favorite,
            goodsInfo: goodsInfo ? {
              name: goodsInfo.name,
              subtitle: goodsInfo.subtitle,
              price: goodsInfo.goods_skus.length > 0 ? goodsInfo.goods_skus[0].sales_price : null,
              image: goodsInfo.goods_images.length > 0 ? goodsInfo.goods_images[0].image_url : null
            } : null
          };
        });
      } else {
        // 其他类型的收藏，保持原有查询方式
        favorites = await this.prisma.mallUserFavorite.findMany({
          where,
          skip,
          take: pageSize,
          orderBy: { createdAt: 'desc' }
        });
      }

      return {
        list: favorites,
        pagination: {
          page,
          pageSize,
          total
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 检查是否已收藏
   * @param {number} userId - 用户ID
   * @param {number} targetId - 目标ID
   * @param {number} targetType - 目标类型
   * @returns {Promise<boolean>} - 是否已收藏
   */
  async checkFavorite(userId, targetId, targetType) {
    try {
      const favorite = await this.prisma.mallUserFavorite.findFirst({
        where: {
          userId,
          targetId,
          targetType,
          deletedAt: null
        }
      });

      return !!favorite;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserFavoriteService;

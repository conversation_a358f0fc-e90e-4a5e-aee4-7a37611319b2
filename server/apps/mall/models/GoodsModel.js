/**
 * 商城商品模型
 * 负责商品数据的访问和处理
 */
const prismaManager = require('../../../core/database/prisma');

/**
 * 将数据库字段名转换为前端字段名
 * @param {Object} item 数据库记录
 * @returns {Object} 转换后的记录
 */
function transformCategoryFields(item) {
  if (!item) return null;
  
  return {
    id: item.id,
    goodsParentCategoryId: item.parent_id,
    name: item.name,
    imageUrl: item.image_url,
    description: item.description,
    metaTitle: item.meta_title,
    metaKeywords: item.meta_keywords,
    metaDescription: item.meta_description,
    sortOrder: item.sort_order || 1,
    isEnabled: item.is_enabled,
    level: item.level || 1,
    children: []
  };
}

/**
 * 商品模型类
 */
class GoodsModel {
  constructor() {
    // 使用单例模式获取Prisma客户端
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 处理查询结果，转换BigInt为字符串
   * @param {Object} data 查询结果数据
   * @returns {Object} 处理后的数据
   */
  processResult(data) {
    if (!data) return data;
    
    if (Array.isArray(data)) {
      return data.map(item => this.processResult(item));
    }
    
    const result = {};
    for (const [key, value] of Object.entries(data)) {
      result[key] = typeof value === 'bigint' ? value.toString() : value;
    }
    return result;
  }
  
  /**
   * 获取商品分类树形结构
   * @returns {Promise<Array>} 分类树形结构
   */
  async getCategoryTree() {
    try {
      // 获取所有未删除的分类
      const allCategories = await this.prisma.goods_categories.findMany({
        where: {
          deleted_at: null
        },
        orderBy: [
          { level: 'asc' },
          { sort_order: 'asc' },
          { created_at: 'desc' }
        ]
      });
      
      // 转换字段名并处理BigInt
      const processedCategories = allCategories.map(item => {
        return transformCategoryFields(this.processResult(item));
      });
      
      // 构建树形结构
      const categoryMap = {};
      const rootCategories = [];
      
      // 先将所有分类放入Map中，方便查找
      processedCategories.forEach(category => {
        categoryMap[category.id] = category;
      });
      
      // 构建树形结构
      processedCategories.forEach(category => {
        // 如果有父分类，则添加到父分类的children中
        if (category.goodsParentCategoryId) {
          const parent = categoryMap[category.goodsParentCategoryId];
          if (parent) {
            parent.children.push(category);
          } else {
            // 如果找不到父分类，则作为根分类
            rootCategories.push(category);
          }
        } else {
          // 没有父分类，则为根分类
          rootCategories.push(category);
        }
      });
      
      return rootCategories;
    } catch (error) {
      console.error('获取商品分类树形结构失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
module.exports = new GoodsModel();

/**
 * 商城用户消息路由
 * 定义用户消息相关的API路由
 */
const express = require('express');
const router = express.Router();
const UserMessageController = require('../controllers/UserMessageController');
const RouterConfig = require('../../../core/routes/RouterConfig');

/**
 * 创建用户消息路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} Express路由实例
 */
function createUserMessageRoutes(prisma) {
  // 创建控制器实例
  const messageController = new UserMessageController(prisma);

  // 创建受JWT保护的路由
  const protectedRouter = RouterConfig.authRoute(router);

  /**
   * @swagger
   * tags:
   *   name: 用户消息
   *   description: 商城用户消息管理接口
   */

  /**
   * @swagger
   * /api/mall/user/messages:
   *   get:
   *     summary: 获取用户消息列表
   *     tags: [用户消息]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: messageType
   *         schema:
   *           type: string
   *           enum: [system, order, promotion, service]
   *         description: 消息类型
   *       - in: query
   *         name: isRead
   *         schema:
   *           type: boolean
   *         description: 是否已读
   *       - in: query
   *         name: priority
   *         schema:
   *           type: integer
   *           enum: [1, 2, 3]
   *         description: 优先级
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取消息列表成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     messages:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/UserMessage'
   *                     pagination:
   *                       $ref: '#/components/schemas/Pagination'
   */
  protectedRouter.get('/messages', (req, res) => messageController.getMessageList(req, res));

  /**
   * @swagger
   * /api/mall/user/messages/unread-count:
   *   get:
   *     summary: 获取未读消息数量
   *     tags: [用户消息]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取未读消息数量成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     count:
   *                       type: integer
   *                       example: 5
   */
  protectedRouter.get('/messages/unread-count', (req, res) => messageController.getUnreadCount(req, res));

  /**
   * @swagger
   * /api/mall/user/messages/{messageId}/read:
   *   put:
   *     summary: 标记消息为已读
   *     tags: [用户消息]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: messageId
   *         required: true
   *         schema:
   *           type: string
   *         description: 消息ID
   *     responses:
   *       200:
   *         description: 标记成功
   */
  protectedRouter.put('/messages/:messageId/read', (req, res) => messageController.markAsRead(req, res));

  /**
   * @swagger
   * /api/mall/user/messages/batch-read:
   *   put:
   *     summary: 批量标记消息为已读
   *     tags: [用户消息]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               messageIds:
   *                 type: array
   *                 items:
   *                   type: string
   *                 example: ["1001", "1002", "1003"]
   *     responses:
   *       200:
   *         description: 批量标记成功
   */
  protectedRouter.put('/messages/batch-read', (req, res) => messageController.batchMarkAsRead(req, res));

  /**
   * @swagger
   * /api/mall/user/messages/clear-all:
   *   delete:
   *     summary: 清空所有消息
   *     tags: [用户消息]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 清空成功
   */
  protectedRouter.delete('/messages/clear-all', (req, res) => messageController.clearAllMessages(req, res));

  /**
   * @swagger
   * /api/mall/user/messages/{messageId}:
   *   delete:
   *     summary: 删除消息
   *     tags: [用户消息]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: messageId
   *         required: true
   *         schema:
   *           type: string
   *         description: 消息ID
   *     responses:
   *       200:
   *         description: 删除成功
   */
  protectedRouter.delete('/messages/:messageId', (req, res) => messageController.deleteMessage(req, res));

  /**
   * @swagger
   * /api/mall/user/messages:
   *   post:
   *     summary: 创建消息（管理员功能）
   *     tags: [用户消息]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - userId
   *               - title
   *             properties:
   *               userId:
   *                 type: string
   *                 description: 用户ID
   *               title:
   *                 type: string
   *                 description: 消息标题
   *               content:
   *                 type: string
   *                 description: 消息内容
   *               messageType:
   *                 type: string
   *                 enum: [system, order, promotion, service]
   *                 default: system
   *                 description: 消息类型
   *               priority:
   *                 type: integer
   *                 enum: [1, 2, 3]
   *                 default: 1
   *                 description: 优先级
   *               actionUrl:
   *                 type: string
   *                 description: 操作链接
   *               actionText:
   *                 type: string
   *                 description: 操作按钮文本
   *               icon:
   *                 type: string
   *                 description: 消息图标
   *     responses:
   *       200:
   *         description: 创建成功
   */
  protectedRouter.post('/messages', (req, res) => messageController.createMessage(req, res));

  return router;
}

module.exports = createUserMessageRoutes;

const express = require('express');
const router = express.Router();
const multer = require('multer');
const upload = multer({ storage: multer.memoryStorage() });

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入拆分后的控制器
const userAuthController = require('../controllers/UserAuthController');
const UserProfileController = require('../controllers/UserProfileController');
const userVerificationController = require('../controllers/UserVerificationController');
const UserTestController = require('../controllers/UserTestController');

/**
 * 商城前端用户路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const userProfileController = new UserProfileController(prisma);
  const userTestController = new UserTestController(prisma);
  /**
   * @swagger
   * /api/mall/user/captcha:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取验证码
   *     description: 获取短信验证码
   *     parameters:
   *       - in: query
   *         name: phone
   *         required: true
   *         schema:
   *           type: string
   *         description: 手机号
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   */
  router.get('/captcha', userVerificationController.getCaptcha);

  /**
   * @swagger
   * /api/mall/user/register:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 用户注册
   *     description: 商城前端用户注册接口
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - username
   *               - password
   *               - phone
   *               - captcha
   *             properties:
   *               username:
   *                 type: string
   *                 description: 用户名
   *               password:
   *                 type: string
   *                 description: 密码
   *               nickname:
   *                 type: string
   *                 description: 昵称
   *               phone:
   *                 type: string
   *                 description: 手机号
   *               email:
   *                 type: string
   *                 description: 邮箱
   *               captcha:
   *                 type: string
   *                 description: 验证码
   *     responses:
   *       200:
   *         description: 注册成功
   *       400:
   *         description: 请求参数错误
   */
  router.post('/register', userAuthController.register);

  /**
   * @swagger
   * /api/mall/user/login:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 用户登录
   *     description: 商城前端用户登录接口
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - username
   *               - password
   *             properties:
   *               username:
   *                 type: string
   *                 description: 用户名
   *               password:
   *                 type: string
   *                 description: 密码
   *     responses:
   *       200:
   *         description: 登录成功
   *       400:
   *         description: 用户名或密码错误
   *       403:
   *         description: 账号已被禁用
   */
  router.post('/login', userAuthController.login);

  /**
   * @swagger
   * /api/mall/user/sms-login:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 短信验证码登录
   *     description: 商城前端用户短信验证码登录接口
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - phone
   *               - smsCode
   *             properties:
   *               phone:
   *                 type: string
   *                 description: 手机号
   *                 example: "18593415401"
   *               smsCode:
   *                 type: string
   *                 description: 短信验证码
   *                 example: "123456"
   *     responses:
   *       200:
   *         description: 登录成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 手机号未注册或验证码错误
   *       403:
   *         description: 账号已被禁用
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/sms-login', userAuthController.smsLogin);

  /**
   * @swagger
   * /api/mall/user/logout:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 用户退出登录
   *     description: 商城前端用户退出登录接口
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 退出登录成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   */
  router.post('/logout', userAuthController.logout);

  /**
   * @swagger
   * /api/mall/user/change-password:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 修改密码
   *     description: 商城前端用户修改密码接口
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - oldPassword
   *               - newPassword
   *             properties:
   *               oldPassword:
   *                 type: string
   *                 description: 原密码
   *               newPassword:
   *                 type: string
   *                 description: 新密码
   *     responses:
   *       200:
   *         description: 密码修改成功
   *       400:
   *         description: 请求参数错误或原密码错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/change-password', authMiddleware, userAuthController.changePassword.bind(userAuthController));

  /**
   * @swagger
   * /api/mall/user/update-avatar:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 修改头像
   *     description: 商城前端用户修改头像接口（通过URL）
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - avatarUrl
   *             properties:
   *               avatarUrl:
   *                 type: string
   *                 description: 头像URL
   *     responses:
   *       200:
   *         description: 头像修改成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/update-avatar', authMiddleware, userProfileController.updateAvatar.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/upload-avatar:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 上传头像
   *     description: 商城前端用户上传头像接口（通过文件上传）
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         multipart/form-data:
   *           schema:
   *             type: object
   *             required:
   *               - file
   *             properties:
   *               file:
   *                 type: string
   *                 format: binary
   *                 description: 头像文件（支持JPG、PNG、GIF、WEBP格式，大小不超过2MB）
   *     responses:
   *       200:
   *         description: 头像上传成功
   *       400:
   *         description: 请求参数错误或文件格式/大小不符合要求
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/upload-avatar', upload.single('file'), userProfileController.uploadAvatar.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/update-nickname:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 修改昵称
   *     description: 商城前端用户修改昵称接口
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - nickname
   *             properties:
   *               nickname:
   *                 type: string
   *                 description: 新昵称
   *     responses:
   *       200:
   *         description: 昵称修改成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/update-nickname', authMiddleware, userProfileController.updateNickname.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/phone-captcha:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取修改手机号验证码
   *     description: 获取修改手机号的短信验证码
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: newPhone
   *         required: true
   *         schema:
   *           type: string
   *         description: 新手机号
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/phone-captcha', authMiddleware, userProfileController.sendPhoneChangeCaptcha.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/update-phone:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 修改手机号
   *     description: 商城前端用户修改手机号接口
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - newPhone
   *               - captcha
   *             properties:
   *               newPhone:
   *                 type: string
   *                 description: 新手机号
   *               captcha:
   *                 type: string
   *                 description: 验证码
   *     responses:
   *       200:
   *         description: 手机号修改成功
   *       400:
   *         description: 请求参数错误或验证码错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/update-phone', authMiddleware, userProfileController.updatePhone.bind(userProfileController));
  
  /**
   * @swagger
   * /api/mall/user/email-captcha:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 获取邮箱验证码
   *     description: 获取邮箱绑定的验证码
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - email
   *             properties:
   *               email:
   *                 type: string
   *                 description: 邮箱地址
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/email-captcha', authMiddleware, userProfileController.sendEmailCaptcha.bind(userProfileController));
  
  /**
   * @swagger
   * /api/mall/user/bind-email:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 绑定邮箱
   *     description: 商城前端用户绑定邮箱接口
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - email
   *               - captcha
   *             properties:
   *               email:
   *                 type: string
   *                 description: 邮箱地址
   *               captcha:
   *                 type: string
   *                 description: 验证码
   *     responses:
   *       200:
   *         description: 邮箱绑定成功
   *       400:
   *         description: 请求参数错误或验证码错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/bind-email', authMiddleware, userProfileController.bindEmail.bind(userProfileController));
  
  /**
   * @swagger
   * /api/mall/user/reset-password-captcha:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取重置密码验证码
   *     description: 获取重置密码的短信验证码
   *     parameters:
   *       - in: query
   *         name: username
   *         required: true
   *         schema:
   *           type: string
   *         description: 用户名或手机号
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误或用户不存在
   *       403:
   *         description: 账号已被禁用
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/reset-password-captcha', userVerificationController.getForgetPasswordCaptcha);
  
  /**
   * @swagger
   * /api/mall/user/reset-password:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 重置密码
   *     description: 忘记密码后重置密码的接口
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - username
   *               - newPassword
   *               - captcha
   *             properties:
   *               username:
   *                 type: string
   *                 description: 用户名或手机号
   *               newPassword:
   *                 type: string
   *                 description: 新密码
   *               captcha:
   *                 type: string
   *                 description: 验证码
   *     responses:
   *       200:
   *         description: 密码重置成功
   *       400:
   *         description: 请求参数错误或验证码错误
   *       403:
   *         description: 账号已被禁用
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/reset-password', userAuthController.resetPassword);

  /**
   * @swagger
   * /api/mall/user/realname-auth:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 保存实名认证信息
   *     description: 保存用户实名认证信息（身份证信息）
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - realName
   *               - idCardNumber
   *               - idCardFront
   *               - idCardBack
   *             properties:
   *               realName:
   *                 type: string
   *                 description: 真实姓名
   *               idCardNumber:
   *                 type: string
   *                 description: 身份证号码
   *               idCardFront:
   *                 type: string
   *                 description: 身份证正面照片URL
   *               idCardBack:
   *                 type: string
   *                 description: 身份证反面照片URL
   *     responses:
   *       200:
   *         description: 保存成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/realname-auth', authMiddleware, userProfileController.saveRealnameAuth.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/realname-auth:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取实名认证信息
   *     description: 获取用户实名认证信息
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取成功
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/realname-auth', authMiddleware, userProfileController.getRealnameAuth.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/security-questions:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 保存安全问题
   *     description: 保存用户的三个安全问题及答案
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - questions
   *             properties:
   *               questions:
   *                 type: array
   *                 description: 安全问题及答案数组（必须包含3个问题）
   *                 items:
   *                   type: object
   *                   required:
   *                     - question_text
   *                     - answer
   *                   properties:
   *                     question_text:
   *                       type: string
   *                       description: 安全问题文本
   *                     answer:
   *                       type: string
   *                       description: 安全问题答案
   *     responses:
   *       200:
   *         description: 安全问题设置成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/security-questions', authMiddleware, userProfileController.saveSecurityQuestions.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/security-questions:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取安全问题列表
   *     description: 获取用户设置的安全问题列表（不包含答案）
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取成功
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/security-questions', authMiddleware, userProfileController.getSecurityQuestions.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/security-questions/{username}:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 通过用户名获取安全问题列表
   *     description: 通过用户名获取用户设置的安全问题列表（不包含答案），用于找回密码流程
   *     parameters:
   *       - in: path
   *         name: username
   *         required: true
   *         schema:
   *           type: string
   *         description: 用户名
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   *       404:
   *         description: 用户不存在或未设置安全问题
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/security-questions/:username', userProfileController.getSecurityQuestionsByUsername.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/profile:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取用户个人资料
   *     description: 获取当前登录用户的个人资料信息，包括微信绑定状态
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取成功
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/profile', authMiddleware, userProfileController.getProfile.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/balance:
   *   get:
   *     tags: [商城用户管理]
   *     summary: 获取用户余额
   *     description: 获取当前登录用户的账户余额信息
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取用户余额成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     balance:
   *                       type: number
   *                       example: 1000.00
   *                       description: 用户账户余额
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/balance', authMiddleware, userProfileController.getBalance.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/verify-security-questions:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 校验用户安全问题答案
   *     description: 校验用户安全问题答案，用于找回密码流程
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - username
   *               - answers
   *             properties:
   *               username:
   *                 type: string
   *                 description: 用户名
   *               answers:
   *                 type: array
   *                 description: 安全问题答案数组
   *                 items:
   *                   type: object
   *                   required:
   *                     - question_id
   *                     - answer
   *                   properties:
   *                     question_id:
   *                       type: string
   *                       description: 安全问题ID
   *                     answer:
   *                       type: string
   *                       description: 安全问题答案
   *     responses:
   *       200:
   *         description: 验证成功，返回临时令牌
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 token:
   *                   type: string
   *                   description: 用于重置密码的临时令牌
   *                 expire_time:
   *                   type: integer
   *                   description: 令牌过期时间戳
   *       400:
   *         description: 请求参数错误或答案不正确
   *       404:
   *         description: 用户不存在
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/verify-security-questions', userProfileController.verifySecurityQuestions.bind(userProfileController));

  /**
   * @swagger
   * /api/mall/user/reset-password-by-token:
   *   post:
   *     tags: [商城用户管理]
   *     summary: 通过令牌重置密码
   *     description: 使用验证安全问题后获得的令牌重置用户密码
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - token
   *               - newPassword
   *               - resetType
   *             properties:
   *               token:
   *                 type: string
   *                 description: 通过验证安全问题获得的临时令牌
   *               newPassword:
   *                 type: string
   *                 description: 新密码
   *               resetType:
   *                 type: string
   *                 description: 重置类型，目前支持 'username'
   *     responses:
   *       200:
   *         description: 密码重置成功
   *       400:
   *         description: 请求参数错误或令牌无效
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/reset-password-by-token', userProfileController.resetPasswordByToken.bind(userProfileController));

  // 添加测试路由（仅在开发环境中可用）
  if (process.env.NODE_ENV !== 'production') {
    router.get('/test-phone-query', userTestController.testPhoneQuery.bind(userTestController));
    router.post('/test-login', userTestController.testLogin.bind(userTestController));
    router.get('/test-password', userTestController.testPassword.bind(userTestController));
    router.get('/test-redis', userTestController.testRedis.bind(userTestController));
    router.post('/test-create-order', userTestController.createTestOrder.bind(userTestController));
    router.get('/test-order-detail', userTestController.getOrderDetail.bind(userTestController));
  }

  return router;
};

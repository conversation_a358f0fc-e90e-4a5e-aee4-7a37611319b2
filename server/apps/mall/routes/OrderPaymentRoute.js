/**
 * 商城订单支付路由
 */

const express = require('express');
const router = express.Router();
const OrderPaymentController = require('../controllers/OrderPaymentController');
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

/**
 * 商城订单支付路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const orderPaymentController = new OrderPaymentController(prisma);

/**
 * @swagger
 * /api/v1/mall/order-payment/create:
 *   post:
 *     summary: 创建支付订单
 *     tags: [Mall-OrderPayment]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - orderId
 *               - paymentMethodId
 *             properties:
 *               orderId:
 *                 type: string
 *                 description: 订单ID
 *                 example: "193257182236643328"
 *               paymentMethodId:
 *                 type: integer
 *                 description: 支付方式ID (1:微信 2:支付宝 3:银行卡 4:余额 5:其他)
 *                 example: 1
 *     responses:
 *       200:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "创建支付成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     paymentRecord:
 *                       type: object
 *                       description: 支付记录
 *                     paymentResult:
 *                       type: object
 *                       description: 第三方支付结果
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 用户未登录
 *       500:
 *         description: 服务器内部错误
 */
  router.post('/create', authMiddleware, orderPaymentController.createPayment.bind(orderPaymentController));

/**
 * @swagger
 * /api/v1/mall/order-payment/status/{paymentSn}:
 *   get:
 *     summary: 查询支付状态
 *     tags: [Mall-OrderPayment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: paymentSn
 *         required: true
 *         schema:
 *           type: string
 *         description: 支付流水号
 *         example: "PAY1750147298192ABC123"
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "查询支付状态成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     paymentStatus:
 *                       type: integer
 *                       description: 支付状态 (0:待支付 1:支付中 2:已支付 3:已取消 4:已过期)
 *                     paymentSn:
 *                       type: string
 *                       description: 支付流水号
 *                     isExpired:
 *                       type: boolean
 *                       description: 是否已过期
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: 用户未登录
 *       404:
 *         description: 支付记录不存在
 *       500:
 *         description: 服务器内部错误
 */
  router.get('/status/:paymentSn', authMiddleware, orderPaymentController.queryPaymentStatus.bind(orderPaymentController));

/**
 * @swagger
 * /api/v1/mall/order-payment/cancel/{paymentSn}:
 *   post:
 *     summary: 取消支付
 *     tags: [Mall-OrderPayment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: paymentSn
 *         required: true
 *         schema:
 *           type: string
 *         description: 支付流水号
 *         example: "PAY1750147298192ABC123"
 *     responses:
 *       200:
 *         description: 取消成功
 *       400:
 *         description: 取消失败
 *       401:
 *         description: 用户未登录
 *       500:
 *         description: 服务器内部错误
 */
  router.post('/cancel/:paymentSn', authMiddleware, orderPaymentController.cancelPayment.bind(orderPaymentController));

/**
 * @swagger
 * /api/v1/mall/order-payment/records/{orderId}:
 *   get:
 *     summary: 获取订单支付记录
 *     tags: [Mall-OrderPayment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *         example: "193257182236643328"
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取支付记录成功"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: 支付记录ID
 *                       paymentSn:
 *                         type: string
 *                         description: 支付流水号
 *                       orderId:
 *                         type: string
 *                         description: 订单ID
 *                       paymentMethodId:
 *                         type: integer
 *                         description: 支付方式ID
 *                       amount:
 *                         type: string
 *                         description: 支付金额
 *                       paymentStatus:
 *                         type: integer
 *                         description: 支付状态
 *                       createdAt:
 *                         type: string
 *                         description: 创建时间
 *                       updatedAt:
 *                         type: string
 *                         description: 更新时间
 *                       paidAt:
 *                         type: string
 *                         description: 支付时间
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: 用户未登录
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
  router.get('/records/:orderId', authMiddleware, orderPaymentController.getOrderPaymentRecords.bind(orderPaymentController));

/**
 * @swagger
 * /api/v1/mall/order-payment/statistics:
 *   get:
 *     summary: 获取支付统计信息
 *     tags: [Mall-OrderPayment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: integer
 *         description: 开始时间戳（毫秒）
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: integer
 *         description: 结束时间戳（毫秒）
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取支付统计成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalCount:
 *                       type: integer
 *                       description: 总支付次数
 *                     successCount:
 *                       type: integer
 *                       description: 成功支付次数
 *                     failedCount:
 *                       type: integer
 *                       description: 失败支付次数
 *                     totalAmount:
 *                       type: number
 *                       description: 总支付金额
 *                     successAmount:
 *                       type: number
 *                       description: 成功支付金额
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: 用户未登录
 *       500:
 *         description: 服务器内部错误
 */
  router.get('/statistics', authMiddleware, orderPaymentController.getPaymentStatistics.bind(orderPaymentController));

  return router;
};

const express = require('express');
const router = express.Router();
const IpLocationController = require('../controllers/IpLocationController');

// 创建控制器实例
const controller = new IpLocationController();

/**
 * @swagger
 * /api/v1/mall/ip/location:
 *   get:
 *     tags: [商城/工具/IP查询]
 *     summary: 查询当前用户IP地址归属地
 *     description: 获取当前访问用户的IP地址归属地信息
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 查询成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     ip:
 *                       type: string
 *                       description: IP地址
 *                       example: ***************
 *                     country:
 *                       type: string
 *                       description: 国家/地区
 *                       example: 中国
 *                     area:
 *                       type: string
 *                       description: 详细位置
 *                       example: 江苏省南京市 信风网络
 *                     location:
 *                       type: string
 *                       description: 完整位置
 *                       example: 中国 江苏省南京市 信风网络
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: 查询IP地址归属地失败
 */
router.get('/location', controller.queryCurrentIp.bind(controller));

/**
 * @swagger
 * /api/v1/mall/ip/location/{ip}:
 *   get:
 *     tags: [商城/工具/IP查询]
 *     summary: 查询指定IP地址归属地
 *     description: 查询指定IP地址的归属地信息
 *     parameters:
 *       - name: ip
 *         in: path
 *         required: true
 *         description: IP地址
 *         schema:
 *           type: string
 *         example: ***************
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 查询成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     ip:
 *                       type: string
 *                       description: IP地址
 *                       example: ***************
 *                     country:
 *                       type: string
 *                       description: 国家/地区
 *                       example: 中国
 *                     area:
 *                       type: string
 *                       description: 详细位置
 *                       example: 江苏省南京市 信风网络
 *                     location:
 *                       type: string
 *                       description: 完整位置
 *                       example: 中国 江苏省南京市 信风网络
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: 无效的IP地址格式
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: 查询IP地址归属地失败
 */
router.get('/location', controller.queryCurrentIp.bind(controller));
router.get('/location/:ip', controller.queryIp.bind(controller));

module.exports = router;
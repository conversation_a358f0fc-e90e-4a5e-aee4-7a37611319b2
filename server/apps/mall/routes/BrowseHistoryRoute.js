const express = require('express');
const router = express.Router();

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入浏览记录控制器
const BrowseHistoryController = require('../controllers/BrowseHistoryController');

/**
 * 商城用户浏览记录路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const browseHistoryController = new BrowseHistoryController(prisma);

  /**
   * @swagger
   * /api/v1/mall/browse-history:
   *   post:
   *     tags: [商城用户浏览记录]
   *     summary: 添加浏览记录
   *     description: 用户浏览商品时添加浏览记录
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - goodsSpuId
   *             properties:
   *               goodsSpuId:
   *                 type: string
   *                 description: 商品SPU ID
   *               goodsSkuId:
   *                 type: string
   *                 description: 商品SKU ID（可选）
   *     responses:
   *       200:
   *         description: 添加成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未授权，请先登录
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/', authMiddleware, browseHistoryController.addBrowseHistory.bind(browseHistoryController));

  /**
   * @swagger
   * /api/v1/mall/browse-history:
   *   get:
   *     tags: [商城用户浏览记录]
   *     summary: 获取浏览记录列表
   *     description: 获取用户的浏览记录列表，支持分页
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *         description: 每页数量
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *         description: 限制数量（用于获取最近几条记录，设置后忽略分页参数）
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取浏览记录成功
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         description: 商品ID
   *                       productId:
   *                         type: string
   *                         description: 商品ID
   *                       name:
   *                         type: string
   *                         description: 商品名称
   *                       subtitle:
   *                         type: string
   *                         description: 商品副标题
   *                       image:
   *                         type: string
   *                         description: 商品图片
   *                       price:
   *                         type: number
   *                         description: 商品价格
   *                       originalPrice:
   *                         type: number
   *                         description: 商品原价
   *                       viewTime:
   *                         type: integer
   *                         description: 浏览时间（毫秒时间戳）
   *                 pagination:
   *                   type: object
   *                   properties:
   *                     page:
   *                       type: integer
   *                     pageSize:
   *                       type: integer
   *                     total:
   *                       type: integer
   *                     totalPages:
   *                       type: integer
   *       401:
   *         description: 未授权，请先登录
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/', authMiddleware, browseHistoryController.getBrowseHistory.bind(browseHistoryController));

  /**
   * @swagger
   * /api/v1/mall/browse-history/batch:
   *   delete:
   *     tags: [商城用户浏览记录]
   *     summary: 批量删除浏览记录
   *     description: 批量删除指定商品的浏览记录
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - goodsSpuIds
   *             properties:
   *               goodsSpuIds:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: 商品SPU ID数组
   *     responses:
   *       200:
   *         description: 删除成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未授权，请先登录
   *       500:
   *         description: 服务器内部错误
   */
  router.delete('/batch', authMiddleware, browseHistoryController.removeBrowseHistoryBatch.bind(browseHistoryController));

  /**
   * @swagger
   * /api/v1/mall/browse-history/clear:
   *   delete:
   *     tags: [商城用户浏览记录]
   *     summary: 清空所有浏览记录
   *     description: 清空用户的所有浏览记录
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 清空成功
   *       401:
   *         description: 未授权，请先登录
   *       500:
   *         description: 服务器内部错误
   */
  router.delete('/clear', authMiddleware, browseHistoryController.clearBrowseHistory.bind(browseHistoryController));

  /**
   * @swagger
   * /api/v1/mall/browse-history/{goodsSpuId}:
   *   delete:
   *     tags: [商城用户浏览记录]
   *     summary: 删除单个浏览记录
   *     description: 删除指定商品的浏览记录
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: goodsSpuId
   *         required: true
   *         schema:
   *           type: string
   *         description: 商品SPU ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未授权，请先登录
   *       500:
   *         description: 服务器内部错误
   */
  router.delete('/:goodsSpuId', authMiddleware, browseHistoryController.removeBrowseHistory.bind(browseHistoryController));

  /**
   * @swagger
   * /api/v1/mall/browse-history/stats:
   *   get:
   *     tags: [商城用户浏览记录]
   *     summary: 获取浏览记录统计
   *     description: 获取用户浏览记录的统计信息
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取浏览记录统计成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     totalCount:
   *                       type: integer
   *                       description: 总浏览记录数
   *                     browseDays:
   *                       type: integer
   *                       description: 浏览天数
   *                     lastBrowseTime:
   *                       type: integer
   *                       description: 最后浏览时间（毫秒时间戳）
   *       401:
   *         description: 未授权，请先登录
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/stats', authMiddleware, browseHistoryController.getBrowseHistoryStats.bind(browseHistoryController));

  return router;
};

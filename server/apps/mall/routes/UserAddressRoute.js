/**
 * 商城用户收货地址路由
 */
const express = require('express');
const router = express.Router();
const userAddressController = require('../controllers/UserAddressController');
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

/**
 * @swagger
 * tags:
 *   name: 商城-用户收货地址
 *   description: 商城用户收货地址相关接口
 */

/**
 * @swagger
 * /api/mall/user/addresses:
 *   get:
 *     summary: 获取用户所有收货地址
 *     description: 获取当前登录用户的所有收货地址
 *     tags: [商城-用户收货地址]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取收货地址成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取收货地址成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: 地址ID
 *                       name:
 *                         type: string
 *                         description: 收货人姓名
 *                       phone:
 *                         type: string
 *                         description: 收货人手机号
 *                       province:
 *                         type: string
 *                         description: 省份
 *                       city:
 *                         type: string
 *                         description: 城市
 *                       district:
 *                         type: string
 *                         description: 区/县
 *                       address:
 *                         type: string
 *                         description: 详细地址
 *                       postcode:
 *                         type: string
 *                         description: 邮政编码
 *                       is_default:
 *                         type: boolean
 *                         description: 是否默认地址
 *       401:
 *         description: 未授权，用户未登录
 *       500:
 *         description: 服务器内部错误
 */
router.get('/addresses', authMiddleware, userAddressController.getAddresses);

/**
 * @swagger
 * /api/mall/user/addresses/default:
 *   get:
 *     summary: 获取用户默认收货地址
 *     description: 获取当前登录用户的默认收货地址
 *     tags: [商城-用户收货地址]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取默认收货地址成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取默认收货地址成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 地址ID
 *                     name:
 *                       type: string
 *                       description: 收货人姓名
 *                     phone:
 *                       type: string
 *                       description: 收货人手机号
 *                     province:
 *                       type: string
 *                       description: 省份
 *                     city:
 *                       type: string
 *                       description: 城市
 *                     district:
 *                       type: string
 *                       description: 区/县
 *                     address:
 *                       type: string
 *                       description: 详细地址
 *                     postcode:
 *                       type: string
 *                       description: 邮政编码
 *                     is_default:
 *                       type: boolean
 *                       description: 是否默认地址
 *       401:
 *         description: 未授权，用户未登录
 *       500:
 *         description: 服务器内部错误
 */
router.get('/addresses/default', authMiddleware, userAddressController.getDefaultAddress);

/**
 * @swagger
 * /api/mall/user/addresses/{id}:
 *   get:
 *     summary: 获取收货地址详情
 *     description: 获取指定ID的收货地址详情
 *     tags: [商城-用户收货地址]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 收货地址ID
 *     responses:
 *       200:
 *         description: 获取收货地址详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取收货地址详情成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 地址ID
 *                     name:
 *                       type: string
 *                       description: 收货人姓名
 *                     phone:
 *                       type: string
 *                       description: 收货人手机号
 *                     province:
 *                       type: string
 *                       description: 省份
 *                     city:
 *                       type: string
 *                       description: 城市
 *                     district:
 *                       type: string
 *                       description: 区/县
 *                     address:
 *                       type: string
 *                       description: 详细地址
 *                     postcode:
 *                       type: string
 *                       description: 邮政编码
 *                     is_default:
 *                       type: boolean
 *                       description: 是否默认地址
 *       401:
 *         description: 未授权，用户未登录
 *       404:
 *         description: 收货地址不存在或已被删除
 *       500:
 *         description: 服务器内部错误
 */
router.get('/addresses/:id', authMiddleware, userAddressController.getAddressById);

/**
 * @swagger
 * /api/mall/user/addresses:
 *   post:
 *     summary: 创建新的收货地址
 *     description: 为当前登录用户创建新的收货地址
 *     tags: [商城-用户收货地址]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - phone
 *               - province
 *               - city
 *               - district
 *               - address
 *             properties:
 *               name:
 *                 type: string
 *                 description: 收货人姓名
 *               phone:
 *                 type: string
 *                 description: 收货人手机号
 *               province:
 *                 type: string
 *                 description: 省份
 *               city:
 *                 type: string
 *                 description: 城市
 *               district:
 *                 type: string
 *                 description: 区/县
 *               address:
 *                 type: string
 *                 description: 详细地址
 *               postcode:
 *                 type: string
 *                 description: 邮政编码
 *               isDefault:
 *                 type: boolean
 *                 description: 是否设为默认地址
 *     responses:
 *       201:
 *         description: 添加收货地址成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 添加收货地址成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 地址ID
 *                     name:
 *                       type: string
 *                       description: 收货人姓名
 *                     phone:
 *                       type: string
 *                       description: 收货人手机号
 *                     province:
 *                       type: string
 *                       description: 省份
 *                     city:
 *                       type: string
 *                       description: 城市
 *                     district:
 *                       type: string
 *                       description: 区/县
 *                     address:
 *                       type: string
 *                       description: 详细地址
 *                     postcode:
 *                       type: string
 *                       description: 邮政编码
 *                     is_default:
 *                       type: boolean
 *                       description: 是否默认地址
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权，用户未登录
 *       500:
 *         description: 服务器内部错误
 */
router.post('/addresses', authMiddleware, userAddressController.createAddress);

/**
 * @swagger
 * /api/mall/user/addresses/{id}:
 *   put:
 *     summary: 更新收货地址
 *     description: 更新指定ID的收货地址信息
 *     tags: [商城-用户收货地址]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 收货地址ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - phone
 *               - province
 *               - city
 *               - district
 *               - address
 *             properties:
 *               name:
 *                 type: string
 *                 description: 收货人姓名
 *               phone:
 *                 type: string
 *                 description: 收货人手机号
 *               province:
 *                 type: string
 *                 description: 省份
 *               city:
 *                 type: string
 *                 description: 城市
 *               district:
 *                 type: string
 *                 description: 区/县
 *               address:
 *                 type: string
 *                 description: 详细地址
 *               postcode:
 *                 type: string
 *                 description: 邮政编码
 *               isDefault:
 *                 type: boolean
 *                 description: 是否设为默认地址
 *     responses:
 *       200:
 *         description: 更新收货地址成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新收货地址成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 地址ID
 *                     name:
 *                       type: string
 *                       description: 收货人姓名
 *                     phone:
 *                       type: string
 *                       description: 收货人手机号
 *                     province:
 *                       type: string
 *                       description: 省份
 *                     city:
 *                       type: string
 *                       description: 城市
 *                     district:
 *                       type: string
 *                       description: 区/县
 *                     address:
 *                       type: string
 *                       description: 详细地址
 *                     postcode:
 *                       type: string
 *                       description: 邮政编码
 *                     is_default:
 *                       type: boolean
 *                       description: 是否默认地址
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权，用户未登录
 *       404:
 *         description: 收货地址不存在或已被删除
 *       500:
 *         description: 服务器内部错误
 */
router.put('/addresses/:id', authMiddleware, userAddressController.updateAddress);

/**
 * @swagger
 * /api/mall/user/addresses/{id}/default:
 *   patch:
 *     summary: 设置默认收货地址
 *     description: 将指定ID的收货地址设为默认地址
 *     tags: [商城-用户收货地址]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 收货地址ID
 *     responses:
 *       200:
 *         description: 设置默认地址成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 设置默认地址成功
 *                 data:
 *                   type: null
 *       401:
 *         description: 未授权，用户未登录
 *       404:
 *         description: 收货地址不存在或已被删除
 *       500:
 *         description: 服务器内部错误
 */
router.patch('/addresses/:id/default', authMiddleware, userAddressController.setDefaultAddress);

/**
 * @swagger
 * /api/mall/user/addresses/{id}:
 *   delete:
 *     summary: 删除收货地址
 *     description: 删除指定ID的收货地址
 *     tags: [商城-用户收货地址]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 收货地址ID
 *     responses:
 *       200:
 *         description: 删除收货地址成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除收货地址成功
 *                 data:
 *                   type: null
 *       401:
 *         description: 未授权，用户未登录
 *       404:
 *         description: 收货地址不存在或已被删除
 *       500:
 *         description: 服务器内部错误
 */
router.delete('/addresses/:id', authMiddleware, userAddressController.deleteAddress);

module.exports = router;

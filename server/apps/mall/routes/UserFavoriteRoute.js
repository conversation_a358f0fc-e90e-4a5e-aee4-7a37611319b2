const express = require('express');
const router = express.Router();

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入用户收藏控制器
const UserFavoriteController = require('../controllers/UserFavoriteController');

/**
 * 用户收藏路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const userFavoriteController = new UserFavoriteController(prisma);

  /**
   * @swagger
   * /api/mall/favorite/add:
   *   post:
   *     tags: [用户收藏管理]
   *     summary: 添加收藏
   *     description: 添加商品、店铺、品牌或活动到收藏，支持批量添加
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - targetId
   *               - targetType
   *             properties:
   *               targetId:
   *                 oneOf:
   *                   - type: integer
   *                     description: 单个收藏目标ID
   *                   - type: array
   *                     items:
   *                       type: integer
   *                     description: 多个收藏目标ID数组
   *               targetType:
   *                 type: integer
   *                 description: 收藏目标类型：1-商品，2-店铺，3-品牌，4-活动
   *               remark:
   *                 type: string
   *                 description: 备注信息
   *     responses:
   *       200:
   *         description: 添加成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   oneOf:
   *                     - type: object
   *                       description: 单个收藏结果
   *                     - type: object
   *                       properties:
   *                         success:
   *                           type: array
   *                           description: 成功添加的收藏记录
   *                         failed:
   *                           type: array
   *                           description: 添加失败的记录
   *                         total:
   *                           type: integer
   *                           description: 总数量
   *                         successCount:
   *                           type: integer
   *                           description: 成功数量
   *                         failedCount:
   *                           type: integer
   *                           description: 失败数量
   *                 message:
   *                   type: string
   *                   example: 收藏成功
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器错误
   */
  router.post('/add', authMiddleware, userFavoriteController.addFavorite.bind(userFavoriteController));

  /**
   * @swagger
   * /api/mall/favorite/cancel:
   *   post:
   *     tags: [用户收藏管理]
   *     summary: 取消收藏
   *     description: 取消已收藏的商品、店铺、品牌或活动，支持批量取消
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - targetId
   *               - targetType
   *             properties:
   *               targetId:
   *                 oneOf:
   *                   - type: integer
   *                     description: 单个收藏目标ID
   *                   - type: array
   *                     items:
   *                       type: integer
   *                     description: 多个收藏目标ID数组
   *               targetType:
   *                 type: integer
   *                 description: 收藏目标类型：1-商品，2-店铺，3-品牌，4-活动
   *     responses:
   *       200:
   *         description: 取消成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   oneOf:
   *                     - type: object
   *                       description: 单个取消结果
   *                     - type: object
   *                       properties:
   *                         success:
   *                           type: array
   *                           description: 成功取消的收藏记录
   *                         failed:
   *                           type: array
   *                           description: 取消失败的记录
   *                         total:
   *                           type: integer
   *                           description: 总数量
   *                         successCount:
   *                           type: integer
   *                           description: 成功数量
   *                         failedCount:
   *                           type: integer
   *                           description: 失败数量
   *                 message:
   *                   type: string
   *                   example: 取消收藏成功
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器错误
   */
  router.post('/cancel', authMiddleware, userFavoriteController.cancelFavorite.bind(userFavoriteController));

  /**
   * @swagger
   * /api/mall/favorite/list:
   *   get:
   *     tags: [用户收藏管理]
   *     summary: 获取用户收藏列表
   *     description: 获取用户收藏的商品、店铺、品牌或活动列表
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: targetType
   *         required: false
   *         schema:
   *           type: integer
   *         description: 收藏目标类型：1-商品，2-店铺，3-品牌，4-活动
   *       - in: query
   *         name: page
   *         required: false
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         required: false
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器错误
   */
  router.get('/list', authMiddleware, userFavoriteController.getUserFavorites.bind(userFavoriteController));

  /**
   * @swagger
   * /api/mall/favorite/check:
   *   get:
   *     tags: [用户收藏管理]
   *     summary: 检查是否已收藏
   *     description: 检查用户是否已收藏指定的商品、店铺、品牌或活动
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: targetId
   *         required: true
   *         schema:
   *           type: integer
   *         description: 收藏目标ID
   *       - in: query
   *         name: targetType
   *         required: true
   *         schema:
   *           type: integer
   *         description: 收藏目标类型：1-商品，2-店铺，3-品牌，4-活动
   *     responses:
   *       200:
   *         description: 查询成功
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器错误
   */
  router.get('/check', authMiddleware, userFavoriteController.checkFavorite.bind(userFavoriteController));

  return router;
};

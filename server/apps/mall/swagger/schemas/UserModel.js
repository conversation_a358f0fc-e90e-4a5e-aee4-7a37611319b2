/**
 * 商城用户模型定义
 */
module.exports = {
  schemas: {
    // 商城用户模型
    MallUser: {
      type: "object",
      properties: {
        id: {
          type: "string",
          description: "用户ID",
          example: "1001"
        },
        username: {
          type: "string",
          description: "用户名",
          example: "user123"
        },
        nickname: {
          type: "string",
          description: "昵称",
          example: "用户昵称"
        },
        phone: {
          type: "string",
          description: "手机号",
          example: "13800138000"
        },
        email: {
          type: "string",
          description: "邮箱",
          example: "<EMAIL>"
        },
        avatar: {
          type: "string",
          description: "头像",
          example: "https://example.com/avatar.jpg"
        },
        status: {
          type: "integer",
          description: "用户状态：1-正常，0-禁用",
          enum: [0, 1],
          example: 1
        },
        last_login_time: {
          type: "integer",
          description: "最后登录时间（时间戳）",
          example: 1619712000000
        },
        last_login_ip: {
          type: "string",
          description: "最后登录IP",
          example: "***********"
        },
        login_count: {
          type: "integer",
          description: "登录次数",
          example: 10
        },
        created_at: {
          type: "integer",
          description: "创建时间（时间戳）",
          example: 1619712000000
        },
        updated_at: {
          type: "integer",
          description: "更新时间（时间戳）",
          example: 1619712000000
        }
      }
    },

    // 用户登录请求
    MallUserLoginRequest: {
      type: "object",
      required: ["username", "password"],
      properties: {
        username: {
          type: "string",
          description: "用户名或手机号",
          example: "user123"
        },
        password: {
          type: "string",
          description: "密码",
          example: "password123"
        },
        encrypted: {
          type: "boolean",
          description: "密码是否已经过RSA加密",
          default: false,
          example: false
        }
      }
    },

    // 用户登录响应
    MallUserLoginResponse: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          default: true
        },
        message: {
          type: "string",
          example: "登录成功"
        },
        data: {
          type: "object",
          properties: {
            user: {
              $ref: "#/components/schemas/MallUser"
            },
            token: {
              type: "string",
              description: "登录令牌",
              example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
          }
        }
      }
    },

    // 用户注册请求
    MallUserRegisterRequest: {
      type: "object",
      required: ["username", "password", "phone", "captcha"],
      properties: {
        username: {
          type: "string",
          description: "用户名",
          example: "user123"
        },
        password: {
          type: "string",
          description: "密码",
          example: "password123"
        },
        nickname: {
          type: "string",
          description: "昵称",
          example: "用户昵称"
        },
        phone: {
          type: "string",
          description: "手机号",
          example: "13800138000"
        },
        email: {
          type: "string",
          description: "邮箱",
          example: "<EMAIL>"
        },
        captcha: {
          type: "string",
          description: "验证码",
          example: "123456"
        },
        encrypted: {
          type: "boolean",
          description: "密码是否已经过RSA加密",
          default: false,
          example: false
        }
      }
    },

    // 用户注册响应
    MallUserRegisterResponse: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          default: true
        },
        message: {
          type: "string",
          example: "注册成功"
        },
        data: {
          $ref: "#/components/schemas/MallUser"
        }
      }
    },

    // 获取验证码响应
    MallCaptchaResponse: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          default: true
        },
        message: {
          type: "string",
          example: "获取验证码成功"
        },
        data: {
          type: "object",
          properties: {
            captcha: {
              type: "string",
              description: "验证码（仅在非生产环境下返回）",
              example: "123456"
            }
          }
        }
      }
    }
  }
};

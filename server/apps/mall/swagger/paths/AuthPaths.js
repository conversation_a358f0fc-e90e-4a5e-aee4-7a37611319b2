/**
 * 商城用户认证相关API路径定义
 */
module.exports = {
  paths: {
    "/api/v1/mall/auth/login": {
      post: {
        tags: ["商城/用户/认证"],
        summary: "用户登录",
        description: "商城前端用户登录接口，支持用户名或手机号登录",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["username", "password"],
                properties: {
                  username: {
                    type: "string",
                    description: "用户名或手机号",
                    example: "user123"
                  },
                  password: {
                    type: "string",
                    description: "密码",
                    example: "password123"
                  },
                  encrypted: {
                    type: "boolean",
                    description: "密码是否已经过RSA加密",
                    default: false,
                    example: false
                  }
                }
              }
            }
          }
        },
        responses: {
          "200": {
            description: "登录成功",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      default: true
                    },
                    message: {
                      type: "string",
                      example: "登录成功"
                    },
                    data: {
                      type: "object",
                      properties: {
                        user: {
                          type: "object",
                          properties: {
                            id: {
                              type: "string",
                              example: "1001"
                            },
                            username: {
                              type: "string",
                              example: "user123"
                            },
                            nickname: {
                              type: "string",
                              example: "用户昵称"
                            },
                            phone: {
                              type: "string",
                              example: "13800138000"
                            },
                            email: {
                              type: "string",
                              example: "<EMAIL>"
                            },
                            avatar: {
                              type: "string",
                              example: "https://example.com/avatar.jpg"
                            },
                            status: {
                              type: "integer",
                              example: 1,
                              description: "用户状态：1-正常，0-禁用"
                            },
                            last_login_time: {
                              type: "integer",
                              example: 1619712000000,
                              description: "最后登录时间（时间戳）"
                            },
                            last_login_ip: {
                              type: "string",
                              example: "***********"
                            },
                            login_count: {
                              type: "integer",
                              example: 10
                            },
                            created_at: {
                              type: "integer",
                              example: 1619712000000
                            },
                            updated_at: {
                              type: "integer",
                              example: 1619712000000
                            }
                          }
                        },
                        token: {
                          type: "string",
                          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                          description: "登录令牌"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            description: "用户名或密码错误",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      default: false
                    },
                    message: {
                      type: "string",
                      example: "用户名/手机号或密码错误"
                    },
                    code: {
                      type: "integer",
                      example: 40001
                    }
                  }
                }
              }
            }
          },
          "403": {
            description: "账号已被禁用",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      default: false
                    },
                    message: {
                      type: "string",
                      example: "账号已被禁用"
                    },
                    code: {
                      type: "integer",
                      example: 40302
                    }
                  }
                }
              }
            }
          },
          "500": {
            description: "服务器内部错误",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/Error"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/mall/auth/register": {
      post: {
        tags: ["商城/用户/认证"],
        summary: "用户注册",
        description: "商城前端用户注册接口",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["username", "password", "phone", "captcha"],
                properties: {
                  username: {
                    type: "string",
                    description: "用户名",
                    example: "user123"
                  },
                  password: {
                    type: "string",
                    description: "密码",
                    example: "password123"
                  },
                  nickname: {
                    type: "string",
                    description: "昵称",
                    example: "用户昵称"
                  },
                  phone: {
                    type: "string",
                    description: "手机号",
                    example: "13800138000"
                  },
                  email: {
                    type: "string",
                    description: "邮箱",
                    example: "<EMAIL>"
                  },
                  captcha: {
                    type: "string",
                    description: "验证码",
                    example: "123456"
                  },
                  encrypted: {
                    type: "boolean",
                    description: "密码是否已经过RSA加密",
                    default: false,
                    example: false
                  }
                }
              }
            }
          }
        },
        responses: {
          "200": {
            description: "注册成功",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      default: true
                    },
                    message: {
                      type: "string",
                      example: "注册成功"
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "1001"
                        },
                        username: {
                          type: "string",
                          example: "user123"
                        },
                        nickname: {
                          type: "string",
                          example: "用户昵称"
                        },
                        phone: {
                          type: "string",
                          example: "13800138000"
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>"
                        },
                        status: {
                          type: "integer",
                          example: 1,
                          description: "用户状态：1-正常，0-禁用"
                        },
                        created_at: {
                          type: "integer",
                          example: 1619712000000
                        },
                        updated_at: {
                          type: "integer",
                          example: 1619712000000
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            description: "请求参数错误",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      default: false
                    },
                    message: {
                      type: "string",
                      example: "用户名已存在"
                    },
                    code: {
                      type: "integer",
                      example: 40001
                    }
                  }
                }
              }
            }
          },
          "500": {
            description: "服务器内部错误",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/Error"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/mall/auth/captcha": {
      get: {
        tags: ["商城/用户/认证"],
        summary: "获取验证码",
        description: "获取短信验证码",
        parameters: [
          {
            in: "query",
            name: "phone",
            required: true,
            schema: {
              type: "string"
            },
            description: "手机号",
            example: "13800138000"
          }
        ],
        responses: {
          "200": {
            description: "获取成功",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      default: true
                    },
                    message: {
                      type: "string",
                      example: "获取验证码成功"
                    },
                    data: {
                      type: "object",
                      properties: {
                        captcha: {
                          type: "string",
                          example: "123456",
                          description: "验证码（仅在非生产环境下返回）"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            description: "请求参数错误",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      default: false
                    },
                    message: {
                      type: "string",
                      example: "手机号格式不正确"
                    },
                    code: {
                      type: "integer",
                      example: 40001
                    }
                  }
                }
              }
            }
          },
          "500": {
            description: "服务器内部错误",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/Error"
                }
              }
            }
          }
        }
      }
    }
  }
};

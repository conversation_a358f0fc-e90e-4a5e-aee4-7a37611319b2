/**
 * 商城基础控制器
 * 提供所有商城控制器共用的方法和属性
 */
const BaseController = require('../../../../core/controllers/BaseController');
const UserService = require('../../services/UserService');
const VerificationCodeService = require('../../../../apps/master/system/integration/services/VerificationCodeService');

class MallBaseController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.userService = new UserService(prisma);
    this.verificationCodeService = new VerificationCodeService(prisma);
  }
  
  /**
   * 验证手机号格式
   * @param {string} phone - 手机号
   * @returns {boolean} - 是否符合格式
   */
  validatePhoneFormat(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }
  
  /**
   * 获取当前用户ID
   * @param {Object} req - 请求对象
   * @returns {BigInt} - 用户ID
   */
  getUserId(req) {
    return req.user && req.user.id ? BigInt(req.user.id) : null;
  }
}

module.exports = MallBaseController;

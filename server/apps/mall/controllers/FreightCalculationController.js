/**
 * 运费计算控制器
 * 负责处理运费计算相关的HTTP请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const FreightCalculationService = require('../services/FreightCalculationService');

/**
 * 运费计算控制器类
 */
class FreightCalculationController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.freightCalculationService = new FreightCalculationService(prisma);
  }

  /**
   * 单商品运费计算
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async calculateSingleProductFreight(req, res) {
    try {
      const {
        spuId,
        skuId,
        quantity = 1,
        provinceCode,
        cityCode // 可选参数
      } = req.body;

      // 构建参数对象
      const params = {
        spuId: spuId,
        skuId: skuId,
        quantity: parseInt(quantity, 10),
        provinceCode: provinceCode,
        cityCode: cityCode || null // 允许为空
      };

      // 调用服务层方法计算运费
      const result = await this.freightCalculationService.calculateSingleProductFreight(params);

      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('计算单商品运费失败:', error);
      return this.fail(res, '计算运费失败', null, 500);
    }
  }

  /**
   * 获取支持的计费类型列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getChargeTypes(req, res) {
    try {
      const chargeTypes = [
        { id: 1, name: '按件计费', description: '根据商品件数计算运费' },
        { id: 2, name: '按重量计费', description: '根据商品重量计算运费' },
        { id: 3, name: '按体积计费', description: '根据商品体积计算运费' }
      ];

      return this.success(res, chargeTypes, '获取计费类型成功');
    } catch (error) {
      console.error('获取计费类型失败:', error);
      return this.fail(res, '获取计费类型失败', null, 500);
    }
  }

  /**
   * 批量计算商品运费
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async calculateBatchFreight(req, res) {
    try {
      const {
        items,
        provinceCode,
        cityCode // 可选参数
      } = req.body;

      // 构建参数对象
      const params = {
        items: items,
        provinceCode: provinceCode,
        cityCode: cityCode || null // 允许为空
      };

      // 调用服务层方法计算运费
      const result = await this.freightCalculationService.calculateBatchFreight(params);

      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else if (result.code === 200) {
        // 部分成功的情况，使用200状态码
        return res.status(200).json({
          success: true,
          message: result.message,
          data: result.data
        });
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('批量计算运费失败:', error);
      return this.fail(res, '批量计算运费失败', null, 500);
    }
  }
}

module.exports = FreightCalculationController; 
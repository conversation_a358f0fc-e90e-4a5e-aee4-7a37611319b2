/**
 * 商城新闻控制器 - 面向前端用户的公共接口
 */
const BaseController = require('../../../core/controllers/BaseController');
const NewsService = require('../services/NewsService');

class NewsController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.newsService = new NewsService(prisma);
  }

  /**
   * 获取新闻分类列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getCategories(req, res) {
    try {
      const { limit = 5 } = req.query;
      
      const categories = await this.newsService.getEnabledCategories({
        limit: parseInt(limit)
      });
      
      this.success(res, categories);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取新闻文章列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getArticles(req, res) {
    try {
      const {
        page = 1,
        pageSize = 10,
        category_id,
        limit
      } = req.query;

      const params = {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

      if (category_id) params.category_id = category_id;
      if (limit) params.limit = parseInt(limit);

      const result = await this.newsService.getEnabledArticles(params);
      this.success(res, result);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取新闻文章详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getArticleDetail(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '文章ID不能为空');
      }

      const article = await this.newsService.getArticleDetail(id);
      this.success(res, article);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取相关文章推荐
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getRelatedArticles(req, res) {
    try {
      const { id } = req.params;
      const { limit = 5 } = req.query;
      
      if (!id) {
        return this.fail(res, '文章ID不能为空');
      }

      // 先获取文章信息以获得分类ID
      const article = await this.newsService.getArticleDetail(id);
      
      const relatedArticles = await this.newsService.getRelatedArticles(
        id, 
        article.category_id, 
        parseInt(limit)
      );
      
      this.success(res, relatedArticles);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取热门文章
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getHotArticles(req, res) {
    try {
      const { limit = 10 } = req.query;
      
      const articles = await this.newsService.getEnabledArticles({
        limit: parseInt(limit)
      });
      
      this.success(res, articles);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取最新文章
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getLatestArticles(req, res) {
    try {
      const { limit = 10, category_id } = req.query;
      
      const params = {
        limit: parseInt(limit)
      };
      
      if (category_id) params.category_id = category_id;
      
      const articles = await this.newsService.getEnabledArticles(params);
      
      this.success(res, articles);
    } catch (error) {
      this.fail(res, error.message);
    }
  }
}

module.exports = NewsController;

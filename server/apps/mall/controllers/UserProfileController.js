/**
 * 商城用户资料控制器
 * 处理用户头像、昵称、手机号和邮箱等个人信息的更新
 */
const MallBaseController = require('./base/MallBaseController');
const redisUtil = require('../../../core/utils/RedisUtil');
const axios = require('axios');
const bcrypt = require('bcryptjs');
const FormData = require('form-data');
const EmailServiceFactory = require('../../../apps/master/system/integration/email/services/EmailServiceFactory'); // 添加邮件服务工厂导入
const crypto = require('crypto'); // 添加加密模块导入
const { Prisma } = require('@prisma/client'); // 添加 Prisma 导入

class UserProfileController extends MallBaseController {
  constructor(prisma) {
    super(prisma);
    this.emailFactory = new EmailServiceFactory(prisma);
  }
  
  /**
   * 获取用户个人资料
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProfile(req, res) {
    try {
      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      if (!userId) {
        return this.fail(res, '未登录状态，无法获取用户信息', 401);
      }
      
      // 查询用户信息，包括微信绑定状态
      const users = await this.prisma.$queryRaw`
        SELECT 
          id, username, nickname, phone, email, avatar, status, 
          wechat_openid, last_login_time, last_login_ip, login_count,
          created_at, updated_at
        FROM "base"."mall_user" 
        WHERE "id" = ${userId}
        AND "deleted_at" IS NULL
      `;
      
      if (!users || users.length === 0) {
        return this.fail(res, '用户不存在或已被删除', 404);
      }
      
      const user = users[0];
      
      // 返回用户信息，包含微信绑定状态
      return this.success(res, {
        id: user.id.toString(),
        username: user.username,
        nickname: user.nickname || user.username,
        phone: user.phone || '',
        email: user.email || '',
        avatar: user.avatar || '',
        status: user.status,
        wechatOpenid: user.wechat_openid || '',
        lastLoginTime: user.last_login_time,
        lastLoginIp: user.last_login_ip,
        loginCount: user.login_count,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return this.fail(res, '获取用户信息失败', 500);
    }
  }
  /**
   * 修改头像（通过URL）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateAvatar(req, res) {
    try {
      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      if (!userId) {
        return this.fail(res, '未登录状态，无法修改头像', 401);
      }
      
      // 验证头像URL
      const { avatarUrl } = req.body;
      if (!avatarUrl) {
        return this.fail(res, '头像URL不能为空', 400);
      }
      
      // URL格式验证
      const urlRegex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
      if (!urlRegex.test(avatarUrl)) {
        return this.fail(res, '头像URL格式不正确', 400);
      }
      
      // 更新用户头像
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "avatar" = ${avatarUrl}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        
        // 查询更新后的用户信息
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        const user = users[0];
        
        return this.success(res, { user }, '头像修改成功');
      } catch (error) {
        console.error('更新头像失败:', error);
        return this.fail(res, '修改头像失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('修改头像过程中出错:', error);
      return this.fail(res, '修改头像失败，请稍后再试', 500);
    }
  }

  /**
   * 上传头像（通过文件）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async uploadAvatar(req, res) {
    try {
      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      if (!userId) {
        return this.fail(res, '未登录状态，无法上传头像', 401);
      }
      
      // 检查是否有文件上传
      if (!req.file) {
        return this.fail(res, '请选择要上传的头像文件', 400);
      }
      
      // 检查文件类型
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedMimeTypes.includes(req.file.mimetype)) {
        return this.fail(res, '只支持JPG、PNG、GIF、WEBP格式的图片', 400);
      }
      
      // 检查文件大小（限制为2MB）
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (req.file.size > maxSize) {
        return this.fail(res, '图片大小不能超过2MB', 400);
      }
      
      try {
        // 准备上传文件到统一上传服务
        const formData = new FormData();
        formData.append('file', req.file.buffer, {
          filename: req.file.originalname,
          contentType: req.file.mimetype
        });
        
        // 调用统一上传服务
        const uploadServiceUrl = process.env.UPLOAD_SERVICE_URL || 'http://localhost:3000/api/master/system/upload/file';
        
        const uploadResponse = await axios.post(uploadServiceUrl, formData, {
          headers: {
            ...formData.getHeaders(),
            'Authorization': req.headers.authorization
          }
        });
        
        if (uploadResponse.status !== 200 || uploadResponse.data.code !== 200) {
          console.error('上传服务返回错误:', uploadResponse.data);
          return this.fail(res, '上传头像失败: ' + (uploadResponse.data.message || '未知错误'), 500);
        }
        
        // 获取上传后的文件URL
        const fileUrl = uploadResponse.data.data.url;
        
        // 更新用户头像
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "avatar" = ${fileUrl}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        
        // 查询更新后的用户信息
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        const user = users[0];
        
        return this.success(res, { 
          user,
          file: {
            url: fileUrl,
            ...this.json(uploadResponse.data.data)
          }
        }, '头像上传成功');
      } catch (error) {
        console.error('上传头像失败:', error);
        return this.fail(res, '上传头像失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('上传头像过程中出错:', error);
      return this.fail(res, '上传头像失败，请稍后再试', 500);
    }
  }
  
  /**
   * 辅助方法：安全地转换JSON数据
   * @param {Object} data - 要转换的数据
   * @returns {Object} - 转换后的数据
   */
  json(data) {
    if (!data) return {};
    
    try {
      if (typeof data === 'string') {
        return JSON.parse(data);
      }
      return data;
    } catch (e) {
      console.error('JSON解析错误:', e);
      return {};
    }
  }

  /**
   * 修改昵称
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateNickname(req, res) {
    try {
      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      if (!userId) {
        return res.status(401).json({
          code: 401,
          message: '未登录状态，无法修改昵称',
          data: null
        });
      }
      
      // 验证昵称
      const { nickname } = req.body;
      if (!nickname) {
        return res.status(400).json({
          code: 400,
          message: '昵称不能为空',
          data: null
        });
      }
      
      // 昵称长度验证
      if (nickname.length < 2 || nickname.length > 20) {
        return res.status(400).json({
          code: 400,
          message: '昵称长度应为2-20个字符',
          data: null
        });
      }
      
      // 更新用户昵称
      try {
        const { prisma } = require('../../../core/database/prisma');
        await prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "nickname" = ${nickname}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        
        // 查询更新后的用户信息
        const users = await prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          return res.status(400).json({
            code: 400,
            message: '用户不存在或已被删除',
            data: null
          });
        }
        
        const user = users[0];
        
        // 处理BigInt序列化
        const serializedUser = JSON.parse(JSON.stringify(user, (key, value) => {
          if (typeof value === 'bigint') {
            return value.toString();
          }
          return value;
        }));
        
        return res.status(200).json({
          code: 200,
          message: '昵称修改成功',
          data: { user: serializedUser }
        });
      } catch (error) {
        console.error('更新昵称失败:', error);
        return res.status(500).json({
          code: 500,
          message: '修改昵称失败，请稍后再试',
          data: null
        });
      }
    } catch (error) {
      console.error('修改昵称过程中出错:', error);
      return res.status(500).json({
        code: 500,
        message: '修改昵称失败，请稍后再试',
        data: null
      });
    }
  }

  /**
   * 发送手机号修改验证码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async sendPhoneChangeCaptcha(req, res) {
    try {
      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      if (!userId) {
        return this.fail(res, '未登录状态，无法发送验证码', 401);
      }
      
      // 获取新手机号
      const { newPhone } = req.query;
      if (!newPhone) {
        return this.fail(res, '新手机号不能为空', 400);
      }
      
      // 验证手机号格式
      if (!this.validatePhoneFormat(newPhone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }
      
      // 检查新手机号是否已被使用
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "base"."mall_user" 
          WHERE "phone" = ${newPhone}
          AND "deleted_at" IS NULL
          AND "id" != ${userId}
        `;
        
        if (existingUsers && existingUsers.length > 0) {
          return this.fail(res, '该手机号已被其他账号使用', 400);
        }
      } catch (queryError) {
        console.error('查询手机号是否存在失败:', queryError);
        return this.fail(res, '验证手机号失败，请稍后再试', 500);
      }
      
      // 使用验证码服务生成和发送验证码
      try {
        console.log('开始生成验证码...');
        const result = await this.verificationCodeService.getCode(newPhone, 'change_phone');
        console.log('验证码生成成功:', result.code);
        
        // 在生产环境中，不应该返回验证码
        // 这里为了方便开发和测试，返回验证码
        if (process.env.NODE_ENV !== 'production') {
          return this.success(res, { captcha: result.code }, '获取验证码成功');
        } else {
          return this.success(res, {}, '验证码已发送到您的手机');
        }
      } catch (error) {
        console.error('生成或发送验证码失败:', error);
        return this.fail(res, '获取验证码失败: ' + error.message, 500);
      }
    } catch (error) {
      console.error('发送验证码过程中出错:', error);
      return this.fail(res, '发送验证码失败，请稍后再试', 500);
    }
  }

  /**
   * 修改手机号
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updatePhone(req, res) {
    try {
      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      
      if (!userId) {
        return this.fail(res, '未登录状态，无法修改手机号', 401);
      }
      
      // 验证请求参数
      const { newPhone, captcha } = req.body;
      if (!newPhone || !captcha) {
        console.log('缺少必要参数');
        return this.fail(res, '新手机号和验证码不能为空', 400);
      }

      // 验证手机号格式
      if (!this.validatePhoneFormat(newPhone)) {
        console.log('手机号格式不正确:', newPhone);
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 检查新手机号是否已被使用
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "base"."mall_user" 
          WHERE "phone" = ${newPhone}
          AND "deleted_at" IS NULL
          AND "id" != ${userId}
        `;
        
        if (existingUsers && existingUsers.length > 0) {
          console.log('手机号已被使用:', newPhone);
          return this.fail(res, '该手机号已被其他账号使用', 400);
        }
      } catch (queryError) {
        console.error('查询手机号是否存在失败:', queryError);
        return this.fail(res, '验证手机号失败，请稍后再试', 500);
      }

      // 验证验证码
      try {
        console.log('开始验证验证码，手机号:', newPhone, '验证码:', captcha);
        
        // 检查Redis连接状态
        console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
        
        // 直接从 Redis 中获取验证码进行比较
        const codeKey = `verification_code:change_phone:${newPhone}`;
        console.log('验证码键:', codeKey);
        
        const savedCode = await redisUtil.getClient().get(codeKey);
        console.log('Redis中存储的验证码:', savedCode || '无');
        
        if (!savedCode) {
          console.log('验证码不存在或已过期');
          return this.fail(res, '验证码不存在或已过期，请重新获取', 400);
        }
        
        if (savedCode !== captcha) {
          console.log('验证码不匹配');
          return this.fail(res, '验证码错误', 400);
        }
        
        console.log('验证码验证成功');
      } catch (error) {
        console.error('验证验证码失败:', error);
        return this.fail(res, '验证验证码失败: ' + error.message, 500);
      }

      // 更新用户手机号
      console.log('更新用户手机号...');
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "phone" = ${newPhone}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        console.log('手机号更新成功');
      } catch (updateError) {
        console.error('更新手机号失败:', updateError);
        return this.fail(res, '修改手机号失败，请稍后再试', 500);
      }

      // 查询更新后的用户信息
      console.log('查询更新后的用户信息...');
      let user = null;
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        user = users[0];
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        // 即使查询失败，也返回成功，因为手机号已经更新
        return this.success(res, {}, '手机号修改成功');
      }

      return this.success(res, { user }, '手机号修改成功');
    } catch (error) {
      console.error('修改手机号过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 发送邮箱验证码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async sendEmailCaptcha(req, res) {
    try {
      // 获取用户ID
      if (!req.user || !req.user.id) {
        return this.fail(res, '用户未登录', 401);
      }
      const userId = req.user.id;

      // 获取邮箱地址
      const { email } = req.body;
      
      console.log('收到发送邮箱验证码请求，用户ID:', userId, '邮箱:', email);
      
      // 验证邮箱
      if (!email) {
        return this.fail(res, '邮箱地址不能为空', 400);
      }
      
      // 验证邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(email)) {
        return this.fail(res, '邮箱格式不正确', 400);
      }
      
      // 检查Redis连接状态
      console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
      
      // 生成6位随机验证码
      const captcha = Math.floor(100000 + Math.random() * 900000).toString();
      console.log('生成的邮箱验证码:', captcha);
      
      // 将验证码存入Redis，有效期1分钟
      const codeKey = `userId_${userId.toString()}_email_${email}_bind_key`;
      await redisUtil.getClient().set(codeKey, captcha, { EX: 60 });
      console.log('验证码已存入Redis，键:', codeKey);
      
      // 发送邮件
      try {
        // 获取默认邮件服务
        const emailService = await this.emailFactory.getDefaultService();
        
        // 发送HTML邮件
        const result = await emailService.sendHtml({
          to: email,
          subject: '邮箱绑定验证码',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
              <h2 style="color: #333; text-align: center;">邮箱绑定验证码</h2>
              <p style="color: #666; font-size: 16px;">您好，</p>
              <p style="color: #666; font-size: 16px;">您正在进行邮箱绑定操作，验证码为：</p>
              <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 24px; font-weight: bold; color: #333; margin: 20px 0; letter-spacing: 5px;">${captcha}</div>
              <p style="color: #666; font-size: 16px;">验证码有效期为10分钟，请勿将验证码泄露给他人。</p>
              <p style="color: #666; font-size: 14px; margin-top: 30px; text-align: center;">此邮件由系统自动发送，请勿回复。</p>
            </div>
          `
        });
        
        if (result.success) {
          // 在生产环境中，不应该返回验证码
          if (process.env.NODE_ENV !== 'production') {
            return this.success(res, { captcha }, '验证码发送成功');
          } else {
            return this.success(res, {}, '验证码已发送到您的邮箱');
          }
        } else {
          console.error('发送邮件失败:', result.message);
          return this.fail(res, '发送验证码失败: ' + result.message, 500);
        }
      } catch (error) {
        console.error('发送邮件失败:', error);
        return this.fail(res, '发送验证码失败: ' + error.message, 500);
      }
    } catch (error) {
      console.error('发送邮箱验证码过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 绑定邮箱
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async bindEmail(req, res) {
    try {
      // 获取用户ID
      if (!req.user || !req.user.id) {
        return this.fail(res, '用户未登录', 401);
      }
      const userId = req.user.id;

      // 验证请求参数
      const { email, captcha } = req.body;
      if (!email || !captcha) {
        console.log('缺少必要参数');
        return this.fail(res, '邮箱和验证码不能为空', 400);
      }

      // 验证邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(email)) {
        console.log('邮箱格式不正确:', email);
        return this.fail(res, '邮箱格式不正确', 400);
      }

      // 检查邮箱是否已被使用
      try {
        // 将userId转换为BigInt类型，以便与数据库中的id字段进行比较
        const userIdBigInt = BigInt(userId);
        
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "base"."mall_user" 
          WHERE "email" = ${email}
          AND "deleted_at" IS NULL
          AND "id" != ${userIdBigInt}
        `;
        
        if (existingUsers && existingUsers.length > 0) {
          console.log('邮箱已被使用:', email);
          return this.fail(res, '该邮箱已被其他账号使用', 400);
        }
      } catch (queryError) {
        console.error('查询邮箱是否存在失败:', queryError);
        return this.fail(res, '验证邮箱失败，请稍后再试', 500);
      }

      // 验证验证码
      try {
        console.log('开始验证验证码，用户ID:', userId, '验证码:', captcha);
        
        // 检查Redis连接状态
        console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
        
        // 直接从 Redis 中获取验证码进行比较
        // 对于Redis键名，使用字符串类型的userId
        const codeKey = `userId_${userId.toString()}_email_${email}_bind_key`;
        console.log('验证码键:', codeKey);
        
        const savedCode = await redisUtil.getClient().get(codeKey);
        console.log('Redis中存储的验证码:', savedCode || '无');
        
        if (!savedCode) {
          console.log('验证码不存在或已过期');
          return this.fail(res, '验证码不存在或已过期，请重新获取', 400);
        }
        
        if (savedCode !== captcha) {
          console.log('验证码不匹配');
          return this.fail(res, '验证码错误', 400);
        }
        
        console.log('验证码验证成功');
        
        // 验证成功后删除Redis中的验证码
        await redisUtil.getClient().del(codeKey);
      } catch (error) {
        console.error('验证验证码失败:', error);
        return this.fail(res, '验证验证码失败: ' + error.message, 500);
      }

      // 更新用户邮箱
      console.log('更新用户邮箱...');
      try {
        // 确保使用BigInt类型的userId
        const userIdBigInt = BigInt(userId);
        
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "email" = ${email}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userIdBigInt}
        `;
        console.log('邮箱更新成功');
      } catch (updateError) {
        console.error('更新邮箱失败:', updateError);
        return this.fail(res, '绑定邮箱失败，请稍后再试', 500);
      }

      // 查询更新后的用户信息
      console.log('查询更新后的用户信息...');
      let user = null;
      try {
        // 确保使用BigInt类型的userId
        const userIdBigInt = BigInt(userId);
        
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        user = users[0];
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        // 即使查询失败，也返回成功，因为邮箱已经更新
        return this.success(res, {}, '邮箱绑定成功');
      }

      return this.success(res, { user }, '邮箱绑定成功');
    } catch (error) {
      console.error('绑定邮箱过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
  /**
   * 保存用户实名认证信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async saveRealnameAuth(req, res) {
    try {
      // 获取用户ID
      if (!req.user || !req.user.id) {
        return this.fail(res, '用户未登录', 401);
      }
      const userId = BigInt(req.user.id);

      // 获取实名认证信息
      const { realName, idCardNumber, idCardFront, idCardBack, authPhone, bankCardNo } = req.body;
      
      // 验证必填字段
      if (!realName) {
        return this.fail(res, '真实姓名不能为空', 400);
      }
      if (!idCardNumber) {
        return this.fail(res, '身份证号码不能为空', 400);
      }
      if (!idCardFront) {
        return this.fail(res, '身份证正面照片不能为空', 400);
      }
      if (!idCardBack) {
        return this.fail(res, '身份证反面照片不能为空', 400);
      }
      if (!authPhone) {
        return this.fail(res, '认证手机号不能为空', 400);
      }
      if (!bankCardNo) {
        return this.fail(res, '银行卡号不能为空', 400);
      }
      
      // 验证身份证号格式（简单验证）
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardRegex.test(idCardNumber)) {
        return this.fail(res, '身份证号码格式不正确', 400);
      }
      
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(authPhone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }
      
      // 验证银行卡号格式（简单验证，长度在6-19位之间）
      const bankCardRegex = /^\d{6,19}$/;
      if (!bankCardRegex.test(bankCardNo)) {
        return this.fail(res, '银行卡号格式不正确', 400);
      }

      // 检查用户是否已存在实名认证记录
      const existingAuth = await this.prisma.$queryRaw`
        SELECT id FROM "base"."mall_user_realname_auth" 
        WHERE "user_id" = ${userId}
        AND "deleted_at" IS NULL
      `;

      const currentTime = Date.now();
      const snowflakeId = this.generateSnowflakeId();

      if (existingAuth && existingAuth.length > 0) {
        // 更新现有记录
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user_realname_auth"
          SET 
            "real_name" = ${realName},
            "identity_no" = ${idCardNumber},
            "id_card_front_url" = ${idCardFront},
            "id_card_back_url" = ${idCardBack},
            "auth_phone" = ${authPhone},
            "bank_card_no" = ${bankCardNo},
            "auth_status" = 1,
            "updated_at" = ${currentTime}
          WHERE "user_id" = ${userId}
          AND "deleted_at" IS NULL
        `;
      } else {
        // 创建新记录
        await this.prisma.$executeRaw`
          INSERT INTO "base"."mall_user_realname_auth" (
            "id", "user_id", "type", "auth_status", "real_name", 
            "identity_no", "id_card_front_url", "id_card_back_url", 
            "auth_phone", "bank_card_no", "created_at", "updated_at"
          ) VALUES (
            ${snowflakeId}, ${userId}, 0, 1, ${realName}, 
            ${idCardNumber}, ${idCardFront}, ${idCardBack}, 
            ${authPhone}, ${bankCardNo}, ${currentTime}, ${currentTime}
          )
        `;
      }
      
      // 同步更新mall_user表中的realname_auth_status
      await this.prisma.$executeRaw`
        UPDATE "base"."mall_user"
        SET 
          "realname_auth_status" = 1,
          "updated_at" = ${currentTime}
        WHERE "id" = ${userId}
        AND "deleted_at" IS NULL
      `;

      // 查询更新后的实名认证信息
      const authInfo = await this.prisma.$queryRaw`
        SELECT 
          id, user_id, type, auth_status, auth_date, real_name, 
          identity_no, id_card_front_url, id_card_back_url, 
          auth_phone, bank_card_no, created_at, updated_at
        FROM "base"."mall_user_realname_auth" 
        WHERE "user_id" = ${userId}
        AND "deleted_at" IS NULL
      `;

      return this.success(res, { authInfo: authInfo[0] }, '实名认证信息保存成功，等待审核');
    } catch (error) {
      console.error('保存实名认证信息过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取用户实名认证信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getRealnameAuth(req, res) {
    try {
      // 获取用户ID
      if (!req.user || !req.user.id) {
        return this.fail(res, '用户未登录', 401);
      }
      const userId = BigInt(req.user.id);

      // 查询用户实名认证信息
      const authInfo = await this.prisma.$queryRaw`
        SELECT 
          id, user_id, type, auth_status, auth_date, real_name, 
          identity_no, id_card_front_url, id_card_back_url,
          auth_phone, bank_card_no, created_at, updated_at,
          audit_remark
        FROM "base"."mall_user_realname_auth" 
        WHERE "user_id" = ${userId}
        AND "deleted_at" IS NULL
      `;

      if (!authInfo || authInfo.length === 0) {
        return this.success(res, { authInfo: null }, '用户未进行实名认证');
      }

      return this.success(res, { authInfo: authInfo[0] }, '获取实名认证信息成功');
    } catch (error) {
      console.error('获取实名认证信息过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 生成雪花算法ID
   * @returns {BigInt} - 生成的ID
   */
  generateSnowflakeId() {
    const timestamp = BigInt(Date.now());
    const workerId = BigInt(1); // 可以根据实际情况设置
    const sequence = BigInt(Math.floor(Math.random() * 4096)); // 随机序列号
    
    // 时间戳左移22位
    const timestampShift = timestamp << 22n;
    // 工作ID左移12位
    const workerShift = workerId << 12n;
    
    // 组合ID
    return timestampShift | workerShift | sequence;
  }

  /**
   * 保存用户安全问题
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async saveSecurityQuestions(req, res) {
    try {
      // 获取用户ID
      if (!req.user || !req.user.id) {
        return this.fail(res, '用户未登录', 401);
      }
      const userId = BigInt(req.user.id);

      // 验证请求参数
      const { questions } = req.body;
      if (!questions || !Array.isArray(questions) || questions.length !== 3) {
        return this.fail(res, '请提供三个安全问题及答案', 400);
      }

      // 验证每个问题的格式
      for (let i = 0; i < questions.length; i++) {
        const { question_text, answer } = questions[i];
        if (!question_text || typeof question_text !== 'string' || question_text.trim() === '') {
          return this.fail(res, `第${i + 1}个安全问题不能为空`, 400);
        }
        if (!answer || typeof answer !== 'string' || answer.trim() === '') {
          return this.fail(res, `第${i + 1}个安全问题的答案不能为空`, 400);
        }
      }

      // 检查是否有重复的问题
      const questionTexts = questions.map(q => q.question_text.trim());
      if (new Set(questionTexts).size !== questionTexts.length) {
        return this.fail(res, '安全问题不能重复', 400);
      }

      // 开始事务处理
      const currentTime = Date.now();

      // 先删除用户现有的安全问题
      await this.prisma.$executeRaw`
        delete from "base"."mall_user_security_question"
        WHERE "user_id" = ${userId}
      `;

      // 插入新的安全问题
      for (let i = 0; i < questions.length; i++) {
        const { question_text, answer } = questions[i];
        const question_order = i + 1;
        const snowflakeId = this.generateSnowflakeId();

        // 对答案进行加盐哈希处理
        const salt = crypto.randomBytes(16).toString('hex');
        const hash = crypto.pbkdf2Sync(answer, salt, 1000, 64, 'sha512').toString('hex');
        const answer_hash = `${salt}:${hash}`;

        await this.prisma.$executeRaw`
          INSERT INTO "base"."mall_user_security_question" (
            "id", "user_id", "question_order", "question_text", "answer_hash", 
            "created_at", "updated_at"
          ) VALUES (
            ${snowflakeId}, ${userId}, ${question_order}, ${question_text.trim()}, ${answer_hash}, 
            ${currentTime}, ${currentTime}
          )
        `;
      }

      return this.success(res, {}, '安全问题设置成功');
    } catch (error) {
      console.error('保存安全问题过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取用户安全问题列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSecurityQuestions(req, res) {
    try {
      // 获取用户ID
      if (!req.user || !req.user.id) {
        return this.fail(res, '用户未登录', 401);
      }
      const userId = BigInt(req.user.id);

      // 查询用户安全问题
      const questions = await this.prisma.$queryRaw`
        SELECT 
          id, user_id, question_order, question_text, created_at, updated_at
        FROM "base"."mall_user_security_question" 
        WHERE "user_id" = ${userId}
        AND "deleted_at" IS NULL
        ORDER BY question_order ASC
      `;

      // 返回问题列表，但不包含答案哈希
      return this.success(res, { questions }, '获取安全问题列表成功');
    } catch (error) {
      console.error('获取安全问题列表过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
  /**
   * 通过用户名获取用户安全问题列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSecurityQuestionsByUsername(req, res) {
    try {
      // 获取用户名
      const { username } = req.params;
      if (!username || typeof username !== 'string' || username.trim() === '') {
        return this.fail(res, '用户名不能为空', 400);
      }

      // 先查询用户是否存在
      const users = await this.prisma.$queryRaw`
        SELECT id, email 
        FROM "base"."mall_user" 
        WHERE "username" = ${username.trim()}
        AND "deleted_at" IS NULL
        LIMIT 1
      `;

      if (!users || users.length === 0) {
        return this.fail(res, '用户不存在', 404);
      }

      const userId = BigInt(users[0].id);

      // 查询用户安全问题
      const questions = await this.prisma.$queryRaw`
        SELECT 
          id, question_order, question_text
        FROM "base"."mall_user_security_question" 
        WHERE "user_id" = ${userId}
        AND "deleted_at" IS NULL
        ORDER BY question_order ASC
      `;

      // 检查用户是否设置了安全问题
      if (!questions || questions.length === 0) {
        return this.fail(res, '该用户未设置安全问题', 404);
      }

      // 返回问题列表和用户邮箱，但不包含答案哈希和用户ID
      const email = users[0].email;
      return this.success(res, { questions, email }, '获取安全问题列表成功');
    } catch (error) {
      console.error('通过用户名获取安全问题列表过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
  
  /**
   * 校验用户安全问题答案
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async verifySecurityQuestions(req, res) {
    try {
      // 获取用户名和安全问题答案
      const { username, answers } = req.body;
      
      // 验证用户名
      if (!username || typeof username !== 'string' || username.trim() === '') {
        return this.fail(res, '用户名不能为空', 400);
      }
      
      // 验证答案格式
      if (!answers || !Array.isArray(answers) || answers.length === 0) {
        return this.fail(res, '请提供安全问题答案', 400);
      }
      
      // 检查每个答案的格式
      for (const answer of answers) {
        if (!answer.question_id || !answer.answer || typeof answer.answer !== 'string' || answer.answer.trim() === '') {
          return this.fail(res, '安全问题答案格式不正确', 400);
        }
      }
      
      // 先查询用户是否存在
      const users = await this.prisma.$queryRaw`
        SELECT id 
        FROM "base"."mall_user" 
        WHERE "username" = ${username.trim()}
        AND "deleted_at" IS NULL
        LIMIT 1
      `;

      if (!users || users.length === 0) {
        return this.fail(res, '用户不存在', 404);
      }

      const userId = BigInt(users[0].id);
      
      // 获取所有问题ID
      const questionIds = answers.map(a => BigInt(a.question_id));
      
      // 查询这些问题是否属于该用户
      const questions = await this.prisma.$queryRaw`
        SELECT 
          id, question_order, question_text, answer_hash
        FROM "base"."mall_user_security_question" 
        WHERE "user_id" = ${userId}
        AND "id" IN (${Prisma.join(questionIds)})
        AND "deleted_at" IS NULL
      `;
      
      // 检查问题数量是否匹配
      if (questions.length !== questionIds.length) {
        return this.fail(res, '部分安全问题不存在', 400);
      }
      
      // 验证每个问题的答案
      let allCorrect = true;
      let incorrectCount = 0;
      
      for (const question of questions) {
        const userAnswer = answers.find(a => BigInt(a.question_id) === BigInt(question.id))?.answer;
        if (!userAnswer) continue;
        
        // 解析存储的哈希值
        const [salt, storedHash] = question.answer_hash.split(':');
        
        // 计算用户提供答案的哈希值
        const hash = crypto.pbkdf2Sync(userAnswer, salt, 1000, 64, 'sha512').toString('hex');
        
        // 比较哈希值
        if (hash !== storedHash) {
          allCorrect = false;
          incorrectCount++;
        }
      }
      
      // 如果所有答案都正确，返回成功
      if (allCorrect) {
        // 生成一个临时令牌，用于后续的密码重置操作
        const token = crypto.randomBytes(32).toString('hex');
        const expireTime = Date.now() + 30 * 60 * 1000; // 30分钟有效期
        
        // 将令牌存储在Redis中，键名为 'reset_password_token:{token}'，值为用户ID
        if (redisUtil.isConnected && redisUtil.getClient()) {
          await redisUtil.getClient().set(`reset_password_token:${token}`, userId.toString(), { EX: 30 * 60 }); // 30分钟过期
        } else {
          console.warn('Redis未连接，无法存储密码重置令牌');
        }
        
        return this.success(res, { token, expire_time: expireTime }, '安全问题验证成功');
      } else {
        return this.fail(res, `安全问题答案不正确，还有${incorrectCount}个问题答案错误`, 400);
      }
    } catch (error) {
      console.error('校验安全问题过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
  /**
   * 重置用户密码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async resetPasswordByToken(req, res) {
    try {
      // 获取请求参数
      const { token, newPassword, resetType } = req.body;
      
      // 验证参数
      if (!token || typeof token !== 'string' || token.trim() === '') {
        return this.fail(res, '令牌不能为空', 400);
      }
      
      if (!newPassword || typeof newPassword !== 'string' || newPassword.trim() === '') {
        return this.fail(res, '新密码不能为空', 400);
      }
      
      if (newPassword.length < 6 || newPassword.length > 20) {
        return this.fail(res, '密码长度必须在6-20个字符之间', 400);
      }
      
      // 从Redis中获取用户ID
      let userId;
      if (redisUtil.isConnected && redisUtil.getClient()) {
        userId = await redisUtil.getClient().get(`reset_password_token:${token}`);
        
        if (!userId) {
          return this.fail(res, '令牌已过期或不存在，请重新验证', 400);
        }
      } else {
        return this.fail(res, 'Redis服务未连接，无法验证令牌', 500);
      }
      
      // 生成新的密码哈希
      // 加密新密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);
      
      // 更新用户密码
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "password" = ${hashedPassword}, "updated_at" = ${Date.now()}
          WHERE "id" = ${BigInt(userId)}
        `;
        
        // 删除Redis中的令牌
        await redisUtil.getClient().del(`reset_password_token:${token}`);
        
        return this.success(res, null, '密码重置成功');
      } catch (error) {
        console.error('更新用户密码失败:', error);
        return this.fail(res, '更新用户密码失败', 500);
      }
    } catch (error) {
      console.error('重置密码过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取用户余额
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getBalance(req, res) {
    try {
      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;

      if (!userId) {
        return this.fail(res, '未登录状态，无法获取余额信息', 401);
      }

      // 查询用户余额信息
      let balance = 0;

      try {
        // 从用户表中查询余额字段，如果没有余额字段则返回0
        const users = await this.prisma.$queryRaw`
          SELECT id, username, balance
          FROM "base"."mall_user"
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在或已被删除', 404);
        }

        const user = users[0];
        // 如果用户表中有余额字段，使用该值，否则默认为0
        balance = user.balance ? parseFloat(user.balance) : 0;

        console.log('用户余额:', balance);
      } catch (queryError) {
        console.error('查询用户余额失败:', queryError);
        // 如果查询失败，可能是因为余额字段不存在，返回默认值0
        balance = 0;
        console.log('查询余额失败，使用默认值0');
      }

      return this.success(res, { balance }, '获取用户余额成功');
    } catch (error) {
      console.error('获取用户余额过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
}

module.exports = UserProfileController;

const BaseController = require('../../../core/controllers/BaseController');
const BrowseHistoryService = require('../services/BrowseHistoryService');

/**
 * 商城用户浏览记录控制器
 */
class BrowseHistoryController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.browseHistoryService = new BrowseHistoryService(prisma);
  }

  /**
   * 添加浏览记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async addBrowseHistory(req, res) {
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = BigInt(req.user.id);
      const { goodsSpuId, goodsSkuId } = req.body;

      // 验证必需参数
      if (!goodsSpuId) {
        return this.fail(res, '商品ID不能为空', 400);
      }

      // 调用服务添加浏览记录
      const result = await this.browseHistoryService.addBrowseHistory({
        userId,
        goodsSpuId: BigInt(goodsSpuId),
        goodsSkuId: goodsSkuId ? BigInt(goodsSkuId) : null
      });

      return this.success(res, result, '浏览记录添加成功');
    } catch (error) {
      console.error('添加浏览记录失败:', error);
      return this.fail(res, error.message || '添加浏览记录失败', 500);
    }
  }

  /**
   * 获取用户浏览记录列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getBrowseHistory(req, res) {
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = BigInt(req.user.id);
      const { page = 1, pageSize = 20, limit } = req.query;

      // 参数验证
      const pageNum = parseInt(page);
      const pageSizeNum = parseInt(pageSize);
      const limitNum = limit ? parseInt(limit) : null;

      if (pageNum < 1 || pageSizeNum < 1 || pageSizeNum > 100) {
        return this.fail(res, '分页参数无效', 400);
      }

      // 调用服务获取浏览记录
      const result = await this.browseHistoryService.getBrowseHistory(userId, {
        page: pageNum,
        pageSize: pageSizeNum,
        limit: limitNum
      });

      // 构建包含分页信息的响应数据
      const responseData = {
        items: result.data,
        pagination: result.pagination
      };

      return this.success(res, responseData, '获取浏览记录成功');
    } catch (error) {
      console.error('获取浏览记录失败:', error);
      return this.fail(res, error.message || '获取浏览记录失败', 500);
    }
  }

  /**
   * 删除单个浏览记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async removeBrowseHistory(req, res) {
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = BigInt(req.user.id);
      const { goodsSpuId } = req.params;

      // 验证必需参数
      if (!goodsSpuId) {
        return this.fail(res, '商品ID不能为空', 400);
      }

      // 验证商品ID是否有效
      try {
        // 调用服务删除浏览记录
        const goodsSpuIdBigInt = BigInt(goodsSpuId);
        const result = await this.browseHistoryService.removeBrowseHistory(
          userId,
          goodsSpuIdBigInt
        );

        return this.success(res, result, '浏览记录删除成功');
      } catch (conversionError) {
        console.error('商品ID转换失败:', conversionError);
        return this.fail(res, '无效的商品ID格式', 400);
      }
    } catch (error) {
      console.error('删除浏览记录失败:', error);
      return this.fail(res, error.message || '删除浏览记录失败', 500);
    }
  }

  /**
   * 批量删除浏览记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async removeBrowseHistoryBatch(req, res) {
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = BigInt(req.user.id);
      const { goodsSpuIds } = req.body;

      // 验证必需参数
      if (!goodsSpuIds || !Array.isArray(goodsSpuIds) || goodsSpuIds.length === 0) {
        return this.fail(res, '请选择要删除的记录', 400);
      }

      // 转换为BigInt数组，过滤掉无效值
      const goodsSpuIdsBigInt = goodsSpuIds
        .filter(id => id !== null && id !== undefined && id !== '')
        .map(id => {
          try {
            return BigInt(id);
          } catch (error) {
            console.warn(`无法将 ${id} 转换为 BigInt，已跳过`);
            return null;
          }
        })
        .filter(id => id !== null);
      
      // 检查是否有有效ID
      if (goodsSpuIdsBigInt.length === 0) {
        return this.fail(res, '没有有效的记录可删除', 400);
      }

      // 调用服务批量删除浏览记录
      const result = await this.browseHistoryService.removeBrowseHistoryBatch(
        userId,
        goodsSpuIdsBigInt
      );

      return this.success(res, result, result.message);
    } catch (error) {
      console.error('批量删除浏览记录失败:', error);
      return this.fail(res, error.message || '批量删除浏览记录失败', 500);
    }
  }

  /**
   * 清空所有浏览记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async clearBrowseHistory(req, res) {
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = BigInt(req.user.id);

      // 调用服务清空浏览记录
      const result = await this.browseHistoryService.clearBrowseHistory(userId);

      return this.success(res, result, '浏览记录清空成功');
    } catch (error) {
      console.error('清空浏览记录失败:', error);
      return this.fail(res, error.message || '清空浏览记录失败', 500);
    }
  }

  /**
   * 获取浏览记录统计
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getBrowseHistoryStats(req, res) {
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = BigInt(req.user.id);

      // 调用服务获取浏览记录统计
      const result = await this.browseHistoryService.getBrowseHistoryStats(userId);

      return this.success(res, result.data, '获取浏览记录统计成功');
    } catch (error) {
      console.error('获取浏览记录统计失败:', error);
      return this.fail(res, error.message || '获取浏览记录统计失败', 500);
    }
  }
}

module.exports = BrowseHistoryController;

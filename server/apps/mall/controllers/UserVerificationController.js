/**
 * 商城用户验证码控制器
 * 处理验证码生成、发送和验证等功能
 */
const redisUtil = require('../../../core/utils/RedisUtil');
const VerificationCodeService = require('../../../apps/master/system/integration/services/VerificationCodeService');

// 创建验证码服务实例
const verificationCodeService = new VerificationCodeService();

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} - 是否符合格式
 */
function validatePhoneFormat(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 响应数据
 * @param {String} message - 成功消息
 * @param {Number} code - 状态码
 */
function success(res, data = null, message = '操作成功', code = 200) {
  // 处理 BigInt 类型，将其转换为字符串
  const replacer = (key, value) => {
    if (typeof value === 'bigint') {
      return value.toString();
    }
    return value;
  };

  // 使用 replacer 先将数据转换为 JSON 字符串，然后再解析回 JSON 对象
  const jsonString = JSON.stringify({ code, message, data }, replacer);
  const jsonData = JSON.parse(jsonString);

  return res.status(200).json(jsonData);
}

/**
 * 失败响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {*} data - 响应数据
 * @param {number} code - 错误代码
 * @returns {Object} 统一格式的错误响应
 */
function fail(res, message = '操作失败', data = null, code = 500) {
  // 处理 BigInt 类型，将其转换为字符串
  const replacer = (key, value) => {
    if (typeof value === 'bigint') {
      return value.toString();
    }
    return value;
  };

  // 使用 replacer 先将数据转换为 JSON 字符串，然后再解析回 JSON 对象
  const jsonString = JSON.stringify({ code, message, data }, replacer);
  const jsonData = JSON.parse(jsonString);

  return res.status(200).json(jsonData);
}

/**
 * 获取验证码
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getCaptcha(req, res) {
  try {
    const { phone } = req.query;
    
    console.log('收到获取验证码请求，手机号:', phone);
    
    // 验证手机号
    if (!phone) {
      return fail(res, '手机号不能为空', null, 400);
    }
    
    // 验证手机号格式
    if (!validatePhoneFormat(phone)) {
      return fail(res, '手机号格式不正确', null, 400);
    }
    
    // 检查Redis连接状态
    console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
    
    // 使用验证码服务生成和发送验证码
    try {
      // 使用验证码服务生成验证码
      console.log('开始生成验证码...');
      const result = await verificationCodeService.getCode(phone, 'register');
      console.log('验证码生成成功:', result.code);
      
      // 在生产环境中，不应该返回验证码
      // 这里为了方便开发和测试，返回验证码
      if (process.env.NODE_ENV !== 'production') {
        return success(res, { captcha: result.code }, '获取验证码成功');
      } else {
        return success(res, {}, '验证码已发送到您的手机');
      }
    } catch (error) {
      console.error('生成或发送验证码失败:', error);
      return fail(res, '获取验证码失败: ' + error.message, null, 500);
    }
  } catch (error) {
    console.error('获取验证码过程中出错:', error);
    return fail(res, error.message || '获取验证码失败', null, 500);
  }
}

/**
 * 验证验证码
 * @param {string} phone - 手机号
 * @param {string} captcha - 验证码
 * @param {string} type - 验证码类型
 * @returns {Promise<boolean>} - 验证结果
 */
async function verifyCaptcha(phone, captcha, type = 'register') {
  try {
    console.log(`开始验证验证码，手机号: ${phone}，验证码: ${captcha}，类型: ${type}`);
    
    // 检查Redis连接状态
    if (!redisUtil.isConnected) {
      console.error('Redis未连接，无法验证验证码');
      throw new Error('验证服务暂时不可用，请稍后再试');
    }
    
    // 从Redis中获取验证码
    const codeKey = `verification_code:${type}:${phone}`;
    console.log('验证码键:', codeKey);
    
    const savedCode = await redisUtil.getClient().get(codeKey);
    console.log('Redis中存储的验证码:', savedCode || '无');
    
    if (!savedCode) {
      console.log('验证码不存在或已过期');
      return false;
    }
    
    // 比较验证码
    console.log('验证码比较:');
    console.log(`- 输入验证码: ${captcha}, 类型: ${typeof captcha}, 长度: ${captcha.length}`);
    console.log(`- 存储验证码: ${savedCode}, 类型: ${typeof savedCode}, 长度: ${savedCode.length}`);
    
    const isValid = savedCode === captcha;
    
    if (isValid) {
      console.log('验证码验证成功');
      // 验证成功后删除验证码
      await redisUtil.getClient().del(codeKey);
    } else {
      console.log('验证码不匹配');
    }
    
    return isValid;
  } catch (error) {
    console.error('验证验证码失败:', error);
    throw error;
  }
}

/**
 * 获取忘记密码验证码
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getForgetPasswordCaptcha(req, res) {
  try {
    const { username } = req.query;
    
    console.log('收到忘记密码验证码请求，用户名:', username);
    
    // 验证用户名
    if (!username) {
      return fail(res, '用户名不能为空', null, 400);
    }
    
    // 查询用户信息，获取手机号
    try {
      // 这里需要使用 Prisma 客户端查询用户信息
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      const users = await prisma.$queryRaw`
        SELECT id, phone, status FROM "base"."mall_user" 
        WHERE ("username" = ${username} OR "phone" = ${username})
        AND "deleted_at" IS NULL
      `;
      
      if (!users || users.length === 0) {
        return fail(res, '用户不存在', null, 400);
      }
      
      const user = users[0];
      
      // 检查用户状态
      if (user.status !== 1) {
        return fail(res, '账号已被禁用，请联系客服', 403);
      }
      
      const phone = user.phone;
      
      // 检查Redis连接状态
      console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
      
      // 使用验证码服务生成和发送验证码
      try {
        // 使用验证码服务生成验证码
        console.log('开始生成忘记密码验证码...');
        const result = await verificationCodeService.getCode(phone, 'forget_password');
        console.log('验证码生成成功:', result.code);
        
        // 在生产环境中，不应该返回验证码
        // 这里为了方便开发和测试，返回验证码
        if (process.env.NODE_ENV !== 'production') {
          return success(res, { 
            captcha: result.code,
            phone: phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') // 隐藏中间4位
          }, '获取验证码成功');
        } else {
          return success(res, {
            phone: phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') // 隐藏中间4位
          }, '验证码已发送到您的手机');
        }
      } catch (error) {
        console.error('生成或发送验证码失败:', error);
        return fail(res, '获取验证码失败: ' + error.message, null, 500);
      }
    } catch (error) {
      console.error('查询用户信息失败:', error);
      return fail(res, '查询用户信息失败，请稍后再试', null, 500);
    }
  } catch (error) {
    console.error('获取忘记密码验证码过程中出错:', error);
    return fail(res, error.message || '获取验证码失败', null, 500);
  }
}

module.exports = {
  getCaptcha,
  verifyCaptcha,
  getForgetPasswordCaptcha
};

const BaseController = require('../../../core/controllers/BaseController');

/**
 * 用户微信绑定控制器
 * 处理用户微信账号绑定相关的请求
 */
class UserWechatController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.wechatAuthService = null;
  }

  /**
   * 初始化服务
   * @param {Object} wechatAuthService - 微信认证服务
   */
  initServices(wechatAuthService) {
    this.wechatAuthService = wechatAuthService;
  }

  /**
   * 获取微信绑定二维码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getBindQrcode(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 生成绑定场景值，格式：bind_时间戳_用户ID
      const timestamp = Date.now();
      const sceneStr = `bind_${timestamp}_${userId}`;
      
      // 创建二维码票据，有效期15分钟
      const qrcodeResult = await this.wechatAuthService.createQRCodeTicket(sceneStr, 900);
      
      // 将绑定场景信息存储到全局缓存
      if (!global.wechatBindScenes) {
        global.wechatBindScenes = {};
      }
      
      global.wechatBindScenes[sceneStr] = {
        userId,
        status: 'CREATED',
        createTime: timestamp,
        expireTime: timestamp + 900 * 1000, // 15分钟过期
      };
      
      // 返回二维码信息
      return this.success(res, {
        ticket: qrcodeResult.ticket,
        qrcodeUrl: qrcodeResult.qrcodeUrl,
        sceneStr,
        expireIn: 900
      });
    } catch (error) {
      console.error('获取微信绑定二维码失败:', error);
      return this.fail(res, '获取微信绑定二维码失败，请稍后再试');
    }
  }

  /**
   * 检查微信绑定状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async checkBindStatus(req, res) {
    try {
      const { sceneStr } = req.query;
      
      if (!sceneStr) {
        return this.fail(res, '参数错误');
      }
      
      // 检查绑定场景是否存在
      if (!global.wechatBindScenes || !global.wechatBindScenes[sceneStr]) {
        return this.fail(res, '绑定请求不存在或已过期');
      }
      
      const bindScene = global.wechatBindScenes[sceneStr];
      
      // 检查是否过期
      if (Date.now() > bindScene.expireTime) {
        return this.fail(res, '绑定请求已过期');
      }
      
      // 返回当前绑定状态
      return this.success(res, {
        status: bindScene.status,
        userInfo: bindScene.userInfo || null
      });
    } catch (error) {
      console.error('检查微信绑定状态失败:', error);
      return this.fail(res, '检查微信绑定状态失败');
    }
  }

  /**
   * 处理微信扫码绑定事件
   * @param {Object} event - 微信事件消息
   * @param {String} openid - 用户openid
   * @param {String} sceneStr - 场景值
   */
  async handleBindEvent(event, openid, sceneStr) {
    try {
      // 检查绑定场景是否存在
      if (!global.wechatBindScenes || !global.wechatBindScenes[sceneStr]) {
        console.log('绑定请求不存在:', sceneStr);
        return false;
      }
      
      const bindScene = global.wechatBindScenes[sceneStr];
      
      // 检查是否过期
      if (Date.now() > bindScene.expireTime) {
        console.log('绑定请求已过期:', sceneStr);
        bindScene.status = 'EXPIRED';
        return false;
      }
      
      // 获取用户信息
      const userInfo = await this.wechatAuthService.getUserInfo(openid);
      
      // 更新绑定场景状态
      bindScene.status = 'SCANNED';
      bindScene.openid = openid;
      bindScene.userInfo = userInfo;
      bindScene.scanTime = Date.now();
      
      console.log('用户扫码成功，准备绑定:', userInfo.nickname);
      
      // 更新用户表，绑定微信openid
      // 将 userId 转换为 BigInt 类型
      const userId = BigInt(bindScene.userId);
      const currentTime = Date.now();
      
      console.log('准备更新用户表，用户ID:', userId, '类型:', typeof userId);
      
      await this.prisma.$executeRaw`
        UPDATE base.mall_user 
        SET wechat_openid = ${openid}, 
            updated_at = ${currentTime}, 
            updated_by = ${userId}
        WHERE id = ${userId}
      `;
      
      // 绑定成功
      bindScene.status = 'BOUND';
      bindScene.bindTime = Date.now();
      
      console.log('微信绑定成功:', openid);
      return true;
    } catch (error) {
      console.error('处理微信绑定事件失败:', error);
      return false;
    }
  }
}

module.exports = UserWechatController;

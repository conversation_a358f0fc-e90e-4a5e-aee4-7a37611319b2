const BaseController = require('../../../core/controllers/BaseController');
const UserFavoriteService = require('../services/UserFavoriteService');

/**
 * 用户收藏控制器
 */
class UserFavoriteController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.userFavoriteService = new UserFavoriteService(prisma);
  }

  /**
   * 添加收藏
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async addFavorite(req, res) {
    try {
      // 从token中获取用户ID
      const userId = req.user.id;
      const { targetId, targetType, remark } = req.body;

      // 参数验证
      if (!targetId || targetType === undefined) {
        return this.fail(res, '参数不完整', 400);
      }

      // 验证目标类型
      if (![1, 2, 3, 4].includes(Number(targetType))) {
        return this.fail(res, '目标类型无效', 400);
      }
      
      let result;
      
      // 处理批量添加收藏（targetId为数组）
      if (Array.isArray(targetId)) {
        const batchData = targetId.map(id => ({
          userId: BigInt(userId),
          targetId: BigInt(id),
          targetType: Number(targetType),
          remark
        }));
        
        result = await this.userFavoriteService.addFavorite(batchData);
      } else {
        // 单个添加收藏
        const favoriteData = {
          userId: BigInt(userId),
          targetId: BigInt(targetId),
          targetType: Number(targetType),
          remark
        };
        
        result = await this.userFavoriteService.addFavorite(favoriteData);
      }
      
      return this.success(res, result, '收藏成功');
    } catch (error) {
      console.error('添加收藏失败:', error);
      return this.fail(res, error.message || '添加收藏失败', 500);
    }
  }

  /**
   * 取消收藏
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async cancelFavorite(req, res) {
    try {
      // 从token中获取用户ID
      const userId = req.user.id;
      const { targetId, targetType } = req.body;

      // 参数验证
      if (!targetId || targetType === undefined) {
        return this.fail(res, '参数不完整', 400);
      }

      // 验证目标类型
      if (![1, 2, 3, 4].includes(Number(targetType))) {
        return this.fail(res, '目标类型无效', 400);
      }

      let result;
      
      // 处理批量取消收藏（targetId为数组）
      if (Array.isArray(targetId)) {
        // 如果targetId是数组，但targetType是单值，则创建相同长度的targetType数组
        const targetTypes = targetId.map(() => Number(targetType));
        
        result = await this.userFavoriteService.cancelFavorite(
          BigInt(userId),
          targetId.map(id => BigInt(id)),
          targetTypes
        );
      } else {
        // 单个取消收藏
        result = await this.userFavoriteService.cancelFavorite(
          BigInt(userId),
          BigInt(targetId),
          Number(targetType)
        );
      }
      
      return this.success(res, result, '取消收藏成功');
    } catch (error) {
      console.error('取消收藏失败:', error);
      return this.fail(res, error.message || '取消收藏失败', 500);
    }
  }

  /**
   * 获取用户收藏列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getUserFavorites(req, res) {
    try {
      // 从token中获取用户ID
      const userId = req.user.id;
      const { targetType, page = 1, pageSize = 10 } = req.query;

      // 无需验证userId，因为从token中获取

      // 验证目标类型（如果提供）
      if (targetType !== undefined && ![1, 2, 3, 4].includes(Number(targetType))) {
        return this.fail(res, '目标类型无效', 400);
      }

      // 获取收藏列表
      const result = await this.userFavoriteService.getUserFavorites(
        BigInt(userId),
        targetType !== undefined ? Number(targetType) : undefined,
        { page: Number(page), pageSize: Number(pageSize) }
      );
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      return this.fail(res, '获取收藏列表失败', 500);
    }
  }

  /**
   * 检查是否已收藏
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async checkFavorite(req, res) {
    try {
      // 从token中获取用户ID
      const userId = req.user.id;
      const { targetId, targetType } = req.query;

      // 参数验证
      if (!targetId || targetType === undefined) {
        return this.fail(res, '参数不完整', 400);
      }

      // 验证目标类型
      if (![1, 2, 3, 4].includes(Number(targetType))) {
        return this.fail(res, '目标类型无效', 400);
      }

      // 检查是否已收藏
      const isFavorited = await this.userFavoriteService.checkFavorite(
        BigInt(userId),
        BigInt(targetId),
        Number(targetType)
      );
      
      return this.success(res, { isFavorited }, '查询成功');
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      return this.fail(res, '检查收藏状态失败', 500);
    }
  }
}

module.exports = UserFavoriteController;

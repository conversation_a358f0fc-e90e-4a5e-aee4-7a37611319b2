[mine-skin="mine"] {

  .arco-menu-light .arco-menu-item .arco-icon,
  .arco-menu-inline-header .arco-icon {
    color: var(--color-text-1);
  }

  .arco-trigger-menu .arco-trigger-menu-has-icon .arco-trigger-menu-icon,
  .sys-menus .arco-menu-icon .icon {
    fill: var(--color-text-2);
  }

  .arco-menu-light .arco-menu-item.arco-menu-selected .arco-icon {
    color: rgb(var(--primary-6));
  }

  .layout-banner-header .banner-menus li.active {
    background-color: rgb(var(--primary-1));
    color: rgb(var(--primary-6));
    fill: rgb(var(--primary-6));
  }

  .layout-columns-left-panel .sider {
    background-color: var(--color-bg-1);
  }

  .parent-menu {
    color: var(--color-text-2);
    fill: var(--color-text-2);
  }

  .parent-menu:hover {
    background-color: var(--color-fill-2);
    fill: var(--color-text-2);
  }


  .parent-menu.active {
    background-color: rgb(var(--primary-1));
    color: rgb(var(--primary-6));
    fill: rgb(var(--primary-6));
  }

  .sys-menus .arco-menu-item.arco-menu-selected .icon {
    fill: rgb(var(--primary-6));
  }
}
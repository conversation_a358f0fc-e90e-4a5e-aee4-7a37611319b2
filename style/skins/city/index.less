[mine-skin="city"] {
  .main-container {
    background-image: url('@/style/skins/city/background.jpg') !important;
    background-size: cover;
  }

  .logo {
    border-bottom: 0;
  }

  .sys-menus .arco-menu-icon .icon {
    fill: #fff;
  }

  .layout-classic-header .layout-classic-header-container,
  .arco-layout-sider,
  .arco-layout-sider-light,
  .arco-menu-light,
  .tags-container,
  .tags-container .tags,
  .layout-banner-header {
    background: none;
  }
  .tags-container { border: 0; }
  .tags,
  .layout-banner-header {
    border: 0;
  }

  .tags-container .tags {
    border: 0px;
  }

  .logo span,
  .arco-breadcrumb div,
  .arco-breadcrumb-item:last-child .layout-banner-header .banner-menus li.active,
  .arco-menu-light .arco-menu-pop-header,
  .layout-banner-header .banner-menus li {
    color: var(--color-white);
    fill: var(--color-white);
  }

  .layout-banner-header .banner-menus li:hover {
    background-color: rgba(0, 0, 0, 0.2);
  }

  .layout-banner-header .banner-menus li.active {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .operation-area .arco-btn-secondary {
    background-color: rgba(0, 0, 0, 0.2);
    color: #fff;
  }

  .operation-area .arco-btn-secondary:hover,
  .operation-area .arco-btn-secondary[type='button']:hover {
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
  }

  .arco-menu-light .arco-menu-item {
    background-color: rgba(0, 0, 0, 0);
    color: #fff;
    fill: #fff;
  }

  .arco-menu-light .arco-menu-item:hover,
  .arco-menu-light .arco-menu-inline-header:hover,
  .arco-menu-light .arco-menu-item.arco-menu-selected {
    background-color: rgba(0, 0, 0, 0.35);
  }


  .ma-menu .icon,
  .arco-menu-selected .icon {
    fill: #fff;
  }

  .arco-menu-light .arco-menu-inline-header,
  .arco-menu-light .arco-menu-pop-header,
  .arco-menu-light .arco-menu-collapse-button,
  .arco-menu-light .arco-menu-collapse-button:hover {
    background-color: rgba(255, 255, 255, 0);
  }

  .arco-menu-light .arco-menu-inline-header.arco-menu-selected:hover,
  .arco-menu-light .arco-menu-pop-header:hover,
  .arco-menu-light .arco-menu-pop-header.arco-menu-selected,
  .arco-menu-light.arco-menu-horizontal .arco-menu-pop-header.arco-menu-selected:hover
  {
    background-color: rgba(0, 0, 0, 0.35);
  }

  .arco-menu-light .arco-menu-pop-header .arco-icon,
  .arco-menu-light .arco-menu-pop-header.arco-menu-selected .arco-menu-icon,
  .arco-menu-light .arco-menu-item .arco-icon,
  .arco-menu-light .arco-menu-item .arco-menu-icon,
  .arco-menu-light .arco-menu-inline-header .arco-icon,
  .arco-menu-light .arco-menu-inline-header {
    color: #fff;
  }

  .layout-banner-header .banner-menus li.active {
    background: rgb(var(--primary-6));
  }

  .work-area {
    background-color: rgba(255, 255, 255, 0.85);
    border-radius: 4px;
  }

  .layout-columns-left-panel {

    .menu-title {
      height: 51px; padding-left: 10px; font-weight: bold;
      color: #fff;
      background-color: rgba(255, 255, 255, 0);
      border-bottom: none;
    }
  }

  .layout-columns-right-panel,
  .layout-columns-right-panel .layout-header {
    background-color: rgba(255, 255, 255, 0);
    box-shadow: none;

    .work-area {
      height: calc(100% - 85px);
    }
  }

  .layout-columns-left-panel .sider {
    background-color: rgba(0, 0, 0, 0.2);
  }
}

[mine-skin="city"][arco-theme="dark"] {
  .work-area {
    background-color: rgba(0, 0, 0, 0.80);
  }

  .arco-trigger-menu .arco-trigger-menu-has-icon .arco-trigger-menu-icon {
    fill: #fff;
  }
}
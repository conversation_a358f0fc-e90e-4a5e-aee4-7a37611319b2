/**
 * 开发环境启动脚本 - 同时启动前端和服务端开发服务
 * 前端端口: 3000
 * 服务端端口: 4000
 */

import { spawn } from 'child_process';
import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __dirname = dirname(fileURLToPath(import.meta.url));

// 定义颜色代码
const colors = {
  frontend: '\x1b[36m', // 青色
  backend: '\x1b[32m',  // 绿色
  error: '\x1b[31m',    // 红色
  reset: '\x1b[0m'      // 重置颜色
};

// 日志前缀
const logPrefix = {
  frontend: `${colors.frontend}[前端开发]\x1b[0m`,
  backend: `${colors.backend}[服务端开发]\x1b[0m`
};

// 启动前端开发服务 (Nuxt Dev)
function startFrontendDev() {
  console.log(`${logPrefix.frontend} 正在启动前端开发服务，端口: 3000...`);
  
  // 使用 nuxt dev 启动 Nuxt 开发服务
  const frontend = spawn('npx', ['nuxt', 'dev'], {
    cwd: __dirname,
    env: {
      ...process.env,
      PORT: 3000,
      HOST: '0.0.0.0',
      // 添加 Sass 相关环境变量
      SASS_PATH: resolve(__dirname, 'node_modules'),
      NODE_OPTIONS: '--max-old-space-size=4096',
      VITE_CJS_IGNORE_WARNING: 'true'
    },
    stdio: 'inherit', // 直接将输出传递到父进程
    shell: true
  });

  frontend.on('close', (code) => {
    if (code !== 0) {
      console.error(`${logPrefix.frontend} ${colors.error}前端开发服务已退出，退出码: ${code}${colors.reset}`);
    } else {
      console.log(`${logPrefix.frontend} 前端开发服务已正常退出`);
    }
  });

  return frontend;
}

// 启动后端开发服务
function startBackendDev() {
  console.log(`${logPrefix.backend} 正在启动服务端开发服务，端口: 4000...`);
  
  // 启动服务端开发服务
  // 假设服务端使用 nodemon 或类似工具进行开发环境热重载
  const backend = spawn('node', ['--watch', 'index.js'], {
    cwd: resolve(__dirname, 'server'),
    env: {
      ...process.env,
      PORT: 4000,
      NODE_ENV: 'development',
      NODE_OPTIONS: '--max-old-space-size=4096'
    },
    stdio: 'inherit', // 直接将输出传递到父进程
    shell: true
  });

  backend.on('close', (code) => {
    if (code !== 0) {
      console.error(`${logPrefix.backend} ${colors.error}服务端开发服务已退出，退出码: ${code}${colors.reset}`);
    } else {
      console.log(`${logPrefix.backend} 服务端开发服务已正常退出`);
    }
  });

  return backend;
}

// 处理进程退出
function handleProcessExit(frontendProcess, backendProcess) {
  process.on('SIGINT', () => {
    console.log('\n正在关闭所有开发服务...');
    
    // 关闭前端进程
    if (frontendProcess && !frontendProcess.killed) {
      frontendProcess.kill('SIGINT');
    }
    
    // 关闭后端进程
    if (backendProcess && !backendProcess.killed) {
      backendProcess.kill('SIGINT');
    }
    
    // 给进程一些时间来清理
    setTimeout(() => {
      console.log('所有开发服务已关闭');
      process.exit(0);
    }, 1000);
  });
}

// 主函数
function main() {
  console.log('正在启动聚灵云4.0开发环境...');
  
  // 启动前端和后端开发服务
  const frontendProcess = startFrontendDev();
  const backendProcess = startBackendDev();
  
  // 处理进程退出
  handleProcessExit(frontendProcess, backendProcess);
  
  console.log('\n开发环境启动中，请等待服务就绪...');
  console.log(`前端开发服务将在: http://localhost:3000`);
  console.log(`服务端开发服务将在: http://localhost:4000`);
}

// 执行主函数
main();
